using HappyWechat.Web.Services.Interfaces;
using Microsoft.Extensions.Logging;
using MudBlazor;

namespace HappyWechat.Web.Services;

/// <summary>
/// UI通知服务实现 - 基于MudBlazor的ISnackbar
/// 提供统一的UI通知功能，支持Blazor组件
/// </summary>
public class UINotificationService : IUINotificationService
{
    private readonly ISnackbar? _snackbar;
    private readonly ILogger<UINotificationService> _logger;

    public UINotificationService(
        ILogger<UINotificationService> logger,
        ISnackbar? snackbar = null)
    {
        _logger = logger;
        _snackbar = snackbar;
    }

    /// <summary>
    /// 检查通知服务是否可用
    /// </summary>
    public bool IsAvailable => _snackbar != null;

    /// <summary>
    /// 显示信息通知
    /// </summary>
    public void ShowInfo(string message)
    {
        if (_snackbar != null)
        {
            _snackbar.Add(message, Severity.Info);
        }
        _logger.LogInformation("UI通知 - 信息: {Message}", message);
    }

    /// <summary>
    /// 显示成功通知
    /// </summary>
    public void ShowSuccess(string message)
    {
        if (_snackbar != null)
        {
            _snackbar.Add(message, Severity.Success);
        }
        _logger.LogInformation("UI通知 - 成功: {Message}", message);
    }

    /// <summary>
    /// 显示警告通知
    /// </summary>
    public void ShowWarning(string message)
    {
        if (_snackbar != null)
        {
            _snackbar.Add(message, Severity.Warning);
        }
        _logger.LogWarning("UI通知 - 警告: {Message}", message);
    }

    /// <summary>
    /// 显示错误通知
    /// </summary>
    public void ShowError(string message)
    {
        if (_snackbar != null)
        {
            _snackbar.Add(message, Severity.Error);
        }
        _logger.LogError("UI通知 - 错误: {Message}", message);
    }

    /// <summary>
    /// 显示加载状态通知
    /// </summary>
    public void ShowLoading(string message)
    {
        if (_snackbar != null)
        {
            _snackbar.Add(message, Severity.Info);
        }
        _logger.LogDebug("UI通知 - 加载: {Message}", message);
    }
}
