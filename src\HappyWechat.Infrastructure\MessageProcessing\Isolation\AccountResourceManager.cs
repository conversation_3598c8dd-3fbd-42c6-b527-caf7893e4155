using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace HappyWechat.Infrastructure.MessageProcessing.Isolation;

/// <summary>
/// 账号资源管理器
/// 实现真正的多租户隔离，为每个微信账号分配独立的资源池
/// </summary>
public interface IAccountResourceManager
{
    /// <summary>
    /// 获取账号的资源配额
    /// </summary>
    Task<AccountResourceQuota> GetAccountQuotaAsync(Guid wxManagerId);
    
    /// <summary>
    /// 申请资源
    /// </summary>
    Task<ResourceAllocation?> AllocateResourceAsync(Guid wxManagerId, ResourceType resourceType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 释放资源
    /// </summary>
    Task ReleaseResourceAsync(ResourceAllocation allocation);
    
    /// <summary>
    /// 获取账号资源使用情况
    /// </summary>
    Task<AccountResourceUsage> GetResourceUsageAsync(Guid wxManagerId);
    
    /// <summary>
    /// 设置账号优先级
    /// </summary>
    Task SetAccountPriorityAsync(Guid wxManagerId, AccountPriority priority);
}

/// <summary>
/// 资源类型
/// </summary>
public enum ResourceType
{
    /// <summary>
    /// CPU处理资源
    /// </summary>
    CpuProcessing,
    
    /// <summary>
    /// 内存资源
    /// </summary>
    Memory,
    
    /// <summary>
    /// 网络带宽
    /// </summary>
    NetworkBandwidth,
    
    /// <summary>
    /// 文件处理
    /// </summary>
    FileProcessing,
    
    /// <summary>
    /// AI调用
    /// </summary>
    AiProcessing
}

/// <summary>
/// 账号优先级
/// </summary>
public enum AccountPriority
{
    Low = 1,
    Normal = 2,
    High = 3,
    VIP = 4
}

/// <summary>
/// 账号资源配额
/// </summary>
public class AccountResourceQuota
{
    public Guid WxManagerId { get; set; }
    public AccountPriority Priority { get; set; } = AccountPriority.Normal;
    public int MaxConcurrentTasks { get; set; } = 5;
    public int MaxMemoryMB { get; set; } = 100;
    public int MaxNetworkMbps { get; set; } = 10;
    public int MaxFileProcessing { get; set; } = 3;
    public int MaxAiCalls { get; set; } = 10;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 资源分配
/// </summary>
public class ResourceAllocation
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public Guid WxManagerId { get; set; }
    public ResourceType ResourceType { get; set; }
    public int AllocatedAmount { get; set; }
    public DateTime AllocatedAt { get; set; } = DateTime.UtcNow;
    public string? TaskId { get; set; }
}

/// <summary>
/// 账号资源使用情况
/// </summary>
public class AccountResourceUsage
{
    public Guid WxManagerId { get; set; }
    public int ActiveTasks { get; set; }
    public int UsedMemoryMB { get; set; }
    public int UsedNetworkMbps { get; set; }
    public int ActiveFileProcessing { get; set; }
    public int ActiveAiCalls { get; set; }
    public double CpuUsagePercent { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class AccountResourceManager : IAccountResourceManager
{
    private readonly ILogger<AccountResourceManager> _logger;
    private readonly IConfiguration _configuration;
    
    // 账号配额缓存
    private readonly ConcurrentDictionary<Guid, AccountResourceQuota> _quotaCache = new();
    
    // 资源分配跟踪
    private readonly ConcurrentDictionary<Guid, List<ResourceAllocation>> _allocations = new();
    
    // 资源信号量池
    private readonly ConcurrentDictionary<Guid, Dictionary<ResourceType, SemaphoreSlim>> _resourceSemaphores = new();
    
    // 账号优先级
    private readonly ConcurrentDictionary<Guid, AccountPriority> _accountPriorities = new();
    
    public AccountResourceManager(
        ILogger<AccountResourceManager> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }
    
    /// <summary>
    /// 获取账号的资源配额
    /// </summary>
    public async Task<AccountResourceQuota> GetAccountQuotaAsync(Guid wxManagerId)
    {
        if (_quotaCache.TryGetValue(wxManagerId, out var cachedQuota))
        {
            return cachedQuota;
        }
        
        // 根据账号优先级创建配额
        var priority = _accountPriorities.GetValueOrDefault(wxManagerId, AccountPriority.Normal);
        var quota = CreateQuotaByPriority(wxManagerId, priority);
        
        _quotaCache.TryAdd(wxManagerId, quota);
        
        _logger.LogDebug("📊 创建账号资源配额 - WxManagerId: {WxManagerId}, Priority: {Priority}, MaxTasks: {MaxTasks}",
            wxManagerId, priority, quota.MaxConcurrentTasks);
        
        return quota;
    }
    
    /// <summary>
    /// 申请资源
    /// </summary>
    public async Task<ResourceAllocation?> AllocateResourceAsync(Guid wxManagerId, ResourceType resourceType, CancellationToken cancellationToken = default)
    {
        try
        {
            var quota = await GetAccountQuotaAsync(wxManagerId);
            var semaphore = GetOrCreateSemaphore(wxManagerId, resourceType, quota);
            
            // 尝试获取资源
            var acquired = await semaphore.WaitAsync(5000, cancellationToken);
            if (!acquired)
            {
                _logger.LogWarning("⚠️ 资源申请超时 - WxManagerId: {WxManagerId}, ResourceType: {ResourceType}",
                    wxManagerId, resourceType);
                return null;
            }
            
            var allocation = new ResourceAllocation
            {
                WxManagerId = wxManagerId,
                ResourceType = resourceType,
                AllocatedAmount = 1
            };
            
            // 记录分配
            _allocations.AddOrUpdate(wxManagerId,
                new List<ResourceAllocation> { allocation },
                (key, existing) =>
                {
                    existing.Add(allocation);
                    return existing;
                });
            
            _logger.LogDebug("✅ 资源分配成功 - WxManagerId: {WxManagerId}, ResourceType: {ResourceType}, AllocationId: {AllocationId}",
                wxManagerId, resourceType, allocation.Id);
            
            return allocation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 资源分配异常 - WxManagerId: {WxManagerId}, ResourceType: {ResourceType}",
                wxManagerId, resourceType);
            return null;
        }
    }
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public async Task ReleaseResourceAsync(ResourceAllocation allocation)
    {
        try
        {
            var semaphore = GetOrCreateSemaphore(allocation.WxManagerId, allocation.ResourceType, null);
            semaphore.Release();
            
            // 移除分配记录
            if (_allocations.TryGetValue(allocation.WxManagerId, out var allocations))
            {
                allocations.RemoveAll(a => a.Id == allocation.Id);
            }
            
            _logger.LogDebug("🔓 资源释放成功 - WxManagerId: {WxManagerId}, ResourceType: {ResourceType}, AllocationId: {AllocationId}",
                allocation.WxManagerId, allocation.ResourceType, allocation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 资源释放异常 - AllocationId: {AllocationId}", allocation.Id);
        }
    }
    
    /// <summary>
    /// 获取账号资源使用情况
    /// </summary>
    public async Task<AccountResourceUsage> GetResourceUsageAsync(Guid wxManagerId)
    {
        var allocations = _allocations.GetValueOrDefault(wxManagerId, new List<ResourceAllocation>());
        
        return new AccountResourceUsage
        {
            WxManagerId = wxManagerId,
            ActiveTasks = allocations.Count(a => a.ResourceType == ResourceType.CpuProcessing),
            UsedMemoryMB = allocations.Count(a => a.ResourceType == ResourceType.Memory) * 10, // 估算
            UsedNetworkMbps = allocations.Count(a => a.ResourceType == ResourceType.NetworkBandwidth),
            ActiveFileProcessing = allocations.Count(a => a.ResourceType == ResourceType.FileProcessing),
            ActiveAiCalls = allocations.Count(a => a.ResourceType == ResourceType.AiProcessing),
            CpuUsagePercent = Math.Min(100, allocations.Count * 10), // 估算
            LastUpdated = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 设置账号优先级
    /// </summary>
    public async Task SetAccountPriorityAsync(Guid wxManagerId, AccountPriority priority)
    {
        _accountPriorities.AddOrUpdate(wxManagerId, priority, (key, existing) => priority);
        
        // 清除配额缓存，强制重新计算
        _quotaCache.TryRemove(wxManagerId, out _);
        
        _logger.LogInformation("🎯 账号优先级已更新 - WxManagerId: {WxManagerId}, Priority: {Priority}",
            wxManagerId, priority);
    }
    
    /// <summary>
    /// 根据优先级创建配额
    /// </summary>
    private AccountResourceQuota CreateQuotaByPriority(Guid wxManagerId, AccountPriority priority)
    {
        var baseQuota = new AccountResourceQuota
        {
            WxManagerId = wxManagerId,
            Priority = priority
        };
        
        // 根据优先级调整配额
        var multiplier = priority switch
        {
            AccountPriority.Low => 0.5,
            AccountPriority.Normal => 1.0,
            AccountPriority.High => 2.0,
            AccountPriority.VIP => 4.0,
            _ => 1.0
        };
        
        baseQuota.MaxConcurrentTasks = (int)(5 * multiplier);
        baseQuota.MaxMemoryMB = (int)(100 * multiplier);
        baseQuota.MaxNetworkMbps = (int)(10 * multiplier);
        baseQuota.MaxFileProcessing = (int)(3 * multiplier);
        baseQuota.MaxAiCalls = (int)(10 * multiplier);
        
        return baseQuota;
    }
    
    /// <summary>
    /// 获取或创建资源信号量
    /// </summary>
    private SemaphoreSlim GetOrCreateSemaphore(Guid wxManagerId, ResourceType resourceType, AccountResourceQuota? quota)
    {
        var accountSemaphores = _resourceSemaphores.GetOrAdd(wxManagerId, _ => new Dictionary<ResourceType, SemaphoreSlim>());
        
        if (accountSemaphores.TryGetValue(resourceType, out var existingSemaphore))
        {
            return existingSemaphore;
        }
        
        // 创建新的信号量
        quota ??= GetAccountQuotaAsync(wxManagerId).Result;
        
        var maxCount = resourceType switch
        {
            ResourceType.CpuProcessing => quota.MaxConcurrentTasks,
            ResourceType.Memory => quota.MaxMemoryMB / 10, // 每10MB一个单位
            ResourceType.NetworkBandwidth => quota.MaxNetworkMbps,
            ResourceType.FileProcessing => quota.MaxFileProcessing,
            ResourceType.AiProcessing => quota.MaxAiCalls,
            _ => 5
        };
        
        var semaphore = new SemaphoreSlim(maxCount, maxCount);
        accountSemaphores[resourceType] = semaphore;
        
        return semaphore;
    }
}
