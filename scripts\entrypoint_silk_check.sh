#!/bin/bash

# 🔧 Docker容器启动时的silk_v3_decoder检查和修复脚本
# 确保容器启动时silk_v3_decoder可用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[ENTRYPOINT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SILK_DIR="/opt/silk_v3_decoder"
SILK_BIN="$SILK_DIR/bin/silk_v3_decoder"
SILK_REPO_URL="https://github.com/kn007/silk-v3-decoder.git"

log_info "容器启动时检查silk_v3_decoder状态..."

# 第一步：检查存储卷目录
if [ ! -d "$SILK_DIR" ]; then
    log_warning "存储卷目录不存在，创建: $SILK_DIR"
    mkdir -p "$SILK_DIR"
fi

# 第二步：检查silk_v3_decoder可执行文件
check_silk_decoder() {
    log_info "检查silk_v3_decoder可执行文件..."
    
    # 检查存储卷中的文件
    if [ -f "$SILK_BIN" ] && [ -x "$SILK_BIN" ]; then
        log_info "发现存储卷中的silk_v3_decoder: $SILK_BIN"
        
        # 测试可执行性
        if "$SILK_BIN" --help >/dev/null 2>&1 || "$SILK_BIN" >/dev/null 2>&1; then
            log_success "silk_v3_decoder功能正常"
            return 0
        else
            log_warning "silk_v3_decoder存在但无法正常执行"
        fi
    else
        log_warning "存储卷中未找到可用的silk_v3_decoder"
    fi
    
    # 检查系统路径中的文件
    local system_paths=(
        "/usr/local/bin/silk_v3_decoder"
        "/usr/bin/silk_v3_decoder"
        "/app/silk_v3_decoder"
        "/opt/silk/silk_v3_decoder"
    )
    
    for path in "${system_paths[@]}"; do
        if [ -f "$path" ] && [ -x "$path" ]; then
            log_info "发现系统路径中的silk_v3_decoder: $path"
            
            # 复制到存储卷
            mkdir -p "$SILK_DIR/bin"
            cp "$path" "$SILK_BIN"
            chmod +x "$SILK_BIN"
            
            log_success "已将silk_v3_decoder复制到存储卷"
            return 0
        fi
    done
    
    return 1
}

# 第三步：如果检查失败，尝试重新编译
rebuild_silk_decoder() {
    log_warning "silk_v3_decoder不可用，尝试重新编译..."
    
    cd "$SILK_DIR"
    
    # 安装编译依赖
    log_info "安装编译依赖..."
    if command -v apk >/dev/null 2>&1; then
        # Alpine系统
        apk update >/dev/null 2>&1 || true
        apk add --no-cache git build-base gcc g++ make >/dev/null 2>&1 || true
    elif command -v apt-get >/dev/null 2>&1; then
        # Debian/Ubuntu系统
        export DEBIAN_FRONTEND=noninteractive
        apt-get update >/dev/null 2>&1 || true
        apt-get install -y git build-essential gcc g++ make >/dev/null 2>&1 || true
    elif command -v yum >/dev/null 2>&1; then
        # RedHat/CentOS系统
        yum groupinstall -y "Development Tools" >/dev/null 2>&1 || true
        yum install -y git gcc gcc-c++ make >/dev/null 2>&1 || true
    fi
    
    # 清理现有内容
    rm -rf .git src bin Makefile README.md 2>/dev/null || true
    
    # 克隆源码
    log_info "克隆silk-v3-decoder源码..."
    if git clone "$SILK_REPO_URL" temp_clone >/dev/null 2>&1; then
        mv temp_clone/* . 2>/dev/null || true
        mv temp_clone/.git . 2>/dev/null || true
        rm -rf temp_clone
        
        # 编译
        log_info "编译silk_v3_decoder..."
        if make >/dev/null 2>&1; then
            if [ -f "$SILK_BIN" ]; then
                chmod +x "$SILK_BIN"
                log_success "silk_v3_decoder重新编译成功"
                return 0
            fi
        fi
    fi
    
    log_error "silk_v3_decoder重新编译失败"
    return 1
}

# 第四步：显示诊断信息
show_diagnostic_info() {
    log_info "=== Silk Decoder 诊断信息 ==="
    echo "检查时间: $(date)"
    echo "存储卷目录: $SILK_DIR"
    echo "目标文件: $SILK_BIN"
    echo ""
    
    if [ -d "$SILK_DIR" ]; then
        echo "存储卷目录存在: $SILK_DIR"
        echo "存储卷内容 ($(ls -1 "$SILK_DIR" 2>/dev/null | wc -l)个项目): $(ls -1 "$SILK_DIR" 2>/dev/null | tr '\n' ', ' | sed 's/,$//')"
        
        if [ -d "$SILK_DIR/bin" ]; then
            echo "bin目录内容: $(ls -1 "$SILK_DIR/bin" 2>/dev/null | tr '\n' ', ' | sed 's/,$//')"
        else
            echo "bin目录不存在"
        fi
    else
        echo "存储卷目录不存在: $SILK_DIR"
    fi
    
    echo ""
    echo "系统路径检查结果:"
    local paths=(
        "/opt/silk_v3_decoder/bin/silk_v3_decoder"
        "/usr/local/bin/silk_v3_decoder"
        "/usr/bin/silk_v3_decoder"
        "/app/silk_v3_decoder"
        "/opt/silk/silk_v3_decoder"
    )
    
    for path in "${paths[@]}"; do
        if [ -f "$path" ] && [ -x "$path" ]; then
            echo "$path: 可用"
        else
            echo "$path: 不可用"
        fi
    done
    echo "================================"
}

# 执行检查流程
main() {
    if check_silk_decoder; then
        log_success "silk_v3_decoder检查通过，容器可以正常启动"
        show_diagnostic_info
        return 0
    else
        log_warning "silk_v3_decoder检查失败，尝试修复..."
        
        if rebuild_silk_decoder; then
            log_success "silk_v3_decoder修复成功"
            show_diagnostic_info
            return 0
        else
            log_error "silk_v3_decoder修复失败"
            show_diagnostic_info
            return 1
        fi
    fi
}

# 运行主函数
main

# 无论成功失败，都继续启动应用
log_info "继续启动应用程序..."
