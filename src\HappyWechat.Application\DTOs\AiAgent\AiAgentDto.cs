using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiAgent;

/// <summary>
/// AI智能体DTO
/// </summary>
public class AiAgentDto
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// AI提供商类型
    /// </summary>
    public AiProviderType ProviderType { get; set; }
    
    /// <summary>
    /// 提供商类型显示名称
    /// </summary>
    public string ProviderTypeName => ProviderType switch
    {
        AiProviderType.CoZe => "扣子",
        AiProviderType.MaxKB => "MaxKB",
        AiProviderType.Dify => "Dify",
        AiProviderType.ChatGPT => "ChatGPT",
        _ => "未知"
    };
    
    /// <summary>
    /// 智能体名称
    /// </summary>
    public required string Name { get; set; }
    
    /// <summary>
    /// 智能体描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
    
    /// <summary>
    /// 配置信息（根据提供商类型解析）
    /// </summary>
    public AiAgentConfigDto? Config { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
