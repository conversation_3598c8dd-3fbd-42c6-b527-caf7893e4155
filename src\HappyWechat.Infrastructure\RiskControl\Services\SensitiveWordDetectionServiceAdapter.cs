using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Infrastructure.RiskControl.Services;

namespace HappyWechat.Infrastructure.RiskControl.Services;

/// <summary>
/// 敏感词检测服务适配器 - 将Infrastructure层的服务适配到Application层接口
/// </summary>
public class SensitiveWordDetectionServiceAdapter : HappyWechat.Application.Interfaces.ISensitiveWordDetectionService
{
    private readonly HappyWechat.Infrastructure.RiskControl.Services.ISensitiveWordDetectionService _infrastructureService;
    private readonly ILogger<SensitiveWordDetectionServiceAdapter> _logger;

    public SensitiveWordDetectionServiceAdapter(
        HappyWechat.Infrastructure.RiskControl.Services.ISensitiveWordDetectionService infrastructureService,
        ILogger<SensitiveWordDetectionServiceAdapter> logger)
    {
        _infrastructureService = infrastructureService;
        _logger = logger;
    }

    public async Task<SensitiveWordDetectionResult> DetectAsync(string text, string messageType = "unknown")
    {
        try
        {
            var result = await _infrastructureService.ProcessAsync(text, SensitiveWordActionType.LogOnly, messageType);
            
            return new SensitiveWordDetectionResult
            {
                HasSensitiveWords = result.ContainsSensitiveWords,
                DetectedWords = result.DetectedWords,
                OriginalText = text,
                DetectionTime = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "敏感词检测失败 - Text: {Text}, MessageType: {MessageType}", text, messageType);
            return new SensitiveWordDetectionResult
            {
                HasSensitiveWords = false,
                DetectedWords = new List<string>(),
                OriginalText = text,
                DetectionTime = DateTime.UtcNow
            };
        }
    }

    public async Task<SensitiveWordProcessResult> ProcessAsync(string text, SensitiveWordActionType action, string messageType = "unknown")
    {
        try
        {
            var result = await _infrastructureService.ProcessAsync(text, action, messageType);
            
            return new SensitiveWordProcessResult
            {
                ShouldBlock = result.ShouldBlock,
                ProcessedText = result.ProcessedText,
                DetectionResult = new SensitiveWordDetectionResult
                {
                    HasSensitiveWords = result.ContainsSensitiveWords,
                    DetectedWords = result.DetectedWords,
                    OriginalText = text,
                    DetectionTime = DateTime.UtcNow
                },
                Action = action,
                Logged = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "敏感词处理失败 - Text: {Text}, Action: {Action}, MessageType: {MessageType}", 
                text, action, messageType);
            return new SensitiveWordProcessResult
            {
                ShouldBlock = false,
                ProcessedText = text,
                DetectionResult = new SensitiveWordDetectionResult
                {
                    HasSensitiveWords = false,
                    DetectedWords = new List<string>(),
                    OriginalText = text,
                    DetectionTime = DateTime.UtcNow
                },
                Action = action,
                Logged = false
            };
        }
    }

    public async Task<bool> ShouldBlockAsync(string text, string messageType = "unknown")
    {
        try
        {
            var result = await _infrastructureService.ProcessAsync(text, SensitiveWordActionType.Block, messageType);
            return result.ShouldBlock;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查是否应该阻止失败 - Text: {Text}, MessageType: {MessageType}", text, messageType);
            return false; // 异常情况下不阻止
        }
    }

    public async Task<string> ReplaceSensitiveWordsAsync(string text)
    {
        try
        {
            var result = await _infrastructureService.ProcessAsync(text, SensitiveWordActionType.Replace, "replace");
            return result.ProcessedText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "替换敏感词失败 - Text: {Text}", text);
            return text; // 异常情况下返回原文本
        }
    }
}
