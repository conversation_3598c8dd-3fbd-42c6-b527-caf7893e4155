﻿namespace HappyWechat.Application.Commons;

public class PageQuery
{
    public int Page { get; set; } = 1;

    public int PageSize { get; set; } = 10;
}

public class PageResponse<T>
{
    public int Page { get; set; } = 1;

    public int PageSize { get; set; } = 10;

    public int TotalCount { get; set; } = 0;

    public int TotalPages { get; set; }

    public bool HasNextPage { get; set; }

    public bool HasPreviousPage { get; set; }

    public List<T> Items { get; set; } = new List<T>();

    /// <summary>
    /// 数据列表（兼容性属性）
    /// </summary>
    public List<T> Data
    {
        get => Items;
        set => Items = value;
    }

    // 自动计算的属性
    public int StartIndex => (Page - 1) * PageSize + 1;
    public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);
    public bool IsFirstPage => Page == 1;
    public bool IsLastPage => Page == TotalPages;

    // 兼容性属性
    [Obsolete("Use TotalPages property instead")]
    public int TotalPagesOld => (int)Math.Ceiling((double)TotalCount / PageSize);

    public static PageResponse<TNew> ReplaceItems<TNew, TOld>(PageResponse<TOld> old, List<TNew> items)
    {
        return new PageResponse<TNew>()
        {
            Page = old.Page,
            PageSize = old.PageSize,
            TotalCount = old.TotalCount,
            TotalPages = old.TotalPages,
            HasNextPage = old.HasNextPage,
            HasPreviousPage = old.HasPreviousPage,
            Items = items
        };
    }

    // 创建空的分页响应
    public static PageResponse<T> Empty()
    {
        return new PageResponse<T>
        {
            Page = 1,
            PageSize = 10,
            TotalCount = 0,
            TotalPages = 0,
            HasNextPage = false,
            HasPreviousPage = false,
            Items = new List<T>()
        };
    }

    // 创建单页响应
    public static PageResponse<T> Single(List<T> items)
    {
        return new PageResponse<T>
        {
            Page = 1,
            PageSize = items.Count,
            TotalCount = items.Count,
            TotalPages = 1,
            HasNextPage = false,
            HasPreviousPage = false,
            Items = items
        };
    }
}