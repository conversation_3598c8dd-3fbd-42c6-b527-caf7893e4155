namespace HappyWechat.Application.DTOs.MessageProcess;

/// <summary>
/// 拆分消息DTO
/// </summary>
public class SplitMessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 原始消息ID
    /// </summary>
    public string OriginalMessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 微信实例ID
    /// </summary>
    public string WId { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者微信ID
    /// </summary>
    public string FromUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者昵称
    /// </summary>
    public string FromUserNickName { get; set; } = string.Empty;
    
    /// <summary>
    /// 接收者微信ID
    /// </summary>
    public string ToUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public SplitMessageType MessageType { get; set; }
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息顺序（在原始消息中的位置）
    /// </summary>
    public int Order { get; set; }
    
    /// <summary>
    /// 是否为主要内容
    /// </summary>
    public bool IsPrimary { get; set; } = false;
    
    /// <summary>
    /// 延迟发送时间（毫秒）
    /// </summary>
    public int DelayMs { get; set; } = 0;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 处理状态
    /// </summary>
    public SplitMessageStatus Status { get; set; } = SplitMessageStatus.Pending;
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object>? ExtendedProperties { get; set; }
}

/// <summary>
/// 拆分消息类型
/// </summary>
public enum SplitMessageType
{
    /// <summary>
    /// 文本
    /// </summary>
    Text = 1,
    
    /// <summary>
    /// 图片
    /// </summary>
    Image = 2,
    
    /// <summary>
    /// 语音
    /// </summary>
    Voice = 3,
    
    /// <summary>
    /// 视频
    /// </summary>
    Video = 4,
    
    /// <summary>
    /// 文件
    /// </summary>
    File = 5,
    
    /// <summary>
    /// 表情包
    /// </summary>
    Emoji = 6,
    
    /// <summary>
    /// 位置信息
    /// </summary>
    Location = 7,
    
    /// <summary>
    /// 链接
    /// </summary>
    Link = 8,
    
    /// <summary>
    /// 小程序
    /// </summary>
    MiniProgram = 9
}

/// <summary>
/// 拆分消息状态
/// </summary>
public enum SplitMessageStatus
{
    /// <summary>
    /// 等待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在处理
    /// </summary>
    Processing = 1,
    
    /// <summary>
    /// 处理完成
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// 处理失败
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// 已跳过
    /// </summary>
    Skipped = 4
}