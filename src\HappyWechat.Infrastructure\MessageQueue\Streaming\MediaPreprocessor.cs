using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using HappyWechat.Infrastructure.MessageQueue.Unified;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageQueue.Streaming;

/// <summary>
/// 媒体预处理器 - 解决大文件阻塞问题
/// 分离下载、处理、发送三个阶段，避免任何阻塞
/// </summary>
public interface IMediaPreprocessor
{
    /// <summary>
    /// 预处理媒体消息 - 立即返回，后台异步处理
    /// </summary>
    Task<MediaPreprocessResult> PreprocessAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 获取预处理统计
    /// </summary>
    Task<MediaPreprocessStats> GetStatsAsync();
}

public class MediaPreprocessor : IMediaPreprocessor
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MediaPreprocessor> _logger;
    
    // 预处理任务跟踪
    private readonly ConcurrentDictionary<string, MediaProcessingTask> _processingTasks = new();
    
    // 大文件检测阈值
    private const long LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB
    private const long HUGE_FILE_THRESHOLD = 200 * 1024 * 1024; // 200MB

    public MediaPreprocessor(
        IServiceProvider serviceProvider,
        ILogger<MediaPreprocessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 预处理媒体消息 - 核心方法，立即返回
    /// </summary>
    public async Task<MediaPreprocessResult> PreprocessAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            // 第一步：快速分析媒体类型和大小
            var analysis = await AnalyzeMediaMessageAsync(callbackMessage, processingId);
            
            if (!analysis.IsMediaMessage)
            {
                return MediaPreprocessResult.NotMedia("非媒体消息");
            }

            // 第二步：创建处理任务跟踪
            var task = new MediaProcessingTask
            {
                Id = processingId,
                CallbackMessage = callbackMessage,
                StartedAt = DateTime.UtcNow,
                EstimatedSize = analysis.EstimatedSize,
                MediaType = analysis.MediaType,
                ProcessingStrategy = DetermineProcessingStrategy(analysis)
            };

            _processingTasks[processingId] = task;

            // 第三步：根据策略启动异步处理
            _ = Task.Run(async () => await ProcessMediaTaskAsync(task));

            // 第四步：立即返回，不等待处理完成
            _logger.LogInformation("📁 媒体预处理启动 - ProcessingId: {ProcessingId}, Type: {Type}, Strategy: {Strategy}, Size: {Size}",
                processingId, analysis.MediaType, task.ProcessingStrategy, FormatFileSize(analysis.EstimatedSize));

            return MediaPreprocessResult.Processing($"媒体处理已启动 - {task.ProcessingStrategy}策略");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 媒体预处理异常 - ProcessingId: {ProcessingId}", processingId);
            return MediaPreprocessResult.Error($"预处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 分析媒体消息
    /// </summary>
    private async Task<MediaAnalysis> AnalyzeMediaMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        var messageType = callbackMessage.MessageType;
        
        var analysis = new MediaAnalysis
        {
            IsMediaMessage = IsMediaMessageType(messageType),
            MediaType = GetMediaType(messageType),
            EstimatedSize = EstimateFileSize(callbackMessage)
        };

        // 对于某些类型，可以从消息内容中提取更准确的大小信息
        if (analysis.IsMediaMessage && callbackMessage.Data != null)
        {
            analysis.EstimatedSize = ExtractFileSizeFromContent(callbackMessage.Data.Content) ?? analysis.EstimatedSize;
        }

        _logger.LogDebug("🔍 媒体分析完成 - ProcessingId: {ProcessingId}, Type: {Type}, Size: {Size}",
            processingId, analysis.MediaType, FormatFileSize(analysis.EstimatedSize));

        return analysis;
    }

    /// <summary>
    /// 确定处理策略
    /// </summary>
    private ProcessingStrategy DetermineProcessingStrategy(MediaAnalysis analysis)
    {
        return analysis.EstimatedSize switch
        {
            > HUGE_FILE_THRESHOLD => ProcessingStrategy.HugeFileAsync,     // >200MB: 超大文件异步处理
            > LARGE_FILE_THRESHOLD => ProcessingStrategy.LargeFileAsync,   // >50MB: 大文件异步处理
            _ => analysis.MediaType switch
            {
                MediaType.Voice => ProcessingStrategy.VoiceOptimized,      // 语音：优化处理
                MediaType.Image => ProcessingStrategy.ImageFast,           // 图片：快速处理
                MediaType.Video => ProcessingStrategy.VideoProgressive,    // 视频：渐进式处理
                _ => ProcessingStrategy.StandardAsync                       // 其他：标准异步
            }
        };
    }

    /// <summary>
    /// 异步处理媒体任务
    /// </summary>
    private async Task ProcessMediaTaskAsync(MediaProcessingTask task)
    {
        var processingId = task.Id;
        
        try
        {
            task.Status = MediaTaskStatus.Downloading;
            _logger.LogInformation("⬇️ 开始下载媒体文件 - ProcessingId: {ProcessingId}, Strategy: {Strategy}",
                processingId, task.ProcessingStrategy);

            using var scope = _serviceProvider.CreateScope();

            switch (task.ProcessingStrategy)
            {
                case ProcessingStrategy.HugeFileAsync:
                    await ProcessHugeFileAsync(scope, task);
                    break;

                case ProcessingStrategy.LargeFileAsync:
                    await ProcessLargeFileAsync(scope, task);
                    break;

                case ProcessingStrategy.VoiceOptimized:
                    await ProcessVoiceOptimizedAsync(scope, task);
                    break;

                case ProcessingStrategy.ImageFast:
                    await ProcessImageFastAsync(scope, task);
                    break;

                case ProcessingStrategy.VideoProgressive:
                    await ProcessVideoProgressiveAsync(scope, task);
                    break;

                case ProcessingStrategy.StandardAsync:
                default:
                    await ProcessStandardAsync(scope, task);
                    break;
            }

            task.Status = MediaTaskStatus.Completed;
            task.CompletedAt = DateTime.UtcNow;
            
            _logger.LogInformation("✅ 媒体处理完成 - ProcessingId: {ProcessingId}, Duration: {Duration}s",
                processingId, (task.CompletedAt - task.StartedAt).Value.TotalSeconds);
        }
        catch (Exception ex)
        {
            task.Status = MediaTaskStatus.Failed;
            task.ErrorMessage = ex.Message;
            task.CompletedAt = DateTime.UtcNow;
            
            _logger.LogError(ex, "❌ 媒体处理失败 - ProcessingId: {ProcessingId}", processingId);
        }
        finally
        {
            // 清理完成的任务（延迟清理，保留一段时间用于统计）
            _ = Task.Delay(TimeSpan.FromMinutes(10)).ContinueWith(_ => 
            {
                _processingTasks.TryRemove(processingId, out var _);
            });
        }
    }

    /// <summary>
    /// 处理超大文件（>200MB）
    /// </summary>
    private async Task ProcessHugeFileAsync(IServiceScope scope, MediaProcessingTask task)
    {
        _logger.LogWarning("🐘 超大文件处理 - ProcessingId: {ProcessingId}, Size: {Size}", 
            task.Id, FormatFileSize(task.EstimatedSize));

        // 超大文件策略：
        // 1. 分块下载
        // 2. 降低AI处理优先级
        // 3. 异步发送回复，不阻塞后续消息

        // 方案1：直接告知用户文件太大，建议缩小后重发
        await SendImmediateResponseAsync(scope, task, "文件过大，正在后台处理，请稍候...");
        
        // 方案2：后台慢速处理
        await ProcessStandardAsync(scope, task);
    }

    /// <summary>
    /// 处理大文件（50-200MB）
    /// </summary>
    private async Task ProcessLargeFileAsync(IServiceScope scope, MediaProcessingTask task)
    {
        _logger.LogInformation("📦 大文件处理 - ProcessingId: {ProcessingId}, Size: {Size}", 
            task.Id, FormatFileSize(task.EstimatedSize));

        // 大文件策略：先回复用户正在处理，然后异步处理
        await SendImmediateResponseAsync(scope, task, "文件较大，正在处理中，请稍候...");
        await ProcessStandardAsync(scope, task);
    }

    /// <summary>
    /// 语音优化处理
    /// </summary>
    private async Task ProcessVoiceOptimizedAsync(IServiceScope scope, MediaProcessingTask task)
    {
        _logger.LogDebug("🎤 语音优化处理 - ProcessingId: {ProcessingId}", task.Id);

        // 语音消息特殊处理：
        // 1. 优先转文字
        // 2. AI理解语音内容
        // 3. 生成文字回复
        
        await ProcessStandardAsync(scope, task);
    }

    /// <summary>
    /// 图片快速处理
    /// </summary>
    private async Task ProcessImageFastAsync(IServiceScope scope, MediaProcessingTask task)
    {
        _logger.LogDebug("🖼️ 图片快速处理 - ProcessingId: {ProcessingId}", task.Id);

        // 图片处理优化：
        // 1. 缩略图预览
        // 2. OCR文字识别
        // 3. AI图像理解
        
        await ProcessStandardAsync(scope, task);
    }

    /// <summary>
    /// 视频渐进式处理
    /// </summary>
    private async Task ProcessVideoProgressiveAsync(IServiceScope scope, MediaProcessingTask task)
    {
        _logger.LogDebug("🎬 视频渐进式处理 - ProcessingId: {ProcessingId}", task.Id);

        // 视频处理策略：
        // 1. 先处理第一帧作为封面
        // 2. 音频转文字
        // 3. 视频内容分析
        
        await SendImmediateResponseAsync(scope, task, "视频正在分析中...");
        await ProcessStandardAsync(scope, task);
    }

    /// <summary>
    /// 标准异步处理
    /// </summary>
    private async Task ProcessStandardAsync(IServiceScope scope, MediaProcessingTask task)
    {
        var mediaToAiProcessor = scope.ServiceProvider.GetRequiredService<IMediaToAiProcessor>();
        
        var mediaRequest = new UnifiedMediaRequest
        {
            Id = task.Id,
            WxManagerId = Guid.Parse(task.CallbackMessage.WxManagerId),
            CallbackMessage = task.CallbackMessage,
            ProcessingId = task.Id
        };

        task.Status = MediaTaskStatus.Processing;
        var result = await mediaToAiProcessor.ProcessMediaToAiAsync(mediaRequest, CancellationToken.None);
        
        if (!result.Success)
        {
            throw new Exception(result.ErrorMessage);
        }
    }

    /// <summary>
    /// 发送立即回复
    /// </summary>
    private async Task SendImmediateResponseAsync(IServiceScope scope, MediaProcessingTask task, string message)
    {
        try
        {
            // 立即发送一个回复，告知用户正在处理
            var unifiedSendQueue = scope.ServiceProvider.GetRequiredService<IUnifiedEYunSendQueue>();
            
            var wxManagerId = Guid.Parse(task.CallbackMessage.WxManagerId);
            var wId = task.CallbackMessage.Data?.WId ?? "";
            var toUser = task.CallbackMessage.Data?.FromUser ?? "";
            var toGroup = task.CallbackMessage.Data?.FromGroup;

            var sendCommand = new
            {
                CommandType = "WxSendTextMessage",
                WId = wId,
                WcId = string.IsNullOrEmpty(toGroup) ? toUser : toGroup,
                Content = message,
                At = string.IsNullOrEmpty(toGroup) ? null : toUser
            };

            await unifiedSendQueue.EnqueueAsync(wxManagerId, sendCommand, 8); // 高优先级
            
            _logger.LogDebug("💬 立即回复已发送 - ProcessingId: {ProcessingId}, Message: {Message}", task.Id, message);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 立即回复发送失败 - ProcessingId: {ProcessingId}", task.Id);
        }
    }

    /// <summary>
    /// 获取预处理统计
    /// </summary>
    public async Task<MediaPreprocessStats> GetStatsAsync()
    {
        var stats = new MediaPreprocessStats
        {
            TotalTasks = _processingTasks.Count,
            CompletedTasks = _processingTasks.Values.Count(t => t.Status == MediaTaskStatus.Completed),
            FailedTasks = _processingTasks.Values.Count(t => t.Status == MediaTaskStatus.Failed),
            ProcessingTasks = _processingTasks.Values.Count(t => t.Status == MediaTaskStatus.Processing || t.Status == MediaTaskStatus.Downloading),
            StrategyCounts = _processingTasks.Values.GroupBy(t => t.ProcessingStrategy).ToDictionary(g => g.Key.ToString(), g => g.Count()),
            GeneratedAt = DateTime.UtcNow
        };

        return stats;
    }

    #region 辅助方法

    private bool IsMediaMessageType(string messageType) =>
        messageType is "60002" or "60004" or "60009" or "60010";

    private MediaType GetMediaType(string messageType) => messageType switch
    {
        "60002" => MediaType.Image,
        "60004" => MediaType.Voice,
        "60009" => MediaType.File,
        "60010" => MediaType.Video,
        _ => MediaType.Unknown
    };

    private long EstimateFileSize(WxCallbackMessageDto callbackMessage) =>
        callbackMessage.MessageType switch
        {
            "60002" => 2 * 1024 * 1024,      // 图片默认2MB
            "60004" => 500 * 1024,           // 语音默认500KB
            "60009" => 10 * 1024 * 1024,     // 文件默认10MB
            "60010" => 50 * 1024 * 1024,     // 视频默认50MB
            _ => 1024 * 1024                 // 默认1MB
        };

    private long? ExtractFileSizeFromContent(string? content)
    {
        // 尝试从消息内容中提取文件大小信息
        // 这里需要根据实际的消息格式来解析
        return null;
    }

    private string FormatFileSize(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB" };
        int i = 0;
        double size = bytes;
        while (size >= 1024 && i < suffixes.Length - 1)
        {
            size /= 1024;
            i++;
        }
        return $"{size:F1}{suffixes[i]}";
    }

    #endregion
}

#region 数据模型

/// <summary>
/// 媒体分析结果
/// </summary>
public class MediaAnalysis
{
    public bool IsMediaMessage { get; set; }
    public MediaType MediaType { get; set; }
    public long EstimatedSize { get; set; }
}

/// <summary>
/// 媒体处理任务
/// </summary>
public class MediaProcessingTask
{
    public string Id { get; set; } = string.Empty;
    public WxCallbackMessageDto CallbackMessage { get; set; } = null!;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public long EstimatedSize { get; set; }
    public MediaType MediaType { get; set; }
    public ProcessingStrategy ProcessingStrategy { get; set; }
    public MediaTaskStatus Status { get; set; } = MediaTaskStatus.Created;
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 媒体预处理结果
/// </summary>
public class MediaPreprocessResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public MediaPreprocessStatus Status { get; set; }

    public static MediaPreprocessResult Processing(string message) =>
        new() { Success = true, Message = message, Status = MediaPreprocessStatus.Processing };

    public static MediaPreprocessResult NotMedia(string message) =>
        new() { Success = false, Message = message, Status = MediaPreprocessStatus.NotMedia };

    public static MediaPreprocessResult Error(string message) =>
        new() { Success = false, Message = message, Status = MediaPreprocessStatus.Error };
}

/// <summary>
/// 媒体预处理统计
/// </summary>
public class MediaPreprocessStats
{
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int FailedTasks { get; set; }
    public int ProcessingTasks { get; set; }
    public Dictionary<string, int> StrategyCounts { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// 媒体类型
/// </summary>
public enum MediaType
{
    Unknown,
    Image,
    Voice,
    File,
    Video
}

/// <summary>
/// 处理策略
/// </summary>
public enum ProcessingStrategy
{
    StandardAsync,          // 标准异步处理
    ImageFast,             // 图片快速处理
    VoiceOptimized,        // 语音优化处理
    VideoProgressive,      // 视频渐进式处理
    LargeFileAsync,        // 大文件异步处理
    HugeFileAsync          // 超大文件异步处理
}

/// <summary>
/// 媒体任务状态
/// </summary>
public enum MediaTaskStatus
{
    Created,
    Downloading,
    Processing,
    Completed,
    Failed
}

/// <summary>
/// 媒体预处理状态
/// </summary>
public enum MediaPreprocessStatus
{
    Processing,
    NotMedia,
    Error
}

#endregion