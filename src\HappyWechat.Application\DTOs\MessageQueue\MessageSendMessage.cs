namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 消息发送消息
/// </summary>
public class MessageSendMessage
{
    /// <summary>
    /// 微信账号ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;
    
    /// <summary>
    /// 接收者ID
    /// </summary>
    public string ToUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object>? AdditionalData { get; set; }
    
    /// <summary>
    /// 发送延迟（毫秒）
    /// </summary>
    public int DelayMs { get; set; } = 0;
}