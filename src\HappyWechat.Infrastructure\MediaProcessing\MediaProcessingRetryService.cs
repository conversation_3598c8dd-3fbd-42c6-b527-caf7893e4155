using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MediaProcessing.Models;

namespace HappyWechat.Infrastructure.MediaProcessing;

/// <summary>
/// 媒体处理重试服务
/// 提供智能重试机制，支持指数退避和错误分类
/// </summary>
public interface IMediaProcessingRetryService
{
    /// <summary>
    /// 执行带重试的媒体处理
    /// </summary>
    Task<MediaProcessingResult> ProcessWithRetryAsync(
        WxCallbackMessageDto callbackMessage,
        Func<WxCallbackMessageDto, CancellationToken, Task<MediaProcessingResult>> processor,
        CancellationToken cancellationToken = default,
        int maxRetries = 3);
}

/// <summary>
/// 媒体处理重试服务实现
/// </summary>
public class MediaProcessingRetryService : IMediaProcessingRetryService
{
    private readonly ILogger<MediaProcessingRetryService> _logger;
    
    /// <summary>
    /// 可重试的错误关键词
    /// </summary>
    private static readonly string[] RetriableErrors = 
    {
        "timeout", "连接", "网络", "服务器", "502", "503", "504", 
        "download", "下载", "临时", "busy", "rate limit"
    };
    
    /// <summary>
    /// 不可重试的错误关键词  
    /// </summary>
    private static readonly string[] NonRetriableErrors = 
    {
        "权限", "无效", "不存在", "格式错误", "forbidden", "unauthorized", 
        "not found", "invalid", "malformed", "corrupt"
    };

    public MediaProcessingRetryService(ILogger<MediaProcessingRetryService> logger)
    {
        _logger = logger;
    }

    public async Task<MediaProcessingResult> ProcessWithRetryAsync(
        WxCallbackMessageDto callbackMessage,
        Func<WxCallbackMessageDto, CancellationToken, Task<MediaProcessingResult>> processor,
        CancellationToken cancellationToken = default,
        int maxRetries = 3)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        MediaProcessingResult? lastResult = null;
        Exception? lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                _logger.LogDebug("[{ProcessingId}] 媒体处理尝试 {Attempt}/{MaxRetries} - MessageType: {MessageType}",
                    processingId, attempt, maxRetries, callbackMessage.MessageType);

                var result = await processor(callbackMessage, cancellationToken);
                
                if (result?.Success == true)
                {
                    if (attempt > 1)
                    {
                        _logger.LogInformation("[{ProcessingId}] 🎉 媒体处理重试成功 - 第{Attempt}次尝试, MessageType: {MessageType}",
                            processingId, attempt, callbackMessage.MessageType);
                    }
                    return result;
                }

                lastResult = result;
                
                // 检查是否应该重试
                if (attempt < maxRetries && ShouldRetry(result?.ErrorMessage, processingId))
                {
                    var delayMs = CalculateRetryDelay(attempt);
                    _logger.LogWarning("[{ProcessingId}] 媒体处理第{Attempt}次失败，{DelayMs}ms后重试 - Error: {Error}",
                        processingId, attempt, delayMs, result?.ErrorMessage);
                    
                    await Task.Delay(delayMs, cancellationToken);
                }
                else if (attempt < maxRetries)
                {
                    _logger.LogError("[{ProcessingId}] 媒体处理失败且不可重试 - Error: {Error}",
                        processingId, result?.ErrorMessage);
                    break; // 不可重试的错误，直接跳出
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
                _logger.LogWarning(ex, "[{ProcessingId}] 媒体处理第{Attempt}次尝试异常",
                    processingId, attempt);

                if (attempt < maxRetries && ShouldRetryException(ex, processingId))
                {
                    var delayMs = CalculateRetryDelay(attempt);
                    await Task.Delay(delayMs, cancellationToken);
                }
                else if (attempt < maxRetries)
                {
                    _logger.LogError(ex, "[{ProcessingId}] 媒体处理异常且不可重试", processingId);
                    break;
                }
            }
        }

        // 所有重试都失败
        var finalErrorMessage = lastResult?.ErrorMessage ?? lastException?.Message ?? "未知错误";
        _logger.LogError("[{ProcessingId}] 媒体处理经{MaxRetries}次重试后仍然失败 - FinalError: {Error}",
            processingId, maxRetries, finalErrorMessage);

        return lastResult ?? MediaProcessingResult.CreateFailure(
            $"重试{maxRetries}次后失败: {finalErrorMessage}", 
            callbackMessage.MessageType ?? "unknown");
    }

    /// <summary>
    /// 判断错误是否应该重试
    /// </summary>
    private bool ShouldRetry(string? errorMessage, string processingId)
    {
        if (string.IsNullOrEmpty(errorMessage))
            return true; // 空错误消息默认重试

        var lowerError = errorMessage.ToLowerInvariant();

        // 检查明确不可重试的错误
        if (NonRetriableErrors.Any(keyword => lowerError.Contains(keyword)))
        {
            _logger.LogDebug("[{ProcessingId}] 错误不可重试 - Keyword found in: {Error}",
                processingId, errorMessage);
            return false;
        }

        // 检查明确可重试的错误
        if (RetriableErrors.Any(keyword => lowerError.Contains(keyword)))
        {
            _logger.LogDebug("[{ProcessingId}] 错误可重试 - Keyword found in: {Error}",
                processingId, errorMessage);
            return true;
        }

        // 默认策略：未知错误可以重试
        _logger.LogDebug("[{ProcessingId}] 未知错误，默认可重试 - Error: {Error}",
            processingId, errorMessage);
        return true;
    }

    /// <summary>
    /// 判断异常是否应该重试
    /// </summary>
    private bool ShouldRetryException(Exception ex, string processingId)
    {
        // HTTP相关异常通常可以重试
        if (ex is HttpRequestException || ex is TaskCanceledException)
        {
            _logger.LogDebug("[{ProcessingId}] HTTP异常可重试 - Type: {Type}",
                processingId, ex.GetType().Name);
            return true;
        }

        // 参数异常通常不可重试
        if (ex is ArgumentException || ex is ArgumentNullException || ex is FormatException)
        {
            _logger.LogDebug("[{ProcessingId}] 参数异常不可重试 - Type: {Type}",
                processingId, ex.GetType().Name);
            return false;
        }

        // 其他异常根据消息内容判断
        return ShouldRetry(ex.Message, processingId);
    }

    /// <summary>
    /// 计算重试延迟时间（指数退避）
    /// </summary>
    private int CalculateRetryDelay(int attempt)
    {
        // 指数退避：1s, 2s, 4s, 8s...，最大30秒
        var baseDelayMs = 1000;
        var delayMs = (int)(baseDelayMs * Math.Pow(2, attempt - 1));
        var maxDelayMs = 30000; // 30秒上限
        
        // 添加随机抖动，避免雷群效应
        var jitterMs = Random.Shared.Next(0, 1000);
        
        return Math.Min(delayMs + jitterMs, maxDelayMs);
    }
}