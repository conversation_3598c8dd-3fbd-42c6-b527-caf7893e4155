using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using HappyWechat.Infrastructure.Extensions;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.Commons;
using HappyWechat.Application.Commands.Wx;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Database;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 简化的微信群组服务实现
/// 提供基本的群组管理功能，避免依赖注入冲突
/// </summary>
public class SimplifiedWxGroupService : IWxGroupService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<SimplifiedWxGroupService> _logger;
    private readonly ISimplifiedQueueService _queueService;
    private readonly IWxContactListRepository _contactListRepository;

    public SimplifiedWxGroupService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        ILogger<SimplifiedWxGroupService> logger,
        ISimplifiedQueueService queueService,
        IWxContactListRepository contactListRepository)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
        _queueService = queueService;
        _contactListRepository = contactListRepository;
    }

    public async Task<PageResponse<WxGroupDto>> GetGroupListAsync(Guid userId, GetGroupListQuery query)
    {
        try
        {
            // 🔧 注释冗余的获取群组列表日志 - 减少日志噪音，每次操作都会重复出现
            // _logger.LogInformation("获取群组列表 - UserId: {UserId}, WxManagerId: {WxManagerId}",
            //     userId, query.WxManagerId);

            var queryable = _dbContext.WxGroupEntities
                .Where(x => x.WxManagerId == query.WxManagerId);

            // 搜索过滤
            if (!string.IsNullOrEmpty(query.SearchedNickName))
            {
                queryable = queryable.Where(x => x.NickName.Contains(query.SearchedNickName));
            }

            // 分页
            var totalCount = await queryable.CountAsync();
            // 🔧 修复：添加排序后再分页，解决EF Core警告
            var groups = await queryable
                .OrderBy(x => x.NickName).ThenBy(x => x.Id)
                .ApplyPagination(query.PageQuery.Page, query.PageQuery.PageSize)
                .ToListAsync();

            var groupDtos = _mapper.Map<List<WxGroupDto>>(groups);

            return new PageResponse<WxGroupDto>
            {
                Data = groupDtos,
                TotalCount = totalCount,
                Page = query.PageQuery.Page,
                PageSize = query.PageQuery.PageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组列表失败 - UserId: {UserId}", userId);
            return new PageResponse<WxGroupDto>
            {
                Data = new List<WxGroupDto>(),
                TotalCount = 0,
                Page = query.PageQuery.Page,
                PageSize = query.PageQuery.PageSize
            };
        }
    }

    public async Task<GroupSyncProgressDto> GetGroupDetailsAsync(GetGroupDetailsCommand command)
    {
        try
        {
            // 🔧 注释冗余的开始同步日志 - 减少日志噪音
            // _logger.LogInformation("🚀 开始群组详情同步 - WxManagerId: {WxManagerId}", command.WxManagerId);

            // 1. 从WxContactListEntities表读取ListType=2的所有群聊ID
            var groupIds = await _contactListRepository.GetContactIdsAsync(command.WxManagerId, WxContactListType.Chatrooms);

            if (!groupIds.Any())
            {
                _logger.LogWarning("⚠️ 未找到需要同步的群聊 - WxManagerId: {WxManagerId}", command.WxManagerId);
                return new GroupSyncProgressDto
                {
                    WxManagerId = command.WxManagerId,
                    Status = SyncStatus.Completed,
                    TotalCount = 0,
                    ProcessedCount = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    StartTime = DateTime.UtcNow,
                    EstimatedEndTime = DateTime.UtcNow,
                    ErrorMessage = "未找到需要同步的群聊"
                };
            }

            // 🔧 注释冗余的找到群聊日志 - 减少日志噪音
            // _logger.LogInformation("📋 找到 {Count} 个群聊需要同步，开始加入队列", groupIds.Count);

            // 2. 生成同步会话ID
            var syncSessionId = Guid.NewGuid().ToString("N")[..8];

            // 3. 创建群组同步消息并加入队列
            var groupSyncMessage = new GroupSyncMessage
            {
                WxManagerId = command.WxManagerId,
                Operation = GroupSyncOperation.FullSync,
                GroupIds = groupIds,
                SyncSessionId = syncSessionId,
                IncludeMembers = command.IncludeMembers,
                MinIntervalMs = command.MinIntervalMs,
                MaxIntervalMs = command.MaxIntervalMs,
                MaxRetries = 3
            };

            // 4. 将消息加入群组同步队列
            var properties = new Dictionary<string, object>
            {
                ["SyncSessionId"] = syncSessionId,
                ["Operation"] = "full_sync",
                ["GroupCount"] = groupIds.Count,
                ["IncludeMembers"] = command.IncludeMembers
            };

            await _queueService.EnqueueAsync(command.WxManagerId, "group_sync", groupSyncMessage, 0, 3, properties);

            _logger.LogInformation("✅ 群组同步任务已加入队列 - SessionId: {SessionId}, 群组数: {Count}",
                syncSessionId, groupIds.Count);

            // 5. 返回进行中状态
            return new GroupSyncProgressDto
            {
                WxManagerId = command.WxManagerId,
                Status = SyncStatus.InProgress,
                TotalCount = groupIds.Count,
                ProcessedCount = 0,
                SuccessCount = 0,
                FailedCount = 0,
                StartTime = DateTime.UtcNow,
                ErrorMessage = $"群组同步已开始，会话ID: {syncSessionId}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组详情同步启动失败 - WxManagerId: {WxManagerId}", command.WxManagerId);
            return new GroupSyncProgressDto
            {
                WxManagerId = command.WxManagerId,
                Status = SyncStatus.Failed,
                TotalCount = 0,
                ProcessedCount = 0,
                SuccessCount = 0,
                FailedCount = 1,
                StartTime = DateTime.UtcNow,
                EstimatedEndTime = DateTime.UtcNow,
                ErrorMessage = $"群组同步启动失败: {ex.Message}"
            };
        }
    }

    public async Task<GroupSyncProgressDto> ExecuteGroupSyncAsync(GetGroupDetailsCommand command)
    {
        _logger.LogInformation("执行群组同步 - WxManagerId: {WxManagerId}", command.WxManagerId);
        
        // 简化实现：模拟同步完成
        await Task.Delay(1000);
        
        return new GroupSyncProgressDto
        {
            WxManagerId = command.WxManagerId,
            Status = SyncStatus.Completed,
            TotalCount = 1,
            ProcessedCount = 1,
            SuccessCount = 1,
            FailedCount = 0,
            StartTime = DateTime.UtcNow.AddSeconds(-1),
            EstimatedEndTime = DateTime.UtcNow,
            ErrorMessage = "群组同步完成"
        };
    }

    public async Task<GroupSyncProgressDto> GetSyncProgressAsync(Guid wxManagerId)
    {
        _logger.LogDebug("获取同步进度 - WxManagerId: {WxManagerId}", wxManagerId);
        
        await Task.Delay(10);
        
        return new GroupSyncProgressDto
        {
            WxManagerId = wxManagerId,
            Status = SyncStatus.Completed,
            TotalCount = 0,
            ProcessedCount = 0,
            SuccessCount = 0,
            FailedCount = 0,
            ErrorMessage = "无进行中的同步任务"
        };
    }

    public async Task<BatchOperationResult> BatchOperateGroupsAsync(BatchGroupOperationCommand command)
    {
        _logger.LogInformation("批量操作群组 - 操作类型: {OperationType}, 群组数量: {Count}", 
            command.OperationType, command.GroupIds.Count);
        
        await Task.Delay(500);
        
        return new BatchOperationResult
        {
            TotalCount = command.GroupIds.Count,
            SuccessCount = command.GroupIds.Count,
            FailedCount = 0
        };
    }

    public async Task<GroupStatisticsDto> GetGroupStatisticsAsync(Guid wxManagerId)
    {
        try
        {
            var totalGroups = await _dbContext.WxGroupEntities
                .Where(x => x.WxManagerId == wxManagerId)
                .CountAsync();

            return new GroupStatisticsDto
            {
                TotalGroups = totalGroups,
                OwnerGroupsCount = totalGroups,
                AdminGroupsCount = 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组统计失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new GroupStatisticsDto();
        }
    }

    public async Task<bool> RefreshGroupMembersAsync(Guid groupId)
    {
        _logger.LogInformation("刷新群组成员 - GroupId: {GroupId}", groupId);
        await Task.Delay(100);
        return true;
    }

    public async Task<GroupAdditionResult> CompleteGroupAdditionAsync(CompleteGroupAdditionCommand command)
    {
        // 🔧 降级为Debug级别 - 减少业务流程的冗余日志噪音
        _logger.LogDebug("完整群组添加流程 - WxManagerId: {WxManagerId}", command.WxManagerId);
        await Task.Delay(200);
        
        return new GroupAdditionResult
        {
            Success = true,
            ProcessingDetails = "群组添加完成"
        };
    }

    public async Task SaveGroupDetailPublicAsync(Guid wxManagerId, EYunChatRoomInfoData groupInfo, 
        List<EYunChatRoomMember> groupMembers, WxContactType contactType)
    {
        try
        {
            // 🔧 降级为Debug级别 - 减少数据保存的冗余日志噪音
            _logger.LogDebug("保存群组详情 - WxManagerId: {WxManagerId}, GroupId: {GroupId}",
                wxManagerId, groupInfo.ChatRoomId);

            // 检查群组是否已存在
            var existingGroup = await _dbContext.WxGroupEntities
                .FirstOrDefaultAsync(x => x.WxManagerId == wxManagerId && x.ChatRoomId == groupInfo.ChatRoomId);

            if (existingGroup == null)
            {
                // 创建新群组记录
                var newGroup = new Domain.Entities.WxGroupEntity
                {
                    Id = Guid.NewGuid(),
                    WxManagerId = wxManagerId,
                    ChatRoomId = groupInfo.ChatRoomId,
                    NickName = groupInfo.NickName ?? "",
                    MemberCount = groupInfo.MemberCount,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _dbContext.WxGroupEntities.Add(newGroup);
            }
            else
            {
                // 更新现有群组
                existingGroup.NickName = groupInfo.NickName ?? existingGroup.NickName;
                existingGroup.MemberCount = groupInfo.MemberCount;
                existingGroup.UpdatedAt = DateTime.UtcNow;
            }

            await _dbContext.SaveChangesAsync();
            // 🔧 降级为Debug级别 - 减少数据保存成功的冗余日志噪音
            _logger.LogDebug("群组详情保存成功 - GroupId: {GroupId}", groupInfo.ChatRoomId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存群组详情失败 - WxManagerId: {WxManagerId}, GroupId: {GroupId}", 
                wxManagerId, groupInfo.ChatRoomId);
            throw;
        }
    }

    public async Task<bool> BatchUpdateGroupsAsync(Guid userId, BatchUpdateGroupCommand command)
    {
        _logger.LogInformation("批量更新群组 - UserId: {UserId}, 群组数量: {Count}",
            userId, command.GroupIds.Count);

        await Task.Delay(300);
        return true;
    }

    public async Task SetGroupAutoReplyStatusAsync(Guid groupId, bool enabled)
    {
        _logger.LogInformation("设置群组自动回复状态 - GroupId: {GroupId}, Enabled: {Enabled}",
            groupId, enabled);

        await Task.Delay(100);
    }

    public async Task SetGroupMentionReplyStatusAsync(Guid groupId, bool enabled)
    {
        _logger.LogInformation("设置群组@回复状态 - GroupId: {GroupId}, Enabled: {Enabled}",
            groupId, enabled);

        await Task.Delay(100);
    }
}
