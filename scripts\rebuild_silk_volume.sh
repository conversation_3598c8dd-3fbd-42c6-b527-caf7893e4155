#!/bin/bash

# 🔧 存储卷重建脚本 - 重新初始化并编译silk_v3_decoder
# 解决存储卷bin目录为空的问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
VOLUME_NAME="happywechat_happywechat_silk_v3_decoder"
CONTAINER_NAME="silk_builder_temp"
SILK_REPO_URL="https://github.com/kn007/silk-v3-decoder.git"

log_info "开始重建silk_v3_decoder存储卷..."

# 第一步：检查并停止相关容器
log_info "步骤1: 检查并停止使用存储卷的容器..."
USING_CONTAINERS=$(docker ps -a --filter volume=$VOLUME_NAME --format "{{.Names}}" || true)
if [ ! -z "$USING_CONTAINERS" ]; then
    log_warning "发现使用存储卷的容器: $USING_CONTAINERS"
    echo "$USING_CONTAINERS" | xargs -r docker stop || true
    log_info "相关容器已停止"
else
    log_info "没有发现使用存储卷的容器"
fi

# 第二步：备份现有存储卷（如果需要）
log_info "步骤2: 检查现有存储卷..."
if docker volume inspect $VOLUME_NAME >/dev/null 2>&1; then
    log_warning "存储卷 $VOLUME_NAME 已存在，将被删除并重建"
    
    # 创建备份（可选）
    BACKUP_NAME="${VOLUME_NAME}_backup_$(date +%Y%m%d_%H%M%S)"
    log_info "创建备份存储卷: $BACKUP_NAME"
    docker run --rm -v $VOLUME_NAME:/source -v $BACKUP_NAME:/backup alpine sh -c "cp -r /source/* /backup/ 2>/dev/null || true"
    
    # 删除现有存储卷
    docker volume rm $VOLUME_NAME
    log_success "现有存储卷已删除"
else
    log_info "存储卷不存在，将创建新的存储卷"
fi

# 第三步：创建新的存储卷
log_info "步骤3: 创建新的存储卷..."
docker volume create $VOLUME_NAME
log_success "存储卷 $VOLUME_NAME 创建成功"

# 第四步：启动临时容器进行编译
log_info "步骤4: 启动临时容器进行silk_v3_decoder编译..."

# 清理可能存在的临时容器
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# 启动Ubuntu容器进行编译
docker run -d --name $CONTAINER_NAME \
    -v $VOLUME_NAME:/opt/silk_v3_decoder \
    ubuntu:20.04 \
    sleep 3600

log_info "临时容器已启动，开始安装编译环境..."

# 第五步：在容器中安装编译环境
log_info "步骤5: 安装编译环境和依赖..."
docker exec $CONTAINER_NAME bash -c "
    export DEBIAN_FRONTEND=noninteractive
    apt-get update
    apt-get install -y git build-essential gcc g++ make cmake
    apt-get clean
    rm -rf /var/lib/apt/lists/*
"

# 第六步：克隆和编译silk_v3_decoder
log_info "步骤6: 克隆silk-v3-decoder源码..."
docker exec $CONTAINER_NAME bash -c "
    cd /opt/silk_v3_decoder
    git clone $SILK_REPO_URL .
    ls -la
"

log_info "步骤7: 编译silk_v3_decoder..."
docker exec $CONTAINER_NAME bash -c "
    cd /opt/silk_v3_decoder
    make
    ls -la
    ls -la bin/
"

# 第七步：验证编译结果
log_info "步骤8: 验证编译结果..."
SILK_EXISTS=$(docker exec $CONTAINER_NAME test -f /opt/silk_v3_decoder/bin/silk_v3_decoder && echo "yes" || echo "no")

if [ "$SILK_EXISTS" = "yes" ]; then
    log_success "silk_v3_decoder编译成功！"
    
    # 设置执行权限
    docker exec $CONTAINER_NAME chmod +x /opt/silk_v3_decoder/bin/silk_v3_decoder
    
    # 显示文件信息
    log_info "编译结果信息:"
    docker exec $CONTAINER_NAME ls -la /opt/silk_v3_decoder/bin/silk_v3_decoder
    
    # 测试可执行性
    log_info "测试silk_v3_decoder可执行性..."
    docker exec $CONTAINER_NAME /opt/silk_v3_decoder/bin/silk_v3_decoder 2>&1 | head -5 || true
    
else
    log_error "silk_v3_decoder编译失败！"
    log_info "显示编译目录内容:"
    docker exec $CONTAINER_NAME ls -la /opt/silk_v3_decoder/
    docker exec $CONTAINER_NAME ls -la /opt/silk_v3_decoder/bin/ || true
    exit 1
fi

# 第八步：清理临时容器
log_info "步骤9: 清理临时容器..."
docker stop $CONTAINER_NAME
docker rm $CONTAINER_NAME
log_success "临时容器已清理"

# 第九步：最终验证
log_info "步骤10: 最终验证存储卷内容..."
docker run --rm -v $VOLUME_NAME:/opt/silk_v3_decoder alpine sh -c "
    echo '存储卷内容:'
    ls -la /opt/silk_v3_decoder/
    echo ''
    echo 'bin目录内容:'
    ls -la /opt/silk_v3_decoder/bin/
    echo ''
    echo 'silk_v3_decoder文件信息:'
    ls -la /opt/silk_v3_decoder/bin/silk_v3_decoder
"

log_success "🎉 存储卷重建完成！"
log_info "存储卷名称: $VOLUME_NAME"
log_info "silk_v3_decoder路径: /opt/silk_v3_decoder/bin/silk_v3_decoder"
log_info "现在可以重新启动应用容器了"

# 显示重启命令提示
echo ""
log_info "建议的重启命令:"
echo "docker-compose restart happywechat"
echo ""
log_info "或者如果需要完全重建:"
echo "docker-compose down && docker-compose up -d"
