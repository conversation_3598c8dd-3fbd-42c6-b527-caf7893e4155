namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 统一ID管理器接口 - 负责WcId和WxManagerId之间的高效转换
/// </summary>
public interface IUnifiedIdManager
{
    /// <summary>
    /// 核心转换方法 - WcId到WxManagerId
    /// </summary>
    /// <param name="wcId">微信ID</param>
    /// <returns>微信管理器ID，找不到时返回null</returns>
    Task<Guid?> ResolveWxManagerIdAsync(string wcId);

    /// <summary>
    /// 核心转换方法 - WxManagerId到WcId
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns>微信ID，找不到时返回null</returns>
    Task<string?> ResolveWcIdAsync(Guid wxManagerId);

    /// <summary>
    /// 批量转换（性能优化）- WcId批量查找
    /// </summary>
    /// <param name="wcIds">微信ID列表</param>
    /// <returns>ID映射字典</returns>
    Task<Dictionary<string, Guid>> BatchResolveWxManagerIdAsync(IEnumerable<string> wcIds);

    /// <summary>
    /// 批量转换（性能优化）- WxManagerId批量查找
    /// </summary>
    /// <param name="wxManagerIds">微信管理器ID列表</param>
    /// <returns>ID映射字典</returns>
    Task<Dictionary<Guid, string>> BatchResolveWcIdAsync(IEnumerable<Guid> wxManagerIds);

    /// <summary>
    /// 确保映射存在（自动创建或刷新）
    /// </summary>
    /// <param name="wcId">微信ID</param>
    /// <returns>是否成功建立映射</returns>
    Task<bool> EnsureMappingAsync(string wcId);

    /// <summary>
    /// 预热活跃账号的缓存
    /// </summary>
    /// <returns>预热成功的账号数量</returns>
    Task<int> WarmupActiveAccountsAsync();

    /// <summary>
    /// 使指定映射的缓存失效
    /// </summary>
    /// <param name="wcId">微信ID</param>
    Task InvalidateMappingAsync(string wcId);

    /// <summary>
    /// 刷新指定账号的映射缓存
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    Task RefreshMappingAsync(Guid wxManagerId);

    /// <summary>
    /// 验证映射是否有效
    /// </summary>
    /// <param name="wcId">微信ID</param>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns>映射是否有效</returns>
    Task<bool> IsValidMappingAsync(string wcId, Guid wxManagerId);

    /// <summary>
    /// 检查ID管理器健康状态
    /// </summary>
    /// <returns>健康状态报告</returns>
    Task<IdMappingHealth> CheckHealthAsync();
}

/// <summary>
/// ID映射健康状态报告
/// </summary>
public class IdMappingHealth
{
    /// <summary>
    /// 缓存命中率（0.0-1.0）
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// 映射覆盖率（活跃账号中有映射的比例）
    /// </summary>
    public double MappingCoverage { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }

    /// <summary>
    /// 缓存中的条目数量
    /// </summary>
    public int CachedEntryCount { get; set; }

    /// <summary>
    /// 总请求数量
    /// </summary>
    public long TotalRequests { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 健康状态消息
    /// </summary>
    public List<string> HealthMessages { get; set; } = new();

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy => CacheHitRate > 0.8 && MappingCoverage > 0.95 && AverageResponseTime < 50;
}