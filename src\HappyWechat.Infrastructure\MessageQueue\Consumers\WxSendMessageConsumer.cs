using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Requests.Commands;
using System.Text.Json;

namespace HappyWechat.Infrastructure.MessageQueue.Consumers;

/// <summary>
/// 微信消息发送消费者 - 修复发送链路断链问题
/// 消费send_text、file_send等队列，调用WxMessageService真实发送到微信
/// </summary>
public class WxSendMessageConsumer : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WxSendMessageConsumer> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    // 支持的队列类型映射
    private static readonly Dictionary<string, string> QueueTypeMapping = new()
    {
        ["send_text"] = "文本消息",
        ["send_image"] = "图片消息", 
        ["file_send"] = "文件消息",
        ["send_voice"] = "语音消息",
        ["send_video"] = "视频消息",
        ["send_emoji"] = "表情消息",
        ["send_link"] = "链接消息"
    };

    public WxSendMessageConsumer(
        IServiceProvider serviceProvider,
        ILogger<WxSendMessageConsumer> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🚀 WxSendMessageConsumer启动 - 开始处理真实微信消息发送");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAllSendQueuesAsync(stoppingToken);
                await Task.Delay(300, stoppingToken); // 300ms检查间隔
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("WxSendMessageConsumer正在停止");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ WxSendMessageConsumer全局异常");
                await Task.Delay(2000, stoppingToken);
            }
        }

        _logger.LogInformation("WxSendMessageConsumer已停止");
    }

    /// <summary>
    /// 处理所有发送队列
    /// </summary>
    private async Task ProcessAllSendQueuesAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();
            var wxMessageService = scope.ServiceProvider.GetRequiredService<IWxMessageService>();
            
            // 获取所有活跃账号
            var activeWxManagerIds = await GetActiveWxManagerIdsAsync(scope.ServiceProvider, cancellationToken);

            foreach (var wxManagerId in activeWxManagerIds)
            {
                // 处理每种队列类型
                foreach (var queueType in QueueTypeMapping.Keys)
                {
                    await ProcessQueueTypeAsync(queueService, wxMessageService, wxManagerId, queueType, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理发送队列异常");
        }
    }

    /// <summary>
    /// 处理特定队列类型
    /// </summary>
    private async Task ProcessQueueTypeAsync(
        ISimplifiedQueueService queueService,
        IWxMessageService wxMessageService,
        Guid wxManagerId,
        string queueType,
        CancellationToken cancellationToken)
    {
        try
        {
            var queueLength = await queueService.GetQueueLengthAsync(wxManagerId, queueType, cancellationToken);
            if (queueLength == 0) return;

            _logger.LogDebug("📋 发现待发送消息 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, Count: {Count}",
                wxManagerId, QueueTypeMapping[queueType], queueLength);

            // 批量处理消息（每次最多3条，避免过载）
            var messages = await queueService.DequeueBatchAsync<object>(wxManagerId, queueType, 3, cancellationToken);
            
            foreach (var message in messages)
            {
                await ProcessSingleMessageAsync(wxMessageService, queueService, message, wxManagerId, queueType, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理队列类型异常 - WxManagerId: {WxManagerId}, QueueType: {QueueType}",
                wxManagerId, queueType);
        }
    }

    /// <summary>
    /// 处理单个消息 - 真实发送到微信
    /// </summary>
    private async Task ProcessSingleMessageAsync(
        IWxMessageService wxMessageService,
        ISimplifiedQueueService queueService,
        dynamic message,
        Guid wxManagerId,
        string queueType,
        CancellationToken cancellationToken)
    {
        var messageId = (message as dynamic)?.Id?.ToString() ?? "Unknown";
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation($"[REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: {messageId}, QueueType: {QueueTypeMapping.GetValueOrDefault(queueType, queueType)}, WxManagerId: {wxManagerId}");

            // 记录消息内容用于调试
            LogMessageContent((message as dynamic)?.Data, queueType, messageId);

            // 根据队列类型调用相应的发送方法
            var result = await CallWxMessageServiceAsync(wxMessageService, (message as dynamic)?.Data, queueType, wxManagerId, cancellationToken);
            var duration = DateTime.UtcNow - startTime;

            if (result.Success)
            {
                await queueService.AckAsync(wxManagerId, queueType, messageId, cancellationToken);
                _logger.LogInformation($"[REAL-SEND] ✅ 微信消息发送成功 - MessageId: {messageId}, QueueType: {QueueTypeMapping[queueType]}, MsgId: {result.MessageId}, Duration: {duration.TotalMilliseconds}ms");
            }
            else
            {
                await queueService.NackAsync(wxManagerId, queueType, messageId, result.ErrorMessage, cancellationToken);
                _logger.LogError($"[REAL-SEND] ❌ 微信消息发送失败 - MessageId: {messageId}, QueueType: {QueueTypeMapping[queueType]}, Error: {result.ErrorMessage}, Duration: {duration.TotalMilliseconds}ms");
            }
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, $"[REAL-SEND] ❌ 微信消息发送异常 - MessageId: {messageId}, QueueType: {QueueTypeMapping.GetValueOrDefault(queueType, queueType)}, Duration: {duration.TotalMilliseconds}ms");

            try
            {
                await queueService.NackAsync(wxManagerId, queueType, messageId, ex.Message, cancellationToken);
            }
            catch (Exception nackEx)
            {
                _logger.LogError(nackEx, $"❌ 标记消息失败异常 - MessageId: {messageId}");
            }
        }
    }

    /// <summary>
    /// 记录消息内容用于调试
    /// </summary>
    private void LogMessageContent(dynamic messageData, string queueType, string messageId)
    {
        try
        {
            switch (queueType)
            {
                case "send_text":
                    var textCommand = JsonSerializer.Deserialize<WxSendTextMessageCommand>(messageData.ToString(), _jsonOptions);
                    _logger.LogInformation($"[CONTENT] 📝 文本消息内容 - MessageId: {messageId}, WId: {textCommand?.WId}, WcId: {textCommand?.WcId}, ContentLength: {textCommand?.Content?.Length ?? 0}");
                    _logger.LogDebug($"[CONTENT] 📝 文本内容: {(textCommand?.Content?.Length > 100 ? textCommand.Content[..100] + "..." : textCommand?.Content)}");
                    break;

                case "file_send":
                    var fileCommand = JsonSerializer.Deserialize<WxSendFileMessageCommand>(messageData.ToString(), _jsonOptions);
                    _logger.LogInformation($"[CONTENT] 📁 文件消息内容 - MessageId: {messageId}, WId: {fileCommand?.WId}, WcId: {fileCommand?.WcId}, Path: {fileCommand?.Path}, FileName: {fileCommand?.FileName}");
                    break;

                case "send_image":
                    var imageCommand = JsonSerializer.Deserialize<WxSendImageMessageCommand>(messageData.ToString(), _jsonOptions);
                    _logger.LogInformation($"[CONTENT] 🖼️ 图片消息内容 - MessageId: {messageId}, WId: {imageCommand?.WId}, WcId: {imageCommand?.WcId}, Path: {imageCommand?.Path}");
                    break;

                default:
                    _logger.LogDebug("[CONTENT] 📦 其他消息类型 - MessageId: {MessageId}, QueueType: {QueueType}",
                        messageId, queueType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录消息内容异常 - MessageId: {MessageId}, QueueType: {QueueType}", messageId, queueType);
        }
    }

    /// <summary>
    /// 调用WxMessageService发送消息
    /// </summary>
    private async Task<(bool Success, string? MessageId, string? ErrorMessage)> CallWxMessageServiceAsync(
        IWxMessageService wxMessageService,
        dynamic messageData,
        string queueType,
        Guid wxManagerId,
        CancellationToken cancellationToken)
    {
        try
        {
            switch (queueType)
            {
                case "send_text":
                    var textCommand = JsonSerializer.Deserialize<WxSendTextMessageCommand>(messageData.ToString(), _jsonOptions);
                    var textResult = await wxMessageService.SendTextMessageAsync(wxManagerId, textCommand);
                    return (textResult?.IsSuccess == true, textResult?.Data, textResult?.Message);

                case "file_send":
                    var fileCommand = JsonSerializer.Deserialize<WxSendFileMessageCommand>(messageData.ToString(), _jsonOptions);
                    var fileResult = await wxMessageService.SendFileMessageAsync(wxManagerId, fileCommand);
                    return (fileResult?.IsSuccess == true, fileResult?.Data, fileResult?.Message);

                case "send_image":
                    var imageCommand = JsonSerializer.Deserialize<WxSendImageMessageCommand>(messageData.ToString(), _jsonOptions);
                    var imageResult = await wxMessageService.SendImageMessageAsync(wxManagerId, imageCommand);
                    return (imageResult?.IsSuccess == true, imageResult?.Data, imageResult?.Message);

                case "send_voice":
                    var voiceCommand = JsonSerializer.Deserialize<WxSendVoiceMessageCommand>(messageData.ToString(), _jsonOptions);
                    var voiceResult = await wxMessageService.SendVoiceMessageAsync(wxManagerId, voiceCommand);
                    return (voiceResult?.IsSuccess == true, voiceResult?.Data, voiceResult?.Message);

                default:
                    _logger.LogWarning("不支持的队列类型 - QueueType: {QueueType}", queueType);
                    return (false, null, $"不支持的队列类型: {queueType}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调用WxMessageService异常 - QueueType: {QueueType}", queueType);
            return (false, null, ex.Message);
        }
    }

    /// <summary>
    /// 获取活跃的微信账号ID列表
    /// </summary>
    private async Task<List<Guid>> GetActiveWxManagerIdsAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var wxManagerRepository = serviceProvider.GetRequiredService<IWxManagerRepository>();
            var allManagers = await wxManagerRepository.GetActiveManagersAsync();
            return allManagers.Select(m => m.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取活跃微信账号失败");
            return new List<Guid>();
        }
    }
}