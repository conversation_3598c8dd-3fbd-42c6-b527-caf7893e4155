using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Caching.Interfaces;
using System.Text.Json;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息处理重试服务 - 处理失败消息的重试机制
/// </summary>
public interface IMessageProcessingRetryService
{
    /// <summary>
    /// 重试处理失败的消息
    /// </summary>
    Task<bool> RetryMessageProcessingAsync(WxCallbackMessageDto callbackMessage, string failureReason, int currentAttempt = 1);

    /// <summary>
    /// 将消息标记为死信
    /// </summary>
    Task MarkAsDeadLetterAsync(WxCallbackMessageDto callbackMessage, string finalFailureReason);

    /// <summary>
    /// 获取重试统计信息
    /// </summary>
    Task<RetryStatistics> GetRetryStatisticsAsync(string wxManagerId);
}

/// <summary>
/// 消息处理重试服务实现
/// </summary>
public class MessageProcessingRetryService : IMessageProcessingRetryService
{
    private readonly IUnifiedCacheService _cacheService;
    private readonly ILogger<MessageProcessingRetryService> _logger;
    
    // 重试配置
    private const int MaxRetryAttempts = 3;
    private const int BaseDelaySeconds = 2;
    private const string RetryKeyPrefix = "hw:retry:";
    private const string DeadLetterKeyPrefix = "hw:deadletter:";
    private const string StatsKeyPrefix = "hw:retry:stats:";

    public MessageProcessingRetryService(
        IUnifiedCacheService cacheService,
        ILogger<MessageProcessingRetryService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<bool> RetryMessageProcessingAsync(WxCallbackMessageDto callbackMessage, string failureReason, int currentAttempt = 1)
    {
        try
        {
            if (currentAttempt > MaxRetryAttempts)
            {
                _logger.LogWarning("消息重试次数已达上限，标记为死信 - WcId: {WcId}, MessageType: {MessageType}, Attempts: {Attempts}", 
                    callbackMessage.WcId, callbackMessage.MessageType, currentAttempt);
                
                await MarkAsDeadLetterAsync(callbackMessage, $"重试失败: {failureReason}");
                return false;
            }

            // 计算延迟时间（指数退避）
            var delaySeconds = (int)Math.Pow(BaseDelaySeconds, currentAttempt);
            
            _logger.LogInformation("安排消息重试 - WcId: {WcId}, MessageType: {MessageType}, Attempt: {Attempt}/{MaxAttempts}, DelaySeconds: {DelaySeconds}", 
                callbackMessage.WcId, callbackMessage.MessageType, currentAttempt, MaxRetryAttempts, delaySeconds);

            // 创建重试任务
            var retryInfo = new RetryInfo
            {
                CallbackMessage = callbackMessage,
                FailureReason = failureReason,
                AttemptCount = currentAttempt,
                NextRetryTime = DateTime.UtcNow.AddSeconds(delaySeconds),
                CreatedAt = DateTime.UtcNow
            };

            // 保存重试信息到缓存
            var retryKey = GetRetryKey(callbackMessage.WcId!, callbackMessage.MessageType!, currentAttempt);
            await _cacheService.SetAsync(retryKey, retryInfo, TimeSpan.FromHours(24));

            // 更新统计信息
            await UpdateRetryStatisticsAsync(callbackMessage.WxManagerId!, currentAttempt, false);

            // 安排延迟重试
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
                    
                    // 这里应该重新触发消息处理流程
                    // 实际实现时需要调用消息处理器
                    _logger.LogInformation("执行消息重试 - WcId: {WcId}, MessageType: {MessageType}, Attempt: {Attempt}", 
                        callbackMessage.WcId, callbackMessage.MessageType, currentAttempt);
                    
                    // TODO: 调用实际的消息处理逻辑
                    // await _messageProcessor.ProcessMessageAsync(callbackMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行消息重试异常 - WcId: {WcId}, MessageType: {MessageType}, Attempt: {Attempt}", 
                        callbackMessage.WcId, callbackMessage.MessageType, currentAttempt);
                    
                    // 递归重试
                    await RetryMessageProcessingAsync(callbackMessage, ex.Message, currentAttempt + 1);
                }
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "安排消息重试异常 - WcId: {WcId}, MessageType: {MessageType}", 
                callbackMessage.WcId, callbackMessage.MessageType);
            return false;
        }
    }

    public async Task MarkAsDeadLetterAsync(WxCallbackMessageDto callbackMessage, string finalFailureReason)
    {
        try
        {
            var deadLetterInfo = new DeadLetterInfo
            {
                CallbackMessage = callbackMessage,
                FinalFailureReason = finalFailureReason,
                CreatedAt = DateTime.UtcNow,
                RequiresManualIntervention = true
            };

            var deadLetterKey = GetDeadLetterKey(callbackMessage.WcId!, callbackMessage.MessageType!);
            await _cacheService.SetAsync(deadLetterKey, deadLetterInfo, TimeSpan.FromDays(7)); // 保留7天

            // 更新统计信息
            await UpdateRetryStatisticsAsync(callbackMessage.WxManagerId!, 0, true);

            _logger.LogError("消息已标记为死信 - WcId: {WcId}, MessageType: {MessageType}, Reason: {Reason}", 
                callbackMessage.WcId, callbackMessage.MessageType, finalFailureReason);

            // 这里可以发送告警通知
            // await _notificationService.SendDeadLetterAlertAsync(deadLetterInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记死信异常 - WcId: {WcId}, MessageType: {MessageType}", 
                callbackMessage.WcId, callbackMessage.MessageType);
        }
    }

    public async Task<RetryStatistics> GetRetryStatisticsAsync(string wxManagerId)
    {
        try
        {
            var statsKey = GetStatsKey(wxManagerId);
            var stats = await _cacheService.GetAsync<RetryStatistics>(statsKey);
            
            return stats ?? new RetryStatistics
            {
                WxManagerId = wxManagerId,
                TotalRetries = 0,
                DeadLetterCount = 0,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取重试统计信息异常 - WxManagerId: {WxManagerId}", wxManagerId);
            return new RetryStatistics { WxManagerId = wxManagerId };
        }
    }

    private async Task UpdateRetryStatisticsAsync(string wxManagerId, int attemptCount, bool isDeadLetter)
    {
        try
        {
            var statsKey = GetStatsKey(wxManagerId);
            var stats = await GetRetryStatisticsAsync(wxManagerId);

            if (isDeadLetter)
            {
                stats.DeadLetterCount++;
            }
            else
            {
                stats.TotalRetries++;
            }

            stats.LastUpdated = DateTime.UtcNow;

            await _cacheService.SetAsync(statsKey, stats, TimeSpan.FromDays(30));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新重试统计信息异常 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    private string GetRetryKey(string wcId, string messageType, int attempt)
    {
        return $"{RetryKeyPrefix}{wcId}:{messageType}:{attempt}:{DateTime.UtcNow:yyyyMMddHHmmss}";
    }

    private string GetDeadLetterKey(string wcId, string messageType)
    {
        return $"{DeadLetterKeyPrefix}{wcId}:{messageType}:{DateTime.UtcNow:yyyyMMddHHmmss}";
    }

    private string GetStatsKey(string wxManagerId)
    {
        return $"{StatsKeyPrefix}{wxManagerId}";
    }
}

/// <summary>
/// 重试信息
/// </summary>
public class RetryInfo
{
    public WxCallbackMessageDto CallbackMessage { get; set; } = null!;
    public string FailureReason { get; set; } = string.Empty;
    public int AttemptCount { get; set; }
    public DateTime NextRetryTime { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 死信信息
/// </summary>
public class DeadLetterInfo
{
    public WxCallbackMessageDto CallbackMessage { get; set; } = null!;
    public string FinalFailureReason { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool RequiresManualIntervention { get; set; }
}

/// <summary>
/// 重试统计信息
/// </summary>
public class RetryStatistics
{
    public string WxManagerId { get; set; } = string.Empty;
    public int TotalRetries { get; set; }
    public int DeadLetterCount { get; set; }
    public DateTime LastUpdated { get; set; }
}
