namespace HappyWechat.Infrastructure.MessageQueue.Models;

/// <summary>
/// 消息优先级
/// </summary>
public enum MessagePriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// 发送消息请求
/// </summary>
public class SendMessageRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public SendMessageType MessageType { get; set; }

    /// <summary>
    /// 目标ID
    /// </summary>
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径（如果是文件消息）
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 延迟发送时间（毫秒）
    /// </summary>
    public int DelayMs { get; set; }

    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// @用户列表（群消息时使用）
    /// </summary>
    public List<string> AtUsers { get; set; } = new();

    /// <summary>
    /// 扩展元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 创建文本消息请求
    /// </summary>
    public static SendMessageRequest CreateTextMessage(
        string wxManagerId,
        string targetId,
        string content,
        bool isGroupMessage = false,
        List<string>? atUsers = null)
    {
        return new SendMessageRequest
        {
            RequestId = Guid.NewGuid().ToString(),
            WxManagerId = wxManagerId,
            MessageType = SendMessageType.Text,
            TargetId = targetId,
            Content = content,
            IsGroupMessage = isGroupMessage,
            AtUsers = atUsers ?? new List<string>()
        };
    }

    /// <summary>
    /// 创建图片消息请求
    /// </summary>
    public static SendMessageRequest CreateImageMessage(
        string wxManagerId,
        string targetId,
        string filePath,
        bool isGroupMessage = false)
    {
        return new SendMessageRequest
        {
            RequestId = Guid.NewGuid().ToString(),
            WxManagerId = wxManagerId,
            MessageType = SendMessageType.Image,
            TargetId = targetId,
            FilePath = filePath,
            IsGroupMessage = isGroupMessage
        };
    }

    /// <summary>
    /// 创建文件消息请求
    /// </summary>
    public static SendMessageRequest CreateFileMessage(
        string wxManagerId,
        string targetId,
        string filePath,
        bool isGroupMessage = false)
    {
        return new SendMessageRequest
        {
            RequestId = Guid.NewGuid().ToString(),
            WxManagerId = wxManagerId,
            MessageType = SendMessageType.File,
            TargetId = targetId,
            FilePath = filePath,
            IsGroupMessage = isGroupMessage
        };
    }

    /// <summary>
    /// 创建语音消息请求
    /// </summary>
    public static SendMessageRequest CreateVoiceMessage(
        string wxManagerId,
        string targetId,
        string filePath,
        bool isGroupMessage = false)
    {
        return new SendMessageRequest
        {
            RequestId = Guid.NewGuid().ToString(),
            WxManagerId = wxManagerId,
            MessageType = SendMessageType.Voice,
            TargetId = targetId,
            FilePath = filePath,
            IsGroupMessage = isGroupMessage
        };
    }
}

/// <summary>
/// 发送消息类型
/// </summary>
public enum SendMessageType
{
    Text = 0,
    Image = 1,
    File = 2,
    Voice = 3,
    Video = 4
}

/// <summary>
/// 通知消息
/// </summary>
public class NotificationMessage
{
    /// <summary>
    /// 通知ID
    /// </summary>
    public string NotificationId { get; set; } = string.Empty;

    /// <summary>
    /// 通知类型
    /// </summary>
    public string NotificationType { get; set; } = string.Empty;

    /// <summary>
    /// 目标ID
    /// </summary>
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 通知内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 扩展数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
}
