namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 队列类型枚举
/// </summary>
public enum QueueType
{
    /// <summary>
    /// 微信消息队列
    /// </summary>
    WxMessage,
    
    /// <summary>
    /// AI消息队列
    /// </summary>
    AiMessage,
    
    /// <summary>
    /// 文件处理队列
    /// </summary>
    FileProcessing,
    
    /// <summary>
    /// 联系人同步队列
    /// </summary>
    ContactSync,
    
    /// <summary>
    /// 群组同步队列
    /// </summary>
    GroupSync,
    
    /// <summary>
    /// 消息发送队列
    /// </summary>
    MessageSend,
    
    /// <summary>
    /// 死信队列
    /// </summary>
    DeadLetter,
    
    /// <summary>
    /// 延迟队列
    /// </summary>
    Delayed
}