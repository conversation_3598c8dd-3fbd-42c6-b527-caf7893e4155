using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Requests.Queries;

namespace HappyWechat.Infrastructure.DataSync;

/// <summary>
/// 数据同步编排器接口
/// 协调页面数据请求与数据同步队列，复用现有业务逻辑
/// </summary>
public interface IDataSyncOrchestrator
{
    /// <summary>
    /// 触发联系人数据同步
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="forceRefresh">是否强制刷新</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务ID</returns>
    Task<string> TriggerContactSyncAsync(Guid wxManagerId, bool forceRefresh = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// 触发群组数据同步
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="forceRefresh">是否强制刷新</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务ID</returns>
    Task<string> TriggerGroupSyncAsync(Guid wxManagerId, bool forceRefresh = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// 触发联系人详情获取（通过EYun API）
    /// </summary>
    /// <param name="command">获取联系人详情命令</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<bool> TriggerContactDetailsAsync(GetContactDetailsCommand command, CancellationToken cancellationToken = default);

    /// <summary>
    /// 触发群组详情获取（通过EYun API）
    /// </summary>
    /// <param name="command">获取群组详情命令</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<bool> TriggerGroupDetailsAsync(GetGroupDetailsCommand command, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查数据同步状态
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="dataType">数据类型（contact/group）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步状态</returns>
    Task<DataSyncStatus> GetSyncStatusAsync(Guid wxManagerId, string dataType, CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据同步状态
/// </summary>
public class DataSyncStatus
{
    public bool IsInProgress { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public string? Status { get; set; }
    public string? ErrorMessage { get; set; }
}
