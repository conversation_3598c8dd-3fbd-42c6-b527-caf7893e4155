using Newtonsoft.Json;

namespace HappyWechat.Application.DTOs.Requests.Commands;

public class WxSendTextMessageCommand
{
    public string? WId { get; set; } // // ��¼ʵ����ʶ
    public string WcId { get; set; } // ������΢��ID��ȺID
    public string Content { get; set; } // 文本消息内容
    public string? At { get; set; } // @用户列表（群聊消息用，多个用逗号分隔）

    // 🎯 新增上下文字段，用于增强发送日志
    /// <summary>
    /// 群组ID（如果是群聊消息）
    /// </summary>
    public string? FromGroup { get; set; }

    /// <summary>
    /// 发送者微信ID（原始消息的发送者）
    /// </summary>
    public string? FromGroupUser { get; set; }

    /// <summary>
    /// 发送者昵称（原始消息的发送者昵称）
    /// </summary>
    public string? FromGroupUserNickName { get; set; }

    public override string ToString()
    {
        return JsonConvert.SerializeObject(this);
    }
}
