namespace HappyWechat.Infrastructure.MessageProcessing.Models;

/// <summary>
/// AI响应消息
/// </summary>
public class AiResponseMessage
{
    /// <summary>
    /// 响应ID
    /// </summary>
    public string ResponseId { get; set; } = string.Empty;

    /// <summary>
    /// 原始请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 原始消息ID
    /// </summary>
    public string OriginalMessageId { get; set; } = string.Empty;

    /// <summary>
    /// 微信ID
    /// </summary>
    public string WId { get; set; } = string.Empty;

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// AI智能体ID
    /// </summary>
    public string AiAgentId { get; set; } = string.Empty;

    /// <summary>
    /// AI响应内容
    /// </summary>
    public string ResponseContent { get; set; } = string.Empty;

    /// <summary>
    /// 内容（兼容性属性）
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// AI提供商
    /// </summary>
    public string AiProvider { get; set; } = string.Empty;

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 目标联系人/群组ID
    /// </summary>
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// 发送目标微信ID
    /// </summary>
    public string ToUser { get; set; } = string.Empty;

    /// <summary>
    /// 群组ID（如果是群消息）
    /// </summary>
    public string? ToGroup { get; set; }

    /// <summary>
    /// 处理ID
    /// </summary>
    public string ProcessingId { get; set; } = string.Empty;

    /// <summary>
    /// 响应类型
    /// </summary>
    public AiResponseType ResponseType { get; set; } = AiResponseType.Text;

    /// <summary>
    /// 消息片段列表
    /// </summary>
    public List<MessageSegment> MessageSegments { get; set; } = new();

    /// <summary>
    /// 媒体文件列表
    /// </summary>
    public List<ResponseMediaFile> MediaFiles { get; set; } = new();

    /// <summary>
    /// 响应状态
    /// </summary>
    public AiResponseStatus Status { get; set; } = AiResponseStatus.Pending;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理开始时间
    /// </summary>
    public DateTime? ProcessingStartedAt { get; set; }

    /// <summary>
    /// 处理完成时间
    /// </summary>
    public DateTime? ProcessingCompletedAt { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();

    /// <summary>
    /// 创建AI响应消息
    /// </summary>
    public static AiResponseMessage Create(
        string requestId,
        string wxManagerId,
        string aiAgentId,
        string responseContent,
        string targetId,
        bool isGroupMessage,
        string processingId)
    {
        return new AiResponseMessage
        {
            ResponseId = Guid.NewGuid().ToString(),
            RequestId = requestId,
            WxManagerId = wxManagerId,
            AiAgentId = aiAgentId,
            ResponseContent = responseContent,
            TargetId = targetId,
            IsGroupMessage = isGroupMessage,
            ProcessingId = processingId
        };
    }

    /// <summary>
    /// 添加消息片段
    /// </summary>
    public void AddMessageSegment(MessageSegmentType type, string content, int order = 0)
    {
        MessageSegments.Add(new MessageSegment
        {
            Type = type,
            Content = content,
            Order = order == 0 ? MessageSegments.Count : order
        });
    }

    /// <summary>
    /// 添加媒体文件
    /// </summary>
    public void AddMediaFile(string fileType, string filePath, string? originalContent = null)
    {
        MediaFiles.Add(new ResponseMediaFile
        {
            FileType = fileType,
            FilePath = filePath,
            OriginalContent = originalContent
        });
    }

    /// <summary>
    /// 设置群组目标
    /// </summary>
    public void SetGroupTarget(string toGroup)
    {
        ToGroup = toGroup;
        IsGroupMessage = true;
    }

    /// <summary>
    /// 标记处理开始
    /// </summary>
    public void MarkProcessingStarted()
    {
        Status = AiResponseStatus.Processing;
        ProcessingStartedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 标记处理完成
    /// </summary>
    public void MarkProcessingCompleted()
    {
        Status = AiResponseStatus.Completed;
        ProcessingCompletedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 标记处理失败
    /// </summary>
    public void MarkProcessingFailed(string errorMessage)
    {
        Status = AiResponseStatus.Failed;
        ErrorMessage = errorMessage;
        ProcessingCompletedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// AI响应类型
/// </summary>
public enum AiResponseType
{
    Text = 0,
    Mixed = 1,
    MediaOnly = 2
}

/// <summary>
/// AI响应状态
/// </summary>
public enum AiResponseStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

/// <summary>
/// 消息片段
/// </summary>
public class MessageSegment
{
    /// <summary>
    /// 片段类型
    /// </summary>
    public MessageSegmentType Type { get; set; }

    /// <summary>
    /// 片段内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 消息片段类型
/// </summary>
public enum MessageSegmentType
{
    Text = 0,
    Image = 1,
    File = 2,
    Voice = 3,
    Video = 4
}

/// <summary>
/// 响应媒体文件
/// </summary>
public class ResponseMediaFile
{
    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 原始内容
    /// </summary>
    public string? OriginalContent { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 错误处理结果
/// </summary>
public class ErrorHandlingResult
{
    /// <summary>
    /// 是否成功处理错误
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 是否应该重试
    /// </summary>
    public bool ShouldRetry { get; set; }

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; }

    /// <summary>
    /// 错误处理消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 错误ID
    /// </summary>
    public string ErrorId { get; set; } = string.Empty;

    /// <summary>
    /// 错误分类
    /// </summary>
    public ErrorCategory ErrorCategory { get; set; } = ErrorCategory.Unknown;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 是否应该记录日志
    /// </summary>
    public bool ShouldLog { get; set; } = true;

    /// <summary>
    /// 扩展数据
    /// </summary>
    public Dictionary<string, object> ExtendedData { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static ErrorHandlingResult CreateSuccess(string? message = null)
    {
        return new ErrorHandlingResult
        {
            Success = true,
            Message = message
        };
    }

    /// <summary>
    /// 创建重试结果
    /// </summary>
    public static ErrorHandlingResult CreateRetry(int retryDelayMs, string? message = null)
    {
        return new ErrorHandlingResult
        {
            Success = false,
            ShouldRetry = true,
            RetryDelayMs = retryDelayMs,
            Message = message
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static ErrorHandlingResult CreateFailure(string? message = null)
    {
        return new ErrorHandlingResult
        {
            Success = false,
            ShouldRetry = false,
            Message = message
        };
    }
}

/// <summary>
/// 错误分类
/// </summary>
public enum ErrorCategory
{
    Unknown = 0,
    Network = 1,
    Database = 2,
    Configuration = 3,
    Validation = 4,
    Authentication = 5,
    Authorization = 6,
    Business = 7,
    System = 8,
    External = 9,
    Temporary = 10,
    DataValidation = 11,
    DataFormat = 12
}
