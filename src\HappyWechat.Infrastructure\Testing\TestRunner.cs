using HappyWechat.Infrastructure.Testing;

namespace HappyWechat.Infrastructure.Testing;

/// <summary>
/// 测试运行器 - 用于验证多ContactType功能
/// </summary>
public class TestRunner
{
    /// <summary>
    /// 主测试入口
    /// </summary>
    public static void Main(string[] args)
    {
        Console.WriteLine("HappyWechat 多ContactType功能测试");
        Console.WriteLine("=====================================");
        
        ContactTypeQueryTest.RunAllTests();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
