namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 混合内容消息（包含文本、图片、文件等多种类型）
/// </summary>
public class MixedContentMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 微信实例ID
    /// </summary>
    public string WId { get; set; } = string.Empty;
    
    /// <summary>
    /// 目标用户微信ID
    /// </summary>
    public string ToUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始AI回复内容
    /// </summary>
    public string OriginalContent { get; set; } = string.Empty;
    
    /// <summary>
    /// 拆分后的消息项列表
    /// </summary>
    public List<MessageQueueItem> MessageItems { get; set; } = new();
    
    /// <summary>
    /// 消息间隔时间（毫秒）
    /// </summary>
    public int IntervalMilliseconds { get; set; } = 1000;
    
    /// <summary>
    /// 总消息数量
    /// </summary>
    public int TotalCount => MessageItems.Count;
    
    /// <summary>
    /// 已发送数量
    /// </summary>
    public int SentCount => MessageItems.Count(m => m.Status == MessageQueueStatus.Sent);
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount => MessageItems.Count(m => m.Status == MessageQueueStatus.Failed);
    
    /// <summary>
    /// 是否全部发送完成
    /// </summary>
    public bool IsCompleted => MessageItems.All(m => m.Status == MessageQueueStatus.Sent || m.Status == MessageQueueStatus.Failed || m.Status == MessageQueueStatus.Cancelled);
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 开始发送时间
    /// </summary>
    public DateTime? StartSendTime { get; set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedTime { get; set; }
    
    /// <summary>
    /// 消息来源类型
    /// </summary>
    public MessageSourceType SourceType { get; set; } = MessageSourceType.AiReply;
    
    /// <summary>
    /// 优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;
}

/// <summary>
/// 消息来源类型
/// </summary>
public enum MessageSourceType
{
    /// <summary>
    /// AI自动回复
    /// </summary>
    AiReply = 1,
    
    /// <summary>
    /// 定时发布
    /// </summary>
    ScheduledPost = 2,
    
    /// <summary>
    /// 手动发送
    /// </summary>
    ManualSend = 3,
    
    /// <summary>
    /// 批量操作
    /// </summary>
    BatchOperation = 4
}


/// <summary>
/// 消息拆分配置
/// </summary>
public class MessageSplitOptions
{
    /// <summary>
    /// 最大文本长度（超过则拆分）
    /// </summary>
    public int MaxTextLength { get; set; } = 2000;
    
    /// <summary>
    /// 单条消息发送最小间隔时间（毫秒）
    /// </summary>
    public int SingleMessageMinIntervalMs { get; set; } = 1500;
    
    /// <summary>
    /// 单条消息发送最大间隔时间（毫秒）
    /// </summary>
    public int SingleMessageMaxIntervalMs { get; set; } = 2000;
    
    /// <summary>
    /// 批量操作最小间隔时间（毫秒）
    /// </summary>
    public int BatchOperationMinIntervalMs { get; set; } = 300;
    
    /// <summary>
    /// 批量操作最大间隔时间（毫秒）
    /// </summary>
    public int BatchOperationMaxIntervalMs { get; set; } = 1500;
    
    /// <summary>
    /// 是否保持图片和文本的关联性
    /// </summary>
    public bool KeepImageTextAssociation { get; set; } = true;
    
    /// <summary>
    /// 是否启用智能拆分（基于语义）
    /// </summary>
    public bool EnableSmartSplit { get; set; } = true;
    
    /// <summary>
    /// 拆分分隔符
    /// </summary>
    public List<string> SplitDelimiters { get; set; } = new() { "\n\n", "\n", "。", "！", "？", "；" };
    
    /// <summary>
    /// 随机数生成器（用于间隔时间）
    /// </summary>
    private static readonly Random _random = new();
    
    /// <summary>
    /// 获取随机间隔时间 - 根据消息来源类型选择不同的间隔
    /// </summary>
    /// <param name="sourceType">消息来源类型</param>
    /// <returns>随机间隔时间（毫秒）</returns>
    public int GetRandomInterval(MessageSourceType sourceType = MessageSourceType.AiReply)
    {
        return sourceType switch
        {
            MessageSourceType.BatchOperation => _random.Next(BatchOperationMinIntervalMs, BatchOperationMaxIntervalMs + 1),
            _ => _random.Next(SingleMessageMinIntervalMs, SingleMessageMaxIntervalMs + 1)
        };
    }
    
    /// <summary>
    /// 获取单条消息随机间隔时间（1500ms-2000ms）
    /// </summary>
    public int GetSingleMessageRandomInterval()
    {
        return _random.Next(SingleMessageMinIntervalMs, SingleMessageMaxIntervalMs + 1);
    }
    
    /// <summary>
    /// 获取批量操作随机间隔时间（300ms-1500ms）
    /// </summary>
    public int GetBatchOperationRandomInterval()
    {
        return _random.Next(BatchOperationMinIntervalMs, BatchOperationMaxIntervalMs + 1);
    }
}
