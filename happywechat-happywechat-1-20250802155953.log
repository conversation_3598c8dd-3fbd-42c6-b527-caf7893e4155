[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 UnifiedEYunSendQueueConsumer启动 - 开始转发消息到发送队列
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1494ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1815ms
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      🚀 WxSendMessageConsumer启动 - 开始处理真实微信消息发送
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 3.2991ms
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      🔍 消息队列健康检查服务启动
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a5237436] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: a5237436, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a5237436] ✅ 流式架构路由完成 - Duration: 58.8617ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a5237436] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: a5237436, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Web.Controllers.WxController[0]
      [4e5870fd] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: a5237436, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a5237436] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a5237436] 📝 AI请求已入队 - MessageId: 60dde76a-a7d7-4b64-9534-86a6dd059f64, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a5237436] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 127.5904ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: a5237436, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-1, ProcessingId: a5237436, MessageType: TextFast, Duration: 132.3886ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a5237436] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a5237436] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [4b663888] 🤖 开始统一AI响应处理 - Length: 33, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 33
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 33
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [4b663888] 📝 Markdown过滤完成 - 原长度: 33, 过滤后: 33
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [4b663888] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [4b663888] 📝 AI响应拆分完成 - 原始长度: 33, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: e97b08c7, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: e97b08c7, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [4b663888] ✅ AI响应已入队统一发送队列 - BatchId: e97b08c7, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a5237436] ✅ AI请求处理完成 - BatchId: e97b08c7, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754361897515
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 9cb78a02-ce4b-4fbe-8d82-962d1743b65a, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 33
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 9cb78a02-ce4b-4fbe-8d82-962d1743b65a, QueueMessageId: 576bf369-7fb2-4318-acab-266554a86531, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 9cb78a02-ce4b-4fbe-8d82-962d1743b65a, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 576bf369-7fb2-4318-acab-266554a86531, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 576bf369-7fb2-4318-acab-266554a86531, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 33
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: 你好？我还以为你要跟我说“再见”呢，因为冷笑话都是来不及回应的嘛！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 33, Content: 你好？我还以为你要跟我说“再见”呢，因为冷笑话都是来不及回应的嘛！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, MsgId: 2195111359, Duration: 320.5569ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2195111359, WcId: wxid_scqpt8dyuxqv41
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 576bf369-7fb2-4318-acab-266554a86531, QueueType: 文本消息, Duration: 368.0993ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 576bf369-7fb2-4318-acab-266554a86531, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [97bf8f62] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 97bf8f62, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 97bf8f62, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [97bf8f62] ✅ 流式架构路由完成 - Duration: 10.1915ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [97bf8f62] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [d2566f4d] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 97bf8f62, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [97bf8f62] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [f1df56a0] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: f1df56a0
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [97bf8f62] 📝 AI请求已入队 - MessageId: 5f265bc6-751b-446b-85cb-3880e14551a5, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [97bf8f62] ✅ 统一消息处理完成 - Category: GroupText, Duration: 48.9985ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 97bf8f62, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-0, ProcessingId: 97bf8f62, MessageType: PriorityAt, Duration: 51.5113ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [97bf8f62] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [97bf8f62] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [b6c38b8c] 🤖 开始统一AI响应处理 - Length: 88, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 88
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 82
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [b6c38b8c] 📝 Markdown过滤完成 - 原长度: 88, 过滤后: 82
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [b6c38b8c] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [b6c38b8c] 📝 AI响应拆分完成 - 原始长度: 88, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 0c345344, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 0c345344, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [b6c38b8c] ✅ AI响应已入队统一发送队列 - BatchId: 0c345344, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [97bf8f62] ✅ AI请求处理完成 - BatchId: 0c345344, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754361915383
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: b1c71de6-e2e5-498b-9851-4c45a18a195b, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 82
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: b1c71de6-e2e5-498b-9851-4c45a18a195b, QueueMessageId: 5ce8d76f-076e-4e5d-9166-eb1a94a02ed6, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: b1c71de6-e2e5-498b-9851-4c45a18a195b, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 5ce8d76f-076e-4e5d-9166-eb1a94a02ed6, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 5ce8d76f-076e-4e5d-9166-eb1a94a02ed6, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 82
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, Content: 张涛对心说：“你好！”
      心回答：“你好呀，你是来给我讲冷笑话的吗？”
      张涛：“不是，我只是想测试一下...
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 82, Content: 张涛对心说：“你好！”
      心回答：“你好呀，你是来给我讲冷笑话的吗？”
      张涛：“不是，我只是想测试一下‘心’的反应速度。”
      心：“那你成功了，因为我现在心跳加速了！”
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, MsgId: 3643165905, Duration: 223.2712ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功（含@） - MsgId: 3643165905, At: wxid_scqpt8dyuxqv41, WcId: 53451126890@chatroom
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 5ce8d76f-076e-4e5d-9166-eb1a94a02ed6, QueueType: 文本消息, Duration: 223.6681ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 5ce8d76f-076e-4e5d-9166-eb1a94a02ed6, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [3ae7d84a] 📥 收到EYun回调数据: FromGroup: 48112916393@chatroom, MessageType: 85008
info: HappyWechat.Web.Controllers.WxController[0]
      [60058a9c] ✅ 回调处理成功
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [68584880] 📥 收到EYun回调数据: FromGroup: 48112916393@chatroom, Content: Hi, MessageType: 80001
warn: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [68584880] ❌ 8字头群聊消息群组不存在，消息被跳过 - FromGroup: '48112916393@chatroom', MessageType: 80001
info: HappyWechat.Web.Controllers.WxController[0]
      [2cc96da3] ✅ 回调处理成功
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 543abb92..., 耗时: 2ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
