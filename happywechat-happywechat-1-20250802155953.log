[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 UnifiedEYunSendQueueConsumer启动 - 开始转发消息到发送队列
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1451ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1712ms
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      🚀 WxSendMessageConsumer启动 - 开始处理真实微信消息发送
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 3.4671ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [570e9190] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 570e9190, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [570e9190] ✅ 流式架构路由完成 - Duration: 21.1453ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 570e9190, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [570e9190] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 570e9190, MessageType: MediaSlow
info: HappyWechat.Web.Controllers.WxController[0]
      [c99709b6] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [570e9190] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [570e9190] 📁 媒体处理请求已入队 - MessageId: b1b7530f-73e0-414d-9b6a-6b2943e7b555, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [570e9190] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 41.052ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 570e9190, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-0, ProcessingId: 570e9190, MessageType: MediaSlow, Duration: 44.8245ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [570e9190] 📁 开始处理媒体请求 - MessageType: 60004, RequestId: 57234b93638b4def8d7852f5aecc587c, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [570e9190] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 🎵 开始处理语音消息 - MsgId: 748144638, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1552" length="2262" bufid="0" aeskey="a7738260c0e24bf0fe1d641e43829e9e" voiceurl="3052020100044b3049020100020462ca6be902032f533f02041d81336f02046890dd76042466663136356133302d313064382d346461382d623361322d336637396
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 2262, BufId: 0, VoiceLength: 1552
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 2262, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] ✅ 语音参数验证通过 - Length: 2262, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 📋 语音参数提取成功 - Length: 2262, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 748144638, Length: 2262, BufId: 0, FromUser: wxid_pbqye5z48yzm22
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] 📡 调用EYun语音下载API - Length: 2262, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 748144638, Length: 2262, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250805/wxid_ic3nmv9anggz22/7718b57c-1c70-47ad-84b7-dd34f4ed53a2.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=DV2R2kTPuXVJt%2BXWH%2BwPaONTE9o%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_748144638_20250805001904.mp3, Size: 22404 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [7ae42cee] ✅ 语音处理完成 - 文件: voice_748144638_20250805001904.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_748144638_20250805001904.mp3, 大小: 22404字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [570e9190] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_748144638_20250805001904.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [570e9190] 📝 AI请求已入队 - MessageId: a47e6f8f-92dd-497e-bf33-2c9cf9a0a9dd, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [570e9190] ✅ 媒体消息AI处理完成 - Duration: 2028.1587ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [570e9190] ✅ 媒体请求处理完成 - MessageType: 60004, AiProcessed: True, Duration: 2031.6464ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_748144638_20250805001904.mp3
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: c9b3319f-8a06-456b-8602-59c811864298
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_748144638_20250805001904.mp3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c59c54c3] 🤖 开始统一AI响应处理 - Length: 79, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 79
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 79
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c59c54c3] 📝 Markdown过滤完成 - 原长度: 79, 过滤后: 79
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c59c54c3] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c59c54c3] 📝 AI响应拆分完成 - 原始长度: 79, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 2405bfde, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 2405bfde, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c59c54c3] ✅ AI响应已入队统一发送队列 - BatchId: 2405bfde, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754324353833
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [570e9190] ✅ AI请求处理完成 - BatchId: 2405bfde, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: b55bb9ea-f731-4e83-9295-41ff663033f3, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 79
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: b55bb9ea-f731-4e83-9295-41ff663033f3, QueueMessageId: 1150a958-d213-4152-a770-85cc258b5b52, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: b55bb9ea-f731-4e83-9295-41ff663033f3, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 1150a958-d213-4152-a770-85cc258b5b52, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 1150a958-d213-4152-a770-85cc258b5b52, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 79
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: , WcId: wxid_pbqye5z48yzm22, Content: 看起来你分享了一个语音消息的链接。如果你需要对这段语音的内容进行分析或处理，你可能需要先下载或播放该...
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 1150a958-d213-4152-a770-85cc258b5b52, QueueType: 文本消息, Duration: 41.9546ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 1150a958-d213-4152-a770-85cc258b5b52, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e07290ca] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: e07290ca, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: e07290ca, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e07290ca] ✅ 流式架构路由完成 - Duration: 31.6757ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: e07290ca, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e07290ca] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [e07290ca] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [31af5be4] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [e07290ca] 📝 AI请求已入队 - MessageId: c4569363-a064-475d-bc50-5c86c58a54ba, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [e07290ca] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 11.0767ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: e07290ca, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-0, ProcessingId: e07290ca, MessageType: TextFast, Duration: 12.9396ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文本消息内容：你好
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: 99da6ecd-ad88-4f10-b492-dfcf68320522
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [277f0a6f] 🤖 开始统一AI响应处理 - Length: 15, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 15
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 15
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [277f0a6f] 📝 Markdown过滤完成 - 原长度: 15, 过滤后: 15
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [277f0a6f] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [277f0a6f] 📝 AI响应拆分完成 - 原始长度: 15, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 40938254, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 40938254, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [277f0a6f] ✅ AI响应已入队统一发送队列 - BatchId: 40938254, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [e07290ca] ✅ AI请求处理完成 - BatchId: 40938254, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754324363820
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 0644adcb-01ec-495b-a422-1193ec3d6ac8, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 15
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 0644adcb-01ec-495b-a422-1193ec3d6ac8, QueueMessageId: 346269eb-ebd4-42b2-90a5-fa968d503102, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 0644adcb-01ec-495b-a422-1193ec3d6ac8, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 346269eb-ebd4-42b2-90a5-fa968d503102, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 346269eb-ebd4-42b2-90a5-fa968d503102, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 15
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: , WcId: wxid_pbqye5z48yzm22, Content: 你好！有什么我可以帮助你的吗？
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 346269eb-ebd4-42b2-90a5-fa968d503102, QueueType: 文本消息, Duration: 1.1059ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 346269eb-ebd4-42b2-90a5-fa968d503102, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [36fa4a06] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 36fa4a06, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 36fa4a06, StreamingType: MediaSlow, OriginalType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 36fa4a06, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [36fa4a06] 🚀 统一消息处理器开始处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [36fa4a06] ✅ 流式架构路由完成 - Duration: 7.1157ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [36fa4a06] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [bf52c8b5] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [36fa4a06] 📁 媒体处理请求已入队 - MessageId: 6f690112-4a31-4848-aa30-512a1d3183ec, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [36fa4a06] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 1.6142ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 36fa4a06, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-1, ProcessingId: 36fa4a06, MessageType: MediaSlow, Duration: 2.742ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [36fa4a06] 📁 开始处理媒体请求 - MessageType: 60002, RequestId: 1b03f1330928468a9e1299a90eca08f9, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [36fa4a06] 🖼️ 60002图片消息开始处理 - FromUser: wxid_pbqye5z48yzm22, MsgId: **********
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [36fa4a06] 📁 开始媒体消息AI处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [b1cfed85] 🖼️ 开始处理图片消息 - MsgId: **********, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun图片下载API调用 - Endpoint: /getMsgImg, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********, Type: 0, ContentLength: 1279
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [b1cfed85] ✅ EYun图片下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250805/wxid_ic3nmv9anggz22/71aaed39-6a50-4060-a2c3-d456d2721bff.png?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=jxKjAhgBnRxSJYWvw0VHhy%2FK2jA%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 228083 bytes - wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg, Size: 228083 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T161929Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=052c86c3cd1301e4b71248a67bc58561d4c1d39ac7635db81f8babb331100cd8, Size: 228083 bytes - image_**********_20250805001929.jpg
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [36fa4a06] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [36fa4a06] 📝 AI请求已入队 - MessageId: 05aa7834-2166-4f3d-bce5-94e024708e93, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 5
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [36fa4a06] ✅ 媒体消息AI处理完成 - Duration: 983.0244ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [36fa4a06] ✅ 媒体请求处理完成 - MessageType: 60002, AiProcessed: True, Duration: 984.0896ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [36fa4a06] 🖼️ 60002图片消息处理完成 - AiProcessed: True, PublicUrl: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，图片消息，媒体文件链接：http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: e363c1f9-1db4-4f41-b9bd-e74194fac4b5
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，图片消息，媒体文件链接：http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805001929.jpg
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [55512d10] 🤖 开始统一AI响应处理 - Length: 52, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 52
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 52
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [55512d10] 📝 Markdown过滤完成 - 原长度: 52, 过滤后: 52
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [55512d10] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [55512d10] 📝 AI响应拆分完成 - 原始长度: 52, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 90b98470, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 90b98470, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [55512d10] ✅ AI响应已入队统一发送队列 - BatchId: 90b98470, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [36fa4a06] ✅ AI请求处理完成 - BatchId: 90b98470, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754324376251
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 78e9d46c-aedf-4ba5-9721-8d0240ec228d, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 52
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 78e9d46c-aedf-4ba5-9721-8d0240ec228d, QueueMessageId: b4b48ca4-2ecd-4cd7-bcd9-6d7f40561c25, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 78e9d46c-aedf-4ba5-9721-8d0240ec228d, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: b4b48ca4-2ecd-4cd7-bcd9-6d7f40561c25, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: b4b48ca4-2ecd-4cd7-bcd9-6d7f40561c25, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 52
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: , WcId: wxid_pbqye5z48yzm22, Content: 看起来您分享了一个微信的图片消息链接。如果您需要对这张图片进行特定的操作或者有其他相关的请求，请告诉...
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: b4b48ca4-2ecd-4cd7-bcd9-6d7f40561c25, QueueType: 文本消息, Duration: 0.9768ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: b4b48ca4-2ecd-4cd7-bcd9-6d7f40561c25, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b03ce996] 📥 收到EYun回调数据: FromGroup: 49248600804@chatroom, MessageType: 85008
info: HappyWechat.Web.Controllers.WxController[0]
      [228e8ad0] ✅ 回调处理成功
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 9ce43737..., 耗时: 2ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [8e093a3a] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60008
info: HappyWechat.Web.Controllers.WxController[0]
      [f1bac07a] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [cc3b8958] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: cc3b8958, MessageType: 60009, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [cc3b8958] ✅ 流式架构路由完成 - Duration: 8.2992ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [cc3b8958] ✅ 回调数据处理完成 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [b4f8ca22] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: cc3b8958, StreamingType: MediaSlow, OriginalType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: cc3b8958, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [cc3b8958] 🚀 统一消息处理器开始处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [cc3b8958] 📁 媒体处理请求已入队 - MessageId: c0c79155-7da6-40a4-9a83-9c13d363f1e7, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60009
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [cc3b8958] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 2.1968ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: cc3b8958, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-2, ProcessingId: cc3b8958, MessageType: MediaSlow, Duration: 2.5044ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [cc3b8958] 📁 开始处理媒体请求 - MessageType: 60009, RequestId: e7888b04b30a40a5af0591ed5fb2485d, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [cc3b8958] 📁 开始媒体消息AI处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun文件下载API调用 - Endpoint: /getMsgFile, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 704163655
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 447 bytes - wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt, Size: 447 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T162020Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=f06272206925bb98e27900019ec3143fe852ae73f7e0437d0597dab345f9e43b, Size: 447 bytes - file_704163655_20250805002020.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [cc3b8958] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [cc3b8958] 📝 AI请求已入队 - MessageId: 0d70d779-07da-48a3-96d7-ca84dd873ce8, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [cc3b8958] ✅ 媒体消息AI处理完成 - Duration: 897.2971ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [cc3b8958] ✅ 媒体请求处理完成 - MessageType: 60009, AiProcessed: True, Duration: 897.3593ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: 55004930-e42b-4e4d-965f-0f0a45f4de82
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_704163655_20250805002020.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [27914450] 🤖 开始统一AI响应处理 - Length: 132, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 132
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 131
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [27914450] 📝 Markdown过滤完成 - 原长度: 132, 过滤后: 131
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [27914450] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [27914450] 📝 AI响应拆分完成 - 原始长度: 132, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: e34d1122, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: e34d1122, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [27914450] ✅ AI响应已入队统一发送队列 - BatchId: e34d1122, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [cc3b8958] ✅ AI请求处理完成 - BatchId: e34d1122, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754324424763
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 636d5480-c70e-4abe-a270-92ceb34d4278, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 131
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 636d5480-c70e-4abe-a270-92ceb34d4278, QueueMessageId: eac49165-58e8-456e-912b-d754701ecf40, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 636d5480-c70e-4abe-a270-92ceb34d4278, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: eac49165-58e8-456e-912b-d754701ecf40, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: eac49165-58e8-456e-912b-d754701ecf40, WId: , WcId: wxid_pbqye5z48yzm22, ContentLength: 131
warn: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      记录消息内容异常 - MessageId: eac49165-58e8-456e-912b-d754701ecf40, QueueType: send_text
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: The best overloaded method match for 'string.this[int]' has some invalid arguments
         at CallSite.Target(Closure, CallSite, Object, Range)
         at System.Dynamic.UpdateDelegates.UpdateAndExecute2[T0,T1,TRet](CallSite site, T0 arg0, T1 arg1)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.LogMessageContent(Object messageData, String queueType, String messageId)
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: , WcId: wxid_pbqye5z48yzm22, Content: 看起来您提供的信息是关于一个文件消息的链接，可能是在微信中发送的。该链接指向一个媒体文件，后面有个文...
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: eac49165-58e8-456e-912b-d754701ecf40, QueueType: 文本消息, Duration: 4.2122ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: eac49165-58e8-456e-912b-d754701ecf40, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
