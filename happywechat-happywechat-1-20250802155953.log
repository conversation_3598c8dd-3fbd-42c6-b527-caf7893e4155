[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 UnifiedEYunSendQueueConsumer启动 - 开始转发消息到发送队列
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1549ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 2054ms
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      🚀 WxSendMessageConsumer启动 - 开始处理真实微信消息发送
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 1.8553ms
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      🔍 消息队列健康检查服务启动
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a88ee174] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: a88ee174, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a88ee174] ✅ 流式架构路由完成 - Duration: 47.4399ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a88ee174] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: a88ee174, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Web.Controllers.WxController[0]
      [fbbb65b7] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: a88ee174, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a88ee174] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a88ee174] 📝 AI请求已入队 - MessageId: 7f387512-0a71-48c4-9d00-60fe9feba82a, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a88ee174] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 68.2714ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: a88ee174, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-0, ProcessingId: a88ee174, MessageType: TextFast, Duration: 73.9851ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a88ee174] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a88ee174] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c58f42d0] 🤖 开始统一AI响应处理 - Length: 35, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 35
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 35
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c58f42d0] 📝 Markdown过滤完成 - 原长度: 35, 过滤后: 35
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c58f42d0] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c58f42d0] 📝 AI响应拆分完成 - 原始长度: 35, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 26ca57f3, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 26ca57f3, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [c58f42d0] ✅ AI响应已入队统一发送队列 - BatchId: 26ca57f3, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a88ee174] ✅ AI请求处理完成 - BatchId: 26ca57f3, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359134574
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 6e68124d-0155-458d-89ac-6cf918538cd6, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 35
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 6e68124d-0155-458d-89ac-6cf918538cd6, QueueMessageId: 314e26d1-e000-42ac-988f-0c7ad4da0216, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 6e68124d-0155-458d-89ac-6cf918538cd6, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 314e26d1-e000-42ac-988f-0c7ad4da0216, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 314e26d1-e000-42ac-988f-0c7ad4da0216, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 35
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: 你好啊！你知道为什么你好这么短吗？因为它已经包含了“好”的全部秘密了！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 35, Content: 你好啊！你知道为什么你好这么短吗？因为它已经包含了“好”的全部秘密了！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, MsgId: 2760964754, Duration: 365.6207ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2760964754
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 314e26d1-e000-42ac-988f-0c7ad4da0216, QueueType: 文本消息, Duration: 411.0293ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 314e26d1-e000-42ac-988f-0c7ad4da0216, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [1f5e2545] 📥 收到EYun回调数据: FromGroup: 49248600804@chatroom, MessageType: 85008
info: HappyWechat.Web.Controllers.WxController[0]
      [ba6458f4] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a588b349] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: a588b349, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: a588b349, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a588b349] ✅ 流式架构路由完成 - Duration: 6.7606ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a588b349] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [a0d5452d] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: a588b349, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a588b349] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a588b349] 📁 媒体处理请求已入队 - MessageId: dec44196-7a23-4ea3-a1c4-d6400919c0b7, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a588b349] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 8.2142ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: a588b349, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-0, ProcessingId: a588b349, MessageType: MediaSlow, Duration: 10.8274ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a588b349] 📁 开始处理媒体请求 - MessageType: 60004, RequestId: 62310d26b5ae4cf098e9e7eebfbf0f1c, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a588b349] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 🎵 开始处理语音消息 - MsgId: 1031126611, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1473" length="2316" bufid="0" aeskey="2b46ecbb5e677919c519cda28b98819e" voiceurl="3052020100044b30490201000204857b835a02032f533f02041081336f0204689165d9042437656231353938322d393161322d343130382d616333332d663263643
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 2316, BufId: 0, VoiceLength: 1473
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 2316, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] ✅ 语音参数验证通过 - Length: 2316, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 📋 语音参数提取成功 - Length: 2316, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1031126611, Length: 2316, BufId: 0, FromUser: wxid_scqpt8dyuxqv41
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] 📡 调用EYun语音下载API - Length: 2316, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1031126611, Length: 2316, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250805/wxid_ic3nmv9anggz22/166bcc9a-e91e-4a47-9c14-2048b96f7768.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=8PRFOkzS6xURajdI7rxFK%2FlIt%2Bo%3D
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_1031126611_20250805100059.mp3, Size: 20315 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [bbca4718] ✅ 语音处理完成 - 文件: voice_1031126611_20250805100059.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_1031126611_20250805100059.mp3, 大小: 20315字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a588b349] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_1031126611_20250805100059.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a588b349] 📝 AI请求已入队 - MessageId: b4fa59e4-e680-4292-b127-c2c1833c61a2, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a588b349] ✅ 媒体消息AI处理完成 - Duration: 1716.4572ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a588b349] ✅ 媒体请求处理完成 - MessageType: 60004, AiProcessed: True, Duration: 1718.8714ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a588b349] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a588b349] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/voice_1031126611_20250805100059.mp3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d8a15fd2] 🤖 开始统一AI响应处理 - Length: 48, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 48
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 48
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d8a15fd2] 📝 Markdown过滤完成 - 原长度: 48, 过滤后: 48
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d8a15fd2] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d8a15fd2] 📝 AI响应拆分完成 - 原始长度: 48, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 362bca2d, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 362bca2d, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d8a15fd2] ✅ AI响应已入队统一发送队列 - BatchId: 362bca2d, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a588b349] ✅ AI请求处理完成 - BatchId: 362bca2d, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359262475
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 9928a2ea-ea10-4d87-9590-7626bd4336c1, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 48
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 9928a2ea-ea10-4d87-9590-7626bd4336c1, QueueMessageId: eb63978c-3e76-4e80-a3ce-570e6401460b, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 9928a2ea-ea10-4d87-9590-7626bd4336c1, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: eb63978c-3e76-4e80-a3ce-570e6401460b, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: eb63978c-3e76-4e80-a3ce-570e6401460b, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 48
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: 抱歉，我无法直接访问或处理语音消息链接。你可以告诉我语音消息的内容吗？我帮你把内容改造成冷笑话！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 48, Content: 抱歉，我无法直接访问或处理语音消息链接。你可以告诉我语音消息的内容吗？我帮你把内容改造成冷笑话！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, MsgId: 3897440118, Duration: 310.6823ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 3897440118
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: eb63978c-3e76-4e80-a3ce-570e6401460b, QueueType: 文本消息, Duration: 311.713ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: eb63978c-3e76-4e80-a3ce-570e6401460b, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [7b1da036] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60008
info: HappyWechat.Web.Controllers.WxController[0]
      [1edb6912] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a427ef74] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: a427ef74, StreamingType: MediaSlow, OriginalType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: a427ef74, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: a427ef74, MessageType: 60009, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a427ef74] ✅ 流式架构路由完成 - Duration: 13.0142ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [a427ef74] ✅ 回调数据处理完成 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [a5e7ccaf] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a427ef74] 🚀 统一消息处理器开始处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a427ef74] 📁 媒体处理请求已入队 - MessageId: 8c9e1d5d-4af2-4bd9-b3fa-914c698a1b4e, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60009
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [a427ef74] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 4.6847ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: a427ef74, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-1, ProcessingId: a427ef74, MessageType: MediaSlow, Duration: 6.7232ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a427ef74] 📁 开始处理媒体请求 - MessageType: 60009, RequestId: be8094df7672444c80c54f55a6a26e85, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a427ef74] 📁 开始媒体消息AI处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun文件下载API调用 - Endpoint: /getMsgFile, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 447 bytes - wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_**********_20250805100116.txt
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_**********_20250805100116.txt, Size: 447 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_**********_20250805100116.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250805%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T020116Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=ed006f82ee837e040f5764f34da9d8a8ed4d6008e883030ca195076b23cce182, Size: 447 bytes - file_**********_20250805100116.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a427ef74] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_**********_20250805100116.txt
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [a427ef74] 📝 AI请求已入队 - MessageId: e65265aa-49c5-4a2b-a07f-c3f4c1c3b8b3, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [a427ef74] ✅ 媒体消息AI处理完成 - Duration: 1035.9643ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a427ef74] ✅ 媒体请求处理完成 - MessageType: 60009, AiProcessed: True, Duration: 1036.858ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a427ef74] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a427ef74] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/file_**********_20250805100116.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [78b870a7] 🤖 开始统一AI响应处理 - Length: 55, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 55
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 53
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [78b870a7] 📝 Markdown过滤完成 - 原长度: 55, 过滤后: 53
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [78b870a7] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [78b870a7] 📝 AI响应拆分完成 - 原始长度: 55, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 0edc5d53, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 0edc5d53, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [78b870a7] ✅ AI响应已入队统一发送队列 - BatchId: 0edc5d53, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [a427ef74] ✅ AI请求处理完成 - BatchId: 0edc5d53, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359278035
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 40debc8a-aada-4be2-a424-d58fc6a07b5e, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 40debc8a-aada-4be2-a424-d58fc6a07b5e, QueueMessageId: 4c201d96-512b-4e02-bd2d-18f203015f6b, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 40debc8a-aada-4be2-a424-d58fc6a07b5e, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 4c201d96-512b-4e02-bd2d-18f203015f6b, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 4c201d96-512b-4e02-bd2d-18f203015f6b, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: 有个人发了个文件，链接长得像迷宫，点开一看，结果是个“未来的冷笑话”：
      “2025年，我还没找到出口...
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53, Content: 有个人发了个文件，链接长得像迷宫，点开一看，结果是个“未来的冷笑话”：
      “2025年，我还没找到出口呢！”
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, MsgId: 2750415767, Duration: 291.3264ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2750415767
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 4c201d96-512b-4e02-bd2d-18f203015f6b, QueueType: 文本消息, Duration: 291.5236ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 4c201d96-512b-4e02-bd2d-18f203015f6b, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [08552c08] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 08552c08, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 08552c08, StreamingType: MediaSlow, OriginalType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 08552c08, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [08552c08] 🚀 统一消息处理器开始处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [08552c08] ✅ 流式架构路由完成 - Duration: 7.3678ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [08552c08] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [d37b06cb] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [08552c08] 📁 媒体处理请求已入队 - MessageId: d41773d4-2758-45a1-aeb3-0dfdf179c284, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [08552c08] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 1.8722ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 08552c08, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-3, ProcessingId: 08552c08, MessageType: MediaSlow, Duration: 2.0283ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] 📁 开始处理媒体请求 - MessageType: 60002, RequestId: 04d48ee9d3124c33834b6a3c825ed1bb, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] 🖼️ 60002图片消息开始处理 - FromUser: wxid_scqpt8dyuxqv41, MsgId: **********
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [08552c08] 📁 开始媒体消息AI处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [67f1ba46] 🖼️ 开始处理图片消息 - MsgId: **********, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun图片下载API调用 - Endpoint: /getMsgImg, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********, Type: 0, ContentLength: 1280
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [67f1ba46] ✅ EYun图片下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250805/wxid_ic3nmv9anggz22/bf47e117-8c05-49e1-bf01-042bbbeb0b6e.png?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=nIqhEDYQDUGu4F2Zx3QqxNFWGJQ%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 234087 bytes - wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg, Size: 234087 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250805%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250805T020147Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=34d9c86a14cb3082a441a7c63e3d8338be02dc0eff64f8ec5d583097091a2826, Size: 234087 bytes - image_**********_20250805100147.jpg
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [08552c08] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [08552c08] 📝 AI请求已入队 - MessageId: 439848ab-d89a-443b-b355-9e4a90309658, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 5
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [08552c08] ✅ 媒体消息AI处理完成 - Duration: 889.0394ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] ✅ 媒体请求处理完成 - MessageType: 60002, AiProcessed: True, Duration: 889.1685ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] 🖼️ 60002图片消息处理完成 - AiProcessed: True, PublicUrl: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，图片消息，媒体文件链接：http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/05/image_**********_20250805100147.jpg
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [aeb0024c] 🤖 开始统一AI响应处理 - Length: 53, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 53
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 53
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [aeb0024c] 📝 Markdown过滤完成 - 原长度: 53, 过滤后: 53
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [aeb0024c] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [aeb0024c] 📝 AI响应拆分完成 - 原始长度: 53, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 646b3eaa, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 646b3eaa, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [aeb0024c] ✅ AI响应已入队统一发送队列 - BatchId: 646b3eaa, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [08552c08] ✅ AI请求处理完成 - BatchId: 646b3eaa, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359309148
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 58b69d68-a359-4407-8c93-14674c3264d5, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 58b69d68-a359-4407-8c93-14674c3264d5, QueueMessageId: 682b9bb9-ccb1-4532-a3fe-d258ae50d06a, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 58b69d68-a359-4407-8c93-14674c3264d5, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 682b9bb9-ccb1-4532-a3fe-d258ae50d06a, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 682b9bb9-ccb1-4532-a3fe-d258ae50d06a, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: 这张图片里的人笑得那么灿烂，看来他刚发现，原来“笑一笑，十年少”不是说十年后才年轻，而是现在年轻得不...
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, ContentLength: 53, Content: 这张图片里的人笑得那么灿烂，看来他刚发现，原来“笑一笑，十年少”不是说十年后才年轻，而是现在年轻得不得了！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, MsgId: 2123165207, Duration: 308.2004ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2123165207
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 682b9bb9-ccb1-4532-a3fe-d258ae50d06a, QueueType: 文本消息, Duration: 308.4089ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 682b9bb9-ccb1-4532-a3fe-d258ae50d06a, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [345ab614] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 345ab614, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [345ab614] ✅ 流式架构路由完成 - Duration: 8.4538ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 345ab614, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [345ab614] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [fdf2ff56] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 345ab614, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [345ab614] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [3a1ebeb1] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: 3a1ebeb1
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [345ab614] 📝 AI请求已入队 - MessageId: 78226208-af99-479b-9a31-3ced4443dc57, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [345ab614] ✅ 统一消息处理完成 - Category: GroupText, Duration: 63.4111ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 345ab614, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-0, ProcessingId: 345ab614, MessageType: PriorityAt, Duration: 65.7875ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [345ab614] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [345ab614] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [ee1c72ce] 🤖 开始统一AI响应处理 - Length: 46, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 46
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 44
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [ee1c72ce] 📝 Markdown过滤完成 - 原长度: 46, 过滤后: 44
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [ee1c72ce] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [ee1c72ce] 📝 AI响应拆分完成 - 原始长度: 46, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 7680477f, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 7680477f, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [ee1c72ce] ✅ AI响应已入队统一发送队列 - BatchId: 7680477f, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [345ab614] ✅ AI请求处理完成 - BatchId: 7680477f, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359334884
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: fc92c2ae-5be0-471e-be4f-bbf35a443714, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 44
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: fc92c2ae-5be0-471e-be4f-bbf35a443714, QueueMessageId: a83a891f-4473-4389-87f7-ea0528985b34, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: fc92c2ae-5be0-471e-be4f-bbf35a443714, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: a83a891f-4473-4389-87f7-ea0528985b34, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: a83a891f-4473-4389-87f7-ea0528985b34, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 44
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, Content: 张涛：@心 你好
      心：你好啊！你这是在跟我打招呼，还是在给“心”发消息？我都有点心慌了！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 44, Content: 张涛：@心 你好
      心：你好啊！你这是在跟我打招呼，还是在给“心”发消息？我都有点心慌了！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, MsgId: 3629198835, Duration: 219.6838ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 3629198835
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: a83a891f-4473-4389-87f7-ea0528985b34, QueueType: 文本消息, Duration: 220.0721ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: a83a891f-4473-4389-87f7-ea0528985b34, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e882ffdc] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, MessageType: 80008
info: HappyWechat.Web.Controllers.WxController[0]
      [12cb7b81] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4bafca7e] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, MessageType: 80009
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4bafca7e] ✅ 流式架构路由完成 - Duration: 6.7657ms, Result: 消息已预处理完成
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4bafca7e] ✅ 回调数据处理完成 - MessageType: 80009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [3b4fdeee] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [5bae1e20] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 5bae1e20, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 5bae1e20, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 5bae1e20, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [5bae1e20] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [5bae1e20] ✅ 流式架构路由完成 - Duration: 9.0739ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [5bae1e20] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [ca400d3e] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [f79b4667] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: f79b4667
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [5bae1e20] 📝 AI请求已入队 - MessageId: 1227832f-55f9-4144-8210-2432709121e7, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [5bae1e20] ✅ 统一消息处理完成 - Category: GroupText, Duration: 35.6657ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 5bae1e20, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-1, ProcessingId: 5bae1e20, MessageType: PriorityAt, Duration: 35.8174ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [5bae1e20] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [5bae1e20] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [fb8c9aff] 🤖 开始统一AI响应处理 - Length: 37, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 37
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 35
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [fb8c9aff] 📝 Markdown过滤完成 - 原长度: 37, 过滤后: 35
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [fb8c9aff] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [fb8c9aff] 📝 AI响应拆分完成 - 原始长度: 37, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 23911c52, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 23911c52, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [fb8c9aff] ✅ AI响应已入队统一发送队列 - BatchId: 23911c52, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [5bae1e20] ✅ AI请求处理完成 - BatchId: 23911c52, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359345868
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: 3df68c93-8ad9-4485-a5c7-bbfcef74d14c, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 35
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: 3df68c93-8ad9-4485-a5c7-bbfcef74d14c, QueueMessageId: 647fb0a5-1d78-47a9-a012-9429f32f36db, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: 3df68c93-8ad9-4485-a5c7-bbfcef74d14c, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 647fb0a5-1d78-47a9-a012-9429f32f36db, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 647fb0a5-1d78-47a9-a012-9429f32f36db, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 35
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, Content: 张涛问心：“你好！”
      心回答：“别叫我，我正在休眠模式，冷冷清清呢！”
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 35, Content: 张涛问心：“你好！”
      心回答：“别叫我，我正在休眠模式，冷冷清清呢！”
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, MsgId: 3382849112, Duration: 202.8639ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 3382849112
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 647fb0a5-1d78-47a9-a012-9429f32f36db, QueueType: 文本消息, Duration: 203.8682ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 647fb0a5-1d78-47a9-a012-9429f32f36db, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [77ded525] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, MessageType: 80002
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [77ded525] ✅ 流式架构路由完成 - Duration: 7.226ms, Result: 消息已预处理完成
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [77ded525] ✅ 回调数据处理完成 - MessageType: 80002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [23cdf174] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [60e8d391] 📥 收到EYun回调数据: FromGroup: 48112916393@chatroom, MessageType: 85008
info: HappyWechat.Web.Controllers.WxController[0]
      [b679b748] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📊 流式架构状态报告 - Queues: Fast(0), Slow(0), Priority(0) | Stats: Processed(6), Failed(0), Enqueued(6)
info: HappyWechat.Infrastructure.MessageQueue.Monitoring.MessageQueueHealthService[0]
      ✅ 消息队列健康报告 - 总计: 5, 全部健康
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b88e9fa5] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, MessageType: 80002
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b88e9fa5] ✅ 流式架构路由完成 - Duration: 7.3939ms, Result: 消息已预处理完成
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b88e9fa5] ✅ 回调数据处理完成 - MessageType: 80002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [1df01359] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c42be1ed] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 分析, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: c42be1ed, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: c42be1ed, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [c42be1ed] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: c42be1ed, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c42be1ed] ✅ 流式架构路由完成 - Duration: 14.2097ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c42be1ed] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [4959f008] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 分析'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [5de6944e] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: 5de6944e
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - MessageType: 80001, BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 分析'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [c42be1ed] 📝 AI请求已入队 - MessageId: 1d1cf917-f904-425b-9ca0-07ab93e7e783, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [c42be1ed] ✅ 统一消息处理完成 - Category: GroupText, Duration: 24.9627ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: c42be1ed, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-2, ProcessingId: c42be1ed, MessageType: PriorityAt, Duration: 25.1335ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [c42be1ed] 通过数据库查询获取wId - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [c42be1ed] 🔧 重新构建AI模板，使用正确的wId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 分析
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [071bbca9] 🤖 开始统一AI响应处理 - Length: 41, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 41
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 41
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [071bbca9] 📝 Markdown过滤完成 - 原长度: 41, 过滤后: 41
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [071bbca9] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [071bbca9] 📝 AI响应拆分完成 - 原始长度: 41, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: f265b120, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: f265b120, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [071bbca9] ✅ AI响应已入队统一发送队列 - BatchId: f265b120, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [c42be1ed] ✅ AI请求处理完成 - BatchId: f265b120, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754359408610
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] 📝 转发文本消息命令 - MessageId: c38184b8-f394-49ff-b76b-4515c7cde9ca, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 41
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发成功 - MessageId: c38184b8-f394-49ff-b76b-4515c7cde9ca, QueueMessageId: 0b54ddf1-3728-4c1c-8e28-ef5bfcb16798, QueueType: send_text, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 消息转发完成 - MessageId: c38184b8-f394-49ff-b76b-4515c7cde9ca, CommandType: WxSendTextMessageCommand, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      [FORWARD] ✅ 批量转发全部成功 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] 🚀 开始真实发送微信消息 - MessageId: 0b54ddf1-3728-4c1c-8e28-ef5bfcb16798, QueueType: 文本消息, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [CONTENT] 📝 文本消息内容 - MessageId: 0b54ddf1-3728-4c1c-8e28-ef5bfcb16798, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 41
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, Content: @心 分析？别急，我刚刚分析了一下，发现心脏其实很懒，它最喜欢的运动就是“跳跃”！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] 📤 EYun发送文本消息开始 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, ContentLength: 41, Content: @心 分析？别急，我刚刚分析了一下，发现心脏其实很懒，它最喜欢的运动就是“跳跃”！
info: HappyWechat.Infrastructure.EYun.EYunWrapper[0]
      [API] ✅ EYun发送文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, MsgId: 3833202727, Duration: 224.1987ms
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 3833202727
fail: HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer[0]
      [REAL-SEND] ❌ 微信消息发送异常 - MessageId: 0b54ddf1-3728-4c1c-8e28-ef5bfcb16798, QueueType: 文本消息, Duration: 224.6188ms
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
         at CallSite.Target(Closure, CallSite, Object)
         at HappyWechat.Infrastructure.MessageQueue.Consumers.WxSendMessageConsumer.ProcessSingleMessageAsync(IWxMessageService wxMessageService, ISimplifiedQueueService queueService, Object message, Guid wxManagerId, String queueType, CancellationToken cancellationToken)
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      ☠️ 消息已拒绝并移入死信队列 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: send_text, MessageId: 0b54ddf1-3728-4c1c-8e28-ef5bfcb16798, Reason: 'System.ValueTuple<bool,string,string>' does not contain a definition for 'Success'
