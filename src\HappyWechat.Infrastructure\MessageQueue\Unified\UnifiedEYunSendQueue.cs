using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using HappyWechat.Application.DTOs.Requests.Commands;

namespace HappyWechat.Infrastructure.MessageQueue.Unified;

/// <summary>
/// 统一EYun发送队列
/// 所有消息统一入队，按账号隔离，严格控制1000-1500ms发送间隔
/// </summary>
public interface IUnifiedEYunSendQueue
{
    /// <summary>
    /// 入队发送消息
    /// </summary>
    Task<string> EnqueueAsync(Guid wxManagerId, object sendCommand, int priority = 5, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量入队发送消息（保证顺序）
    /// </summary>
    Task<string> EnqueueBatchAsync(Guid wxManagerId, List<object> sendCommands, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取下一批待发送的消息
    /// </summary>
    Task<List<UnifiedSendMessage>> DequeueReadyMessagesAsync(Guid wxManagerId, int maxCount = 5, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记消息为已发送
    /// </summary>
    Task MarkAsSentAsync(Guid wxManagerId, string messageId, bool success, string? errorMessage = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取队列状态
    /// </summary>
    Task<UnifiedSendQueueStatus> GetQueueStatusAsync(Guid wxManagerId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 统一发送消息
/// </summary>
public class UnifiedSendMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public Guid WxManagerId { get; set; }
    public string CommandType { get; set; } = string.Empty; // SendTextCommand, SendImageCommand, etc.
    public string CommandJson { get; set; } = string.Empty;
    public int Priority { get; set; } = 5;
    public int Order { get; set; } // 批次内的顺序
    public long ScheduledTime { get; set; } // Unix时间戳（毫秒）
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string BatchId { get; set; } = string.Empty; // 批次ID，用于关联
    public int RetryCount { get; set; } = 0;
    public DateTime? LastRetryAt { get; set; }
}

/// <summary>
/// 发送队列状态
/// </summary>
public class UnifiedSendQueueStatus
{
    public Guid WxManagerId { get; set; }
    public int PendingCount { get; set; }
    public int ProcessingCount { get; set; }
    public DateTime? LastSentAt { get; set; }
    public DateTime? NextScheduledAt { get; set; }
    public double AverageIntervalMs { get; set; }
}

public class UnifiedEYunSendQueue : IUnifiedEYunSendQueue
{
    private readonly IDatabase _database;
    private readonly ILogger<UnifiedEYunSendQueue> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly Random _random = new();
    
    private const string QUEUE_PREFIX = "hw:unified_send";
    private const string SCHEDULED_SET_PREFIX = "hw:unified_send_scheduled";
    private const string STATUS_PREFIX = "hw:unified_send_status";
    private const int MIN_INTERVAL_MS = 1000; // 最小间隔1000ms
    private const int MAX_INTERVAL_MS = 1500; // 最大间隔1500ms
    
    public UnifiedEYunSendQueue(
        IConnectionMultiplexer redis,
        ILogger<UnifiedEYunSendQueue> logger)
    {
        _database = redis.GetDatabase();
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }
    
    /// <summary>
    /// 入队发送消息
    /// </summary>
    public async Task<string> EnqueueAsync(Guid wxManagerId, object sendCommand, int priority = 5, CancellationToken cancellationToken = default)
    {
        var message = new UnifiedSendMessage
        {
            WxManagerId = wxManagerId,
            CommandType = sendCommand.GetType().Name,
            CommandJson = JsonSerializer.Serialize(sendCommand, _jsonOptions),
            Priority = priority,
            Order = 1,
            BatchId = Guid.NewGuid().ToString("N")[..8]
        };
        
        return await EnqueueSingleMessageAsync(message, cancellationToken);
    }
    
    /// <summary>
    /// 批量入队发送消息（保证顺序）
    /// </summary>
    public async Task<string> EnqueueBatchAsync(Guid wxManagerId, List<object> sendCommands, CancellationToken cancellationToken = default)
    {
        if (!sendCommands.Any())
        {
            return string.Empty;
        }
        
        var batchId = Guid.NewGuid().ToString("N")[..8];
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        
        _logger.LogInformation("🔄 开始批量入队发送消息 - WxManagerId: {WxManagerId}, BatchId: {BatchId}, Count: {Count}",
            wxManagerId, batchId, sendCommands.Count);
        
        try
        {
            // 获取上次发送时间，计算延迟
            var lastSentTime = await GetLastSentTimeAsync(wxManagerId);
            var baseDelay = CalculateInitialDelay(lastSentTime, currentTime);
            
            var transaction = _database.CreateTransaction();
            
            for (int i = 0; i < sendCommands.Count; i++)
            {
                var command = sendCommands[i];

                // 获取正确的命令类型名称
                var commandType = GetCommandTypeName(command);
                if (string.IsNullOrEmpty(commandType))
                {
                    _logger.LogWarning("⚠️ 跳过未知命令类型 - Type: {Type}", command.GetType().Name);
                    continue;
                }

                var message = new UnifiedSendMessage
                {
                    WxManagerId = wxManagerId,
                    CommandType = commandType,
                    CommandJson = JsonSerializer.Serialize(command, _jsonOptions),
                    Priority = 5,
                    Order = i + 1,
                    BatchId = batchId
                };
                
                // 计算延迟时间：第一条消息使用基础延迟，后续消息按间隔递增
                var delayMs = baseDelay + (i * GetRandomInterval());
                message.ScheduledTime = currentTime + delayMs;
                
                var scheduledSetKey = $"{SCHEDULED_SET_PREFIX}:{wxManagerId}";
                var json = JsonSerializer.Serialize(message, _jsonOptions);
                
                // 添加到有序集合，使用调度时间作为分数
                transaction.SortedSetAddAsync(scheduledSetKey, json, message.ScheduledTime);
                
                _logger.LogDebug("📝 发送消息已调度 - Order: {Order}, DelayMs: {DelayMs}, CommandType: {CommandType}",
                    message.Order, delayMs, message.CommandType);
            }
            
            // 更新状态
            var statusKey = $"{STATUS_PREFIX}:{wxManagerId}";
            transaction.HashSetAsync(statusKey, new HashEntry[]
            {
                new("last_batch_id", batchId),
                new("last_enqueue_time", currentTime),
                new("pending_count", sendCommands.Count)
            });
            
            // 设置过期时间
            transaction.KeyExpireAsync($"{SCHEDULED_SET_PREFIX}:{wxManagerId}", TimeSpan.FromHours(24));
            transaction.KeyExpireAsync(statusKey, TimeSpan.FromHours(24));

            await transaction.ExecuteAsync();

            _logger.LogInformation("✅ 批量入队完成 - BatchId: {BatchId}, 首条延迟: {BaseDelay}ms", batchId, baseDelay);

            return batchId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 批量入队失败 - WxManagerId: {WxManagerId}", wxManagerId);
            throw;
        }
    }
    
    /// <summary>
    /// 获取下一批待发送的消息
    /// </summary>
    public async Task<List<UnifiedSendMessage>> DequeueReadyMessagesAsync(Guid wxManagerId, int maxCount = 5, CancellationToken cancellationToken = default)
    {
        var scheduledSetKey = $"{SCHEDULED_SET_PREFIX}:{wxManagerId}";
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        
        try
        {
            // 获取已到时间的消息（按分数升序）
            var readyMessages = await _database.SortedSetRangeByScoreAsync(
                scheduledSetKey, 
                0, 
                currentTime, 
                Exclude.None, 
                Order.Ascending, 
                0, 
                maxCount);
            
            if (!readyMessages.Any())
            {
                _logger.LogDebug("📦 没有待发送消息 - WxManagerId: {WxManagerId}, CurrentTime: {CurrentTime}",
                    wxManagerId, currentTime);
                return new List<UnifiedSendMessage>();
            }
            
            var results = new List<UnifiedSendMessage>();
            
            foreach (var messageJson in readyMessages)
            {
                try
                {
                    var message = JsonSerializer.Deserialize<UnifiedSendMessage>(messageJson!, _jsonOptions);
                    if (message != null)
                    {
                        results.Add(message);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "发送消息反序列化失败 - WxManagerId: {WxManagerId}", wxManagerId);
                }
            }
            
            _logger.LogInformation("📦 获取待发送消息 - WxManagerId: {WxManagerId}, Count: {Count}, CurrentTime: {CurrentTime}",
                wxManagerId, results.Count, currentTime);
            
            return results.OrderBy(r => r.Order).ToList(); // 确保按顺序返回
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取待发送消息失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new List<UnifiedSendMessage>();
        }
    }
    
    /// <summary>
    /// 入队单个消息
    /// </summary>
    private async Task<string> EnqueueSingleMessageAsync(UnifiedSendMessage message, CancellationToken cancellationToken)
    {
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var lastSentTime = await GetLastSentTimeAsync(message.WxManagerId);
        var delayMs = CalculateInitialDelay(lastSentTime, currentTime);
        
        message.ScheduledTime = currentTime + delayMs;
        
        var scheduledSetKey = $"{SCHEDULED_SET_PREFIX}:{message.WxManagerId}";
        var json = JsonSerializer.Serialize(message, _jsonOptions);
        
        await _database.SortedSetAddAsync(scheduledSetKey, json, message.ScheduledTime);
        await _database.KeyExpireAsync(scheduledSetKey, TimeSpan.FromHours(24));
        
        _logger.LogDebug("📝 单个发送消息已入队 - MessageId: {MessageId}, DelayMs: {DelayMs}",
            message.Id, delayMs);
        
        return message.Id;
    }
    
    /// <summary>
    /// 获取随机间隔（1000-1500ms）
    /// </summary>
    private int GetRandomInterval()
    {
        return _random.Next(MIN_INTERVAL_MS, MAX_INTERVAL_MS + 1);
    }
    
    /// <summary>
    /// 计算初始延迟
    /// </summary>
    private int CalculateInitialDelay(long lastSentTime, long currentTime)
    {
        if (lastSentTime == 0)
        {
            return 0; // 首次发送，立即发送
        }
        
        var timeSinceLastSent = currentTime - lastSentTime;
        var minInterval = GetRandomInterval();
        
        if (timeSinceLastSent >= minInterval)
        {
            return 0; // 已经等待足够时间，立即发送
        }
        
        return (int)(minInterval - timeSinceLastSent);
    }
    
    /// <summary>
    /// 获取上次发送时间
    /// </summary>
    private async Task<long> GetLastSentTimeAsync(Guid wxManagerId)
    {
        try
        {
            var statusKey = $"{STATUS_PREFIX}:{wxManagerId}";
            var lastSentTimeStr = await _database.HashGetAsync(statusKey, "last_sent_time");
            
            if (lastSentTimeStr.HasValue && long.TryParse(lastSentTimeStr, out var lastSentTime))
            {
                return lastSentTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取上次发送时间失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
        
        return 0;
    }

    /// <summary>
    /// 标记消息为已发送
    /// </summary>
    public async Task MarkAsSentAsync(Guid wxManagerId, string messageId, bool success, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        var scheduledSetKey = $"{SCHEDULED_SET_PREFIX}:{wxManagerId}";
        var statusKey = $"{STATUS_PREFIX}:{wxManagerId}";
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        try
        {
            // 从调度集合中移除已发送的消息
            var allMessages = await _database.SortedSetRangeByScoreAsync(scheduledSetKey);

            foreach (var messageJson in allMessages)
            {
                try
                {
                    var message = JsonSerializer.Deserialize<UnifiedSendMessage>(messageJson!, _jsonOptions);
                    if (message?.Id == messageId)
                    {
                        await _database.SortedSetRemoveAsync(scheduledSetKey, messageJson);

                        // 更新状态
                        if (success)
                        {
                            await _database.HashSetAsync(statusKey, new HashEntry[]
                            {
                                new("last_sent_time", currentTime),
                                new("last_sent_message_id", messageId),
                                new("total_sent", await _database.HashIncrementAsync(statusKey, "total_sent", 1))
                            });

                            _logger.LogDebug("✅ 发送消息已标记为成功 - MessageId: {MessageId}", messageId);
                        }
                        else
                        {
                            await _database.HashIncrementAsync(statusKey, "total_failed", 1);
                            _logger.LogWarning("❌ 发送消息已标记为失败 - MessageId: {MessageId}, Error: {Error}",
                                messageId, errorMessage);
                        }
                        break;
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "标记已发送时反序列化失败 - MessageId: {MessageId}", messageId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 标记消息发送状态异常 - MessageId: {MessageId}", messageId);
        }
    }

    /// <summary>
    /// 获取队列状态
    /// </summary>
    public async Task<UnifiedSendQueueStatus> GetQueueStatusAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        var scheduledSetKey = $"{SCHEDULED_SET_PREFIX}:{wxManagerId}";
        var statusKey = $"{STATUS_PREFIX}:{wxManagerId}";

        try
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            // 获取待处理消息数量
            var pendingCount = await _database.SortedSetLengthAsync(scheduledSetKey);

            // 获取下一条消息的调度时间
            var nextMessage = await _database.SortedSetRangeByScoreAsync(scheduledSetKey, 0, double.PositiveInfinity, Exclude.None, Order.Ascending, 0, 1);
            DateTime? nextScheduledAt = null;

            if (nextMessage.Any())
            {
                try
                {
                    var message = JsonSerializer.Deserialize<UnifiedSendMessage>(nextMessage[0]!, _jsonOptions);
                    if (message != null)
                    {
                        nextScheduledAt = DateTimeOffset.FromUnixTimeMilliseconds(message.ScheduledTime).DateTime;
                    }
                }
                catch (JsonException)
                {
                    // 忽略反序列化错误
                }
            }

            // 获取状态信息
            var statusFields = await _database.HashGetAllAsync(statusKey);
            var statusDict = statusFields.ToDictionary(x => x.Name.ToString(), x => x.Value.ToString());

            DateTime? lastSentAt = null;
            if (statusDict.TryGetValue("last_sent_time", out var lastSentTimeStr) &&
                long.TryParse(lastSentTimeStr, out var lastSentTime))
            {
                lastSentAt = DateTimeOffset.FromUnixTimeMilliseconds(lastSentTime).DateTime;
            }

            return new UnifiedSendQueueStatus
            {
                WxManagerId = wxManagerId,
                PendingCount = (int)pendingCount,
                ProcessingCount = 0, // 当前实现中没有处理中状态
                LastSentAt = lastSentAt,
                NextScheduledAt = nextScheduledAt,
                AverageIntervalMs = (MIN_INTERVAL_MS + MAX_INTERVAL_MS) / 2.0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列状态失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new UnifiedSendQueueStatus { WxManagerId = wxManagerId };
        }
    }

    /// <summary>
    /// 获取正确的命令类型名称
    /// </summary>
    private string GetCommandTypeName(object command)
    {
        return command.GetType().Name switch
        {
            "WxSendTextMessageCommand" => "WxSendTextMessageCommand",
            "WxSendImageMessageCommand" => "WxSendImageMessageCommand",
            "WxSendFileMessageCommand" => "WxSendFileMessageCommand",
            "WxSendVoiceMessageCommand" => "WxSendVoiceMessageCommand",
            "WxSendVideoMessageCommand" => "WxSendVideoMessageCommand",
            "WxSendFileBase64MessageCommand" => "WxSendFileBase64MessageCommand",
            "WxSendEmojiMessageCommand" => "WxSendEmojiMessageCommand",
            "WxSendLinkMessageCommand" => "WxSendLinkMessageCommand",
            _ => string.Empty
        };
    }
}
