namespace HappyWechat.Application.DTOs.Requests.Commands;

public class WxFirstTimeLoginCommand
{
    /// <summary>
    /// 登录地区代理
    /// </summary>
    public required int Proxy { get; set; }
    
    /// <summary>
    /// 登录设备类型：ipad(默认), mac, android
    /// </summary>
    public string DeviceType { get; set; } = "ipad";
    
    /// <summary>
    /// 自定义长效代理IP+端口
    /// </summary>
    public string? ProxyIp { get; set; } = string.Empty;
    
    /// <summary>
    /// 自定义长效代理IP平台账号
    /// </summary>
    public string? ProxyUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 自定义长效代理IP平台密码
    /// </summary>
    public string? ProxyPassword { get; set; } = string.Empty;
}