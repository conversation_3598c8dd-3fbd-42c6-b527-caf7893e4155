namespace HappyWechat.Infrastructure.MessageProcessing.Models;

/// <summary>
/// 好友请求信息
/// </summary>
public class FriendRequestInfo
{
    /// <summary>
    /// 请求者微信ID
    /// </summary>
    public string FromUser { get; set; } = string.Empty;

    /// <summary>
    /// 请求者昵称
    /// </summary>
    public string FromNickname { get; set; } = string.Empty;

    /// <summary>
    /// 验证消息
    /// </summary>
    public string VerifyMessage { get; set; } = string.Empty;

    /// <summary>
    /// 请求消息（兼容性属性）
    /// </summary>
    public string RequestMessage { get; set; } = string.Empty;

    /// <summary>
    /// 请求场景
    /// </summary>
    public int Scene { get; set; }

    /// <summary>
    /// 微信ID（用于API调用）
    /// </summary>
    public string WId { get; set; } = string.Empty;

    /// <summary>
    /// V1参数（用于API调用）
    /// </summary>
    public string V1 { get; set; } = string.Empty;

    /// <summary>
    /// V2参数（用于API调用）
    /// </summary>
    public string V2 { get; set; } = string.Empty;

    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime RequestTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 时间戳（兼容性属性）
    /// </summary>
    public long Timestamp { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 是否包含通过关键词
    /// </summary>
    public bool ContainsAcceptKeywords { get; set; }

    /// <summary>
    /// 匹配的关键词
    /// </summary>
    public List<string> MatchedKeywords { get; set; } = new();

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();

    /// <summary>
    /// 创建好友请求信息
    /// </summary>
    public static FriendRequestInfo Create(string fromUser, string fromNickname, string verifyMessage, int scene)
    {
        return new FriendRequestInfo
        {
            FromUser = fromUser,
            FromNickname = fromNickname,
            VerifyMessage = verifyMessage,
            Scene = scene
        };
    }

    /// <summary>
    /// 设置API参数
    /// </summary>
    public void SetApiParameters(string wId, string v1, string v2)
    {
        WId = wId;
        V1 = v1;
        V2 = v2;
    }

    /// <summary>
    /// 检查是否包含通过关键词
    /// </summary>
    public void CheckAcceptKeywords(IEnumerable<string> keywords)
    {
        MatchedKeywords.Clear();
        
        foreach (var keyword in keywords)
        {
            if (!string.IsNullOrEmpty(keyword) && VerifyMessage.Contains(keyword, StringComparison.OrdinalIgnoreCase))
            {
                MatchedKeywords.Add(keyword);
            }
        }

        ContainsAcceptKeywords = MatchedKeywords.Count > 0;
    }
}
