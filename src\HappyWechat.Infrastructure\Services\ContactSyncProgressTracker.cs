using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Caching.Interfaces;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Notifications.Interfaces;
using HappyWechat.Infrastructure.Notifications.Models;
using System.Text.Json;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 联系人同步进度跟踪服务
/// </summary>
public class ContactSyncProgressTracker
{
    private readonly ILogger<ContactSyncProgressTracker> _logger;
    private readonly IUnifiedCacheManager _cacheManager;
    private readonly IUnifiedSyncNotificationService _notificationService;
    private const string PROGRESS_KEY_PREFIX = "contact_sync_progress";
    private const int PROGRESS_CACHE_DURATION_MINUTES = 30;

    public ContactSyncProgressTracker(
        ILogger<ContactSyncProgressTracker> logger,
        IUnifiedCacheManager cacheManager,
        IUnifiedSyncNotificationService notificationService)
    {
        _logger = logger;
        _cacheManager = cacheManager;
        _notificationService = notificationService;
    }

    /// <summary>
    /// 初始化同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="syncSessionId">同步会话ID</param>
    /// <param name="totalFriends">个人联系人总数</param>
    /// <param name="totalEnterprise">企业联系人总数</param>
    /// <param name="totalFriendBatches">个人联系人批次数</param>
    /// <param name="totalEnterpriseBatches">企业联系人批次数</param>
    /// <returns></returns>
    public async Task InitializeProgressAsync(
        Guid wxManagerId, 
        string syncSessionId,
        int totalFriends, 
        int totalEnterprise,
        int totalFriendBatches,
        int totalEnterpriseBatches)
    {
        var progress = new ContactSyncProgressDto
        {
            WxManagerId = wxManagerId,
            SyncSessionId = syncSessionId,
            Status = SyncStatus.InProgress,
            StartTime = DateTime.UtcNow,
            
            // 总体统计
            TotalCount = totalFriends + totalEnterprise,
            ProcessedCount = 0,
            SuccessCount = 0,
            FailedCount = 0,
            
            // 个人联系人统计
            FriendsTotal = totalFriends,
            FriendsProcessed = 0,
            FriendsSuccess = 0,
            FriendsFailed = 0,
            FriendsBatchesTotal = totalFriendBatches,
            FriendsBatchesCompleted = 0,
            
            // 企业联系人统计
            EnterpriseTotal = totalEnterprise,
            EnterpriseProcessed = 0,
            EnterpriseSuccess = 0,
            EnterpriseFailed = 0,
            EnterpriseBatchesTotal = totalEnterpriseBatches,
            EnterpriseBatchesCompleted = 0,
            
            ProgressPercentage = 0,
            EstimatedCompletionTime = CalculateEstimatedCompletionTime(totalFriends + totalEnterprise),
            CurrentPhase = "初始化同步任务",
            LastUpdateTime = DateTime.UtcNow
        };

        await SaveProgressAsync(wxManagerId, progress);
        _logger.LogInformation("初始化联系人同步进度 - WxManagerId: {WxManagerId}, SessionId: {SessionId}, 总数: {Total} (个人: {Friends}, 企业: {Enterprise})",
            wxManagerId, syncSessionId, progress.TotalCount, totalFriends, totalEnterprise);
    }

    /// <summary>
    /// 更新批次完成进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="listType">联系人类型</param>
    /// <param name="batchIndex">批次索引</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="successCount">成功数量</param>
    /// <param name="failedCount">失败数量</param>
    /// <returns></returns>
    public async Task UpdateBatchProgressAsync(
        Guid wxManagerId,
        WxContactListType listType,
        int batchIndex,
        int batchSize,
        int successCount,
        int failedCount)
    {
        var progress = await GetProgressAsync(wxManagerId);
        if (progress == null)
        {
            _logger.LogWarning("未找到同步进度信息 - WxManagerId: {WxManagerId}", wxManagerId);
            return;
        }

        // 更新对应类型的统计
        if (listType == WxContactListType.Friends)
        {
            progress.FriendsBatchesCompleted++;
            progress.FriendsProcessed += batchSize;
            progress.FriendsSuccess += successCount;
            progress.FriendsFailed += failedCount;
            progress.CurrentPhase = $"处理个人联系人批次 {batchIndex + 1}/{progress.FriendsBatchesTotal}";
        }
        else if (listType == WxContactListType.Enterprise)
        {
            progress.EnterpriseBatchesCompleted++;
            progress.EnterpriseProcessed += batchSize;
            progress.EnterpriseSuccess += successCount;
            progress.EnterpriseFailed += failedCount;
            progress.CurrentPhase = $"处理企业联系人批次 {batchIndex + 1}/{progress.EnterpriseBatchesTotal}";
        }

        // 更新总体统计
        progress.ProcessedCount = progress.FriendsProcessed + progress.EnterpriseProcessed;
        progress.SuccessCount = progress.FriendsSuccess + progress.EnterpriseSuccess;
        progress.FailedCount = progress.FriendsFailed + progress.EnterpriseFailed;
        progress.ProgressPercentage = CalculateProgressPercentage(progress.ProcessedCount, progress.TotalCount);
        progress.LastUpdateTime = DateTime.UtcNow;

        // 检查是否完成
        var totalBatchesCompleted = progress.FriendsBatchesCompleted + progress.EnterpriseBatchesCompleted;
        var totalBatches = progress.FriendsBatchesTotal + progress.EnterpriseBatchesTotal;

        if (totalBatchesCompleted >= totalBatches)
        {
            progress.Status = SyncStatus.Completed;
            progress.CompletedTime = DateTime.UtcNow;
            progress.CurrentPhase = "同步完成";
            progress.ProgressPercentage = 100;

            // 🔧 发送同步完成通知
            await SendSyncCompletedNotificationAsync(wxManagerId, progress);
        }
        else
        {
            // 🔧 发送进度更新通知
            await SendProgressUpdateNotificationAsync(wxManagerId, progress);
        }

        await SaveProgressAsync(wxManagerId, progress);

        // 🔧 优化日志等级：进度更新使用Debug级别
        _logger.LogDebug("更新批次进度 - WxManagerId: {WxManagerId}, 类型: {ListType}, 批次: {BatchIndex}, 进度: {Progress}%",
            wxManagerId, listType, batchIndex, progress.ProgressPercentage);
    }

    /// <summary>
    /// 标记同步失败
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="errorMessage">错误信息</param>
    /// <returns></returns>
    public async Task MarkSyncFailedAsync(Guid wxManagerId, string errorMessage)
    {
        var progress = await GetProgressAsync(wxManagerId);
        if (progress == null)
        {
            _logger.LogWarning("未找到同步进度信息 - WxManagerId: {WxManagerId}", wxManagerId);
            return;
        }

        progress.Status = SyncStatus.Failed;
        progress.CompletedTime = DateTime.UtcNow;
        progress.ErrorMessage = errorMessage;
        progress.CurrentPhase = "同步失败";
        progress.LastUpdateTime = DateTime.UtcNow;

        await SaveProgressAsync(wxManagerId, progress);

        // 🔧 发送同步失败通知
        await _notificationService.SendSyncFailedAsync(wxManagerId, "Contact", errorMessage);

        _logger.LogError("标记同步失败 - WxManagerId: {WxManagerId}, 错误: {Error}", wxManagerId, errorMessage);
    }

    /// <summary>
    /// 获取同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task<ContactSyncProgressDto?> GetProgressAsync(Guid wxManagerId)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            var cachedProgress = await _cacheManager.GetAsync<string>(cacheKey);
            
            if (string.IsNullOrEmpty(cachedProgress))
            {
                return null;
            }

            var progress = JsonSerializer.Deserialize<ContactSyncProgressDto>(cachedProgress);
            return progress;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    /// <summary>
    /// 清除同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task ClearProgressAsync(Guid wxManagerId)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            await _cacheManager.RemoveAsync(cacheKey);
            _logger.LogInformation("清除同步进度 - WxManagerId: {WxManagerId}", wxManagerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 保存进度到缓存
    /// </summary>
    private async Task SaveProgressAsync(Guid wxManagerId, ContactSyncProgressDto progress)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            var progressJson = JsonSerializer.Serialize(progress);
            await _cacheManager.SetAsync(cacheKey, progressJson, TimeSpan.FromMinutes(PROGRESS_CACHE_DURATION_MINUTES));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 获取进度缓存键
    /// </summary>
    private string GetProgressCacheKey(Guid wxManagerId)
    {
        return $"{PROGRESS_KEY_PREFIX}:{wxManagerId}";
    }

    /// <summary>
    /// 计算进度百分比
    /// </summary>
    private decimal CalculateProgressPercentage(int processed, int total)
    {
        if (total <= 0) return 0;
        return Math.Round((decimal)processed / total * 100, 2);
    }

    /// <summary>
    /// 计算预计完成时间
    /// </summary>
    private DateTime? CalculateEstimatedCompletionTime(int totalCount)
    {
        if (totalCount <= 0) return null;

        // 假设每个联系人平均处理时间为1秒（包含延时）
        var estimatedSeconds = totalCount * 1;
        return DateTime.UtcNow.AddSeconds(estimatedSeconds);
    }

    /// <summary>
    /// 发送同步完成通知
    /// </summary>
    private async Task SendSyncCompletedNotificationAsync(Guid wxManagerId, ContactSyncProgressDto progress)
    {
        try
        {
            var notification = new ContactSyncCompletionNotification
            {
                WxManagerId = wxManagerId,
                Status = progress.Status,
                SuccessCount = progress.SuccessCount,
                FailedCount = progress.FailedCount,
                FriendsSuccessCount = progress.FriendsSuccess,
                EnterpriseSuccessCount = progress.EnterpriseSuccess,
                FriendsFailedCount = progress.FriendsFailed,
                EnterpriseFailedCount = progress.EnterpriseFailed,
                ElapsedMilliseconds = progress.CompletedTime.HasValue
                    ? (long)(progress.CompletedTime.Value - progress.StartTime).TotalMilliseconds
                    : 0,
                CompletedTime = progress.CompletedTime ?? DateTime.UtcNow,
                SessionId = progress.SyncSessionId
            };

            var result = await _notificationService.SendContactSyncCompletedAsync(notification);

            if (result.Success)
            {
                _logger.LogInformation("✅ 联系人同步完成通知发送成功 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
                    wxManagerId, notification.SuccessCount, notification.FailedCount);
            }
            else
            {
                _logger.LogWarning("⚠️ 联系人同步完成通知发送失败 - WxManagerId: {WxManagerId}, Error: {Error}",
                    wxManagerId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送联系人同步完成通知异常 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 发送进度更新通知
    /// </summary>
    private async Task SendProgressUpdateNotificationAsync(Guid wxManagerId, ContactSyncProgressDto progress)
    {
        try
        {
            var notification = new SyncProgressNotification
            {
                WxManagerId = wxManagerId,
                Status = progress.Status,
                ProgressPercentage = (int)progress.ProgressPercentage,
                ProcessedCount = Convert.ToInt32(progress.ProcessedCount),
                TotalCount = progress.TotalCount,
                CurrentPhase = progress.CurrentPhase ?? "同步中",
                EstimatedRemainingMs = CalculateEstimatedRemainingTime(progress)
            };

            // 进度通知使用低优先级，避免过于频繁
            var options = new NotificationSendOptions
            {
                Priority = NotificationPriority.Low,
                EnableRetry = false // 进度通知失败不重试
            };

            await _notificationService.SendContactSyncProgressAsync(notification, options);
        }
        catch (Exception ex)
        {
            // 进度通知失败不记录错误日志，避免日志噪音
            _logger.LogDebug(ex, "进度通知发送失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 计算预计剩余时间
    /// </summary>
    private long? CalculateEstimatedRemainingTime(ContactSyncProgressDto progress)
    {
        if ((int)progress.ProcessedCount <= 0 || progress.TotalCount <= (int)progress.ProcessedCount)
        {
            return null;
        }

        var elapsed = DateTime.UtcNow - progress.StartTime;
        var avgTimePerItem = elapsed.TotalMilliseconds / progress.ProcessedCount;
        var remainingItems = progress.TotalCount - progress.ProcessedCount;

        return (long)(avgTimePerItem * remainingItems);
    }
}
