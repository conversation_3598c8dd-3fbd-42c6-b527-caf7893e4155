namespace HappyWechat.Application.DTOs.File;

/// <summary>
/// 文件发送DTO
/// </summary>
public class FileSendDto
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string? FileType { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件路径或URL
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 接收者
    /// </summary>
    public string? ToUser { get; set; }

    /// <summary>
    /// 群ID（如果是群消息）
    /// </summary>
    public string? GroupId { get; set; }

    /// <summary>
    /// 时长（音频/视频）
    /// </summary>
    public int Duration { get; set; }

    /// <summary>
    /// 附加选项
    /// </summary>
    public Dictionary<string, object> Options { get; set; } = new();
}