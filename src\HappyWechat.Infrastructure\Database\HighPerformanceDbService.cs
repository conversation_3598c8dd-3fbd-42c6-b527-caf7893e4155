using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Identity.Repositories;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 高性能数据库服务实现
/// 基于IndependentDbContextFactory提供优化的数据库访问
/// 支持批量操作、性能监控和自动优化
/// </summary>
public class HighPerformanceDbService : IHighPerformanceDbService
{
    private readonly IDbContextFactory _dbContextFactory;
    private readonly ILogger<HighPerformanceDbService> _logger;
    
    // 性能统计
    private readonly ConcurrentDictionary<string, long> _operationCounts = new();
    private readonly ConcurrentQueue<double> _executionTimes = new();
    private long _totalOperations;

    public HighPerformanceDbService(
        IDbContextFactory dbContextFactory,
        ILogger<HighPerformanceDbService> logger)
    {
        _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行批量查询操作
    /// </summary>
    public async Task<TResult> ExecuteBatchQueryAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> query)
    {
        if (query == null) throw new ArgumentNullException(nameof(query));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _dbContextFactory.CreateReadContextAsync();
            
            // 查询优化配置
            context.ChangeTracker.QueryTrackingBehavior = Microsoft.EntityFrameworkCore.QueryTrackingBehavior.NoTracking;
            context.ChangeTracker.AutoDetectChangesEnabled = false;
            
            var result = await query(context);
            
            RecordOperation("BatchQuery", stopwatch.ElapsedMilliseconds);
            
            _logger.LogTrace("批量查询操作完成 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量查询操作失败 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 执行批量命令操作
    /// </summary>
    public async Task<TResult> ExecuteBatchCommandAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> command)
    {
        if (command == null) throw new ArgumentNullException(nameof(command));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _dbContextFactory.CreateWriteContextAsync();
            
            // 批量操作优化
            context.ChangeTracker.AutoDetectChangesEnabled = true;
            // 注意：在EF Core中，命令超时通过DbContextOptions配置，或在具体命令执行时设置
            
            var result = await command(context);
            
            RecordOperation("BatchCommand", stopwatch.ElapsedMilliseconds);
            
            _logger.LogTrace("批量命令操作完成 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量命令操作失败 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 执行事务操作
    /// </summary>
    public async Task<TResult> ExecuteTransactionAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> operation)
    {
        if (operation == null) throw new ArgumentNullException(nameof(operation));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _dbContextFactory.CreateWriteContextAsync();
            await using var transaction = await context.Database.BeginTransactionAsync();
            
            try
            {
                var result = await operation(context);
                await transaction.CommitAsync();
                
                RecordOperation("Transaction", stopwatch.ElapsedMilliseconds);
                
                _logger.LogTrace("事务操作完成 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
                
                return result;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "事务操作失败 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    public HighPerformanceDbStatistics GetStatistics()
    {
        var executionTimesArray = _executionTimes.ToArray();
        var averageTime = executionTimesArray.Length > 0 ? executionTimesArray.Average() : 0;

        return new HighPerformanceDbStatistics
        {
            TotalOperations = _totalOperations,
            QueryOperations = _operationCounts.GetValueOrDefault("BatchQuery", 0),
            CommandOperations = _operationCounts.GetValueOrDefault("BatchCommand", 0),
            TransactionOperations = _operationCounts.GetValueOrDefault("Transaction", 0),
            AverageExecutionTimeMs = averageTime,
            LastStatisticsUpdate = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 记录操作统计
    /// </summary>
    private void RecordOperation(string operationType, long durationMs)
    {
        Interlocked.Increment(ref _totalOperations);
        _operationCounts.AddOrUpdate(operationType, 1, (key, count) => count + 1);
        
        // 保持最近1000次执行时间用于计算平均值
        _executionTimes.Enqueue(durationMs);
        if (_executionTimes.Count > 1000)
        {
            _executionTimes.TryDequeue(out _);
        }
    }
}