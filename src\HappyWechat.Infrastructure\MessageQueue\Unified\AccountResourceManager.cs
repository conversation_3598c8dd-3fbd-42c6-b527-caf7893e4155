using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using StackExchange.Redis;
using Microsoft.Extensions.DependencyInjection;

namespace HappyWechat.Infrastructure.MessageQueue.Unified;

/// <summary>
/// 账号级别资源管理器 - 按账号隔离连接池和资源分配
/// </summary>
public interface IAccountResourceManager
{
    /// <summary>
    /// 获取账号专用的Redis数据库连接
    /// </summary>
    Task<IDatabase> GetAccountDatabaseAsync(Guid wxManagerId);
    
    /// <summary>
    /// 获取账号资源使用统计
    /// </summary>
    Task<AccountResourceStats> GetAccountResourceStatsAsync(Guid wxManagerId);
    
    /// <summary>
    /// 清理账号资源
    /// </summary>
    Task CleanupAccountResourcesAsync(Guid wxManagerId);
    
    /// <summary>
    /// 检查账号资源健康状态
    /// </summary>
    Task<bool> IsAccountResourceHealthyAsync(Guid wxManagerId);
}

public class AccountResourceManager : IAccountResourceManager
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<AccountResourceManager> _logger;
    private readonly ConcurrentDictionary<Guid, AccountResourceContext> _accountResources = new();
    private readonly SemaphoreSlim _resourceLock = new(1, 1);
    private readonly Timer _resourceCleanupTimer;

    public AccountResourceManager(
        IConnectionMultiplexer redis,
        ILogger<AccountResourceManager> logger)
    {
        _redis = redis;
        _logger = logger;
        
        // 每10分钟清理一次资源
        _resourceCleanupTimer = new Timer(PerformResourceCleanup, null, 
            TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
    }

    /// <summary>
    /// 获取账号专用的Redis数据库连接
    /// </summary>
    public async Task<IDatabase> GetAccountDatabaseAsync(Guid wxManagerId)
    {
        var context = _accountResources.GetOrAdd(wxManagerId, id => new AccountResourceContext
        {
            WxManagerId = id,
            CreatedAt = DateTime.UtcNow,
            LastAccessedAt = DateTime.UtcNow,
            DatabaseConnectionCount = 0
        });

        context.LastAccessedAt = DateTime.UtcNow;
        context.DatabaseConnectionCount++;

        // 使用不同的数据库索引来隔离账号数据
        var databaseIndex = GetAccountDatabaseIndex(wxManagerId);
        return _redis.GetDatabase(databaseIndex);
    }

    /// <summary>
    /// 获取账号资源使用统计
    /// </summary>
    public async Task<AccountResourceStats> GetAccountResourceStatsAsync(Guid wxManagerId)
    {
        if (!_accountResources.TryGetValue(wxManagerId, out var context))
        {
            return new AccountResourceStats { WxManagerId = wxManagerId };
        }

        return new AccountResourceStats
        {
            WxManagerId = wxManagerId,
            DatabaseConnectionCount = context.DatabaseConnectionCount,
            MemoryUsageBytes = await EstimateAccountMemoryUsageAsync(wxManagerId),
            CreatedAt = context.CreatedAt,
            LastAccessedAt = context.LastAccessedAt,
            IsHealthy = await IsAccountResourceHealthyAsync(wxManagerId)
        };
    }

    /// <summary>
    /// 清理账号资源
    /// </summary>
    public async Task CleanupAccountResourcesAsync(Guid wxManagerId)
    {
        if (_accountResources.TryRemove(wxManagerId, out var context))
        {
            _logger.LogInformation("🧹 清理账号资源 - WxManagerId: {WxManagerId}, 连接数: {ConnectionCount}",
                wxManagerId, context.DatabaseConnectionCount);
        }

        // 清理Redis中的账号相关数据
        var database = await GetAccountDatabaseAsync(wxManagerId);
        var server = _redis.GetServer(_redis.GetEndPoints().First());
        
        try
        {
            var accountKeys = server.Keys(pattern: $"*{wxManagerId}*", database: GetAccountDatabaseIndex(wxManagerId));
            var expiredKeys = new List<RedisKey>();
            
            foreach (var key in accountKeys)
            {
                var ttl = await database.KeyTimeToLiveAsync(key);
                if (!ttl.HasValue || ttl.Value.TotalMinutes < 5)
                {
                    expiredKeys.Add(key);
                }
            }

            if (expiredKeys.Any())
            {
                await database.KeyDeleteAsync(expiredKeys.ToArray());
                _logger.LogDebug("🗑️ 清理过期键 - WxManagerId: {WxManagerId}, 数量: {Count}",
                    wxManagerId, expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 清理账号Redis数据时发生异常 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 检查账号资源健康状态
    /// </summary>
    public async Task<bool> IsAccountResourceHealthyAsync(Guid wxManagerId)
    {
        try
        {
            var database = await GetAccountDatabaseAsync(wxManagerId);
            
            // 执行简单的ping测试
            var pingTime = await database.PingAsync();
            
            if (pingTime.TotalMilliseconds > 1000) // 1秒超时认为不健康
            {
                _logger.LogWarning("⚠️ 账号Redis连接延迟过高 - WxManagerId: {WxManagerId}, Ping: {PingTime}ms",
                    wxManagerId, pingTime.TotalMilliseconds);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 账号资源健康检查异常 - WxManagerId: {WxManagerId}", wxManagerId);
            return false;
        }
    }

    /// <summary>
    /// 获取账号的数据库索引（0-15，Redis默认支持16个数据库）
    /// </summary>
    private int GetAccountDatabaseIndex(Guid wxManagerId)
    {
        // 使用账号ID的哈希值来分配数据库索引，确保相同账号总是使用相同的数据库
        var hash = wxManagerId.GetHashCode();
        return Math.Abs(hash % 16); // Redis默认支持0-15数据库
    }

    /// <summary>
    /// 估算账号内存使用量
    /// </summary>
    private async Task<long> EstimateAccountMemoryUsageAsync(Guid wxManagerId)
    {
        try
        {
            var database = await GetAccountDatabaseAsync(wxManagerId);
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            
            var info = await server.InfoAsync("memory");
            var memoryInfo = info.ToString();
            
            // 简单解析Redis memory info
            if (memoryInfo.Contains("used_memory:"))
            {
                var lines = memoryInfo.Split('\n');
                var usedMemoryLine = lines.FirstOrDefault(line => line.StartsWith("used_memory:"));
                if (usedMemoryLine != null)
                {
                    var value = usedMemoryLine.Split(':')[1].Trim();
                    var totalMemory = ParseRedisMemoryValue(value);
                    return totalMemory / Math.Max(_accountResources.Count, 1); // 平均分配
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 估算账号内存使用量失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }

        return 0;
    }

    /// <summary>
    /// 解析Redis内存值
    /// </summary>
    private long ParseRedisMemoryValue(string memoryValue)
    {
        if (long.TryParse(memoryValue, out var value))
        {
            return value;
        }
        return 0;
    }

    /// <summary>
    /// 定期资源清理
    /// </summary>
    private async void PerformResourceCleanup(object? state)
    {
        try
        {
            await _resourceLock.WaitAsync(TimeSpan.FromSeconds(10));
            
            var cutoffTime = DateTime.UtcNow.AddHours(-2); // 2小时未访问的资源
            var staleResources = _accountResources.Values
                .Where(ctx => ctx.LastAccessedAt < cutoffTime)
                .ToList();

            foreach (var resource in staleResources)
            {
                await CleanupAccountResourcesAsync(resource.WxManagerId);
            }

            if (staleResources.Any())
            {
                _logger.LogInformation("🧹 定期资源清理完成 - 清理账号数: {Count}", staleResources.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 定期资源清理异常");
        }
        finally
        {
            _resourceLock.Release();
        }
    }

    public void Dispose()
    {
        _resourceCleanupTimer?.Dispose();
        _resourceLock?.Dispose();
    }
}

/// <summary>
/// 账号资源上下文
/// </summary>
public class AccountResourceContext
{
    public Guid WxManagerId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastAccessedAt { get; set; }
    public long DatabaseConnectionCount { get; set; }
}

/// <summary>
/// 账号资源统计
/// </summary>
public class AccountResourceStats
{
    public Guid WxManagerId { get; set; }
    public long DatabaseConnectionCount { get; set; }
    public long MemoryUsageBytes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastAccessedAt { get; set; }
    public bool IsHealthy { get; set; }
}