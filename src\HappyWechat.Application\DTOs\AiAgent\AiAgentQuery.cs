using HappyWechat.Application.Commons;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiAgent;

/// <summary>
/// AI智能体查询
/// </summary>
public class AiAgentQuery
{
    /// <summary>
    /// 分页查询参数
    /// </summary>
    public PageQuery PageQuery { get; set; } = new();
    
    /// <summary>
    /// 搜索关键词（名称或描述）
    /// </summary>
    public string? SearchKeyword { get; set; }
    
    /// <summary>
    /// AI提供商类型筛选
    /// </summary>
    public AiProviderType? ProviderType { get; set; }
    
    /// <summary>
    /// 启用状态筛选
    /// </summary>
    public bool? IsEnabled { get; set; }
}
