namespace HappyWechat.Application.Commons;

/// <summary>
/// API响应结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// 状态码
    /// </summary>
    public int Code { get; set; }
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }
    
    /// <summary>
    /// 数据
    /// </summary>
    public T? Data { get; set; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess => Code == 200;
    
    public ApiResponse()
    {
        Message = "";
    }
    
    public ApiResponse(int code, string message, T? data = default)
    {
        Code = code;
        Message = message;
        Data = data;
    }
    
    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static ApiResponse<T> Success(T? data = default, string message = "操作成功")
    {
        return new ApiResponse<T>(200, message, data);
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    public static ApiResponse<T> Failure(string message, int code = 400)
    {
        return new ApiResponse<T>(code, message);
    }
}

/// <summary>
/// 无数据的API响应结果
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    public ApiResponse() : base()
    {
    }
    
    public ApiResponse(int code, string message) : base(code, message)
    {
    }
    
    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static ApiResponse Success(string message = "操作成功")
    {
        return new ApiResponse(200, message);
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    public static new ApiResponse Failure(string message, int code = 400)
    {
        return new ApiResponse(code, message);
    }
}