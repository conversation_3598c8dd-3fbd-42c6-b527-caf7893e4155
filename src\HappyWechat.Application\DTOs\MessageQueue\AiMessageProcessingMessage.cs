namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// AI消息处理消息
/// </summary>
public class AiMessageProcessingMessage
{
    /// <summary>
    /// 微信账号ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;
    
    /// <summary>
    /// AI智能体ID
    /// </summary>
    public string AiAgentId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户消息
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 联系人/群组ID
    /// </summary>
    public string ContactId { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }
    
    /// <summary>
    /// 上下文信息
    /// </summary>
    public Dictionary<string, object>? Context { get; set; }
}