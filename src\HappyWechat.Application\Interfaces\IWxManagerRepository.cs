using HappyWechat.Domain.Entities;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 微信管理器仓储接口
/// </summary>
public interface IWxManagerRepository
{
    /// <summary>
    /// 根据ID获取微信管理器
    /// </summary>
    /// <param name="id">微信管理器ID</param>
    /// <returns>微信管理器实体，如果不存在则返回null</returns>
    Task<WxMangerEntity?> GetByIdAsync(Guid id);

    /// <summary>
    /// 根据WId获取微信管理器
    /// </summary>
    /// <param name="wId">微信WId</param>
    /// <returns>微信管理器实体，如果不存在则返回null</returns>
    Task<WxMangerEntity?> GetByWIdAsync(string wId);

    /// <summary>
    /// 获取所有已登录的微信管理器
    /// </summary>
    /// <returns>已登录的微信管理器列表</returns>
    Task<List<WxMangerEntity>> GetLoggedInManagersAsync();

    /// <summary>
    /// 获取指定用户的微信管理器列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>微信管理器列表</returns>
    Task<List<WxMangerEntity>> GetByUserIdAsync(Guid userId);

    /// <summary>
    /// 创建微信管理器
    /// </summary>
    /// <param name="entity">微信管理器实体</param>
    /// <returns>创建的微信管理器实体</returns>
    Task<WxMangerEntity> CreateAsync(WxMangerEntity entity);

    /// <summary>
    /// 更新微信管理器
    /// </summary>
    /// <param name="entity">微信管理器实体</param>
    /// <returns>更新的微信管理器实体</returns>
    Task<WxMangerEntity> UpdateAsync(WxMangerEntity entity);

    /// <summary>
    /// 删除微信管理器
    /// </summary>
    /// <param name="id">微信管理器ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(Guid id);

    /// <summary>
    /// 检查微信管理器是否存在
    /// </summary>
    /// <param name="id">微信管理器ID</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(Guid id);

    /// <summary>
    /// 根据WcId获取微信管理器
    /// </summary>
    /// <param name="wcId">微信WcId</param>
    /// <returns>微信管理器实体，如果不存在则返回null</returns>
    Task<WxMangerEntity?> GetByWcIdAsync(string wcId);

    /// <summary>
    /// 根据ID列表批量获取微信管理器
    /// </summary>
    /// <param name="ids">微信管理器ID列表</param>
    /// <returns>微信管理器列表</returns>
    Task<List<WxMangerEntity>> GetByIdsAsync(IEnumerable<Guid> ids);

    /// <summary>
    /// 根据WcId列表批量获取微信管理器
    /// </summary>
    /// <param name="wcIds">微信WcId列表</param>
    /// <returns>微信管理器列表</returns>
    Task<List<WxMangerEntity>> GetByWcIdsAsync(IEnumerable<string> wcIds);

    /// <summary>
    /// 获取所有活跃的微信管理器
    /// </summary>
    /// <returns>活跃的微信管理器列表</returns>
    Task<List<WxMangerEntity>> GetActiveManagersAsync();

    /// <summary>
    /// 获取活跃的微信管理器数量
    /// </summary>
    /// <returns>活跃管理器数量</returns>
    Task<int> GetActiveManagerCountAsync();

    /// <summary>
    /// 获取已映射的微信管理器数量（有WcId的）
    /// </summary>
    /// <returns>已映射管理器数量</returns>
    Task<int> GetMappedManagerCountAsync();
}
