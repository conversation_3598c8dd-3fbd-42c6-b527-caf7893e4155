using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 设置群组AI智能体命令 - 支持独立配置更新
/// </summary>
public class SetGroupAiAgentCommand
{
    /// <summary>
    /// 群组ID
    /// </summary>
    public Guid GroupId { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string? AiAgentName { get; set; }
    
    /// <summary>
    /// 是否启用AI自动回复 (可空，仅在需要更新时传值)
    /// </summary>
    public bool? IsEnabled { get; set; }
    

    
    /// <summary>
    /// 回复延迟时间（秒）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 5;
    
    /// <summary>
    /// 是否仅在@后回复 (可空，仅在需要更新时传值)
    /// </summary>
    public bool? OnlyReplyWhenMentioned { get; set; }
    
    /// <summary>
    /// 是否启用敏感词过滤
    /// </summary>
    public bool EnableSensitiveWordFilter { get; set; } = true;
    
    /// <summary>
    /// 自定义提示词
    /// </summary>
    public string? CustomPrompt { get; set; }
    
    /// <summary>
    /// 是否清除AI代理
    /// </summary>
    public bool ClearAiAgent { get; set; } = false;
}

/// <summary>
/// 批量设置群组AI智能体命令 - 批量独立配置更新
/// </summary>
public class BatchSetGroupAiAgentCommand
{
    /// <summary>
    /// 群组ID列表
    /// </summary>
    public List<Guid> GroupIds { get; set; } = new();
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string? AiAgentName { get; set; }
    
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool IsEnabled { get; set; }
    

    
    /// <summary>
    /// 回复延迟时间（秒）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 5;
    
    /// <summary>
    /// 是否仅在@后回复
    /// </summary>
    public bool OnlyReplyWhenMentioned { get; set; } = true;
    
    /// <summary>
    /// 是否启用敏感词过滤
    /// </summary>
    public bool EnableSensitiveWordFilter { get; set; } = true;

    /// <summary>
    /// 自定义提示词
    /// </summary>
    public string? CustomPrompt { get; set; }

    /// <summary>
    /// 是否异步处理
    /// </summary>
    public bool IsAsync { get; set; } = true;
}