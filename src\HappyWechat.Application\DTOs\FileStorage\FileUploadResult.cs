namespace HappyWechat.Application.DTOs.FileStorage;

/// <summary>
/// 文件上传结果
/// </summary>
public class FileUploadResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 文件路径
    /// </summary>
    public string? FilePath { get; set; }
    
    /// <summary>
    /// 文件URL
    /// </summary>
    public string? FileUrl { get; set; }
    
    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string? FileExtension { get; set; }
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string? MimeType { get; set; }
    
    /// <summary>
    /// 原始文件名
    /// </summary>
    public string? OriginalFileName { get; set; }
    
    /// <summary>
    /// 缩略图路径（如果生成了缩略图）
    /// </summary>
    public string? ThumbnailPath { get; set; }
    
    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; set; }
}
