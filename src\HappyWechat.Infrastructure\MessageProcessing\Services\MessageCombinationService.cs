using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Distributed;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Coordination;
using System.Text.Json;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息组合服务实现 - 仅用于群组"仅@后回复"模式的30秒消息组合
/// 🚀 新架构：集成MessageCoordinator，提供向后兼容的接口
/// </summary>
public class MessageCombinationService : IMessageCombinationService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly IMessageCoordinator _messageCoordinator;
    private readonly ILogger<MessageCombinationService> _logger;

    private const int COMBINATION_TIMEOUT_SECONDS = 30;
    private const string CACHE_PREFIX = "msg_combination";

    public MessageCombinationService(
        IDistributedCache distributedCache,
        IUnifiedMediaProcessor mediaProcessor,
        IMessageCoordinator messageCoordinator,
        ILogger<MessageCombinationService> logger)
    {
        _distributedCache = distributedCache;
        _mediaProcessor = mediaProcessor;
        _messageCoordinator = messageCoordinator;
        _logger = logger;
    }

    public async Task<MessageCombinationResult> ProcessMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messageType = callbackMessage.MessageType;
            var fromGroup = callbackMessage.Data?.FromGroup;
            var wxManagerId = callbackMessage.WxManagerId;

            _logger.LogInformation("[{ProcessingId}] 🔄 消息组合处理 - MessageType: {MessageType}, FromGroup: {FromGroup}",
                processingId, messageType, fromGroup);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromGroup) || string.IsNullOrEmpty(wxManagerId))
            {
                return MessageCombinationResult.ProcessImmediately(callbackMessage, "缺少群组或WxManagerId信息");
            }

            // 2. 根据消息类型决定处理策略
            return messageType switch
            {
                MessageTypeConstants.GROUP_TEXT => await ProcessTextMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeConstants.GROUP_IMAGE => await ProcessMediaMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeConstants.GROUP_FILE => await ProcessMediaMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeConstants.GROUP_VOICE => await ProcessVoiceMessageAsync(callbackMessage, processingId, cancellationToken),
                _ => MessageCombinationResult.ProcessImmediately(callbackMessage, "不支持的消息类型")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息组合处理异常", processingId);
            return MessageCombinationResult.ProcessImmediately(callbackMessage, $"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理文本消息 - 检查是否有等待组合的媒体消息
    /// </summary>
    private async Task<MessageCombinationResult> ProcessTextMessageAsync(
        WxCallbackMessageDto textMessage, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        var cacheKey = GetCacheKey(textMessage.WxManagerId!, textMessage.Data!.FromGroup!, textMessage.Data.FromUser!);
        
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📝 开始处理文本消息 - Group: {Group}, User: {User}", 
                processingId, textMessage.Data.FromGroup, textMessage.Data.FromUser);

            // 🔧 增强：使用原子操作检查和获取缓存
            var cachedMediaJson = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedMediaJson))
            {
                // 没有等待组合的媒体消息，直接处理文本
                _logger.LogDebug("[{ProcessingId}] 📝 单独文本消息，直接处理", processingId);
                return MessageCombinationResult.ProcessImmediately(textMessage, "单独文本消息");
            }

            // 🔧 增强：更健壮的JSON反序列化
            CachedMediaMessage? cachedMedia;
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                };
                cachedMedia = JsonSerializer.Deserialize<CachedMediaMessage>(cachedMediaJson, options);
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "[{ProcessingId}] ⚠️ 缓存数据反序列化失败，清理缓存", processingId);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(textMessage, "缓存数据损坏");
            }
            if (cachedMedia == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 缓存数据为空，清理缓存", processingId);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(textMessage, "缓存数据为空");
            }

            // 🔧 增强：更精确的过期检查
            var cacheAge = DateTime.UtcNow.Subtract(cachedMedia.CachedAt).TotalSeconds;
            if (cacheAge > COMBINATION_TIMEOUT_SECONDS)
            {
                _logger.LogInformation("[{ProcessingId}] ⏰ 媒体消息缓存已过期 - 缓存时间: {CacheAge}秒, 超时限制: {Timeout}秒", 
                    processingId, cacheAge, COMBINATION_TIMEOUT_SECONDS);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(textMessage, $"媒体消息缓存已过期({cacheAge:F1}秒)");
            }

            // 🔧 增强：组合消息，支持失败的媒体消息
            var combinedMessage = CreateEnhancedCombinedMessage(cachedMedia, textMessage, processingId);
            
            // 清理缓存
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            
            var logLevel = cachedMedia.MediaProcessingFailed ? LogLevel.Warning : LogLevel.Information;
            _logger.Log(logLevel, "[{ProcessingId}] 🔗 媒体消息组合成功 - MediaType: {MediaType}, MediaProcessingFailed: {MediaFailed}, TextLength: {TextLength}, CacheAge: {CacheAge}秒",
                processingId, cachedMedia.MediaMessage.MessageType, cachedMedia.MediaProcessingFailed, 
                textMessage.Data.Content?.Length ?? 0, cacheAge);

            return MessageCombinationResult.CombinedMessage(combinedMessage, cachedMedia.MediaMessage, 
                cachedMedia.MediaProcessingFailed ? "媒体处理失败+文本组合" : "媒体+文本组合");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 文本消息组合处理异常", processingId);
            
            // 🔧 增强：异常情况下尝试清理缓存
            try
            {
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            }
            catch (Exception cleanupEx)
            {
                _logger.LogWarning(cleanupEx, "[{ProcessingId}] ⚠️ 清理缓存失败", processingId);
            }
            
            return MessageCombinationResult.ProcessImmediately(textMessage, $"组合异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理媒体消息 - 先下载处理，然后缓存外链等待后续文本消息
    /// </summary>
    private async Task<MessageCombinationResult> ProcessMediaMessageAsync(
        WxCallbackMessageDto mediaMessage, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        var cacheKey = GetCacheKey(mediaMessage.WxManagerId!, mediaMessage.Data!.FromGroup!, mediaMessage.Data.FromUser!);
        
        try
        {
            // 先处理媒体消息，获取下载后的外链
            var mediaResult = await _mediaProcessor.ProcessMediaMessageAsync(mediaMessage, cancellationToken);

            // 🔧 修复：即使媒体处理失败，也要缓存消息以支持合并
            string mediaUrl = "无媒体链接";
            string mediaType = "unknown";
            string fileName = "未知文件";

            if (mediaResult.Success && !string.IsNullOrEmpty(mediaResult.PublicUrl))
            {
                mediaUrl = mediaResult.PublicUrl;
                mediaType = mediaResult.MediaType ?? "unknown";
                fileName = mediaResult.FileName ?? "未知文件";
                _logger.LogDebug("[{ProcessingId}] 媒体处理成功 - URL: {MediaUrl}", processingId, mediaUrl);
            }
            else
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 媒体处理失败，但继续缓存以支持消息合并 - Error: {Error}",
                    processingId, mediaResult.ErrorMessage);
            }

            // 创建缓存对象，包含媒体处理结果（即使失败也缓存）
            var cachedMedia = new CachedMediaMessage
            {
                MediaMessage = mediaMessage,
                MediaUrl = mediaUrl,
                MediaType = mediaType,
                FileName = fileName,
                CachedAt = DateTime.UtcNow,
                ProcessingId = processingId,
                MediaProcessingFailed = !mediaResult.Success // 🔧 新增：标记媒体处理是否失败
            };

            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(COMBINATION_TIMEOUT_SECONDS)
            };

            // 缓存媒体消息（包含外链）
            var jsonData = JsonSerializer.Serialize(cachedMedia);
            await _distributedCache.SetStringAsync(cacheKey, jsonData, cacheOptions, cancellationToken);

            _logger.LogInformation("[{ProcessingId}] 📎 媒体消息已下载并缓存 - MessageType: {MessageType}, URL: {MediaUrl}, ExpiresIn: {Seconds}秒, Group: {Group}, User: {User}",
                processingId, mediaMessage.MessageType, mediaResult.PublicUrl, COMBINATION_TIMEOUT_SECONDS,
                mediaMessage.Data?.FromGroup, mediaMessage.Data?.FromUser);

            return MessageCombinationResult.WaitForCombination("媒体消息已下载缓存，等待后续文本消息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 媒体消息处理缓存异常，回退到立即处理", processingId);
            return MessageCombinationResult.ProcessImmediately(mediaMessage, $"处理缓存异常，立即处理: {ex.Message}");
        }
    }

    /// <summary>
    /// 🔧 修复：处理语音消息 - 80004群聊语音应该和80001群聊文本一样处理，支持消息合并
    /// 注意：60004私聊语音与群组设置无关，但当前服务只处理群聊消息
    /// </summary>
    private async Task<MessageCombinationResult> ProcessVoiceMessageAsync(WxCallbackMessageDto voiceMessage, string processingId, CancellationToken cancellationToken)
    {
        var cacheKey = GetCacheKey(voiceMessage.WxManagerId!, voiceMessage.Data!.FromGroup!, voiceMessage.Data.FromUser!);
        
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🎵 开始处理语音消息 - Group: {Group}, User: {User}", 
                processingId, voiceMessage.Data.FromGroup, voiceMessage.Data.FromUser);

            // 🔧 修复：检查是否有等待组合的媒体消息（与文本消息类似的逻辑）
            var cachedMediaJson = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedMediaJson))
            {
                // 没有等待组合的媒体消息，直接处理语音
                _logger.LogDebug("[{ProcessingId}] 🎵 单独语音消息，直接处理", processingId);
                return MessageCombinationResult.ProcessImmediately(voiceMessage, "单独语音消息");
            }

            // 🔧 增强：更健壮的JSON反序列化（复用文本消息的逻辑）
            CachedMediaMessage? cachedMedia;
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                };
                cachedMedia = JsonSerializer.Deserialize<CachedMediaMessage>(cachedMediaJson, options);
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "[{ProcessingId}] ⚠️ 缓存数据反序列化失败，清理缓存", processingId);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(voiceMessage, "缓存数据损坏");
            }
            
            if (cachedMedia == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 缓存数据为空，清理缓存", processingId);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(voiceMessage, "缓存数据为空");
            }

            // 🔧 增强：更精确的过期检查
            var cacheAge = DateTime.UtcNow.Subtract(cachedMedia.CachedAt).TotalSeconds;
            if (cacheAge > COMBINATION_TIMEOUT_SECONDS)
            {
                _logger.LogInformation("[{ProcessingId}] ⏰ 媒体消息缓存已过期 - 缓存时间: {CacheAge}秒, 超时限制: {Timeout}秒", 
                    processingId, cacheAge, COMBINATION_TIMEOUT_SECONDS);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return MessageCombinationResult.ProcessImmediately(voiceMessage, $"媒体消息缓存已过期({cacheAge:F1}秒)");
            }

            // 🔧 增强：组合消息，支持失败的媒体消息
            var combinedMessage = CreateEnhancedCombinedMessageForVoice(cachedMedia, voiceMessage, processingId);
            
            // 清理缓存
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            
            var logLevel = cachedMedia.MediaProcessingFailed ? LogLevel.Warning : LogLevel.Information;
            _logger.Log(logLevel, "[{ProcessingId}] 🔗 媒体+语音消息组合成功 - MediaType: {MediaType}, MediaProcessingFailed: {MediaFailed}, CacheAge: {CacheAge}秒",
                processingId, cachedMedia.MediaMessage.MessageType, cachedMedia.MediaProcessingFailed, cacheAge);

            return MessageCombinationResult.CombinedMessage(combinedMessage, cachedMedia.MediaMessage, 
                cachedMedia.MediaProcessingFailed ? "媒体处理失败+语音组合" : "媒体+语音组合");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 语音消息组合处理异常", processingId);
            
            // 🔧 增强：异常情况下尝试清理缓存
            try
            {
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            }
            catch (Exception cleanupEx)
            {
                _logger.LogWarning(cleanupEx, "[{ProcessingId}] ⚠️ 清理缓存失败", processingId);
            }
            
            return MessageCombinationResult.ProcessImmediately(voiceMessage, $"组合异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建增强版组合消息 - 支持失败的媒体消息处理
    /// </summary>
    private WxCallbackMessageDto CreateEnhancedCombinedMessage(CachedMediaMessage cachedMedia, WxCallbackMessageDto textMessage, string processingId)
    {
        try
        {
            // 创建新的组合消息，基于文本消息但包含媒体信息
            var combinedMessage = new WxCallbackMessageDto
            {
                WcId = textMessage.WcId,
                MessageType = MessageTypeConstants.GROUP_TEXT, // 组合后作为文本消息处理
                WxManagerId = textMessage.WxManagerId,
                Data = new MessageData
                {
                    FromUser = textMessage.Data!.FromUser,
                    FromGroup = textMessage.Data.FromGroup,
                    ToUser = textMessage.Data.ToUser,
                    Content = textMessage.Data.Content,
                    Timestamp = textMessage.Data.Timestamp,
                    MsgId = textMessage.Data.MsgId,
                    NewMsgId = textMessage.Data.NewMsgId,
                    Self = textMessage.Data.Self,
                    WId = textMessage.Data.WId,
                    Atlist = textMessage.Data.Atlist ?? new List<string>()
                }
            };

            // 🔧 增强：根据媒体处理结果创建不同的组合内容
            string mediaInfo;
            string contentPrefix;
            
            if (cachedMedia.MediaProcessingFailed)
            {
                // 媒体处理失败时的处理
                var mediaTypeName = GetMediaTypeName(cachedMedia.MediaMessage.MessageType);
                mediaInfo = $"[媒体处理失败 - {mediaTypeName}]: {cachedMedia.FallbackMessage ?? "处理失败"}";
                contentPrefix = $"🚨 {mediaInfo}\n\n📝 用户同时发送了文本内容：";
                
                _logger.LogWarning("[{ProcessingId}] ⚠️ 创建失败媒体的组合消息 - MediaType: {MediaType}", 
                    processingId, cachedMedia.MediaMessage.MessageType);
            }
            else
            {
                // 媒体处理成功时的处理
                var mediaTypeName = GetMediaTypeName(cachedMedia.MediaMessage.MessageType);
                mediaInfo = $"[媒体文件 - {mediaTypeName}]: {cachedMedia.MediaUrl}";
                
                if (!string.IsNullOrEmpty(cachedMedia.FileName) && cachedMedia.FileName != "未知文件")
                {
                    mediaInfo += $" (文件名: {cachedMedia.FileName})";
                }
                
                contentPrefix = $"📁 {mediaInfo}\n\n📝 用户同时发送了文本内容：";
                
                _logger.LogDebug("[{ProcessingId}] ✅ 创建成功媒体的组合消息 - MediaUrl: {MediaUrl}", 
                    processingId, cachedMedia.MediaUrl);
            }
            
            // 组合最终内容
            var textContent = string.IsNullOrWhiteSpace(textMessage.Data.Content) ? "[无文本内容]" : textMessage.Data.Content;
            combinedMessage.Data.Content = $"{contentPrefix}\n{textContent}";

            _logger.LogDebug("[{ProcessingId}] 组合消息创建成功 - 内容长度: {Length}", 
                processingId, combinedMessage.Data.Content.Length);

            return combinedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❤️ 创建组合消息异常，使用简化版本", processingId);
            
            // 异常时使用简化版本
            return CreateSimpleCombinedMessage(cachedMedia, textMessage);
        }
    }
    
    /// <summary>
    /// 创建简化版组合消息 (作为备用方案)
    /// </summary>
    private WxCallbackMessageDto CreateSimpleCombinedMessage(CachedMediaMessage cachedMedia, WxCallbackMessageDto textMessage)
    {
        var combinedMessage = new WxCallbackMessageDto
        {
            WcId = textMessage.WcId,
            MessageType = MessageTypeConstants.GROUP_TEXT,
            WxManagerId = textMessage.WxManagerId,
            Data = new MessageData
            {
                FromUser = textMessage.Data!.FromUser,
                FromGroup = textMessage.Data.FromGroup,
                ToUser = textMessage.Data.ToUser,
                Content = $"[媒体文件]: {cachedMedia.MediaUrl}\n文本内容: {textMessage.Data.Content}",
                Timestamp = textMessage.Data.Timestamp,
                MsgId = textMessage.Data.MsgId,
                NewMsgId = textMessage.Data.NewMsgId,
                Self = textMessage.Data.Self,
                WId = textMessage.Data.WId,
                Atlist = textMessage.Data.Atlist ?? new List<string>()
            }
        };
        
        return combinedMessage;
    }

    /// <summary>
    /// 🔧 新增：为语音消息创建增强版组合消息
    /// </summary>
    private WxCallbackMessageDto CreateEnhancedCombinedMessageForVoice(CachedMediaMessage cachedMedia, WxCallbackMessageDto voiceMessage, string processingId)
    {
        try
        {
            // 创建新的组合消息，基于语音消息但包含媒体信息
            var combinedMessage = new WxCallbackMessageDto
            {
                WcId = voiceMessage.WcId,
                MessageType = MessageTypeConstants.GROUP_VOICE, // 保持为语音消息类型
                WxManagerId = voiceMessage.WxManagerId,
                Data = new MessageData
                {
                    FromUser = voiceMessage.Data!.FromUser,
                    FromGroup = voiceMessage.Data.FromGroup,
                    ToUser = voiceMessage.Data.ToUser,
                    Content = voiceMessage.Data.Content,
                    Timestamp = voiceMessage.Data.Timestamp,
                    MsgId = voiceMessage.Data.MsgId,
                    NewMsgId = voiceMessage.Data.NewMsgId,
                    Self = voiceMessage.Data.Self,
                    WId = voiceMessage.Data.WId,
                    Atlist = voiceMessage.Data.Atlist ?? new List<string>()
                }
            };

            // 🔧 增强：根据媒体处理结果创建不同的组合内容
            string mediaInfo;
            string contentPrefix;
            
            if (cachedMedia.MediaProcessingFailed)
            {
                // 媒体处理失败时的处理
                var mediaTypeName = GetMediaTypeName(cachedMedia.MediaMessage.MessageType);
                mediaInfo = $"[媒体处理失败 - {mediaTypeName}]: {cachedMedia.FallbackMessage ?? "处理失败"}";
                contentPrefix = $"🚨 {mediaInfo}\\n\\n🎵 用户同时发送了语音消息";
                
                _logger.LogWarning("[{ProcessingId}] ⚠️ 创建失败媒体+语音的组合消息 - MediaType: {MediaType}", 
                    processingId, cachedMedia.MediaMessage.MessageType);
            }
            else
            {
                // 媒体处理成功时的处理
                var mediaTypeName = GetMediaTypeName(cachedMedia.MediaMessage.MessageType);
                mediaInfo = $"[媒体文件 - {mediaTypeName}]: {cachedMedia.MediaUrl}";
                
                if (!string.IsNullOrEmpty(cachedMedia.FileName) && cachedMedia.FileName != "未知文件")
                {
                    mediaInfo += $" (文件名: {cachedMedia.FileName})";
                }
                
                contentPrefix = $"📁 {mediaInfo}\\n\\n🎵 用户同时发送了语音消息";
                
                _logger.LogDebug("[{ProcessingId}] ✅ 创建成功媒体+语音的组合消息 - MediaUrl: {MediaUrl}", 
                    processingId, cachedMedia.MediaUrl);
            }
            
            // 组合最终内容 - 对于语音消息，content通常是XML格式的语音信息
            var voiceContent = string.IsNullOrWhiteSpace(voiceMessage.Data.Content) ? "[语音消息]" : "[语音消息]";
            combinedMessage.Data.Content = $"{contentPrefix}\\n\\n原始语音数据:\\n{voiceMessage.Data.Content}";

            _logger.LogDebug("[{ProcessingId}] 语音组合消息创建成功 - 内容长度: {Length}", 
                processingId, combinedMessage.Data.Content.Length);

            return combinedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❤️ 创建语音组合消息异常，使用简化版本", processingId);
            
            // 异常时使用简化版本
            return CreateSimpleCombinedMessageForVoice(cachedMedia, voiceMessage);
        }
    }
    
    /// <summary>
    /// 🔧 新增：创建简化版语音组合消息 (作为备用方案)
    /// </summary>
    private WxCallbackMessageDto CreateSimpleCombinedMessageForVoice(CachedMediaMessage cachedMedia, WxCallbackMessageDto voiceMessage)
    {
        var combinedMessage = new WxCallbackMessageDto
        {
            WcId = voiceMessage.WcId,
            MessageType = MessageTypeConstants.GROUP_VOICE,
            WxManagerId = voiceMessage.WxManagerId,
            Data = new MessageData
            {
                FromUser = voiceMessage.Data!.FromUser,
                FromGroup = voiceMessage.Data.FromGroup,
                ToUser = voiceMessage.Data.ToUser,
                Content = $"[媒体文件]: {cachedMedia.MediaUrl}\\n语音消息: {voiceMessage.Data.Content}",
                Timestamp = voiceMessage.Data.Timestamp,
                MsgId = voiceMessage.Data.MsgId,
                NewMsgId = voiceMessage.Data.NewMsgId,
                Self = voiceMessage.Data.Self,
                WId = voiceMessage.Data.WId,
                Atlist = voiceMessage.Data.Atlist ?? new List<string>()
            }
        };
        
        return combinedMessage;
    }

    /// <summary>
    /// 获取媒体类型名称
    /// </summary>
    private string GetMediaTypeName(string? messageType)
    {
        return messageType switch
        {
            MessageTypeConstants.GROUP_IMAGE => "图片",
            MessageTypeConstants.GROUP_FILE => "文件",
            MessageTypeConstants.GROUP_VOICE => "语音",
            _ => "未知"
        };
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    private string GetCacheKey(string wxManagerId, string fromGroup, string fromUser)
    {
        return $"{CACHE_PREFIX}:{wxManagerId}:{fromGroup}:{fromUser}";
    }

    /// <summary>
    /// 检查是否过期
    /// </summary>
    private bool IsExpired(DateTime cachedAt)
    {
        return DateTime.UtcNow.Subtract(cachedAt).TotalSeconds > COMBINATION_TIMEOUT_SECONDS;
    }

    /// <summary>
    /// 清理过期的缓存消息
    /// </summary>
    public async Task CleanupExpiredMessagesAsync()
    {
        // Redis的过期机制会自动清理过期的键，这里预留接口用于其他清理逻辑
        _logger.LogDebug("🧹 消息组合缓存清理 - 依赖Redis自动过期机制");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 🚀 新架构：使用MessageCoordinator进行消息协调
    /// 提供向后兼容的接口包装
    /// </summary>
    public async Task<MessageCombinationResult> CoordinateMessageAsync(
        WxCallbackMessageDto message,
        string processingId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔗 使用新架构协调消息 - MessageType: {MessageType}",
                processingId, message.MessageType);

            var coordinationResult = await _messageCoordinator.CoordinateMessageAsync(message, processingId, cancellationToken);

            // 转换为旧的结果格式，保持向后兼容
            return coordinationResult.Action switch
            {
                CoordinationAction.ProcessImmediately => MessageCombinationResult.ProcessImmediately(
                    coordinationResult.CombinedMessage ?? message, coordinationResult.Reason),
                CoordinationAction.ProcessCombined => MessageCombinationResult.CombinedMessage(
                    coordinationResult.CombinedMessage!, new WxCallbackMessageDto(), coordinationResult.Reason),
                CoordinationAction.WaitForCombination => MessageCombinationResult.WaitForCombination(coordinationResult.Reason),
                CoordinationAction.Skip => MessageCombinationResult.Skip(coordinationResult.Reason),
                _ => MessageCombinationResult.Skip("未知协调结果")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息协调异常", processingId);
            return MessageCombinationResult.ProcessImmediately(message, $"协调异常，直接处理: {ex.Message}");
        }
    }
}

/// <summary>
/// 缓存的媒体消息 (增强版)
/// </summary>
internal class CachedMediaMessage
{
    public WxCallbackMessageDto MediaMessage { get; set; } = null!;
    public string MediaUrl { get; set; } = string.Empty;
    public string MediaType { get; set; } = string.Empty;
    public string? FileName { get; set; }
    public DateTime CachedAt { get; set; }
    public string ProcessingId { get; set; } = string.Empty;

    /// <summary>
    /// 标记媒体处理是否失败
    /// </summary>
    public bool MediaProcessingFailed { get; set; } = false;
    
    /// <summary>
    /// 🔧 新增：失败时的默认消息
    /// </summary>
    public string? FallbackMessage { get; set; }
    
    /// <summary>
    /// 🔧 新增：缓存版本，用于兼容性检查
    /// </summary>
    public int CacheVersion { get; set; } = 1;
}