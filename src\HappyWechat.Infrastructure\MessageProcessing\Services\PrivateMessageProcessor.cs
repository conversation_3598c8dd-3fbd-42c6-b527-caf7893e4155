using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.AI;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Constants;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.RiskControl.Services;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Handlers;
using HappyWechat.Infrastructure.MessageProcessing.Recovery;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;
using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 私聊消息处理器 - 实现完整的私聊消息处理流程
/// 包含AI配置检查、AI处理和消息发送
/// </summary>
public interface IPrivateMessageProcessor
{
    /// <summary>
    /// 处理私聊消息
    /// </summary>
    Task<PrivateMessageProcessResult> ProcessPrivateMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 私聊消息处理结果
/// </summary>
public class PrivateMessageProcessResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ProcessingDetails { get; set; }
    public bool ShouldContinue { get; set; } = true;
    public ContactAiConfigResult? ConfigResult { get; set; }

    public static PrivateMessageProcessResult CreateSuccess(string details, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = true,
            ShouldContinue = true,
            ProcessingDetails = details,
            ConfigResult = configResult
        };
    }

    public static PrivateMessageProcessResult CreateFailure(string errorMessage, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = false,
            ShouldContinue = false,
            ErrorMessage = errorMessage,
            ConfigResult = configResult
        };
    }

    public static PrivateMessageProcessResult CreateSkipped(string reason, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = true,
            ShouldContinue = false,
            ProcessingDetails = reason,
            ConfigResult = configResult
        };
    }
}

/// <summary>
/// 私聊消息处理器实现
/// </summary>
public class PrivateMessageProcessor : IPrivateMessageProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IContactAiConfigChecker _contactAiConfigChecker;
    private readonly IAiService _aiService;
    private readonly IWxMessageService _wxMessageService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly IAiMessageTemplateService _messageTemplateService;
    private readonly IUnifiedAiResponseProcessor _unifiedAiResponseProcessor;
    private readonly ICircuitBreakerService _circuitBreakerService;
    private readonly ILogger<PrivateMessageProcessor> _logger;

    public PrivateMessageProcessor(
        ApplicationDbContext dbContext,
        IContactAiConfigChecker contactAiConfigChecker,
        IAiService aiService,
        IWxMessageService wxMessageService,
        IServiceProvider serviceProvider,
        IUnifiedMediaProcessor mediaProcessor,
        IAiMessageTemplateService messageTemplateService,
        IUnifiedAiResponseProcessor unifiedAiResponseProcessor,
        ICircuitBreakerService circuitBreakerService,
        ILogger<PrivateMessageProcessor> logger)
    {
        _dbContext = dbContext;
        _contactAiConfigChecker = contactAiConfigChecker;
        _aiService = aiService;
        _wxMessageService = wxMessageService;
        _serviceProvider = serviceProvider;
        _mediaProcessor = mediaProcessor;
        _messageTemplateService = messageTemplateService;
        _unifiedAiResponseProcessor = unifiedAiResponseProcessor;
        _circuitBreakerService = circuitBreakerService;
        _logger = logger;
    }

    public async Task<PrivateMessageProcessResult> ProcessPrivateMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var fromUser = callbackMessage.Data?.FromUser;

            _logger.LogInformation("[{ProcessingId}] 👤 开始处理私聊消息 - FromUser: {FromUser}, MessageType: {MessageType}, MsgId: {MsgId}",
                processingId, fromUser, callbackMessage.MessageType, callbackMessage.Data?.MsgId);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromUser))
            {
                return PrivateMessageProcessResult.CreateFailure("FromUser为空");
            }

            // 2. 联系人AI配置检查
            var configResult = await _contactAiConfigChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);
            
            if (!configResult.ContactExists)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 联系人不存在，消息被丢弃 - FromUser: '{FromUser}'",
                    processingId, fromUser);
                return PrivateMessageProcessResult.CreateSkipped("联系人不存在", configResult);
            }

            if (!configResult.IsAiConfigured)
            {
                _logger.LogDebug("[{ProcessingId}] 🚫 联系人AI未配置，跳过处理 - FromUser: {FromUser}, ContactName: {ContactName}",
                    processingId, fromUser, configResult.ContactName);
                return PrivateMessageProcessResult.CreateSkipped("联系人AI未配置", configResult);
            }

            // 3. 媒体处理（如果需要）- 增强错误处理，确保媒体失败不阻塞文本处理
            MediaProcessingResult? mediaResult = null;
            if (ShouldProcessMedia(callbackMessage.MessageType!))
            {
                try
                {
                    mediaResult = await _mediaProcessor.ProcessMediaMessageAsync(callbackMessage, cancellationToken);
                    if (mediaResult != null && !mediaResult.Success)
                    {
                        _logger.LogWarning("[{ProcessingId}] ⚠️ 媒体处理失败，继续文本处理流程 - Error: {Error}",
                            processingId, mediaResult.ErrorMessage);
                        
                        // 🔧 增强：即使媒体处理失败，也创建一个包含友好提示的结果，继续AI处理
                        // 这样AI可以告知用户媒体处理失败，而不是整个对话被阻断
                    }
                }
                catch (Exception mediaEx)
                {
                    _logger.LogError(mediaEx, "[{ProcessingId}] ❌ 媒体处理异常，使用降级方案继续处理", processingId);
                    
                    // 🔧 新增：媒体处理异常时创建降级结果
                    mediaResult = MediaProcessingResult.CreateFailureWithFriendlyMessage(
                        $"媒体处理异常: {mediaEx.Message}",
                        "抱歉，处理您的媒体文件时遇到了问题。请尝试重新发送或使用文字描述您的需求。",
                        callbackMessage.MessageType!,
                        "media_error");
                }
            }

            // 4. AI处理，无需@逻辑（私聊模式）- 🚀 使用新架构的断路器保护
            var aiResult = await _circuitBreakerService.ExecuteAsync("private_ai_processing", async () =>
            {
                return await ProcessAiReplyAsync(callbackMessage, configResult, mediaResult, processingId, cancellationToken);
            }, cancellationToken);

            if (!aiResult.Success)
            {
                return PrivateMessageProcessResult.CreateFailure(aiResult.ErrorMessage!, configResult);
            }

            // 5. 消息发送
            var sendResult = await SendPrivateReplyAsync(callbackMessage, aiResult.AiResponse!, processingId, cancellationToken);
            
            if (sendResult.Success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 私聊消息处理完成 - FromUser: {FromUser}, ContactName: {ContactName}",
                    processingId, fromUser, configResult.ContactName);
                return PrivateMessageProcessResult.CreateSuccess("私聊消息处理完成", configResult);
            }
            else
            {
                return PrivateMessageProcessResult.CreateFailure($"消息发送失败: {sendResult.ErrorMessage}", configResult);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 私聊消息处理异常", processingId);
            return PrivateMessageProcessResult.CreateFailure($"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理AI回复
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage, AiMessageDto? AiResponse)> ProcessAiReplyAsync(
        WxCallbackMessageDto callbackMessage,
        ContactAiConfigResult configResult,
        MediaProcessingResult? mediaResult,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤖 开始AI处理 - ContactName: {ContactName}, AiAgentId: {AiAgentId}",
                processingId, configResult.ContactName, configResult.AiAgentId);

            // 获取WxManager信息用于AI传参
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId, w.WcId })
                .FirstOrDefaultAsync(cancellationToken);

            // 构建AI消息DTO，携带完整的消息参数（包括媒体信息）
            var processStartTime = DateTime.UtcNow;
            var aiMessageDto = new AiMessageDto
            {
                WxManagerId = wxManagerId,
                Content = callbackMessage.Data?.Content,
                MessageType = callbackMessage.MessageType,
                FromUser = callbackMessage.Data?.FromUser,
                ToUser = callbackMessage.Data?.ToUser,
                IsGroupMessage = false, // 私聊消息
                Context = new Dictionary<string, object>
                {
                    // 基础配置信息
                    ["ContactName"] = configResult.ContactName!,
                    ["AiAgentId"] = configResult.AiAgentId!,
                    ["ProcessingId"] = processingId,

                    // 微信消息完整参数 - 私聊模式
                    ["WId"] = wxManager?.WId ?? "",
                    ["WcId"] = callbackMessage.Data?.FromUser ?? "", // 私聊中WcId就是发送者ID
                    ["Content"] = callbackMessage.Data?.Content ?? "",
                    ["NickName"] = configResult.ContactName!, // 私聊联系人昵称
                    ["Timestamp"] = DateTime.UtcNow,
                    ["ProcessStartTime"] = processStartTime,
                    ["ProviderType"] = "待确定", // 将在AI服务中更新

                    // 媒体信息（如果有）
                    ["MediaUrls"] = mediaResult?.PublicUrl != null ? new List<string> { mediaResult.PublicUrl } : new List<string>(),
                    ["MediaDescriptions"] = mediaResult?.FileName != null ? new List<string> { $"文件: {mediaResult.FileName}" } : new List<string>(),
                    ["HasMedia"] = mediaResult?.Success == true,

                    // 原始回调数据
                    ["OriginalCallback"] = callbackMessage
                }
            };

            // 🔧 使用统一消息模板构建AI输入内容（支持降级处理）
            var mediaUrls = new List<string>();
            var mediaDescriptions = new List<string>();
            
            if (mediaResult?.Success == true)
            {
                if (!string.IsNullOrEmpty(mediaResult.PublicUrl) && mediaResult.PublicUrl != "fallback://media-processing-failed")
                {
                    mediaUrls.Add(mediaResult.PublicUrl);
                }
                
                if (!string.IsNullOrEmpty(mediaResult.FileName))
                {
                    var description = mediaResult.MediaType == "fallback" 
                        ? mediaResult.FallbackMessage ?? "媒体处理失败"
                        : $"文件: {mediaResult.FileName}";
                    mediaDescriptions.Add(description);
                }
            }

            // 构建消息上下文
            var messageContext = await _messageTemplateService.BuildMessageContextAsync(
                callbackMessage,
                wxManagerId,
                mediaDescriptions,
                mediaUrls);

            // 获取AI代理配置来确定提供商类型
            var aiAgent = await _dbContext.AiAgentEntities
                .Where(a => a.Id == configResult.AiAgentId!.Value)
                .Select(a => (AiProviderType?)a.ProviderType)
                .FirstOrDefaultAsync(cancellationToken);

            if (aiAgent == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ AI代理配置未找到 - AiAgentId: {AiAgentId}", processingId, configResult.AiAgentId.Value);
                return (false, "AI代理配置未找到", null);
            }

            var providerType = aiAgent.Value;

            // 使用模板服务构建标准化AI输入内容
            var aiInputContent = await _messageTemplateService.BuildMessageTemplateAsync(messageContext, providerType);

            // 🔧 优化日志记录，区分正常媒体和降级处理
            if (mediaResult?.Success == true)
            {
                if (mediaResult.MediaType == "fallback")
                {
                    _logger.LogWarning("[{ProcessingId}] 🔄 使用降级模板传递媒体失败信息给AI - Message: {FallbackMessage}",
                        processingId, mediaResult.FallbackMessage);
                }
                else
                {
                    _logger.LogInformation("[{ProcessingId}] 📎 使用统一模板传递媒体消息给AI - URL: {MediaUrl}, Type: {MediaType}",
                        processingId, mediaResult.PublicUrl, mediaResult.MediaType);
                }
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] 📝 使用统一模板传递文本消息给AI - Length: {Length}",
                    processingId, aiInputContent.Length);
            }

            // 调用AI服务获取原始响应
            var aiAgentService = _serviceProvider.GetRequiredService<IAiAgentService>();
            var aiResponse = await aiAgentService.ProcessMessageAsync(
                configResult.AiAgentId!.Value,
                aiInputContent,
                aiMessageDto);

            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ AI处理返回空结果 - ContactName: {ContactName}",
                    processingId, configResult.ContactName);
                return (false, "AI处理返回空结果", null);
            }

            // 使用AiResponseProcessor处理AI响应（包括markdown清理和图文混排解析）
            var responseProcessor = _serviceProvider.GetRequiredService<IAiResponseProcessor>();
            var processedResult = await responseProcessor.ProcessAiResponseAsync(
                aiResponse,
                Guid.Parse(callbackMessage.WxManagerId!),
                callbackMessage.Data?.WId ?? "",
                callbackMessage.Data?.FromUser ?? "");

            // 敏感词检测
            var sensitiveWordService = _serviceProvider.GetRequiredService<HappyWechat.Infrastructure.RiskControl.Services.ISensitiveWordDetectionService>();
            var sensitiveResult = await sensitiveWordService.ProcessAIReplyAsync(aiResponse);

            if (sensitiveResult.ShouldBlock)
            {
                _logger.LogWarning("[{ProcessingId}] AI回复被敏感词拦截 - ContactName: {ContactName}",
                    processingId, configResult.ContactName);
                return (false, "AI回复包含敏感词，已拦截", null);
            }

            // 如果敏感词被替换，使用替换后的内容
            var finalContent = sensitiveResult.ProcessedText;

            _logger.LogDebug("[{ProcessingId}] ✅ AI处理成功 - ContactName: {ContactName}, ResponseLength: {Length}",
                processingId, configResult.ContactName, finalContent.Length);

            // 🚀 新架构：使用流式AI响应处理器进行拆分和队列发送
            // 重用已有的wxManagerId变量
            var wId = callbackMessage.Data?.WId ?? "";
            var toUser = callbackMessage.Data?.FromUser ?? "";

            var result = await _unifiedAiResponseProcessor.ProcessAiResponseAsync(
                finalContent, wxManagerId, wId, toUser, cancellationToken: cancellationToken);

            _logger.LogInformation("[{ProcessingId}] 🚀 AI响应已提交到统一处理队列 - Success: {Success}, MessageCount: {MessageCount}",
                processingId, result.Success, result.MessageCount);

            // 构建响应DTO（保持向后兼容）
            var response = new AiMessageDto
            {
                WxManagerId = wxManagerId,
                Content = finalContent,
                MessageType = "text",
                FromUser = "AI",
                ToUser = toUser,
                IsGroupMessage = false,
                CreatedAt = DateTime.UtcNow
            };

            return (true, null, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ AI处理异常 - ContactName: {ContactName}",
                processingId, configResult.ContactName);
            return (false, $"AI处理异常: {ex.Message}", null);
        }
    }

    /// <summary>
    /// 发送私聊回复
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage)> SendPrivateReplyAsync(
        WxCallbackMessageDto callbackMessage,
        AiMessageDto aiResponse,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取WxManager信息
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId })
                .FirstOrDefaultAsync(cancellationToken);

            if (wxManager == null)
            {
                return (false, $"未找到WxManager - WxManagerId: {wxManagerId}");
            }

            // 构建发送命令
            var sendCommand = new WxSendTextMessageCommand
            {
                WId = wxManager.WId,
                WcId = callbackMessage.Data?.FromUser!, // 私聊回复给发送者
                Content = aiResponse.Content!
            };

            // 发送消息
            var result = await _wxMessageService.SendTextMessageAsync(Guid.Empty, sendCommand);

            if (result.IsSuccess)
            {
                _logger.LogInformation("[{ProcessingId}] 🚀 发送wx文本消息成功 - WId: {WId}, WcId: {WcId}, Content: {Content}",
                    processingId, sendCommand.WId, sendCommand.WcId, 
                    sendCommand.Content?.Substring(0, Math.Min(50, sendCommand.Content?.Length ?? 0)));
                return (true, null);
            }
            else
            {
                _logger.LogError("[{ProcessingId}] ❌ 发送wx文本消息失败 - Error: {Error}",
                    processingId, result.Message);
                return (false, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 发送私聊回复异常", processingId);
            return (false, $"发送异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 判断是否需要处理媒体文件
    /// </summary>
    private bool ShouldProcessMedia(string messageType)
    {
        return messageType switch
        {
            "60002" => true, // 私聊图片
            "60004" => true, // 私聊语音
            "60009" => true, // 私聊文件
            _ => false
        };
    }
}