using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.Commons;

/// <summary>
/// 消息验证结果
/// </summary>
public class MessageValidationResult
{
    /// <summary>
    /// 处理ID
    /// </summary>
    public string ProcessingId { get; set; } = string.Empty;

    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 是否应该丢弃消息
    /// </summary>
    public bool ShouldDiscard { get; set; }

    /// <summary>
    /// 消息丢弃原因
    /// </summary>
    public MessageDiscardReason DiscardReason { get; set; } = MessageDiscardReason.None;

    /// <summary>
    /// 是否被敏感词阻拦
    /// </summary>
    public bool BlockedBySensitiveWords { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理详情
    /// </summary>
    public string? ProcessingDetails { get; set; }

    /// <summary>
    /// 验证耗时（毫秒）
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static MessageValidationResult CreateSuccess(string processingId)
    {
        return new MessageValidationResult
        {
            ProcessingId = processingId,
            Success = true,
            ShouldDiscard = false,
            DiscardReason = MessageDiscardReason.None
        };
    }

    /// <summary>
    /// 创建丢弃结果
    /// </summary>
    public static MessageValidationResult CreateDiscard(string processingId, MessageDiscardReason reason, string? errorMessage = null)
    {
        return new MessageValidationResult
        {
            ProcessingId = processingId,
            Success = false,
            ShouldDiscard = true,
            DiscardReason = reason,
            ErrorMessage = errorMessage ?? reason.ToString()
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static MessageValidationResult CreateFailure(string processingId, string errorMessage)
    {
        return new MessageValidationResult
        {
            ProcessingId = processingId,
            Success = false,
            ShouldDiscard = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 通用验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 详细信息
    /// </summary>
    public string? Details { get; set; }
}