namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 微信消息处理消息
/// </summary>
public class WxMessageProcessingMessage
{
    /// <summary>
    /// 微信账号ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者
    /// </summary>
    public string FromUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 接收者
    /// </summary>
    public string ToUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }
    
    /// <summary>
    /// 原始回调数据
    /// </summary>
    public string? RawData { get; set; }
}