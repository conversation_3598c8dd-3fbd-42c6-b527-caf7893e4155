using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace HappyWechat.Infrastructure.MessageQueue.Streaming;

/// <summary>
/// 流式消息处理架构 - 解决阻塞和消息丢失问题
/// 采用生产者-消费者模式，完全异步处理，避免任何阻塞
/// </summary>
public class StreamingMessageArchitecture : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<StreamingMessageArchitecture> _logger;
    
    // 核心通道：高性能无锁队列
    private readonly Channel<StreamingMessage> _fastMessageChannel;   // 快速消息通道（文本消息）
    private readonly Channel<StreamingMessage> _slowMessageChannel;   // 慢速消息通道（媒体消息）
    private readonly Channel<StreamingMessage> _priorityChannel;      // 优先级通道（@消息、紧急消息）
    
    // 账号级别的处理统计
    private readonly ConcurrentDictionary<Guid, AccountStreamingStats> _accountStats = new();
    
    // 背压控制
    private readonly SemaphoreSlim _globalRateLimit;
    private readonly ConcurrentDictionary<Guid, SemaphoreSlim> _accountRateLimits = new();

    public StreamingMessageArchitecture(
        IServiceProvider serviceProvider,
        ILogger<StreamingMessageArchitecture> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        // 🔧 优化：增强通道容量配置 - 支持4账号高并发处理
        var fastChannelOptions = new BoundedChannelOptions(2000) // 2千消息缓冲（支持4账号×30条/秒×16秒缓冲）
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,  // 多消费者
            SingleWriter = false,  // 多生产者
            AllowSynchronousContinuations = false
        };

        var slowChannelOptions = new BoundedChannelOptions(5000) // 5千消息缓冲（媒体消息处理更慢）
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        };

        _fastMessageChannel = Channel.CreateBounded<StreamingMessage>(fastChannelOptions);
        _slowMessageChannel = Channel.CreateBounded<StreamingMessage>(slowChannelOptions);
        _priorityChannel = Channel.CreateBounded<StreamingMessage>(new BoundedChannelOptions(200) // 增加优先级通道容量
        {
            FullMode = BoundedChannelFullMode.DropOldest, // 优先级通道：丢弃旧消息
            SingleReader = false,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        });
        
        // 🔧 优化：提升全局速率限制，支持4账号高并发（每账号30条/秒×4账号×2倍缓冲）
        _globalRateLimit = new SemaphoreSlim(2000, 2000);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🚀 流式消息处理架构启动 - 三通道异步流水线模式");

        // 启动多个并行处理管道
        var processingTasks = new[]
        {
            // 优先级通道：4个并行处理器，最高优先级
            Task.Run(() => ProcessChannelAsync(_priorityChannel.Reader, "Priority", 4, stoppingToken)),
            
            // 快速通道：8个并行处理器，处理文本消息
            Task.Run(() => ProcessChannelAsync(_fastMessageChannel.Reader, "Fast", 8, stoppingToken)),
            
            // 慢速通道：16个并行处理器，处理媒体消息（更多并发）
            Task.Run(() => ProcessChannelAsync(_slowMessageChannel.Reader, "Slow", 16, stoppingToken)),
            
            // 背压监控和统计
            Task.Run(() => MonitorBackpressureAsync(stoppingToken)),
            
            // 账号健康监控
            Task.Run(() => MonitorAccountHealthAsync(stoppingToken))
        };

        try
        {
            await Task.WhenAll(processingTasks);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("流式消息处理架构正在停止");
        }
        
        await ShutdownGracefullyAsync();
    }

    /// <summary>
    /// 入队消息 - 核心入口，永不阻塞
    /// </summary>
    public async Task<bool> EnqueueMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            var streamingMessage = new StreamingMessage
            {
                Id = Guid.NewGuid().ToString(),
                CallbackMessage = callbackMessage,
                ProcessingId = processingId,
                WxManagerId = Guid.Parse(callbackMessage.WxManagerId),
                EnqueuedAt = DateTime.UtcNow,
                MessageType = DetermineMessageType(callbackMessage)
            };

            // 基于消息类型和优先级路由到不同通道
            var channel = SelectChannel(streamingMessage);
            
            // 非阻塞入队，如果通道满了立即返回失败而不是等待
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(10));
            if (await channel.Writer.WaitToWriteAsync(cts.Token))
            {
                if (channel.Writer.TryWrite(streamingMessage))
                {
                    // 更新统计
                    UpdateAccountStats(streamingMessage.WxManagerId, AccountStatsType.Enqueued);
                    
                    _logger.LogInformation("📥 流式架构消息入队 - Channel: {Channel}, ProcessingId: {ProcessingId}, MessageType: {MessageType}, StreamingType: {StreamingType}",
                        GetChannelName(channel), processingId, callbackMessage.MessageType, streamingMessage.MessageType);
                    return true;
                }
            }
            
            // 如果入队失败，记录但不阻塞
            _logger.LogWarning("⚠️ 消息入队失败（通道满）- ProcessingId: {ProcessingId}, 启用降级处理", processingId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 消息入队异常 - ProcessingId: {ProcessingId}", processingId);
            return false;
        }
    }

    /// <summary>
    /// 处理特定通道的消息
    /// </summary>
    private async Task ProcessChannelAsync(
        ChannelReader<StreamingMessage> reader,
        string channelName,
        int concurrency,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 启动 {ChannelName} 通道处理器 - 并发度: {Concurrency}", channelName, concurrency);

        // 创建指定数量的并行处理任务
        var processingTasks = Enumerable.Range(0, concurrency)
            .Select(i => ProcessSingleMessageAsync(reader, $"{channelName}-{i}", cancellationToken))
            .ToArray();

        try
        {
            await Task.WhenAll(processingTasks);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("{ChannelName} 通道处理器已停止", channelName);
        }
    }

    /// <summary>
    /// 处理单个消息 - 真正的异步处理
    /// </summary>
    private async Task ProcessSingleMessageAsync(
        ChannelReader<StreamingMessage> reader,
        string processorName,
        CancellationToken cancellationToken)
    {
        await foreach (var message in reader.ReadAllAsync(cancellationToken))
        {
            var processingStart = DateTime.UtcNow;
            
            try
            {
                // 全局速率限制
                await _globalRateLimit.WaitAsync(cancellationToken);
                
                // 账号级别速率限制
                var accountLimit = GetAccountRateLimit(message.WxManagerId);
                await accountLimit.WaitAsync(100, cancellationToken); // 100ms超时

                using var scope = _serviceProvider.CreateScope();
                
                // 根据消息类型选择处理策略
                var success = await ProcessMessageByTypeAsync(scope, message, cancellationToken);
                
                var processingTime = DateTime.UtcNow - processingStart;
                
                // 更新统计
                if (success)
                {
                    UpdateAccountStats(message.WxManagerId, AccountStatsType.Processed);
                    _logger.LogInformation("✅ 流式架构处理完成 - Processor: {Processor}, ProcessingId: {ProcessingId}, MessageType: {MessageType}, Duration: {Duration}ms",
                        processorName, message.ProcessingId, message.MessageType, processingTime.TotalMilliseconds);
                }
                else
                {
                    UpdateAccountStats(message.WxManagerId, AccountStatsType.Failed);
                    _logger.LogWarning("❌ 流式架构处理失败 - Processor: {Processor}, ProcessingId: {ProcessingId}, MessageType: {MessageType}",
                        processorName, message.ProcessingId, message.MessageType);
                }
                
                // 释放速率限制
                accountLimit.Release();
                _globalRateLimit.Release();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 消息处理异常 - Processor: {Processor}, ProcessingId: {ProcessingId}",
                    processorName, message.ProcessingId);
                
                UpdateAccountStats(message.WxManagerId, AccountStatsType.Failed);
                
                // 确保释放信号量
                try { _globalRateLimit.Release(); } catch { }
                try { GetAccountRateLimit(message.WxManagerId).Release(); } catch { }
            }
        }
    }

    /// <summary>
    /// 根据消息类型处理消息
    /// </summary>
    private async Task<bool> ProcessMessageByTypeAsync(
        IServiceScope scope,
        StreamingMessage message,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 流式架构开始分派消息 - ProcessingId: {ProcessingId}, StreamingType: {StreamingType}, OriginalType: {OriginalType}",
            message.ProcessingId, message.MessageType, message.CallbackMessage.MessageType);
            
        switch (message.MessageType)
        {
            case StreamingMessageType.TextFast:
                return await ProcessTextMessageAsync(scope, message, cancellationToken);

            case StreamingMessageType.MediaSlow:
                return await ProcessMediaMessageAsync(scope, message, cancellationToken);

            case StreamingMessageType.PriorityAt:
                return await ProcessPriorityMessageAsync(scope, message, cancellationToken);

            default:
                _logger.LogWarning("⚠️ 流式架构未知消息类型 - Type: {Type}, ProcessingId: {ProcessingId}",
                    message.MessageType, message.ProcessingId);
                return false;
        }
    }

    /// <summary>
    /// 处理文本消息 - 快速通道
    /// </summary>
    private async Task<bool> ProcessTextMessageAsync(
        IServiceScope scope,
        StreamingMessage message,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🚀 流式架构开始处理文本消息 - ProcessingId: {ProcessingId}, MessageType: {MessageType}",
                message.ProcessingId, message.MessageType);
                
            var processor = scope.ServiceProvider.GetRequiredService<IUnifiedMessageProcessor>();
            var result = await processor.ProcessMessageAsync(message.CallbackMessage, message.ProcessingId, cancellationToken);

            _logger.LogInformation("📋 流式架构文本消息处理结果 - ProcessingId: {ProcessingId}, Success: {Success}",
                message.ProcessingId, result.Success);

            return result.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 文本消息处理异常 - ProcessingId: {ProcessingId}", message.ProcessingId);
            return false;
        }
    }

    /// <summary>
    /// 处理媒体消息 - 慢速通道，通过统一消息处理器
    /// </summary>
    private async Task<bool> ProcessMediaMessageAsync(
        IServiceScope scope,
        StreamingMessage message,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🚀 流式架构开始处理媒体消息 - ProcessingId: {ProcessingId}, MessageType: {MessageType}",
                message.ProcessingId, message.MessageType);
                
            // 🔧 修复：使用统一消息处理器，避免重复处理逻辑
            var processor = scope.ServiceProvider.GetRequiredService<IUnifiedMessageProcessor>();
            var result = await processor.ProcessMessageAsync(message.CallbackMessage, message.ProcessingId, cancellationToken);
            
            _logger.LogInformation("📋 流式架构媒体消息处理结果 - ProcessingId: {ProcessingId}, Success: {Success}",
                message.ProcessingId, result.Success);
                
            return result.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 媒体消息处理异常 - ProcessingId: {ProcessingId}", message.ProcessingId);
            return false;
        }
    }

    /// <summary>
    /// 处理优先级消息 - 最高优先级
    /// </summary>
    private async Task<bool> ProcessPriorityMessageAsync(
        IServiceScope scope,
        StreamingMessage message,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🚀 流式架构开始处理优先级消息 - ProcessingId: {ProcessingId}, MessageType: {MessageType}",
                message.ProcessingId, message.MessageType);
                
            // 优先级消息立即处理，不受速率限制
            var processor = scope.ServiceProvider.GetRequiredService<IUnifiedMessageProcessor>();
            var result = await processor.ProcessMessageAsync(message.CallbackMessage, message.ProcessingId, cancellationToken);
            
            _logger.LogInformation("⚡ 流式架构优先级消息处理完成 - ProcessingId: {ProcessingId}, Success: {Success}",
                message.ProcessingId, result.Success);
                
            return result.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 优先级消息处理异常 - ProcessingId: {ProcessingId}", message.ProcessingId);
            return false;
        }
    }

    #region 辅助方法

    private StreamingMessageType DetermineMessageType(WxCallbackMessageDto callbackMessage)
    {
        // 优先级消息：@消息
        if (callbackMessage.MessageType == "80001")
            return StreamingMessageType.PriorityAt;

        // 文本消息：快速通道
        if (callbackMessage.MessageType == "60001")
            return StreamingMessageType.TextFast;

        // 媒体消息：慢速通道
        if (callbackMessage.MessageType is "60002" or "60004" or "60009" or "60010")
            return StreamingMessageType.MediaSlow;

        // 默认快速通道
        return StreamingMessageType.TextFast;
    }

    private Channel<StreamingMessage> SelectChannel(StreamingMessage message)
    {
        return message.MessageType switch
        {
            StreamingMessageType.PriorityAt => _priorityChannel,
            StreamingMessageType.TextFast => _fastMessageChannel,
            StreamingMessageType.MediaSlow => _slowMessageChannel,
            _ => _fastMessageChannel
        };
    }

    private string GetChannelName(Channel<StreamingMessage> channel)
    {
        if (channel == _priorityChannel) return "Priority";
        if (channel == _fastMessageChannel) return "Fast";
        if (channel == _slowMessageChannel) return "Slow";
        return "Unknown";
    }

    private SemaphoreSlim GetAccountRateLimit(Guid wxManagerId)
    {
        // 🔧 优化：提升账号级别并发限制，支持高频消息处理
        return _accountRateLimits.GetOrAdd(wxManagerId, _ => new SemaphoreSlim(100, 100)); // 每账号100并发
    }

    private void UpdateAccountStats(Guid wxManagerId, AccountStatsType statsType)
    {
        var stats = _accountStats.GetOrAdd(wxManagerId, _ => new AccountStreamingStats { WxManagerId = wxManagerId });
        
        switch (statsType)
        {
            case AccountStatsType.Enqueued:
                stats.EnqueuedCount++;
                break;
            case AccountStatsType.Processed:
                stats.ProcessedCount++;
                break;
            case AccountStatsType.Failed:
                stats.FailedCount++;
                break;
        }
        
        stats.LastUpdatedAt = DateTime.UtcNow;
    }

    private async Task MonitorBackpressureAsync(CancellationToken cancellationToken)
    {
        var lastReportTime = DateTime.UtcNow;
        
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var fastCount = _fastMessageChannel.Reader.CanCount ? _fastMessageChannel.Reader.Count : -1;
                var slowCount = _slowMessageChannel.Reader.CanCount ? _slowMessageChannel.Reader.Count : -1;
                var priorityCount = _priorityChannel.Reader.CanCount ? _priorityChannel.Reader.Count : -1;

                // 🔧 优化：调整背压监控阈值，适应更高的并发量
                if (fastCount > 1500 || slowCount > 3500 || priorityCount > 150)
                {
                    _logger.LogWarning("⚠️ 流式架构背压警告 - Fast: {Fast}, Slow: {Slow}, Priority: {Priority}",
                        fastCount, slowCount, priorityCount);
                }

                // 添加严重背压警告
                if (fastCount > 1800 || slowCount > 4500 || priorityCount > 180)
                {
                    _logger.LogError("🚨 流式架构严重背压 - Fast: {Fast}, Slow: {Slow}, Priority: {Priority}",
                        fastCount, slowCount, priorityCount);
                }
                
                // 每5分钟报告一次正常状态
                if (DateTime.UtcNow - lastReportTime > TimeSpan.FromMinutes(5))
                {
                    var totalStats = _accountStats.Values.Aggregate(
                        new { Processed = 0L, Failed = 0L, Enqueued = 0L },
                        (acc, stats) => new { 
                            Processed = acc.Processed + stats.ProcessedCount,
                            Failed = acc.Failed + stats.FailedCount,
                            Enqueued = acc.Enqueued + stats.EnqueuedCount
                        });
                        
                    _logger.LogInformation("📊 流式架构状态报告 - Queues: Fast({Fast}), Slow({Slow}), Priority({Priority}) | Stats: Processed({Processed}), Failed({Failed}), Enqueued({Enqueued})",
                        fastCount, slowCount, priorityCount, totalStats.Processed, totalStats.Failed, totalStats.Enqueued);
                    lastReportTime = DateTime.UtcNow;
                }

                await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 背压监控异常");
            }
        }
    }

    private async Task MonitorAccountHealthAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                foreach (var stats in _accountStats.Values)
                {
                    var successRate = stats.ProcessedCount > 0 
                        ? (double)stats.ProcessedCount / (stats.ProcessedCount + stats.FailedCount) * 100 
                        : 0;

                    if (successRate < 90 && stats.ProcessedCount > 10)
                    {
                        _logger.LogWarning("⚠️ 账号处理成功率低 - WxManagerId: {WxManagerId}, Success: {Rate:F1}%",
                            stats.WxManagerId, successRate);
                    }
                }

                await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 账号健康监控异常");
            }
        }
    }

    private async Task ShutdownGracefullyAsync()
    {
        _logger.LogInformation("🔄 开始优雅关闭流式消息处理架构...");

        // 关闭写入器，但允许现有消息处理完成
        _fastMessageChannel.Writer.Complete();
        _slowMessageChannel.Writer.Complete();
        _priorityChannel.Writer.Complete();

        // 等待所有消息处理完成（最多30秒）
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        
        try
        {
            await _fastMessageChannel.Reader.Completion;
            await _slowMessageChannel.Reader.Completion;
            await _priorityChannel.Reader.Completion;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("⚠️ 优雅关闭超时，强制关闭");
        }

        // 释放资源
        _globalRateLimit.Dispose();
        foreach (var semaphore in _accountRateLimits.Values)
        {
            semaphore.Dispose();
        }

        _logger.LogInformation("✅ 流式消息处理架构已完全关闭");
    }

    #endregion
}

/// <summary>
/// 流式消息
/// </summary>
public class StreamingMessage
{
    public string Id { get; set; } = string.Empty;
    public WxCallbackMessageDto CallbackMessage { get; set; } = null!;
    public string ProcessingId { get; set; } = string.Empty;
    public Guid WxManagerId { get; set; }
    public DateTime EnqueuedAt { get; set; }
    public StreamingMessageType MessageType { get; set; }
}

/// <summary>
/// 流式消息类型
/// </summary>
public enum StreamingMessageType
{
    TextFast,      // 文本消息 - 快速通道
    MediaSlow,     // 媒体消息 - 慢速通道
    PriorityAt     // 优先级消息 - @消息等
}

/// <summary>
/// 账号流式统计
/// </summary>
public class AccountStreamingStats
{
    public Guid WxManagerId { get; set; }
    public long EnqueuedCount { get; set; }
    public long ProcessedCount { get; set; }
    public long FailedCount { get; set; }
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 账号统计类型
/// </summary>
public enum AccountStatsType
{
    Enqueued,
    Processed,
    Failed
}