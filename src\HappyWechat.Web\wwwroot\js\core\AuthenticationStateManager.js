/**
 * 统一认证状态管理器
 * 作为唯一的认证状态源，提供原子性的状态更新和通知机制
 * 
 * 功能特性：
 * - 单例模式，确保状态一致性
 * - 多层存储策略（内存 -> LocalStorage -> Cookie -> Blazor）
 * - 原子性状态更新
 * - 事件驱动的状态同步
 * - 线程安全的状态管理
 */
class AuthenticationStateManager {
    constructor() {
        if (AuthenticationStateManager.instance) {
            return AuthenticationStateManager.instance;
        }

        // 认证状态
        this.state = {
            sessionId: null,
            userInfo: null,
            isAuthenticated: false,
            lastUpdated: null,
            source: null
        };

        // 存储配置
        this.storageConfig = {
            sessionKey: 'hw_session_id',
            userInfoKey: 'hw_user_info',
            cookieName: 'HW_SessionId',
            cookieOptions: {
                path: '/',
                secure: window.location.protocol === 'https:',
                sameSite: 'Lax'
            }
        };

        // 状态锁，防止并发更新
        this.updateLock = false;
        this.pendingUpdates = [];

        // 初始化
        this.init();
        
        AuthenticationStateManager.instance = this;
    }

    /**
     * 初始化认证状态管理器
     */
    async init() {
        try {
            console.log('🔐 初始化统一认证状态管理器...');

            // 确保事件总线可用
            await this.ensureEventBusReady();

            // 恢复认证状态
            await this.restoreAuthenticationState();

            // 设置存储监听器
            this.setupStorageListeners();

            // 设置页面可见性监听器
            this.setupVisibilityListener();

            console.log('✅ 统一认证状态管理器初始化完成');
        } catch (error) {
            console.error('❌ 认证状态管理器初始化失败:', error);
        }
    }

    /**
     * 确保事件总线就绪
     */
    async ensureEventBusReady() {
        let attempts = 0;
        const maxAttempts = 50;

        while (!window.authEventBus && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.authEventBus) {
            throw new Error('事件总线未就绪');
        }
    }

    /**
     * 恢复认证状态 - 按优先级从多个存储源恢复
     */
    async restoreAuthenticationState() {
        try {
            console.log('🔄 恢复认证状态...');

            // Priority 1: 内存状态（如果已有）
            if (this.state.sessionId) {
                console.log('✅ 从内存恢复认证状态');
                return;
            }

            // Priority 2: Blazor AuthHelper（最权威）
            const blazorSessionId = await this.getSessionIdFromBlazor();
            if (blazorSessionId) {
                await this.updateStateInternal(blazorSessionId, null, 'blazor');
                console.log('✅ 从Blazor恢复认证状态');
                return;
            }

            // Priority 3: LocalStorage
            const localSessionId = this.getSessionIdFromLocalStorage();
            if (localSessionId) {
                const userInfo = this.getUserInfoFromLocalStorage();
                await this.updateStateInternal(localSessionId, userInfo, 'localStorage');
                console.log('✅ 从LocalStorage恢复认证状态');
                return;
            }

            // Priority 4: Cookie
            const cookieSessionId = this.getSessionIdFromCookie();
            if (cookieSessionId) {
                await this.updateStateInternal(cookieSessionId, null, 'cookie');
                console.log('✅ 从Cookie恢复认证状态');
                return;
            }

            console.log('ℹ️ 未找到有效的认证状态');
        } catch (error) {
            console.error('❌ 恢复认证状态失败:', error);
        }
    }

    /**
     * 获取当前SessionId - 统一接口
     * @returns {Promise<string|null>} SessionId
     */
    async getSessionId() {
        // 如果有内存缓存，直接返回
        if (this.state.sessionId) {
            return this.state.sessionId;
        }

        // 尝试恢复状态
        await this.restoreAuthenticationState();
        
        return this.state.sessionId;
    }

    /**
     * 获取用户信息
     * @returns {Object|null} 用户信息
     */
    getUserInfo() {
        return this.state.userInfo;
    }

    /**
     * 检查是否已认证
     * @returns {boolean} 是否已认证
     */
    isAuthenticated() {
        return this.state.isAuthenticated && !!this.state.sessionId;
    }

    /**
     * 更新认证状态 - 原子性操作
     * @param {string|null} sessionId 会话ID
     * @param {Object|null} userInfo 用户信息
     * @param {string} source 更新源
     * @returns {Promise<boolean>} 更新是否成功
     */
    async updateAuthenticationState(sessionId, userInfo = null, source = 'external') {
        // 如果正在更新，加入队列
        if (this.updateLock) {
            return new Promise((resolve) => {
                this.pendingUpdates.push({ sessionId, userInfo, source, resolve });
            });
        }

        return await this.executeUpdate(sessionId, userInfo, source);
    }

    /**
     * 执行状态更新
     * @param {string|null} sessionId 会话ID
     * @param {Object|null} userInfo 用户信息
     * @param {string} source 更新源
     * @returns {Promise<boolean>} 更新是否成功
     */
    async executeUpdate(sessionId, userInfo, source) {
        this.updateLock = true;

        try {
            const success = await this.updateStateInternal(sessionId, userInfo, source);
            
            if (success) {
                // 发布认证状态变更事件
                this.publishAuthStateChanged(sessionId, userInfo, source);
            }

            return success;
        } catch (error) {
            console.error('❌ 更新认证状态失败:', error);
            return false;
        } finally {
            this.updateLock = false;
            
            // 处理待处理的更新
            this.processPendingUpdates();
        }
    }

    /**
     * 内部状态更新逻辑
     * @param {string|null} sessionId 会话ID
     * @param {Object|null} userInfo 用户信息
     * @param {string} source 更新源
     * @returns {Promise<boolean>} 更新是否成功
     */
    async updateStateInternal(sessionId, userInfo, source) {
        try {
            const previousState = { ...this.state };

            // 更新内存状态
            this.state.sessionId = sessionId;
            this.state.userInfo = userInfo;
            this.state.isAuthenticated = !!sessionId;
            this.state.lastUpdated = Date.now();
            this.state.source = source;

            // 同步到所有存储层
            await this.syncToAllStorages(sessionId, userInfo);

            console.log(`✅ 认证状态更新成功 - 来源: ${source}, SessionId: ${sessionId ? sessionId.substring(0, 8) + '...' : 'null'}`);
            
            return true;
        } catch (error) {
            console.error('❌ 内部状态更新失败:', error);
            return false;
        }
    }

    /**
     * 同步到所有存储层
     * @param {string|null} sessionId 会话ID
     * @param {Object|null} userInfo 用户信息
     */
    async syncToAllStorages(sessionId, userInfo) {
        const syncPromises = [];

        // 同步到LocalStorage
        syncPromises.push(this.syncToLocalStorage(sessionId, userInfo));

        // 同步到Cookie
        syncPromises.push(this.syncToCookie(sessionId));

        // 同步到Blazor
        syncPromises.push(this.syncToBlazor(sessionId));

        // 等待所有同步完成
        await Promise.allSettled(syncPromises);
    }

    /**
     * 同步到LocalStorage
     */
    async syncToLocalStorage(sessionId, userInfo) {
        try {
            if (sessionId) {
                localStorage.setItem(this.storageConfig.sessionKey, sessionId);
                if (userInfo) {
                    localStorage.setItem(this.storageConfig.userInfoKey, JSON.stringify(userInfo));
                }
            } else {
                localStorage.removeItem(this.storageConfig.sessionKey);
                localStorage.removeItem(this.storageConfig.userInfoKey);
            }
        } catch (error) {
            console.warn('⚠️ 同步到LocalStorage失败:', error);
        }
    }

    /**
     * 同步到Cookie
     */
    async syncToCookie(sessionId) {
        try {
            if (sessionId) {
                this.setCookie(this.storageConfig.cookieName, sessionId, this.storageConfig.cookieOptions);
            } else {
                this.deleteCookie(this.storageConfig.cookieName);
            }
        } catch (error) {
            console.warn('⚠️ 同步到Cookie失败:', error);
        }
    }

    /**
     * 同步到Blazor
     */
    async syncToBlazor(sessionId) {
        try {
            if (window.blazorAuthHelper && typeof window.blazorAuthHelper.invokeMethodAsync === 'function') {
                await window.blazorAuthHelper.invokeMethodAsync('NotifyAuthenticationStateChanged', sessionId);
            }
        } catch (error) {
            console.warn('⚠️ 同步到Blazor失败:', error);
        }
    }

    /**
     * 发布认证状态变更事件
     */
    publishAuthStateChanged(sessionId, userInfo, source) {
        if (window.authEventBus) {
            const eventData = {
                sessionId,
                userInfo,
                isAuthenticated: !!sessionId,
                source,
                timestamp: Date.now()
            };

            window.authEventBus.publish('authStateChanged', eventData);
            
            // 发布具体的登录/登出事件
            if (sessionId) {
                window.authEventBus.publish('userLoggedIn', eventData);
            } else {
                window.authEventBus.publish('userLoggedOut', eventData);
            }
        }
    }

    /**
     * 处理待处理的更新
     */
    async processPendingUpdates() {
        if (this.pendingUpdates.length === 0) return;

        const update = this.pendingUpdates.shift();
        if (update) {
            const success = await this.executeUpdate(update.sessionId, update.userInfo, update.source);
            update.resolve(success);
        }
    }

    /**
     * 从Blazor获取SessionId - 支持重试机制
     */
    async getSessionIdFromBlazor() {
        const maxRetries = 3;
        const retryDelay = 300;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // 等待Blazor AuthHelper就绪
                await this.waitForBlazorAuthHelper(2000);

                if (window.blazorAuthHelper && typeof window.blazorAuthHelper.invokeMethodAsync === 'function') {
                    const sessionId = await window.blazorAuthHelper.invokeMethodAsync('GetSessionId');
                    if (sessionId) {
                        console.log(`✅ 从Blazor获取SessionId成功 (尝试 ${attempt}/${maxRetries}): ${sessionId.substring(0, 8)}...`);
                        return sessionId;
                    }
                }

                if (attempt < maxRetries) {
                    console.debug(`⏳ Blazor获取SessionId失败，等待 ${retryDelay}ms 后重试 (尝试 ${attempt}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            } catch (error) {
                console.debug(`❌ 从Blazor获取SessionId异常 (尝试 ${attempt}/${maxRetries}):`, error);

                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }

        console.debug('⚠️ 所有Blazor获取SessionId尝试都失败');
        return null;
    }

    /**
     * 等待Blazor AuthHelper就绪
     */
    async waitForBlazorAuthHelper(timeoutMs = 5000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeoutMs) {
            if (window.blazorAuthHelper && typeof window.blazorAuthHelper.invokeMethodAsync === 'function') {
                return true;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        throw new Error('Blazor AuthHelper未在指定时间内就绪');
    }

    /**
     * 从LocalStorage获取SessionId
     */
    getSessionIdFromLocalStorage() {
        try {
            return localStorage.getItem(this.storageConfig.sessionKey);
        } catch (error) {
            console.debug('从LocalStorage获取SessionId失败:', error);
            return null;
        }
    }

    /**
     * 从LocalStorage获取用户信息
     */
    getUserInfoFromLocalStorage() {
        try {
            const userInfoStr = localStorage.getItem(this.storageConfig.userInfoKey);
            return userInfoStr ? JSON.parse(userInfoStr) : null;
        } catch (error) {
            console.debug('从LocalStorage获取用户信息失败:', error);
            return null;
        }
    }

    /**
     * 从Cookie获取SessionId
     */
    getSessionIdFromCookie() {
        try {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${this.storageConfig.cookieName}=`);
            if (parts.length === 2) {
                return parts.pop().split(';').shift();
            }
        } catch (error) {
            console.debug('从Cookie获取SessionId失败:', error);
        }
        return null;
    }

    /**
     * 设置Cookie
     */
    setCookie(name, value, options = {}) {
        let cookieString = `${name}=${value}`;

        if (options.path) cookieString += `; path=${options.path}`;
        if (options.secure) cookieString += '; secure';
        if (options.sameSite) cookieString += `; samesite=${options.sameSite}`;

        document.cookie = cookieString;
    }

    /**
     * 删除Cookie
     */
    deleteCookie(name) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }

    /**
     * 设置存储监听器
     */
    setupStorageListeners() {
        // 监听localStorage变化
        window.addEventListener('storage', (event) => {
            if (event.key === this.storageConfig.sessionKey) {
                this.handleStorageChange(event.newValue, 'localStorage');
            }
        });
    }

    /**
     * 处理存储变化
     */
    async handleStorageChange(newSessionId, source) {
        if (newSessionId !== this.state.sessionId) {
            console.log(`🔄 检测到存储变化 - 来源: ${source}`);
            await this.updateAuthenticationState(newSessionId, null, source);
        }
    }

    /**
     * 设置页面可见性监听器
     */
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查认证状态
                this.restoreAuthenticationState();
            }
        });
    }

    /**
     * 清除认证状态
     */
    async clearAuthenticationState() {
        await this.updateAuthenticationState(null, null, 'clear');
    }

    /**
     * 获取认证状态统计信息
     */
    getStats() {
        return {
            ...this.state,
            pendingUpdates: this.pendingUpdates.length,
            updateLock: this.updateLock
        };
    }

    /**
     * 销毁认证状态管理器
     */
    destroy() {
        this.clearAuthenticationState();
        console.log('💥 认证状态管理器已销毁');
    }
}

// 创建全局单例实例
if (!window.authStateManager) {
    window.authStateManager = new AuthenticationStateManager();
}

console.log('✅ AuthenticationStateManager已加载并注册到window.authStateManager');
