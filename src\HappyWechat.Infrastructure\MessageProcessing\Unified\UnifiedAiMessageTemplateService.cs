using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Unified;

/// <summary>
/// 统一AI消息模板服务
/// 为所有消息类型（文字、媒体）提供统一的AI模板，确保携带wid、wcid等完整参数
/// </summary>
public interface IUnifiedAiMessageTemplateService
{
    /// <summary>
    /// 构建文本消息的AI模板
    /// </summary>
    string BuildTextMessageTemplate(WxCallbackMessageDto callbackMessage);

    /// <summary>
    /// 构建文本消息的AI模板（带wId参数）
    /// </summary>
    string BuildTextMessageTemplate(WxCallbackMessageDto callbackMessage, string wId);

    /// <summary>
    /// 构建媒体消息的AI模板
    /// </summary>
    string BuildMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult);

    /// <summary>
    /// 构建媒体消息的AI模板（带wId参数）
    /// </summary>
    string BuildMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string wId);

    /// <summary>
    /// 构建群聊消息的AI模板
    /// </summary>
    string BuildGroupMessageTemplate(WxCallbackMessageDto callbackMessage, string senderNickname);

    /// <summary>
    /// 构建群聊消息的AI模板（带wId参数）
    /// </summary>
    string BuildGroupMessageTemplate(WxCallbackMessageDto callbackMessage, string senderNickname, string wId);

    /// <summary>
    /// 构建群聊媒体消息的AI模板
    /// </summary>
    string BuildGroupMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string senderNickname);

    /// <summary>
    /// 构建群聊媒体消息的AI模板（带wId参数）
    /// </summary>
    string BuildGroupMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string senderNickname, string wId);
}

public class UnifiedAiMessageTemplateService : IUnifiedAiMessageTemplateService
{
    private readonly ILogger<UnifiedAiMessageTemplateService> _logger;

    public UnifiedAiMessageTemplateService(ILogger<UnifiedAiMessageTemplateService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 构建文本消息的AI模板
    /// </summary>
    public string BuildTextMessageTemplate(WxCallbackMessageDto callbackMessage)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建文本消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        return BuildTextMessageTemplate(callbackMessage, data.WId ?? "");
    }

    /// <summary>
    /// 构建文本消息的AI模板（带wId参数）
    /// </summary>
    public string BuildTextMessageTemplate(WxCallbackMessageDto callbackMessage, string wId)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建文本消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        var template = $"WId: {data.WId},WcId: {callbackMessage.WcId},FromUser: {data.FromUser}";

        // 添加群组信息（如果是群聊）
        if (!string.IsNullOrEmpty(data.FromGroup))
        {
            template += $",FromGroup: {data.FromGroup}";
            // 在群聊中，FromUser就是群聊中的发送者
            template += $",FromGroupUser: {data.FromUser}";
        }

        // 添加消息内容
        template += $"，文本消息内容：{data.Content}";

        _logger.LogDebug("构建文本消息AI模板完成 - Template: {Template}", template);
        return template;
    }

    /// <summary>
    /// 构建媒体消息的AI模板
    /// </summary>
    public string BuildMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建媒体消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        return BuildMediaMessageTemplate(callbackMessage, mediaResult, data.WId ?? "");
    }

    /// <summary>
    /// 构建媒体消息的AI模板（带wId参数）
    /// </summary>
    public string BuildMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string wId)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建媒体消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        var template = $"WId: {data.WId},WcId: {callbackMessage.WcId},FromUser: {data.FromUser}";

        // 添加群组信息（如果是群聊）
        if (!string.IsNullOrEmpty(data.FromGroup))
        {
            template += $",FromGroup: {data.FromGroup}";
            // 在群聊中，FromUser就是群聊中的发送者
            template += $",FromGroupUser: {data.FromUser}";
        }

        // 添加媒体信息
        var mediaType = GetMediaTypeDescription(callbackMessage.MessageType);
        template += $"，{mediaType}消息";

        if (!string.IsNullOrEmpty(mediaResult.PublicUrl))
        {
            template += $"，媒体文件链接：{mediaResult.PublicUrl}";
        }

        // 🔧 优化：对于图片、文件、语音消息，不添加冗余的XML内容
        // AI处理媒体消息时，XML内容是冗余的，只需要媒体文件链接即可
        // 只有在特殊情况下才添加附加内容（如需要特定的元数据）

        _logger.LogDebug("构建媒体消息AI模板完成 - Template: {Template}", template);
        return template;
    }

    /// <summary>
    /// 构建群聊消息的AI模板
    /// </summary>
    public string BuildGroupMessageTemplate(WxCallbackMessageDto callbackMessage, string senderNickname)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建群聊消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        return BuildGroupMessageTemplate(callbackMessage, senderNickname, data.WId ?? "");
    }

    /// <summary>
    /// 构建群聊消息的AI模板（带wId参数）
    /// </summary>
    public string BuildGroupMessageTemplate(WxCallbackMessageDto callbackMessage, string senderNickname, string wId)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建群聊消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        var template = $"WId: {data.WId},WcId: {callbackMessage.WcId},FromUser: {data.FromUser}";
        template += $",FromGroup: {data.FromGroup}";

        // 在群聊中，FromUser就是群聊中的发送者
        template += $",FromGroupUser: {data.FromUser}";

        if (!string.IsNullOrEmpty(senderNickname))
        {
            template += $",SenderNickname: {senderNickname}";
        }

        // 添加消息内容
        template += $"，群聊文本消息内容：{data.Content}";

        _logger.LogDebug("构建群聊消息AI模板完成 - Template: {Template}", template);
        return template;
    }

    /// <summary>
    /// 构建群聊媒体消息的AI模板
    /// </summary>
    public string BuildGroupMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string senderNickname)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建群聊媒体消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        return BuildGroupMediaMessageTemplate(callbackMessage, mediaResult, senderNickname, data.WId ?? "");
    }

    /// <summary>
    /// 构建群聊媒体消息的AI模板（带wId参数）
    /// </summary>
    public string BuildGroupMediaMessageTemplate(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string senderNickname, string wId)
    {
        var data = callbackMessage.Data;
        if (data == null)
        {
            _logger.LogWarning("构建群聊媒体消息AI模板失败：消息数据为空");
            return string.Empty;
        }

        var template = $"WId: {data.WId},WcId: {callbackMessage.WcId},FromUser: {data.FromUser}";
        template += $",FromGroup: {data.FromGroup}";

        // 在群聊中，FromUser就是群聊中的发送者
        template += $",FromGroupUser: {data.FromUser}";

        if (!string.IsNullOrEmpty(senderNickname))
        {
            template += $",SenderNickname: {senderNickname}";
        }

        // 添加媒体信息
        var mediaType = GetMediaTypeDescription(callbackMessage.MessageType);
        template += $"，群聊{mediaType}消息";

        if (!string.IsNullOrEmpty(mediaResult.PublicUrl))
        {
            template += $"，媒体文件链接：{mediaResult.PublicUrl}";
        }

        // 🔧 优化：群聊媒体消息也不添加冗余的XML内容
        // AI处理媒体消息时，XML内容是冗余的，只需要媒体文件链接即可

        _logger.LogDebug("构建群聊媒体消息AI模板完成 - Template: {Template}", template);
        return template;
    }

    /// <summary>
    /// 获取媒体类型描述
    /// </summary>
    private string GetMediaTypeDescription(string? messageType)
    {
        return messageType switch
        {
            "60002" => "图片",
            "60004" => "语音",
            "60009" => "文件",
            "60010" => "视频",
            _ => "媒体"
        };
    }
}
