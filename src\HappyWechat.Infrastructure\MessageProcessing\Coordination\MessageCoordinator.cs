using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Constants;

namespace HappyWechat.Infrastructure.MessageProcessing.Coordination;

/// <summary>
/// 消息协调器
/// 处理群聊"仅@后回复"模式下的消息组合逻辑
/// 替代原有的MessageCombinationService，提供更清晰的业务逻辑
/// </summary>
public interface IMessageCoordinator
{
    /// <summary>
    /// 协调消息处理
    /// </summary>
    Task<CoordinationResult> CoordinateMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清理过期的协调数据
    /// </summary>
    Task CleanupExpiredCoordinationAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 协调结果
/// </summary>
public class CoordinationResult
{
    public CoordinationAction Action { get; set; }
    public string Reason { get; set; } = string.Empty;
    public WxCallbackMessageDto? CombinedMessage { get; set; }
    public List<string> CombinedMediaUrls { get; set; } = new();
    
    public static CoordinationResult ProcessImmediately(WxCallbackMessageDto message, string reason)
    {
        return new CoordinationResult
        {
            Action = CoordinationAction.ProcessImmediately,
            Reason = reason,
            CombinedMessage = message
        };
    }
    
    public static CoordinationResult WaitForCombination(string reason)
    {
        return new CoordinationResult
        {
            Action = CoordinationAction.WaitForCombination,
            Reason = reason
        };
    }
    
    public static CoordinationResult ProcessCombined(WxCallbackMessageDto combinedMessage, List<string> mediaUrls, string reason)
    {
        return new CoordinationResult
        {
            Action = CoordinationAction.ProcessCombined,
            Reason = reason,
            CombinedMessage = combinedMessage,
            CombinedMediaUrls = mediaUrls
        };
    }
    
    public static CoordinationResult Skip(string reason)
    {
        return new CoordinationResult
        {
            Action = CoordinationAction.Skip,
            Reason = reason
        };
    }
}

/// <summary>
/// 协调动作
/// </summary>
public enum CoordinationAction
{
    /// <summary>
    /// 立即处理
    /// </summary>
    ProcessImmediately,
    
    /// <summary>
    /// 等待组合
    /// </summary>
    WaitForCombination,
    
    /// <summary>
    /// 处理组合后的消息
    /// </summary>
    ProcessCombined,
    
    /// <summary>
    /// 跳过处理
    /// </summary>
    Skip
}

/// <summary>
/// 缓存的媒体消息
/// </summary>
public class CachedMediaMessage
{
    public string MessageType { get; set; } = string.Empty;
    public string FromUser { get; set; } = string.Empty;
    public string FromGroup { get; set; } = string.Empty;
    public string MediaUrl { get; set; } = string.Empty;
    public DateTime CachedAt { get; set; }
    public long MsgId { get; set; }
}

public class MessageCoordinator : IMessageCoordinator
{
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<MessageCoordinator> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    private const int CACHE_TIMEOUT_SECONDS = 30;
    
    public MessageCoordinator(
        IDistributedCache distributedCache,
        ILogger<MessageCoordinator> logger)
    {
        _distributedCache = distributedCache;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }
    
    /// <summary>
    /// 协调消息处理
    /// 实现群聊"仅@后回复"模式的消息组合逻辑
    /// </summary>
    public async Task<CoordinationResult> CoordinateMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken = default)
    {
        var messageType = message.MessageType;
        var fromGroup = message.Data?.FromGroup;
        var fromUser = message.Data?.FromUser;
        
        if (string.IsNullOrEmpty(fromGroup) || string.IsNullOrEmpty(fromUser))
        {
            return CoordinationResult.ProcessImmediately(message, "缺少群组或用户信息");
        }
        
        var cacheKey = GenerateCacheKey(fromGroup, fromUser);
        
        _logger.LogInformation("[{ProcessingId}] 🔗 开始消息协调 - MessageType: {MessageType}, FromGroup: {FromGroup}, FromUser: {FromUser}",
            processingId, messageType, fromGroup, fromUser);
        
        try
        {
            return messageType switch
            {
                MessageTypeConstants.GROUP_TEXT => await CoordinateTextMessageAsync(message, cacheKey, processingId, cancellationToken),
                MessageTypeConstants.GROUP_IMAGE => await CoordinateMediaMessageAsync(message, cacheKey, processingId, cancellationToken),
                MessageTypeConstants.GROUP_FILE => await CoordinateMediaMessageAsync(message, cacheKey, processingId, cancellationToken),
                MessageTypeConstants.GROUP_VOICE => CoordinationResult.Skip("群聊语音消息在仅@后回复模式下跳过"),
                _ => CoordinationResult.ProcessImmediately(message, $"不支持的协调消息类型: {messageType}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息协调异常", processingId);
            return CoordinationResult.ProcessImmediately(message, $"协调异常，立即处理: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 协调文本消息
    /// 检查是否有等待的媒体消息需要组合
    /// </summary>
    private async Task<CoordinationResult> CoordinateTextMessageAsync(WxCallbackMessageDto textMessage, string cacheKey, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            // 检查是否有缓存的媒体消息
            var cachedMediaJson = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedMediaJson))
            {
                // 没有等待组合的媒体消息，直接处理文本
                _logger.LogDebug("[{ProcessingId}] 📝 单独文本消息，直接处理", processingId);
                return CoordinationResult.ProcessImmediately(textMessage, "单独文本消息");
            }
            
            // 反序列化缓存的媒体消息
            var cachedMedia = JsonSerializer.Deserialize<CachedMediaMessage>(cachedMediaJson, _jsonOptions);
            if (cachedMedia == null)
            {
                _logger.LogWarning("[{ProcessingId}] 缓存媒体消息反序列化失败", processingId);
                return CoordinationResult.ProcessImmediately(textMessage, "缓存数据异常，直接处理文本");
            }
            
            // 检查缓存是否过期
            if (DateTime.UtcNow - cachedMedia.CachedAt > TimeSpan.FromSeconds(CACHE_TIMEOUT_SECONDS))
            {
                _logger.LogDebug("[{ProcessingId}] 缓存媒体消息已过期，清理缓存", processingId);
                await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
                return CoordinationResult.ProcessImmediately(textMessage, "缓存过期，直接处理文本");
            }
            
            // 组合消息：将媒体URL添加到文本消息中
            var combinedMessage = CreateCombinedMessage(textMessage, cachedMedia);
            var mediaUrls = new List<string> { cachedMedia.MediaUrl };
            
            // 清理缓存
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            
            _logger.LogInformation("[{ProcessingId}] 🔗 消息组合成功 - TextLength: {TextLength}, MediaType: {MediaType}",
                processingId, textMessage.Data?.Content?.Length ?? 0, cachedMedia.MessageType);
            
            return CoordinationResult.ProcessCombined(combinedMessage, mediaUrls, "文本+媒体组合消息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 文本消息协调异常", processingId);
            return CoordinationResult.ProcessImmediately(textMessage, $"协调异常: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 协调媒体消息
    /// 缓存媒体消息，等待后续的文本消息
    /// </summary>
    private async Task<CoordinationResult> CoordinateMediaMessageAsync(WxCallbackMessageDto mediaMessage, string cacheKey, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            // 创建缓存的媒体消息
            var cachedMedia = new CachedMediaMessage
            {
                MessageType = mediaMessage.MessageType!,
                FromUser = mediaMessage.Data?.FromUser ?? string.Empty,
                FromGroup = mediaMessage.Data?.FromGroup ?? string.Empty,
                MediaUrl = "pending_download", // 实际URL在文件处理完成后更新
                CachedAt = DateTime.UtcNow,
                MsgId = mediaMessage.Data?.MsgId ?? 0
            };
            
            var json = JsonSerializer.Serialize(cachedMedia, _jsonOptions);
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(CACHE_TIMEOUT_SECONDS)
            };
            
            await _distributedCache.SetStringAsync(cacheKey, json, cacheOptions, cancellationToken);
            
            _logger.LogInformation("[{ProcessingId}] 📁 媒体消息已缓存，等待文本消息 - MessageType: {MessageType}, CacheKey: {CacheKey}",
                processingId, mediaMessage.MessageType, cacheKey);
            
            return CoordinationResult.WaitForCombination("媒体消息已缓存，等待文本消息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 媒体消息协调异常", processingId);
            return CoordinationResult.ProcessImmediately(mediaMessage, $"缓存失败，立即处理: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 创建组合消息
    /// </summary>
    private WxCallbackMessageDto CreateCombinedMessage(WxCallbackMessageDto textMessage, CachedMediaMessage cachedMedia)
    {
        // 克隆文本消息
        var combinedMessage = JsonSerializer.Deserialize<WxCallbackMessageDto>(
            JsonSerializer.Serialize(textMessage, _jsonOptions), _jsonOptions)!;
        
        // 在内容中添加媒体信息
        var originalContent = combinedMessage.Data?.Content ?? string.Empty;
        var mediaInfo = cachedMedia.MessageType switch
        {
            MessageTypeConstants.GROUP_IMAGE => $"[图片: {cachedMedia.MediaUrl}]",
            MessageTypeConstants.GROUP_FILE => $"[文件: {cachedMedia.MediaUrl}]",
            _ => $"[媒体: {cachedMedia.MediaUrl}]"
        };
        
        if (combinedMessage.Data != null)
        {
            combinedMessage.Data.Content = $"{mediaInfo}\n{originalContent}".Trim();
        }
        
        return combinedMessage;
    }
    
    /// <summary>
    /// 生成缓存键
    /// </summary>
    private string GenerateCacheKey(string fromGroup, string fromUser)
    {
        return $"msg_coord:{fromGroup}:{fromUser}";
    }
    
    /// <summary>
    /// 清理过期的协调数据
    /// </summary>
    public async Task CleanupExpiredCoordinationAsync(CancellationToken cancellationToken = default)
    {
        // Redis的TTL机制会自动清理过期数据
        // 这里可以添加额外的清理逻辑，比如统计信息等
        _logger.LogDebug("执行消息协调数据清理");
        await Task.CompletedTask;
    }
}
