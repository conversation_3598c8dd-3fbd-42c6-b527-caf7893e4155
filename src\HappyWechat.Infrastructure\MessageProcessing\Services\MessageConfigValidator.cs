using Microsoft.Extensions.Logging;
using StackExchange.Redis.Extensions.Core.Abstractions;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息配置验证结果
/// </summary>
public class MessageConfigValidationResult
{
    public bool ShouldProcessWithAi { get; set; }
    public bool IsManualReplyMode { get; set; }
    public bool IsMentioned { get; set; }
    public Guid? AiAgentId { get; set; }
    public string? EntityName { get; set; }
    public string? EntityType { get; set; }
    public string? ValidationDetails { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 是否应该丢弃消息
    /// </summary>
    public bool ShouldDiscard { get; set; }

    /// <summary>
    /// 消息丢弃原因
    /// </summary>
    public MessageDiscardReason DiscardReason { get; set; } = MessageDiscardReason.None;
}

/// <summary>
/// 消息配置验证器接口
/// </summary>
public interface IMessageConfigValidator
{
    /// <summary>
    /// 验证消息是否应该进行AI处理
    /// </summary>
    Task<MessageConfigValidationResult> ValidateForAiProcessingAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 缓存验证结果
    /// </summary>
    Task CacheValidationResultAsync(string cacheKey, MessageConfigValidationResult result, TimeSpan? expiry = null);
    
    /// <summary>
    /// 获取缓存的验证结果
    /// </summary>
    Task<MessageConfigValidationResult?> GetCachedValidationResultAsync(string cacheKey);
}

/// <summary>
/// 消息配置验证器实现
/// </summary>
public class MessageConfigValidator : IMessageConfigValidator
{
    private readonly IContactAiConfigChecker _contactAiConfigChecker;
    private readonly IGroupAiConfigChecker _groupAiConfigChecker;
    private readonly IEnhancedGroupMessageProcessor _enhancedGroupMessageProcessor;
    private readonly IRedisDatabase _redisDb;
    private readonly ILogger<MessageConfigValidator> _logger;
    
    private const string VALIDATION_CACHE_PREFIX = "hw:cache:msg_validation";
    private static readonly TimeSpan DefaultCacheExpiry = TimeSpan.FromMinutes(30);

    public MessageConfigValidator(
        IContactAiConfigChecker contactAiConfigChecker,
        IGroupAiConfigChecker groupAiConfigChecker,
        IEnhancedGroupMessageProcessor enhancedGroupMessageProcessor,
        IRedisDatabase redisDb,
        ILogger<MessageConfigValidator> logger)
    {
        _contactAiConfigChecker = contactAiConfigChecker;
        _groupAiConfigChecker = groupAiConfigChecker;
        _enhancedGroupMessageProcessor = enhancedGroupMessageProcessor;
        _redisDb = redisDb;
        _logger = logger;
    }

    public async Task<MessageConfigValidationResult> ValidateForAiProcessingAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始消息配置验证 - MessageType: {MessageType}, WcId: {WcId}", 
                processingId, callbackMessage.MessageType, callbackMessage.WcId);

            // 检查缓存
            var cacheKey = GenerateCacheKey(callbackMessage);
            var cachedResult = await GetCachedValidationResultAsync(cacheKey);
            if (cachedResult != null)
            {
                _logger.LogDebug("[{ProcessingId}] 使用缓存的验证结果", processingId);
                return cachedResult;
            }

            var result = new MessageConfigValidationResult();
            
            if (!Guid.TryParse(callbackMessage.WxManagerId, out var wxManagerId))
            {
                _logger.LogWarning("[{ProcessingId}] WxManagerId格式无效 - WxManagerId: {WxManagerId}", 
                    processingId, callbackMessage.WxManagerId);
                
                result.ValidationDetails = "WxManagerId格式无效";
                return result;
            }

            // 根据消息类型进行验证
            if (IsPrivateMessage(callbackMessage.MessageType))
            {
                result = await ValidatePrivateMessageAsync(callbackMessage, wxManagerId, processingId);
            }
            else if (IsGroupMessage(callbackMessage.MessageType))
            {
                result = await ValidateGroupMessageAsync(callbackMessage, wxManagerId, processingId);
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] 非聊天消息，跳过AI处理验证 - MessageType: {MessageType}", 
                    processingId, callbackMessage.MessageType);
                result.ValidationDetails = "非聊天消息类型";
            }

            // 缓存结果
            await CacheValidationResultAsync(cacheKey, result);

            _logger.LogDebug("[{ProcessingId}] 消息配置验证完成 - ShouldProcessWithAi: {ShouldProcess}, EntityType: {EntityType}, EntityName: {EntityName}", 
                processingId, result.ShouldProcessWithAi, result.EntityType, result.EntityName);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 消息配置验证失败", processingId);
            return new MessageConfigValidationResult
            {
                ValidationDetails = $"验证失败: {ex.Message}"
            };
        }
    }

    public async Task CacheValidationResultAsync(string cacheKey, MessageConfigValidationResult result, TimeSpan? expiry = null)
    {
        try
        {
            var cacheExpiry = expiry ?? DefaultCacheExpiry;
            await _redisDb.AddAsync($"{VALIDATION_CACHE_PREFIX}:{cacheKey}", result, cacheExpiry);
            
            _logger.LogDebug("缓存验证结果 - CacheKey: {CacheKey}, Expiry: {Expiry}", cacheKey, cacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存验证结果失败 - CacheKey: {CacheKey}", cacheKey);
        }
    }

    public async Task<MessageConfigValidationResult?> GetCachedValidationResultAsync(string cacheKey)
    {
        try
        {
            var result = await _redisDb.GetAsync<MessageConfigValidationResult>($"{VALIDATION_CACHE_PREFIX}:{cacheKey}");
            
            if (result != null)
            {
                _logger.LogDebug("获取缓存验证结果成功 - CacheKey: {CacheKey}", cacheKey);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存验证结果失败 - CacheKey: {CacheKey}", cacheKey);
            return null;
        }
    }

    private async Task<MessageConfigValidationResult> ValidatePrivateMessageAsync(
        WxCallbackMessageDto callbackMessage,
        Guid wxManagerId,
        string processingId)
    {
        var fromUser = callbackMessage.Data?.FromUser ?? "";

        // 首先进行实体存在性验证
        var validationResult = await _contactAiConfigChecker.ValidateContactMessageAsync(wxManagerId, fromUser, processingId);
        if (validationResult.ShouldDiscard)
        {
            _logger.LogWarning("[{ProcessingId}] 联系人消息验证失败，消息将被丢弃 - FromUser: {FromUser}, Reason: {Reason}",
                processingId, fromUser, validationResult.DiscardReason);

            return new MessageConfigValidationResult
            {
                ShouldProcessWithAi = false,
                ShouldDiscard = true,
                DiscardReason = validationResult.DiscardReason,
                EntityName = fromUser,
                EntityType = "Contact",
                ValidationDetails = validationResult.ErrorMessage ?? "联系人验证失败"
            };
        }

        // 实体存在，继续进行AI配置检查
        var contactConfig = await _contactAiConfigChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);

        return new MessageConfigValidationResult
        {
            ShouldProcessWithAi = contactConfig.IsAiConfigured && contactConfig.IsEnabled,
            IsManualReplyMode = !contactConfig.IsEnabled, // 简化：未启用就是人工模式
            AiAgentId = contactConfig.AiAgentId,
            EntityName = contactConfig.ContactName,
            EntityType = "Contact",
            ValidationDetails = $"联系人AI配置检查完成 - IsAiConfigured: {contactConfig.IsAiConfigured}, IsEnabled: {contactConfig.IsEnabled}, AiAgentId: {contactConfig.AiAgentId}",
            ShouldDiscard = false,
            DiscardReason = MessageDiscardReason.None,
            Metadata =
            {
                ["ContactName"] = contactConfig.ContactName ?? "",
                ["FromUser"] = fromUser,
                ["MessageType"] = callbackMessage.MessageType,
                ["AiAgentId"] = contactConfig.AiAgentId?.ToString() ?? "",
                ["IsAiConfigured"] = contactConfig.IsAiConfigured.ToString()
            }
        };
    }

    private async Task<MessageConfigValidationResult> ValidateGroupMessageAsync(
        WxCallbackMessageDto callbackMessage,
        Guid wxManagerId,
        string processingId)
    {
        var fromGroup = callbackMessage.Data?.FromGroup ?? "";
        if (string.IsNullOrEmpty(fromGroup))
        {
            return new MessageConfigValidationResult
            {
                ValidationDetails = "群组信息缺失"
            };
        }

        try
        {
            // 🔧 精简日志：注释掉群组消息验证的详细日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] 🔍 开始群组消息验证 - FromGroup: {FromGroup}, MessageType: {MessageType}, WxManagerId: {WxManagerId}",
            //     processingId, fromGroup, callbackMessage.MessageType, wxManagerId);

            // 使用增强的群消息处理器进行验证
            var enhancedResult = await _enhancedGroupMessageProcessor.ShouldTriggerAiReplyAsync(callbackMessage);

            // 🔧 精简日志：注释掉增强群消息处理结果日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] 🎯 增强群消息处理结果 - ShouldReply: {ShouldReply}, MessageType: {MessageType}, Reason: {Reason}, IsConfigured: {IsConfigured}, IsMentioned: {IsMentioned}",
            //     processingId, enhancedResult.ShouldReply, enhancedResult.MessageType, enhancedResult.Reason, enhancedResult.IsConfigured, enhancedResult.IsMentioned);

            // 如果群组不存在或未配置，需要检查是否应该丢弃
            if (!enhancedResult.IsConfigured)
            {
                // 首先进行实体存在性验证（保持向后兼容）
                var validationResult = await _groupAiConfigChecker.ValidateGroupMessageAsync(wxManagerId, fromGroup, callbackMessage.Data!, processingId);
                if (validationResult.ShouldDiscard)
                {
                    _logger.LogWarning("[{ProcessingId}] 群组消息验证失败，消息将被丢弃 - FromGroup: {FromGroup}, Reason: {Reason}",
                        processingId, fromGroup, validationResult.DiscardReason);

                    return new MessageConfigValidationResult
                    {
                        ShouldProcessWithAi = false,
                        ShouldDiscard = true,
                        DiscardReason = validationResult.DiscardReason,
                        EntityName = fromGroup,
                        EntityType = "Group",
                        ValidationDetails = validationResult.ErrorMessage ?? "群组验证失败"
                    };
                }
            }

            var result = new MessageConfigValidationResult
            {
                ShouldProcessWithAi = enhancedResult.ShouldReply,
                IsManualReplyMode = !enhancedResult.ShouldReply, // 简化：不回复就是人工模式
                IsMentioned = enhancedResult.IsMentioned,
                AiAgentId = null, // 需要从群组配置中获取，暂时设为null
                EntityName = enhancedResult.GroupName,
                EntityType = "Group",
                ValidationDetails = $"增强群消息处理完成 - MessageType: {enhancedResult.MessageType}, ShouldReply: {enhancedResult.ShouldReply}, Reason: {enhancedResult.Reason}, IsMentioned: {enhancedResult.IsMentioned}",
                ShouldDiscard = false,
                DiscardReason = MessageDiscardReason.None,
                Metadata =
                {
                    ["GroupName"] = enhancedResult.GroupName ?? "",
                    ["FromGroup"] = fromGroup,
                    ["IsMentioned"] = enhancedResult.IsMentioned,
                    ["MessageType"] = enhancedResult.MessageType,
                    ["ProcessingReason"] = enhancedResult.Reason,
                    ["IsConfigured"] = enhancedResult.IsConfigured.ToString(),
                    ["EnhancedProcessing"] = "true"
                }
            };

            // 🔧 精简日志：注释掉验证结果日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] ✅ 群组消息验证完成 - ShouldProcessWithAi: {ShouldProcessWithAi}, IsManualReplyMode: {IsManualReplyMode}, IsMentioned: {IsMentioned}, Reason: {Reason}",
            //     processingId, result.ShouldProcessWithAi, result.IsManualReplyMode, result.IsMentioned, enhancedResult.Reason);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 增强群消息处理失败，回退到原始逻辑", processingId);

            // 回退到原始逻辑
            var validationResult = await _groupAiConfigChecker.ValidateGroupMessageAsync(wxManagerId, fromGroup, callbackMessage.Data!, processingId);
            if (validationResult.ShouldDiscard)
            {
                return new MessageConfigValidationResult
                {
                    ShouldProcessWithAi = false,
                    ShouldDiscard = true,
                    DiscardReason = validationResult.DiscardReason,
                    EntityName = fromGroup,
                    EntityType = "Group",
                    ValidationDetails = validationResult.ErrorMessage ?? "群组验证失败"
                };
            }

            var groupConfig = await _groupAiConfigChecker.CheckGroupAiConfigAsync(wxManagerId, fromGroup, callbackMessage.Data!);
            var shouldProcess = groupConfig.IsAiConfigured && groupConfig.IsEnabled && (!groupConfig.OnlyReplyWhenMentioned || groupConfig.IsMentioned);

            return new MessageConfigValidationResult
            {
                ShouldProcessWithAi = shouldProcess,
                IsManualReplyMode = !groupConfig.IsEnabled,
                IsMentioned = groupConfig.IsMentioned,
                AiAgentId = groupConfig.AiAgentId,
                EntityName = groupConfig.GroupName,
                EntityType = "Group",
                ValidationDetails = $"回退处理完成 - 异常: {ex.Message}",
                ShouldDiscard = false,
                DiscardReason = MessageDiscardReason.None
            };
        }
    }

    /// <summary>
    /// 🔧 重构：判断是否为私聊消息 - 更新注释使用60009替代60008
    /// </summary>
    private bool IsPrivateMessage(string messageType)
    {
        return messageType.StartsWith("600"); // 60001, 60002, 60004, 60009
    }

    /// <summary>
    /// 🔧 重构：判断是否为群聊消息 - 更新注释使用80009替代80008
    /// </summary>
    private bool IsGroupMessage(string messageType)
    {
        return messageType.StartsWith("800"); // 80001, 80002, 80004, 80009
    }

    private string GenerateCacheKey(WxCallbackMessageDto callbackMessage)
    {
        var keyParts = new[]
        {
            callbackMessage.WxManagerId ?? "",
            callbackMessage.MessageType ?? "",
            callbackMessage.Data?.FromUser ?? "",
            callbackMessage.Data?.FromGroup ?? ""
        };
        
        return string.Join(":", keyParts.Where(p => !string.IsNullOrEmpty(p)));
    }
}
