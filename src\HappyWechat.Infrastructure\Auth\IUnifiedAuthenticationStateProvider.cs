using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// 统一认证状态提供者接口
/// 定义标准的认证状态操作契约，确保前后端认证状态的一致性
/// </summary>
public interface IUnifiedAuthenticationStateProvider
{
    /// <summary>
    /// 获取当前认证状态
    /// </summary>
    /// <returns>认证状态</returns>
    Task<AuthenticationState> GetAuthenticationStateAsync();

    /// <summary>
    /// 获取当前会话ID
    /// </summary>
    /// <returns>会话ID，如果未认证则返回null</returns>
    Task<string?> GetCurrentSessionIdAsync();

    /// <summary>
    /// 通知认证状态已更改
    /// </summary>
    /// <param name="sessionId">新的会话ID，null表示登出</param>
    /// <returns>操作是否成功</returns>
    Task<bool> NotifyAuthenticationStateChangedAsync(string? sessionId = null);

    /// <summary>
    /// 标记用户为已认证状态
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="userInfo">用户信息（可选）</param>
    /// <returns>操作是否成功</returns>
    Task<bool> MarkUserAsAuthenticatedAsync(string sessionId, object? userInfo = null);

    /// <summary>
    /// 标记用户为已登出状态
    /// </summary>
    /// <returns>操作是否成功</returns>
    Task<bool> MarkUserAsLoggedOutAsync();

    /// <summary>
    /// 验证会话是否有效
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>会话是否有效</returns>
    Task<bool> ValidateSessionAsync(string sessionId);

    /// <summary>
    /// 刷新认证状态
    /// 从权威数据源重新获取认证状态
    /// </summary>
    /// <returns>刷新后的认证状态</returns>
    Task<AuthenticationState> RefreshAuthenticationStateAsync();

    /// <summary>
    /// 获取用户Claims
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>用户Claims集合</returns>
    Task<IEnumerable<Claim>> GetUserClaimsAsync(string sessionId);

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="role">角色名称</param>
    /// <returns>是否具有指定角色</returns>
    Task<bool> IsInRoleAsync(string sessionId, string role);

    /// <summary>
    /// 检查用户是否具有指定权限
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="permission">权限名称</param>
    /// <returns>是否具有指定权限</returns>
    Task<bool> HasPermissionAsync(string sessionId, string permission);

    /// <summary>
    /// 获取认证状态统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<AuthenticationStateStats> GetStatsAsync();

    /// <summary>
    /// 强制刷新SessionId缓存
    /// </summary>
    /// <returns>刷新后的SessionId</returns>
    Task<string?> RefreshSessionIdAsync();

    /// <summary>
    /// 获取SessionId获取服务的统计信息
    /// </summary>
    /// <returns>SessionId获取统计信息</returns>
    Task<SessionIdRetrievalStats> GetSessionIdRetrievalStatsAsync();

    /// <summary>
    /// 认证状态变更事件
    /// </summary>
    event Func<AuthenticationStateChangedEventArgs, Task> AuthenticationStateChanged;
}

/// <summary>
/// 认证状态统计信息
/// </summary>
public class AuthenticationStateStats
{
    /// <summary>
    /// 当前是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 当前会话ID
    /// </summary>
    public string? CurrentSessionId { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime? LastUpdated { get; set; }

    /// <summary>
    /// 认证状态来源
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 用户角色
    /// </summary>
    public IEnumerable<string> Roles { get; set; } = new List<string>();

    /// <summary>
    /// 会话创建时间
    /// </summary>
    public DateTime? SessionCreatedAt { get; set; }

    /// <summary>
    /// 会话过期时间
    /// </summary>
    public DateTime? SessionExpiresAt { get; set; }
}

/// <summary>
/// 认证状态变更事件参数
/// </summary>
public class AuthenticationStateChangedEventArgs
{
    /// <summary>
    /// 新的会话ID
    /// </summary>
    public string? NewSessionId { get; set; }

    /// <summary>
    /// 旧的会话ID
    /// </summary>
    public string? OldSessionId { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更来源
    /// </summary>
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// 是否为登录操作
    /// </summary>
    public bool IsLogin => !string.IsNullOrEmpty(NewSessionId) && string.IsNullOrEmpty(OldSessionId);

    /// <summary>
    /// 是否为登出操作
    /// </summary>
    public bool IsLogout => string.IsNullOrEmpty(NewSessionId) && !string.IsNullOrEmpty(OldSessionId);

    /// <summary>
    /// 是否为会话切换
    /// </summary>
    public bool IsSessionSwitch => !string.IsNullOrEmpty(NewSessionId) && !string.IsNullOrEmpty(OldSessionId) && NewSessionId != OldSessionId;

    /// <summary>
    /// 用户信息
    /// </summary>
    public object? UserInfo { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
