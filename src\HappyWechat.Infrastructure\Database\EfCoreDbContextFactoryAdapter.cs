using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// EF Core标准IDbContextFactory适配器
/// 将IndependentDbContextFactory适配为EF Core标准接口
/// 解决Repository依赖问题，避免生命周期冲突
/// </summary>
public class EfCoreDbContextFactoryAdapter : IDbContextFactory<ApplicationDbContext>
{
    private readonly IDbContextFactory _independentFactory;
    private readonly ILogger<EfCoreDbContextFactoryAdapter> _logger;

    public EfCoreDbContextFactoryAdapter(
        IDbContextFactory independentFactory,
        ILogger<EfCoreDbContextFactoryAdapter> logger)
    {
        _independentFactory = independentFactory ?? throw new ArgumentNullException(nameof(independentFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 创建数据库上下文（EF Core标准接口）
    /// </summary>
    public ApplicationDbContext CreateDbContext()
    {
        try
        {
            // 使用我们自定义接口的CreateContextAsync方法（同步版本）
            var context = _independentFactory.CreateContextAsync(false).GetAwaiter().GetResult();
            _logger.LogTrace("EF Core适配器创建DbContext成功");
            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "EF Core适配器创建DbContext失败");
            throw;
        }
    }
}