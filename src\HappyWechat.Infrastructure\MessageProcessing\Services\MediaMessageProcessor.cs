using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.MessageProcess;
using HappyWechat.Infrastructure.MediaProcessing;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 媒体处理结果
/// </summary>
public class MediaProcessResult
{
    public bool IsSuccess { get; set; }
    public string? LocalPath { get; set; }
    public string? StorageUrl { get; set; }
    public string? PublicUrl { get; set; }
    public string? FileName { get; set; }
    public long FileSize { get; set; }
    public string? ContentType { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 媒体消息处理器接口
/// </summary>
public interface IMediaMessageProcessor
{
    /// <summary>
    /// 处理图片消息
    /// </summary>
    Task<MediaProcessResult> ProcessImageMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 处理语音消息
    /// </summary>
    Task<MediaProcessResult> ProcessVoiceMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 处理文件消息
    /// </summary>
    Task<MediaProcessResult> ProcessFileMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 检查是否需要处理媒体
    /// </summary>
    bool ShouldProcessMedia(string messageType);
}

/// <summary>
/// 媒体消息处理器实现 - 简化版，直接使用UnifiedMediaProcessor
/// </summary>
public class MediaMessageProcessor : IMediaMessageProcessor
{
    private readonly IUnifiedMediaProcessor _unifiedMediaProcessor;
    private readonly ILogger<MediaMessageProcessor> _logger;

    public MediaMessageProcessor(
        IUnifiedMediaProcessor unifiedMediaProcessor,
        ILogger<MediaMessageProcessor> logger)
    {
        _unifiedMediaProcessor = unifiedMediaProcessor;
        _logger = logger;
    }

    public async Task<MediaProcessResult> ProcessImageMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        return await ProcessMediaAsync(callbackMessage, processingId, "图片");
    }

    public async Task<MediaProcessResult> ProcessVoiceMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        return await ProcessMediaAsync(callbackMessage, processingId, "语音");
    }

    public async Task<MediaProcessResult> ProcessFileMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        return await ProcessMediaAsync(callbackMessage, processingId, "文件");
    }

    /// <summary>
    /// 🔧 重构：判断是否需要处理媒体文件 - 使用60009/80009替代60008/80008
    /// </summary>
    public bool ShouldProcessMedia(string messageType)
    {
        return messageType switch
        {
            "60002" or "80002" => true, // 图片消息
            "60004" or "80004" => true, // 语音消息
            "60009" or "80009" => true, // 文件发送完成消息
            _ => false
        };
    }

    /// <summary>
    /// 通用媒体处理方法
    /// </summary>
    private async Task<MediaProcessResult> ProcessMediaAsync(WxCallbackMessageDto callbackMessage, string processingId, string mediaType)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 🎵 开始处理{MediaType}消息 - WcId: {WcId}, FromUser: {FromUser}, MsgId: {MsgId}, MessageType: {MessageType}",
                processingId, mediaType, callbackMessage.WcId, callbackMessage.Data?.FromUser,
                callbackMessage.Data?.MsgId, callbackMessage.MessageType);

            // 使用统一媒体处理器
            _logger.LogDebug("[{ProcessingId}] 调用统一媒体处理器 - MediaType: {MediaType}", processingId, mediaType);
            var mediaResult = await _unifiedMediaProcessor.ProcessMediaAsync(callbackMessage);

            _logger.LogInformation("[{ProcessingId}] 统一媒体处理器返回结果 - Success: {Success}, Error: {Error}",
                processingId, mediaResult.Success, mediaResult.ErrorMessage);

            if (!mediaResult.Success)
            {
                return new MediaProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = mediaResult.ErrorMessage ?? $"{mediaType}处理失败"
                };
            }

            return new MediaProcessResult
            {
                IsSuccess = true,
                LocalPath = mediaResult.LocalFilePath,
                PublicUrl = mediaResult.PublicUrl,
                FileName = mediaResult.FileName,
                FileSize = mediaResult.FileSize,
                Metadata =
                {
                    ["MessageType"] = callbackMessage.MessageType,
                    ["MediaType"] = mediaResult.MediaType,
                    ["ProcessingId"] = processingId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理{MediaType}消息失败", processingId, mediaType);
            return new MediaProcessResult
            {
                IsSuccess = false,
                ErrorMessage = $"处理{mediaType}消息失败: {ex.Message}"
            };
        }
    }
}
