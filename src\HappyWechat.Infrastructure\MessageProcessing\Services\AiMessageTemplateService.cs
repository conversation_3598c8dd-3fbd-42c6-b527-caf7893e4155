using HappyWechat.Application.DTOs.AiProvider;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// AI消息模板服务实现
/// </summary>
public class AiMessageTemplateService : IAiMessageTemplateService
{
    private readonly IContactNicknameService _contactNicknameService;
    private readonly ILogger<AiMessageTemplateService> _logger;

    public AiMessageTemplateService(
        IContactNicknameService contactNicknameService,
        ILogger<AiMessageTemplateService> logger)
    {
        _contactNicknameService = contactNicknameService;
        _logger = logger;
    }

    public async Task<string> BuildMessageTemplateAsync(AiMessageContext context, AiProviderType providerType)
    {
        // ChatGPT特殊处理：仅支持文本消息，直接返回原文
        if (providerType == AiProviderType.ChatGPT)
        {
            return await BuildChatGPTMessageAsync(context);
        }

        // 其他AI提供商使用简化模板
        return await BuildSimplifiedTemplateAsync(context);
    }

    /// <summary>
    /// 构建ChatGPT消息（仅支持文本消息）
    /// </summary>
    private async Task<string> BuildChatGPTMessageAsync(AiMessageContext context)
    {
        // ChatGPT仅处理文本消息
        if (!AiMessageTemplate.IsTextMessage(context.MessageType))
        {
            _logger.LogWarning("ChatGPT不支持处理媒体消息，消息类型：{MessageType}，跳过处理", context.MessageType);
            throw new NotSupportedException($"ChatGPT不支持处理媒体消息，消息类型：{context.MessageType}");
        }

        // 直接返回原文内容
        return context.Content;
    }

    /// <summary>
    /// 构建简化模板（Coze、Dify、MaxKB）
    /// </summary>
    private async Task<string> BuildSimplifiedTemplateAsync(AiMessageContext context)
    {
        try
        {
            // 确保昵称信息已获取
            await EnsureNicknamesAsync(context);

            // 获取对应的模板
            var template = AiMessageTemplate.GetTemplate(context.MessageType, context.IsGroupMessage);

            // 填充基础参数
            var result = template
                .Replace("{WId}", context.WId)
                .Replace("{WcId}", context.WcId)
                .Replace("{FromUser}", context.FromUser)
                .Replace("{FromUserNickName}", context.FromUserNickName ?? "未知用户");

            // 根据消息类型填充不同参数
            if (context.IsGroupMessage)
            {
                result = result
                    .Replace("{FromGroup}", context.FromGroup ?? string.Empty)
                    .Replace("{FromGroupUser}", context.FromGroupUser ?? context.FromUser);
            }

            // 填充内容或媒体URL
            if (AiMessageTemplate.IsTextMessage(context.MessageType))
            {
                result = result.Replace("{Content}", context.Content);
            }
            else if (AiMessageTemplate.IsMediaMessage(context.MessageType))
            {
                var mediaUrl = GetFirstMediaUrl(context);
                result = result.Replace("{MediaUrl}", mediaUrl);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建简化模板失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, MessageType: {MessageType}",
                context.WxManagerId, context.FromUser, context.MessageType);

            // 失败时返回原始内容
            return context.Content;
        }
    }

    /// <summary>
    /// 获取第一个媒体URL
    /// </summary>
    private string GetFirstMediaUrl(AiMessageContext context)
    {
        if (context.MediaUrls.Any())
        {
            return context.MediaUrls.First();
        }

        // 如果没有URL，尝试从描述中提取
        if (context.MediaDescriptions.Any())
        {
            var description = context.MediaDescriptions.First();
            // 简单的URL提取逻辑
            if (description.Contains("http"))
            {
                var urlStart = description.IndexOf("http");
                var urlEnd = description.IndexOf(' ', urlStart);
                if (urlEnd == -1) urlEnd = description.Length;
                return description.Substring(urlStart, urlEnd - urlStart);
            }
        }

        return "无媒体链接";
    }



    public async Task<AiMessageContext> BuildMessageContextAsync(
        WxCallbackMessageDto callbackMessage,
        Guid wxManagerId,
        List<string>? mediaDescriptions = null,
        List<string>? mediaUrls = null)
    {
        var context = new AiMessageContext
        {
            WxManagerId = wxManagerId,
            WId = callbackMessage.Data?.WId ?? callbackMessage.WcId ?? string.Empty,
            WcId = callbackMessage.WcId,
            FromUser = callbackMessage.Data?.FromUser ?? string.Empty,
            MessageType = callbackMessage.MessageType,
            Content = callbackMessage.Data?.Content ?? string.Empty,
            IsGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup),
            FromGroup = callbackMessage.Data?.FromGroup,
            FromGroupUser = callbackMessage.Data?.FromUser,
            MediaDescriptions = mediaDescriptions ?? new List<string>(),
            MediaUrls = mediaUrls ?? new List<string>(),
            IsMediaMessage = mediaDescriptions?.Any() == true || mediaUrls?.Any() == true,
            OriginalCallbackMessage = callbackMessage,
            Timestamp = DateTime.UtcNow,
            ProcessStartTime = DateTime.UtcNow,
            ProviderType = "待确定", // 将在AI提供商中更新
            AtList = ExtractAtListFromContent(callbackMessage.Data?.Content ?? "")
        };

        return context;
    }

    /// <summary>
    /// 确保昵称信息已获取
    /// </summary>
    private async Task EnsureNicknamesAsync(AiMessageContext context)
    {
        try
        {
            if (context.IsGroupMessage)
            {
                // 群消息：获取群成员昵称
                if (string.IsNullOrEmpty(context.FromGroupUserNickName) && 
                    !string.IsNullOrEmpty(context.FromGroup) && 
                    !string.IsNullOrEmpty(context.FromGroupUser))
                {
                    context.FromGroupUserNickName = await _contactNicknameService.GetGroupMemberNicknameAsync(
                        context.WxManagerId, context.FromGroup, context.FromGroupUser);
                }
            }
            else
            {
                // 私聊消息：获取联系人昵称
                if (string.IsNullOrEmpty(context.FromUserNickName) && !string.IsNullOrEmpty(context.FromUser))
                {
                    context.FromUserNickName = await _contactNicknameService.GetContactNicknameAsync(
                        context.WxManagerId, context.FromUser);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取用户昵称失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, IsGroup: {IsGroup}", 
                context.WxManagerId, context.FromUser, context.IsGroupMessage);
        }
    }

    /// <summary>
    /// 从消息内容中提取@用户列表
    /// </summary>
    private List<string> ExtractAtListFromContent(string content)
    {
        var atList = new List<string>();
        if (string.IsNullOrEmpty(content))
            return atList;

        // 使用正则表达式提取@用户
        var atPattern = @"@([^\s@]+)";
        var matches = System.Text.RegularExpressions.Regex.Matches(content, atPattern);

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            if (match.Groups.Count > 1)
            {
                atList.Add(match.Groups[1].Value);
            }
        }

        return atList;
    }
}
