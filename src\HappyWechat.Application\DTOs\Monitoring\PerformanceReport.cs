namespace HappyWechat.Application.DTOs.Monitoring;

/// <summary>
/// 性能报告
/// </summary>
public class PerformanceReport
{
    /// <summary>
    /// 报告生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; set; }
    
    /// <summary>
    /// 成功操作数
    /// </summary>
    public long SuccessfulOperations { get; set; }
    
    /// <summary>
    /// 失败操作数
    /// </summary>
    public long FailedOperations { get; set; }
    
    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }
    
    /// <summary>
    /// 最大响应时间（毫秒）
    /// </summary>
    public double MaxResponseTime { get; set; }
    
    /// <summary>
    /// 最小响应时间（毫秒）
    /// </summary>
    public double MinResponseTime { get; set; }
    
    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;
    
    /// <summary>
    /// 每秒操作数
    /// </summary>
    public double OperationsPerSecond { get; set; }
    
    /// <summary>
    /// 总内存使用量（字节）
    /// </summary>
    public long TotalMemoryUsage { get; set; }
    
    /// <summary>
    /// 操作报告列表
    /// </summary>
    public List<OperationReport> OperationReports { get; set; } = new();
    
    /// <summary>
    /// 按操作类型分组的指标
    /// </summary>
    public Dictionary<string, OperationMetrics> OperationMetrics { get; set; } = new();
    
    /// <summary>
    /// 错误统计
    /// </summary>
    public Dictionary<string, int> ErrorCounts { get; set; } = new();
    
    /// <summary>
    /// 资源使用情况
    /// </summary>
    public ResourceUsage ResourceUsage { get; set; } = new();
    
    /// <summary>
    /// 页面统计信息
    /// </summary>
    public List<PageStats> PageStats { get; set; } = new();
    
    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public List<CacheStats> CacheStats { get; set; } = new();
    
    /// <summary>
    /// 生成性能报告摘要
    /// </summary>
    public string GenerateSummary()
    {
        return $"Performance Summary: " +
               $"Total Operations: {TotalOperations}, " +
               $"Success Rate: {SuccessRate:F2}%, " +
               $"Average Response Time: {AverageResponseTime:F2}ms, " +
               $"Operations/sec: {OperationsPerSecond:F2}";
    }
}

/// <summary>
/// 操作报告
/// </summary>
public class OperationReport
{
    public string OperationName { get; set; } = string.Empty;
    public int TotalCalls { get; set; }
    public double AverageTime { get; set; }
    public double MinTime { get; set; }
    public double MaxTime { get; set; }
    public double MedianTime { get; set; }
    public double P95Time { get; set; }
    public double P99Time { get; set; }
    public int ErrorCount { get; set; }
    public DateTime FirstCallTime { get; set; }
    public DateTime LastCallTime { get; set; }
}

/// <summary>
/// 操作指标
/// </summary>
public class OperationMetrics
{
    /// <summary>
    /// 操作名称
    /// </summary>
    public string OperationName { get; set; } = string.Empty;
    
    /// <summary>
    /// 执行次数
    /// </summary>
    public long Count { get; set; }
    
    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageTime { get; set; }
    
    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public double MaxTime { get; set; }
    
    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public double MinTime { get; set; }
    
    /// <summary>
    /// 错误次数
    /// </summary>
    public long ErrorCount { get; set; }
    
    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRate => Count > 0 ? (double)(Count - ErrorCount) / Count * 100 : 0;
}

/// <summary>
/// 资源使用情况
/// </summary>
public class ResourceUsage
{
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsage { get; set; }
    
    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public double MemoryUsage { get; set; }
    
    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    public double AvailableMemory { get; set; }
    
    /// <summary>
    /// 磁盘使用率（百分比）
    /// </summary>
    public double DiskUsage { get; set; }
    
    /// <summary>
    /// 网络接收速率（KB/s）
    /// </summary>
    public double NetworkReceiveRate { get; set; }
    
    /// <summary>
    /// 网络发送速率（KB/s）
    /// </summary>
    public double NetworkSendRate { get; set; }
}

/// <summary>
/// 页面统计信息
/// </summary>
public class PageStats
{
    /// <summary>
    /// 页面名称
    /// </summary>
    public string PageName { get; set; } = string.Empty;
    
    /// <summary>
    /// 总加载次数
    /// </summary>
    public long TotalLoads { get; set; }
    
    /// <summary>
    /// 平均加载时间（毫秒）
    /// </summary>
    public double AverageLoadTimeMs { get; set; }
    
    /// <summary>
    /// 缓存命中率（百分比）
    /// </summary>
    public double CacheHitRate { get; set; }
    
    /// <summary>
    /// 最大加载时间（毫秒）
    /// </summary>
    public double MaxLoadTimeMs { get; set; }
    
    /// <summary>
    /// 最小加载时间（毫秒）
    /// </summary>
    public double MinLoadTimeMs { get; set; }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStats
{
    /// <summary>
    /// 缓存类型
    /// </summary>
    public string CacheType { get; set; } = string.Empty;
    
    /// <summary>
    /// 命中率（百分比）
    /// </summary>
    public double HitRate { get; set; }
    
    /// <summary>
    /// 总请求数
    /// </summary>
    public long TotalRequests { get; set; }
    
    /// <summary>
    /// 命中次数
    /// </summary>
    public long HitCount { get; set; }
    
    /// <summary>
    /// 未命中次数
    /// </summary>
    public long MissCount { get; set; }
    
    /// <summary>
    /// 平均访问时间（毫秒）
    /// </summary>
    public double AverageAccessTimeMs { get; set; }
}