执行工作流
执行已发布的工作流。​
接口说明​
此接口为非流式响应模式，对于支持流式输出的节点，应使用接口执行工作流（流式响应）获取流式响应。调用接口后，你可以从响应中获得 debug_url，访问链接即可通过可视化界面查看工作流的试运行过程，其中包含每个执行节点的输入输出等详细信息，帮助你在线调试或排障。​
扣子个人进阶版、团队版、企业版和专业版用户调用此接口时，支持通过 is_async 参数异步运行工作流，适用于工作流执行耗时较长，导致运行超时的情况。异步运行后可通过本接口返回的 execute_id 调用查询工作流异步执行结果API 获取工作流的执行结果。​
限制说明​
​
 限制项​
 说明 ​
工作流发布状态​
 必须为已发布。执行未发布的工作流会返回错误码 4200。 创建并发布工作流的操作可参考使用工作流。​
节点限制​
工作流中不能包含消息节点、开启了流式输出的结束节点、问答节点。​
关联智能体​
调用此 API 之前，应先在扣子平台中试运行此工作流，如果试运行时需要关联智能体，则调用此 API 执行工作流时，也需要指定智能体ID。通常情况下，执行存在数据库节点、变量节点等节点的工作流需要关联智能体。​
请求大小上限​
 20 MB，包括输入参数及运行期间产生的消息历史等所有相关数据。 ​
超时时间 ​
未开启工作流异步运行时，工作流整体超时时间为 10 分钟，建议执行时间控制在 5 分钟以内，否则不保障执行结果的准确性。 详细说明可参考工作流使用限制。​
开启工作流异步运行后，工作流整体超时时间为 24 小时。​
​
基础信息​
​
请求方式​
POST​
请求地址​
​
https://api.coze.cn/v1/workflow/run​
​
权限​
run​
确保调用该接口使用的个人令牌开通了 run 权限，详细信息参考鉴权方式。​
接口说明​
执行已发布的工作流。​
​
请求参数​
Header​
​
参数​
取值​
说明​
Authorization​
Bearer $Access_Token​
用于验证客户端身份的访问令牌。你可以在扣子平台中生成访问令牌，详细信息，参考准备工作。​
Content-Type​
application/json​
解释请求正文的方式。​
​
Body​
​
参数​
类型​
是否必选​
示例​
说明​
workflow_id​
String​
必选​
73664689170551*****​
待执行的 Workflow ID，此工作流应已发布。​
进入 Workflow 编排页面，在页面 URL 中，workflow 参数后的数字就是 Workflow ID。例如 https://www.coze.com/work_flow?space_id=42463***&workflow_id=73505836754923***，Workflow ID 为 73505836754923***。​
parameters​
String​
可选​
{​
"user_id":"12345",​
"user_name":"George"​
}​
工作流开始节点的输入参数及取值，你可以在指定工作流的编排页面查看参数列表。​
如果工作流输入参数为 Image 等类型的文件，可以调用上传文件 API 获取 file_id，在调用此 API 时，在 parameters 中以序列化之后的 JSON 格式传入 file_id。例如 “parameters” : { "input": "{\"file_id\": \"xxxxx\"}" }。​
bot_id​
String​
可选​
73428668*****​
需要关联的智能体 ID。 部分工作流执行时需要指定关联的智能体，例如存在数据库节点、变量节点等节点的工作流。​
​
​​

图片​
​
进入智能体的开发页面，开发页面 URL 中 bot 参数后的数字就是智能体t ID。例如 https://www.coze.com/space/341****/bot/73428668*****，智能体 ID 为 73428668*****。​
确保调用该接口使用的令牌开通了此智能体所在空间的权限。​
确保该智能体已发布为 API 服务。​
​
ext​
JSON Map​
可选​
​
用于指定一些额外的字段，以 Map[String][String] 格式传入。例如某些插件 会隐式用到的经纬度等字段。​
目前仅支持以下字段：​
latitude：String 类型，表示经度。​
longitude：String 类型，表示纬度。​
user_id：String 类型，表示用户 ID。​
is_async​
Boolean​
可选​
true​
是否异步运行。异步运行后可通过本接口返回的 execute_id 调用查询工作流异步执行结果API 获取工作流的最终执行结果。​
true：异步运行。​
false：（默认）同步运行。​
异步运行的参数 is_async 仅限扣子个人进阶版、团队版、企业版和专业版使用，否则调用此接口会报错 6003 Workflow execution with is_async=true is a premium feature available only to Coze Professional users。​
​
app_id​
String​
可选​
749081945898306****​
该工作流关联的应用的 ID​
​
返回参数​
​
参数​
类型​
示例​
说明​
code​
Long​
0​
调用状态码。​
0 表示调用成功。​
其他值表示调用失败。你可以通过 msg 字段判断详细的错误原因。​
msg​
String​
Success​
状态信息。API 调用失败时可通过此字段查看详细错误信息。​
data​
String​
​
工作流执行结果，通常为 JSON 序列化字符串，部分场景下可能返回非 JSON 结构的字符串。​
execute_id​
String​
741364789030728****​
异步执行的事件 ID。​
token​
Long​
​
预留字段，无需关注。​
cost​
String​
0​
预留字段，无需关注。​
debug_url​
String​
https://www.coze.cn/work_flow?execute_id=741364789030728****&space_id=736142423532160****&workflow_id=738958910358870****​
工作流试运行调试页面。访问此页面可查看每个工作流节点的运行结果、输入输出等信息。​
detail​
Object of ResponseDetail​
​
返回的详情。​
​
ResponseDetail​
​
参数​
类型​
示例​
说明​
logid​
String​
20241210152726467C48D89D6DB2****​
本次请求的日志 ID。如果遇到异常报错场景，且反复重试仍然报错，可以根据此 logid 及错误码联系扣子团队获取帮助。详细说明可参考获取帮助和技术支持。​
​
示例​
请求示例​
​
curl --location --request POST 'https://api.coze.cn/v1/workflow/run' \​
--header 'Authorization: Bearer pat_hfwkehfncaf****' \​
--header 'Content-Type: application/json' \​
--data-raw '{​
    "workflow_id": "73664689170551*****",​
    "parameters": {​
        "user_id":"12345",​
        "user_name":"George"​
    }​
}'​
​
​
返回示例​
​
{​
    "code": 0,​
    "cost": "0",​
    "data": "{\"output\":\"北京的经度为116.4074°E，纬度为39.9042°N。\"}",​
    "debug_url": "https://www.coze.cn/work_flow?execute_id=741364789030728****&space_id=736142423532160****&workflow_id=738958910358870****",​
    "msg": "Success",​
    "token": 98​
}​
​
​
​
错误码​
如果成功调用扣子的 API，返回信息中 code 字段为 0。如果状态码为其他值，则表示接口调用失败。此时 msg 字段中包含详细错误信息，你可以参考错误码文档查看对应的解决方法。