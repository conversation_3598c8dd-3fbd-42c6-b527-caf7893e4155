using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.MessageProcessing.Handlers;

/// <summary>
/// 流式AI响应处理器 - 简化版本
/// 专注于AI响应的智能拆分和内容解析，不涉及队列操作
/// </summary>
public interface IStreamingAiResponseHandler
{
    /// <summary>
    /// 解析AI响应内容，拆分为混合内容项
    /// </summary>
    List<MixedContentItem> ParseAiResponse(string aiResponse);

    /// <summary>
    /// 拆分长文本为多个片段
    /// </summary>
    List<string> SplitLongText(string text, int maxLength = 500);
}

public class StreamingAiResponseHandler : IStreamingAiResponseHandler
{
    private readonly IMarkdownProcessor _markdownProcessor;
    private readonly ILogger<StreamingAiResponseHandler> _logger;

    // 增强的图文混排内容识别正则
    private static readonly Regex ImageUrlRegex = new(@"图片链接[：:]\s*(https?://[^\s\n]+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex FileUrlRegex = new(@"文件链接[：:]\s*(https?://[^\s\n]+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex MarkdownImageRegex = new(@"!\[.*?\]\((https?://[^\s\)]+)\)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex HtmlImageRegex = new(@"<img[^>]+src=[""']([^""']+)[""'][^>]*>", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex DirectImageUrlRegex = new(@"(https?://[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s]*)?)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex CozeImageRegex = new(@"(https?://s\.coze\.cn/[^\s]+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public StreamingAiResponseHandler(
        IMarkdownProcessor markdownProcessor,
        ILogger<StreamingAiResponseHandler> logger)
    {
        _markdownProcessor = markdownProcessor;
        _logger = logger;
    }

    /// <summary>
    /// 解析AI响应内容，拆分为混合内容项
    /// </summary>
    public List<MixedContentItem> ParseAiResponse(string aiResponse)
    {
        try
        {
            _logger.LogDebug("🔍 开始解析AI响应 - Length: {Length}", aiResponse.Length);

            // 1. Markdown过滤
            var filteredContent = _markdownProcessor.ProcessMarkdownAsync(aiResponse).Result;
            _logger.LogDebug("📝 Markdown过滤完成 - 原长度: {Original}, 过滤后: {Filtered}",
                aiResponse.Length, filteredContent.Length);

            // 2. 解析混合内容
            var mixedContent = ParseMixedContent(filteredContent);
            _logger.LogDebug("✅ AI响应解析完成 - 拆分为: {Count}个片段", mixedContent.Count);

            return mixedContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ AI响应解析异常");
            // 异常时返回原始文本作为单个文本项
            return new List<MixedContentItem>
            {
                new MixedContentItem
                {
                    Type = "text",
                    Content = aiResponse,
                    Order = 1
                }
            };
        }
    }

    /// <summary>
    /// 拆分长文本为多个片段
    /// </summary>
    public List<string> SplitLongText(string text, int maxLength = 500)
    {
        if (string.IsNullOrWhiteSpace(text))
            return new List<string>();

        if (text.Length <= maxLength)
            return new List<string> { text };

        var segments = new List<string>();
        var remaining = text;

        while (remaining.Length > maxLength)
        {
            var splitIndex = FindBestSplitPoint(remaining, maxLength);
            if (splitIndex <= 0)
                splitIndex = maxLength;

            var segment = remaining.Substring(0, splitIndex).Trim();
            if (!string.IsNullOrEmpty(segment))
                segments.Add(segment);

            remaining = remaining.Substring(splitIndex).Trim();
        }

        if (!string.IsNullOrEmpty(remaining))
            segments.Add(remaining);

        return segments;
    }
    
    /// <summary>
    /// 解析混合内容（文本、图片、文件等） - 增强版本
    /// </summary>
    private List<MixedContentItem> ParseMixedContent(string content)
    {
        var items = new List<MixedContentItem>();
        var currentPosition = 0;

        // 查找所有媒体内容的位置
        var mediaMatches = new List<(int Start, int End, string Type, string Url, string OriginalText)>();

        // 1. 查找"图片链接："格式
        foreach (Match match in ImageUrlRegex.Matches(content))
        {
            mediaMatches.Add((match.Index, match.Index + match.Length, "image", match.Groups[1].Value, match.Value));
        }

        // 2. 查找"文件链接："格式
        foreach (Match match in FileUrlRegex.Matches(content))
        {
            mediaMatches.Add((match.Index, match.Index + match.Length, "file", match.Groups[1].Value, match.Value));
        }

        // 3. 查找Markdown图片格式
        foreach (Match match in MarkdownImageRegex.Matches(content))
        {
            mediaMatches.Add((match.Index, match.Index + match.Length, "image", match.Groups[1].Value, match.Value));
        }

        // 4. 查找HTML图片格式
        foreach (Match match in HtmlImageRegex.Matches(content))
        {
            mediaMatches.Add((match.Index, match.Index + match.Length, "image", match.Groups[1].Value, match.Value));
        }

        // 5. 查找Coze平台图片
        foreach (Match match in CozeImageRegex.Matches(content))
        {
            mediaMatches.Add((match.Index, match.Index + match.Length, "image", match.Groups[1].Value, match.Value));
        }

        // 6. 查找直接的图片URL
        foreach (Match match in DirectImageUrlRegex.Matches(content))
        {
            // 避免重复匹配已经被其他规则匹配的内容
            if (!mediaMatches.Any(m => m.Start <= match.Index && match.Index < m.End))
            {
                mediaMatches.Add((match.Index, match.Index + match.Length, "image", match.Groups[1].Value, match.Value));
            }
        }
        
        // 按位置排序
        mediaMatches = mediaMatches.OrderBy(m => m.Start).ToList();
        
        // 拆分内容
        foreach (var media in mediaMatches)
        {
            // 添加媒体前的文本
            if (media.Start > currentPosition)
            {
                var textContent = content.Substring(currentPosition, media.Start - currentPosition).Trim();
                if (!string.IsNullOrEmpty(textContent))
                {
                    items.Add(new MixedContentItem
                    {
                        Type = "text",
                        Content = textContent,
                        Order = items.Count + 1
                    });
                }
            }
            
            // 添加媒体内容
            items.Add(new MixedContentItem
            {
                Type = media.Type,
                Content = media.Url,
                Order = items.Count + 1
            });
            
            currentPosition = media.End;
        }
        
        // 添加剩余文本
        if (currentPosition < content.Length)
        {
            var remainingText = content.Substring(currentPosition).Trim();
            if (!string.IsNullOrEmpty(remainingText))
            {
                items.Add(new MixedContentItem
                {
                    Type = "text",
                    Content = remainingText,
                    Order = items.Count + 1
                });
            }
        }
        
        // 如果没有找到任何媒体内容，整个内容作为文本处理
        if (!items.Any())
        {
            items.Add(new MixedContentItem
            {
                Type = "text",
                Content = content.Trim(),
                Order = 1
            });
        }
        
        return items;
    }

    /// <summary>
    /// 查找最佳分割点
    /// </summary>
    private int FindBestSplitPoint(string text, int maxLength)
    {
        if (text.Length <= maxLength)
            return text.Length;

        // 优先在句号、问号、感叹号处分割
        var sentenceEnders = new[] { '。', '？', '！', '.', '?', '!' };
        for (int i = maxLength - 1; i >= maxLength / 2; i--)
        {
            if (sentenceEnders.Contains(text[i]))
                return i + 1;
        }

        // 其次在逗号、分号处分割
        var clauseEnders = new[] { '，', '；', ',', ';' };
        for (int i = maxLength - 1; i >= maxLength / 2; i--)
        {
            if (clauseEnders.Contains(text[i]))
                return i + 1;
        }

        // 最后在空格处分割
        for (int i = maxLength - 1; i >= maxLength / 2; i--)
        {
            if (char.IsWhiteSpace(text[i]))
                return i;
        }

        // 如果找不到合适的分割点，就在最大长度处强制分割
        return maxLength;
    }
}

/// <summary>
/// 混合内容项
/// </summary>
public class MixedContentItem
{
    public string Type { get; set; } = string.Empty; // text, image, file
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
}
