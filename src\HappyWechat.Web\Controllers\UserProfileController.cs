using HappyWechat.Application.Commons;
using HappyWechat.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace HappyWechat.Web.Controllers;

/// <summary>
/// 用户资料控制器
/// 展示如何使用BaseApiController进行更灵活的用户上下文处理
/// </summary>
[Route("api/user-profile")]
public class UserProfileController : BaseApiController
{
    private readonly IUserProfileService _userProfileService;

    public UserProfileController(
        ICurrentUserContext userContext,
        IUserProfileService userProfileService,
        ILogger<UserProfileController> logger) : base(userContext, logger)
    {
        _userProfileService = userProfileService;
    }

    /// <summary>
    /// 获取当前用户资料
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<dynamic>>> GetProfile()
    {
        try
        {
            var authResult = EnsureAuthenticated<object>();
            if (authResult != null) return authResult;

            LogApiOperation("GetUserProfile");

            var userProfile = new
            {
                UserId = CurrentUserId,
                Username = CurrentUsername,
                DisplayName = CurrentUserDisplayName,
                Roles = UserContext.Roles,
                IsAuthenticated = IsUserAuthenticated
            };

            Logger.LogInformation("用户获取个人资料成功 - UserId: {UserId}, Username: {Username}", 
                CurrentUserId, CurrentUsername);

            return Success((object)userProfile, "获取用户资料成功");
        }
        catch (Exception ex)
        {
            return HandleException<dynamic>(ex, "获取用户资料失败");
        }
    }

    /// <summary>
    /// 更新用户显示名称
    /// </summary>
    [HttpPut("display-name")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDisplayName([FromBody] string newDisplayName)
    {
        try
        {
            var authResult = EnsureAuthenticated<string>();
            if (authResult != null) return authResult;

            if (string.IsNullOrWhiteSpace(newDisplayName))
            {
                return Failure<string>("显示名称不能为空");
            }

            LogApiOperation("UpdateDisplayName", new { NewDisplayName = newDisplayName });

            // 这里调用实际的用户服务更新显示名称
            // await _userService.UpdateDisplayNameAsync(CurrentUserId, newDisplayName);

            Logger.LogInformation("用户更新显示名称成功 - UserId: {UserId}, NewDisplayName: {DisplayName}", 
                CurrentUserId, newDisplayName);

            return Success(newDisplayName, "显示名称更新成功");
        }
        catch (Exception ex)
        {
            return HandleException<string>(ex, "更新显示名称失败");
        }
    }

    /// <summary>
    /// 获取用户权限信息 - 管理员专用
    /// </summary>
    [HttpGet("permissions")]
    public async Task<ActionResult<ApiResponse<dynamic>>> GetPermissions()
    {
        try
        {
            var roleResult = EnsureRole<object>("Admin");
            if (roleResult != null) return roleResult;

            LogApiOperation("GetUserPermissions");

            var permissions = new
            {
                UserId = CurrentUserId,
                Username = CurrentUsername,
                Roles = UserContext.Roles,
                HasAdminRole = UserContext.HasRole("Admin"),
                HasUserRole = UserContext.HasRole("User"),
                HasAnyManagementRole = UserContext.HasAnyRole("Admin", "Manager"),
                CanManageUsers = UserContext.HasAllRoles("Admin", "UserManager")
            };

            Logger.LogInformation("管理员获取用户权限信息成功 - UserId: {UserId}", CurrentUserId);

            return Success((object)permissions, "获取权限信息成功");
        }
        catch (Exception ex)
        {
            return HandleException<dynamic>(ex, "获取权限信息失败");
        }
    }

    /// <summary>
    /// 验证用户角色 - 演示手动验证
    /// </summary>
    [HttpPost("validate-role")]
    public ActionResult<ApiResponse<bool>> ValidateRole([FromBody] string role)
    {
        try
        {
            var authResult = EnsureAuthenticated<bool>();
            if (authResult != null) return authResult;

            if (string.IsNullOrWhiteSpace(role))
            {
                return Failure<bool>("角色名称不能为空");
            }

            LogApiOperation("ValidateUserRole", new { Role = role });

            var hasRole = UserContext.HasRole(role);

            Logger.LogInformation("用户角色验证 - UserId: {UserId}, Role: {Role}, HasRole: {HasRole}", 
                CurrentUserId, role, hasRole);

            return Success(hasRole, $"用户{(hasRole ? "拥有" : "没有")}角色 '{role}'");
        }
        catch (Exception ex)
        {
            return HandleException<bool>(ex, "验证用户角色失败");
        }
    }

    /// <summary>
    /// 获取当前会话信息
    /// </summary>
    [HttpGet("session")]
    public ActionResult<ApiResponse<dynamic>> GetSessionInfo()
    {
        try
        {
            // 这个端点不强制要求认证，可以用于调试
            var sessionInfo = new
            {
                IsAuthenticated = IsUserAuthenticated,
                UserId = IsUserAuthenticated ? CurrentUserId : (Guid?)null,
                Username = CurrentUsername,
                DisplayName = CurrentUserDisplayName,
                Roles = UserContext.Roles,
                RoleCount = UserContext.Roles.Count,
                HasAnyRole = UserContext.Roles.Any(),
                Timestamp = DateTime.UtcNow
            };

            LogApiOperation("GetSessionInfo");

            return Success((object)sessionInfo, "获取会话信息成功");
        }
        catch (Exception ex)
        {
            return HandleException<dynamic>(ex, "获取会话信息失败");
        }
    }
}