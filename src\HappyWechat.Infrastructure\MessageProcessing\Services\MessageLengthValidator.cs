using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Infrastructure.Configuration;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息长度验证服务接口
/// </summary>
public interface IMessageLengthValidator
{
    /// <summary>
    /// 验证并处理消息长度
    /// </summary>
    Task<MessageLengthValidationResult> ValidateAndProcessAsync(string content, string context = "");
    
    /// <summary>
    /// 检查消息是否超长
    /// </summary>
    Task<bool> IsMessageTooLongAsync(string content);
    
    /// <summary>
    /// 分割超长消息
    /// </summary>
    Task<List<string>> SplitLongMessageAsync(string content);
}

/// <summary>
/// 消息长度验证结果
/// </summary>
public class MessageLengthValidationResult
{
    /// <summary>
    /// 是否需要分割
    /// </summary>
    public bool NeedsSplit { get; set; }
    
    /// <summary>
    /// 原始消息
    /// </summary>
    public string OriginalMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 处理后的消息列表
    /// </summary>
    public List<string> ProcessedMessages { get; set; } = new();
    
    /// <summary>
    /// 最大长度限制
    /// </summary>
    public int MaxLength { get; set; }
    
    /// <summary>
    /// 原始长度
    /// </summary>
    public int OriginalLength { get; set; }
    
    /// <summary>
    /// 处理上下文
    /// </summary>
    public string Context { get; set; } = string.Empty;
}

/// <summary>
/// 消息长度验证服务实现
/// </summary>
public class MessageLengthValidator : IMessageLengthValidator
{
    private readonly IUnifiedConfigurationManager _configManager;
    private readonly ILogger<MessageLengthValidator> _logger;
    
    // 默认配置
    private const int DEFAULT_MAX_LENGTH = 2000;
    private const int MIN_SPLIT_LENGTH = 100;
    private const string SPLIT_INDICATOR = "...";

    public MessageLengthValidator(
        IUnifiedConfigurationManager configManager,
        ILogger<MessageLengthValidator> logger)
    {
        _configManager = configManager;
        _logger = logger;
    }

    public async Task<MessageLengthValidationResult> ValidateAndProcessAsync(string content, string context = "")
    {
        if (string.IsNullOrEmpty(content))
        {
            return new MessageLengthValidationResult
            {
                NeedsSplit = false,
                OriginalMessage = content,
                ProcessedMessages = new List<string> { content },
                OriginalLength = 0,
                Context = context
            };
        }

        try
        {
            // 获取配置
            var config = await GetMessageLengthConfigAsync();

            // 检查是否启用消息长度检查
            if (!config.EnableMessageLengthCheck)
            {
                _logger.LogDebug("消息长度检查未启用 - 上下文: {Context}", context);
                return new MessageLengthValidationResult
                {
                    NeedsSplit = false,
                    OriginalMessage = content,
                    ProcessedMessages = new List<string> { content },
                    OriginalLength = content.Length,
                    Context = context
                };
            }

            // 使用新的配置字段，如果为0则使用旧字段作为后备
            var maxLength = config.MessageLengthLimit > 0 ? config.MessageLengthLimit :
                           (config.MaxContentLength > 0 ? config.MaxContentLength : DEFAULT_MAX_LENGTH);

            var result = new MessageLengthValidationResult
            {
                OriginalMessage = content,
                MaxLength = maxLength,
                OriginalLength = content.Length,
                Context = context
            };

            // 检查是否需要分割
            if (content.Length <= maxLength)
            {
                result.NeedsSplit = false;
                result.ProcessedMessages = new List<string> { content };
                
                _logger.LogDebug("消息长度检查通过 - 长度: {Length}, 限制: {MaxLength}, 上下文: {Context}", 
                    content.Length, maxLength, context);
            }
            else
            {
                result.NeedsSplit = true;
                result.ProcessedMessages = await SplitMessageInternalAsync(content, maxLength);
                
                _logger.LogInformation("消息超长已分割 - 原长度: {OriginalLength}, 限制: {MaxLength}, 分割数: {SplitCount}, 上下文: {Context}", 
                    content.Length, maxLength, result.ProcessedMessages.Count, context);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息长度验证异常 - 上下文: {Context}", context);
            
            // 异常情况下返回原消息
            return new MessageLengthValidationResult
            {
                NeedsSplit = false,
                OriginalMessage = content,
                ProcessedMessages = new List<string> { content },
                OriginalLength = content.Length,
                Context = context
            };
        }
    }

    public async Task<bool> IsMessageTooLongAsync(string content)
    {
        if (string.IsNullOrEmpty(content))
        {
            return false;
        }

        try
        {
            var config = await GetMessageLengthConfigAsync();

            // 如果未启用长度检查，直接返回false
            if (!config.EnableMessageLengthCheck)
            {
                return false;
            }

            // 使用新的配置字段
            var maxLength = config.MessageLengthLimit > 0 ? config.MessageLengthLimit :
                           (config.MaxContentLength > 0 ? config.MaxContentLength : DEFAULT_MAX_LENGTH);
            
            return content.Length > maxLength;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查消息长度异常");
            return false;
        }
    }

    public async Task<List<string>> SplitLongMessageAsync(string content)
    {
        if (string.IsNullOrEmpty(content))
        {
            return new List<string> { content };
        }

        try
        {
            var config = await GetMessageLengthConfigAsync();

            // 如果未启用长度检查，直接返回原消息
            if (!config.EnableMessageLengthCheck)
            {
                return new List<string> { content };
            }

            // 使用新的配置字段
            var maxLength = config.MessageLengthLimit > 0 ? config.MessageLengthLimit :
                           (config.MaxContentLength > 0 ? config.MaxContentLength : DEFAULT_MAX_LENGTH);
            
            if (content.Length <= maxLength)
            {
                return new List<string> { content };
            }

            return await SplitMessageInternalAsync(content, maxLength);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分割消息异常");
            return new List<string> { content };
        }
    }

    /// <summary>
    /// 获取消息长度配置
    /// </summary>
    private async Task<EYunRiskControlConfigDto> GetMessageLengthConfigAsync()
    {
        return await _configManager.GetConfigAsync<EYunRiskControlConfigDto>(
            "EYunRiskControl",
            "EYunRiskControlConfig",
            () => new EYunRiskControlConfigDto
            {
                MaxContentLength = DEFAULT_MAX_LENGTH
            });
    }

    /// <summary>
    /// 内部消息分割实现
    /// </summary>
    private async Task<List<string>> SplitMessageInternalAsync(string content, int maxLength)
    {
        var result = new List<string>();
        
        if (string.IsNullOrEmpty(content) || maxLength <= 0)
        {
            result.Add(content);
            return result;
        }

        try
        {
            // 计算实际可用长度（考虑分割指示符）
            var availableLength = maxLength - SPLIT_INDICATOR.Length;
            
            if (availableLength < MIN_SPLIT_LENGTH)
            {
                // 如果可用长度太小，直接返回原消息
                _logger.LogWarning("最大长度设置过小，无法有效分割 - MaxLength: {MaxLength}", maxLength);
                result.Add(content);
                return result;
            }

            var currentIndex = 0;
            var totalLength = content.Length;
            var partIndex = 1;

            while (currentIndex < totalLength)
            {
                var remainingLength = totalLength - currentIndex;
                var currentPartLength = Math.Min(availableLength, remainingLength);
                
                // 尝试在合适的位置分割（避免在单词中间分割）
                var splitPosition = FindBestSplitPosition(content, currentIndex, currentPartLength);
                
                var part = content.Substring(currentIndex, splitPosition);
                
                // 如果不是最后一部分，添加分割指示符
                if (currentIndex + splitPosition < totalLength)
                {
                    part += SPLIT_INDICATOR;
                }

                result.Add(part);
                currentIndex += splitPosition;
                partIndex++;

                _logger.LogDebug("消息分割 - 第{PartIndex}部分，长度: {PartLength}", partIndex - 1, part.Length);
            }

            _logger.LogInformation("消息分割完成 - 原长度: {OriginalLength}, 分割为: {PartCount}部分", 
                totalLength, result.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息分割内部异常");
            result.Clear();
            result.Add(content);
            return result;
        }
    }

    /// <summary>
    /// 找到最佳分割位置（尽量在句子或段落边界）
    /// </summary>
    private int FindBestSplitPosition(string content, int startIndex, int maxLength)
    {
        if (startIndex + maxLength >= content.Length)
        {
            return content.Length - startIndex;
        }

        var endIndex = startIndex + maxLength;
        var searchStart = Math.Max(startIndex, endIndex - 100); // 在最后100个字符中寻找合适的分割点

        // 优先级：句号 > 感叹号 > 问号 > 换行符 > 逗号 > 空格
        var splitChars = new char[] { '。', '！', '？', '\n', '，', ' ', '、', '；' };

        foreach (var splitChar in splitChars)
        {
            for (int i = endIndex - 1; i >= searchStart; i--)
            {
                if (content[i] == splitChar)
                {
                    return i - startIndex + 1;
                }
            }
        }

        // 如果找不到合适的分割点，就在最大长度处分割
        return maxLength;
    }
}
