using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// HTTP上下文SessionId获取策略实现
/// 处理Header、<PERSON><PERSON>、查询参数等来源
/// </summary>
public class HttpContextSessionIdStrategy : SessionIdRetrievalStrategy
{
    private readonly IRedisAuthenticationService _authService;

    public HttpContextSessionIdStrategy(
        IRedisAuthenticationService authService,
        ILogger<HttpContextSessionIdStrategy> logger) : base(logger)
    {
        _authService = authService;
    }

    public override SessionIdRetrievalStrategyType StrategyType => SessionIdRetrievalStrategyType.HttpContext;

    public override int Priority => 2;

    public override string StrategyName => "HTTP上下文策略";

    public override Task<bool> IsAvailableAsync(HttpContext? httpContext = null)
    {
        var isAvailable = httpContext != null;
        if (!isAvailable)
        {
            LogUnavailable("HTTP上下文为空");
        }
        return Task.FromResult(isAvailable);
    }

    public override async Task<string?> GetSessionIdAsync(HttpContext? httpContext = null)
    {
        if (httpContext == null)
        {
            LogFailure("HTTP上下文为空");
            return null;
        }

        return await SafeExecuteAsync(async () =>
        {
            // 使用现有的RedisAuthenticationService获取SessionId
            // 它已经实现了完整的多源获取逻辑（Header、Cookie、查询参数）
            var sessionId = _authService.GetSessionIdFromContext(httpContext);
            
            if (!string.IsNullOrEmpty(sessionId))
            {
                LogSuccess(sessionId);
                return sessionId;
            }

            LogFailure("HTTP上下文中未找到SessionId");
            return null;
        }, null, "HTTP上下文SessionId获取");
    }

    public override async Task<bool> ValidateSessionIdAsync(string sessionId)
    {
        if (!await base.ValidateSessionIdAsync(sessionId))
        {
            return false;
        }

        return await SafeExecuteAsync(async () =>
        {
            // 使用认证服务验证SessionId
            var authState = await _authService.GetAuthenticationStateAsync(sessionId);
            var isValid = authState?.User?.Identity?.IsAuthenticated == true;
            
            if (isValid)
            {
                Logger.LogDebug("✅ HTTP上下文策略 - SessionId验证成功: {SessionId}", 
                    sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
            }
            else
            {
                Logger.LogDebug("❌ HTTP上下文策略 - SessionId验证失败: {SessionId}", 
                    sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
            }

            return isValid;
        }, false, "SessionId验证");
    }

    public override string GetDescription()
    {
        return $"{base.GetDescription()} - 从HTTP请求的Header、Cookie、查询参数中获取SessionId";
    }
}

/// <summary>
/// 内存缓存SessionId获取策略实现
/// 最高优先级，用于缓存已获取的SessionId
/// </summary>
public class MemoryCacheSessionIdStrategy : SessionIdRetrievalStrategy
{
    private string? _cachedSessionId;
    private DateTime? _cacheTime;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);

    public MemoryCacheSessionIdStrategy(ILogger<MemoryCacheSessionIdStrategy> logger) : base(logger)
    {
    }

    public override SessionIdRetrievalStrategyType StrategyType => SessionIdRetrievalStrategyType.MemoryCache;

    public override int Priority => 1;

    public override string StrategyName => "内存缓存策略";

    public override Task<bool> IsAvailableAsync(HttpContext? httpContext = null)
    {
        var isAvailable = !string.IsNullOrEmpty(_cachedSessionId) && 
                         _cacheTime.HasValue && 
                         DateTime.UtcNow - _cacheTime.Value < _cacheExpiration;

        if (!isAvailable)
        {
            LogUnavailable(_cachedSessionId == null ? "无缓存数据" : "缓存已过期");
        }

        return Task.FromResult(isAvailable);
    }

    public override Task<string?> GetSessionIdAsync(HttpContext? httpContext = null)
    {
        if (string.IsNullOrEmpty(_cachedSessionId))
        {
            LogFailure("缓存中无SessionId");
            return Task.FromResult<string?>(null);
        }

        if (!_cacheTime.HasValue || DateTime.UtcNow - _cacheTime.Value >= _cacheExpiration)
        {
            LogFailure("缓存已过期");
            ClearCache();
            return Task.FromResult<string?>(null);
        }

        LogSuccess(_cachedSessionId);
        return Task.FromResult<string?>(_cachedSessionId);
    }

    /// <summary>
    /// 设置缓存
    /// </summary>
    /// <param name="sessionId">SessionId</param>
    public void SetCache(string sessionId)
    {
        if (!string.IsNullOrEmpty(sessionId))
        {
            _cachedSessionId = sessionId;
            _cacheTime = DateTime.UtcNow;
            Logger.LogDebug("✅ 内存缓存策略 - 缓存SessionId: {SessionId}", 
                sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
        }
    }

    /// <summary>
    /// 清除缓存
    /// </summary>
    public void ClearCache()
    {
        _cachedSessionId = null;
        _cacheTime = null;
        Logger.LogDebug("🗑️ 内存缓存策略 - 缓存已清除");
    }

    /// <summary>
    /// 获取缓存状态
    /// </summary>
    /// <returns>缓存状态信息</returns>
    public (string? SessionId, DateTime? CacheTime, bool IsExpired) GetCacheStatus()
    {
        var isExpired = !_cacheTime.HasValue || DateTime.UtcNow - _cacheTime.Value >= _cacheExpiration;
        return (_cachedSessionId, _cacheTime, isExpired);
    }

    public override string GetDescription()
    {
        var (sessionId, cacheTime, isExpired) = GetCacheStatus();
        var status = sessionId == null ? "无缓存" : isExpired ? "已过期" : "有效";
        return $"{base.GetDescription()} - 缓存状态: {status}";
    }
}
