using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageProcessing.Coordination;

namespace HappyWechat.Infrastructure.MessageProcessing.GroupMode;

/// <summary>
/// 群聊模式专用处理器
/// 专门处理群聊的两种模式："回复所有消息"和"仅@后回复"
/// </summary>
public interface IGroupModeProcessor
{
    /// <summary>
    /// 处理群聊消息
    /// </summary>
    Task<GroupModeProcessResult> ProcessGroupMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取群聊模式
    /// </summary>
    Task<GroupChatMode> GetGroupModeAsync(string groupId, Guid wxManagerId);
}

/// <summary>
/// 群聊模式
/// </summary>
public enum GroupChatMode
{
    /// <summary>
    /// 回复所有消息
    /// </summary>
    ReplyToAll,
    
    /// <summary>
    /// 仅@后回复
    /// </summary>
    OnlyReplyWhenMentioned
}

/// <summary>
/// 群聊模式处理结果
/// </summary>
public class GroupModeProcessResult
{
    public bool ShouldProcess { get; set; }
    public GroupChatMode Mode { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool RequiresCoordination { get; set; }
    public WxCallbackMessageDto? ProcessedMessage { get; set; }
}

public class GroupModeProcessor : IGroupModeProcessor
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IMessageCoordinator _messageCoordinator;
    private readonly ILogger<GroupModeProcessor> _logger;
    
    public GroupModeProcessor(
        IServiceProvider serviceProvider,
        IMessageCoordinator messageCoordinator,
        ILogger<GroupModeProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _messageCoordinator = messageCoordinator;
        _logger = logger;
    }
    
    /// <summary>
    /// 处理群聊消息
    /// </summary>
    public async Task<GroupModeProcessResult> ProcessGroupMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken = default)
    {
        var messageType = message.MessageType;
        var fromGroup = message.Data?.FromGroup;
        var fromUser = message.Data?.FromUser;
        
        if (string.IsNullOrEmpty(fromGroup))
        {
            return new GroupModeProcessResult
            {
                ShouldProcess = false,
                Reason = "非群聊消息"
            };
        }
        
        _logger.LogInformation("[{ProcessingId}] 🏢 开始群聊模式处理 - Group: {Group}, MessageType: {MessageType}",
            processingId, fromGroup, messageType);
        
        try
        {
            // 1. 获取群聊模式
            var wxManagerId = GetWxManagerIdFromMessage(message);
            var groupMode = await GetGroupModeAsync(fromGroup, wxManagerId);
            
            // 2. 根据模式和消息类型处理
            return groupMode switch
            {
                GroupChatMode.ReplyToAll => await ProcessReplyToAllModeAsync(message, processingId, cancellationToken),
                GroupChatMode.OnlyReplyWhenMentioned => await ProcessOnlyMentionedModeAsync(message, processingId, cancellationToken),
                _ => new GroupModeProcessResult { ShouldProcess = false, Reason = "未知群聊模式" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群聊模式处理异常", processingId);
            return new GroupModeProcessResult
            {
                ShouldProcess = false,
                Reason = $"处理异常: {ex.Message}"
            };
        }
    }
    
    /// <summary>
    /// 处理"回复所有消息"模式
    /// 支持：80001（文本）、80002（图片）、80004（语音）、80009（文件）
    /// </summary>
    private async Task<GroupModeProcessResult> ProcessReplyToAllModeAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken)
    {
        var messageType = message.MessageType;
        
        _logger.LogDebug("[{ProcessingId}] 📢 处理'回复所有消息'模式 - MessageType: {MessageType}", processingId, messageType);
        
        // 所有支持的消息类型都直接处理
        var supportedTypes = new[]
        {
            MessageTypeConstants.GROUP_TEXT,   // 80001
            MessageTypeConstants.GROUP_IMAGE,  // 80002
            MessageTypeConstants.GROUP_VOICE,  // 80004
            MessageTypeConstants.GROUP_FILE    // 80009
        };
        
        if (!supportedTypes.Contains(messageType))
        {
            return new GroupModeProcessResult
            {
                ShouldProcess = false,
                Mode = GroupChatMode.ReplyToAll,
                Reason = $"不支持的消息类型: {messageType}"
            };
        }
        
        return new GroupModeProcessResult
        {
            ShouldProcess = true,
            Mode = GroupChatMode.ReplyToAll,
            Reason = $"回复所有消息模式，直接处理: {messageType}",
            RequiresCoordination = false,
            ProcessedMessage = message
        };
    }
    
    /// <summary>
    /// 处理"仅@后回复"模式
    /// 复杂的消息组合逻辑
    /// </summary>
    private async Task<GroupModeProcessResult> ProcessOnlyMentionedModeAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken)
    {
        var messageType = message.MessageType;
        
        _logger.LogDebug("[{ProcessingId}] 🎯 处理'仅@后回复'模式 - MessageType: {MessageType}", processingId, messageType);
        
        // 根据消息类型执行不同的处理逻辑
        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT => await ProcessMentionedTextMessageAsync(message, processingId, cancellationToken),
            MessageTypeConstants.GROUP_IMAGE => await ProcessMentionedImageMessageAsync(message, processingId, cancellationToken),
            MessageTypeConstants.GROUP_FILE => await ProcessMentionedFileMessageAsync(message, processingId, cancellationToken),
            MessageTypeConstants.GROUP_VOICE => ProcessMentionedVoiceMessage(message, processingId),
            _ => new GroupModeProcessResult
            {
                ShouldProcess = false,
                Mode = GroupChatMode.OnlyReplyWhenMentioned,
                Reason = $"不支持的消息类型: {messageType}"
            }
        };
    }
    
    /// <summary>
    /// 处理@模式下的文本消息
    /// 需要检查@状态，并查找是否有等待组合的媒体消息
    /// </summary>
    private async Task<GroupModeProcessResult> ProcessMentionedTextMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken)
    {
        // 使用消息协调器处理
        var coordinationResult = await _messageCoordinator.CoordinateMessageAsync(message, processingId, cancellationToken);
        
        return coordinationResult.Action switch
        {
            CoordinationAction.ProcessImmediately => new GroupModeProcessResult
            {
                ShouldProcess = true,
                Mode = GroupChatMode.OnlyReplyWhenMentioned,
                Reason = coordinationResult.Reason,
                RequiresCoordination = false,
                ProcessedMessage = coordinationResult.CombinedMessage
            },
            CoordinationAction.ProcessCombined => new GroupModeProcessResult
            {
                ShouldProcess = true,
                Mode = GroupChatMode.OnlyReplyWhenMentioned,
                Reason = coordinationResult.Reason,
                RequiresCoordination = true,
                ProcessedMessage = coordinationResult.CombinedMessage
            },
            _ => new GroupModeProcessResult
            {
                ShouldProcess = false,
                Mode = GroupChatMode.OnlyReplyWhenMentioned,
                Reason = coordinationResult.Reason
            }
        };
    }
    
    /// <summary>
    /// 处理@模式下的图片消息
    /// 缓存等待30秒内的文本消息
    /// </summary>
    private async Task<GroupModeProcessResult> ProcessMentionedImageMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken)
    {
        var coordinationResult = await _messageCoordinator.CoordinateMessageAsync(message, processingId, cancellationToken);
        
        return new GroupModeProcessResult
        {
            ShouldProcess = coordinationResult.Action == CoordinationAction.ProcessImmediately,
            Mode = GroupChatMode.OnlyReplyWhenMentioned,
            Reason = coordinationResult.Reason,
            RequiresCoordination = true
        };
    }
    
    /// <summary>
    /// 处理@模式下的文件消息
    /// 缓存等待30秒内的文本消息
    /// </summary>
    private async Task<GroupModeProcessResult> ProcessMentionedFileMessageAsync(WxCallbackMessageDto message, string processingId, CancellationToken cancellationToken)
    {
        var coordinationResult = await _messageCoordinator.CoordinateMessageAsync(message, processingId, cancellationToken);
        
        return new GroupModeProcessResult
        {
            ShouldProcess = coordinationResult.Action == CoordinationAction.ProcessImmediately,
            Mode = GroupChatMode.OnlyReplyWhenMentioned,
            Reason = coordinationResult.Reason,
            RequiresCoordination = true
        };
    }
    
    /// <summary>
    /// 处理@模式下的语音消息
    /// 直接跳过不处理
    /// </summary>
    private GroupModeProcessResult ProcessMentionedVoiceMessage(WxCallbackMessageDto message, string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 🎵 语音消息在'仅@后回复'模式下跳过处理", processingId);
        
        return new GroupModeProcessResult
        {
            ShouldProcess = false,
            Mode = GroupChatMode.OnlyReplyWhenMentioned,
            Reason = "语音消息在仅@后回复模式下跳过处理",
            RequiresCoordination = false
        };
    }
    
    /// <summary>
    /// 获取群聊模式
    /// </summary>
    public async Task<GroupChatMode> GetGroupModeAsync(string groupId, Guid wxManagerId)
    {
        try
        {
            // TODO: 从数据库或配置中获取群聊模式
            // 这里暂时返回默认模式，实际实现需要查询群组配置
            
            using var scope = _serviceProvider.CreateScope();
            var groupConfigChecker = scope.ServiceProvider.GetService<IGroupAiConfigChecker>();
            
            if (groupConfigChecker != null)
            {
                // 可以通过群组配置检查器获取模式信息
                // 暂时使用默认逻辑
            }
            
            // 默认返回"仅@后回复"模式，这是更常见的使用场景
            return GroupChatMode.OnlyReplyWhenMentioned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取群聊模式失败 - GroupId: {GroupId}", groupId);
            return GroupChatMode.OnlyReplyWhenMentioned; // 默认模式
        }
    }
    
    /// <summary>
    /// 从消息中提取WxManagerId
    /// </summary>
    private Guid GetWxManagerIdFromMessage(WxCallbackMessageDto message)
    {
        // TODO: 从消息中提取WxManagerId
        // 这里需要根据实际的消息结构来实现
        return Guid.Empty; // 暂时返回空GUID
    }
}
