using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// ProtectedLocalStorage SessionId获取策略实现
/// 处理Blazor本地存储访问和异常
/// </summary>
public class LocalStorageSessionIdStrategy : SessionIdRetrievalStrategy
{
    private readonly IServiceProvider _serviceProvider;
    private const string SessionStorageKey = "hw_session_id";

    public LocalStorageSessionIdStrategy(
        IServiceProvider serviceProvider,
        ILogger<LocalStorageSessionIdStrategy> logger) : base(logger)
    {
        _serviceProvider = serviceProvider;
    }

    public override SessionIdRetrievalStrategyType StrategyType => SessionIdRetrievalStrategyType.ProtectedLocalStorage;

    public override int Priority => 3;

    public override string StrategyName => "本地存储策略";

    public override async Task<bool> IsAvailableAsync(HttpContext? httpContext = null)
    {
        return await SafeExecuteAsync(async () =>
        {
            // 尝试获取ProtectedLocalStorage服务
            var localStorage = _serviceProvider.GetService<ProtectedLocalStorage>();
            if (localStorage == null)
            {
                LogUnavailable("ProtectedLocalStorage服务不可用");
                return false;
            }

            // 简单测试是否可以访问（不实际读取数据）
            return true;
        }, false, "可用性检查");
    }

    public override async Task<string?> GetSessionIdAsync(HttpContext? httpContext = null)
    {
        return await SafeExecuteAsync(async () =>
        {
            var localStorage = _serviceProvider.GetService<ProtectedLocalStorage>();
            if (localStorage == null)
            {
                LogFailure("ProtectedLocalStorage服务不可用");
                return null;
            }

            try
            {
                var result = await localStorage.GetAsync<string>(SessionStorageKey);
                if (result.Success && !string.IsNullOrEmpty(result.Value))
                {
                    LogSuccess(result.Value);
                    return result.Value;
                }
                else
                {
                    LogFailure($"本地存储中未找到SessionId - Success: {result.Success}, Value: {(string.IsNullOrEmpty(result.Value) ? "null/empty" : "has value")}");
                    return null;
                }
            }
            catch (JSDisconnectedException ex)
            {
                LogFailure("Blazor Circuit已断开，无法访问本地存储", ex);
                return null;
            }
            catch (InvalidOperationException ex)
            {
                LogFailure("预渲染阶段，无法访问本地存储", ex);
                return null;
            }
            catch (JSException ex)
            {
                LogFailure("JavaScript互操作异常", ex);
                return null;
            }
        }, null, "本地存储SessionId获取");
    }

    /// <summary>
    /// 设置SessionId到本地存储
    /// </summary>
    /// <param name="sessionId">SessionId</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SetSessionIdAsync(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            return false;
        }

        return await SafeExecuteAsync(async () =>
        {
            var localStorage = _serviceProvider.GetService<ProtectedLocalStorage>();
            if (localStorage == null)
            {
                LogFailure("ProtectedLocalStorage服务不可用");
                return false;
            }

            try
            {
                await localStorage.SetAsync(SessionStorageKey, sessionId);
                Logger.LogDebug("✅ 本地存储策略 - SessionId已保存: {SessionId}", 
                    sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
                return true;
            }
            catch (JSDisconnectedException ex)
            {
                LogFailure("Blazor Circuit已断开，无法保存到本地存储", ex);
                return false;
            }
            catch (InvalidOperationException ex)
            {
                LogFailure("预渲染阶段，无法保存到本地存储", ex);
                return false;
            }
            catch (JSException ex)
            {
                LogFailure("JavaScript互操作异常", ex);
                return false;
            }
        }, false, "本地存储SessionId保存");
    }

    /// <summary>
    /// 清除本地存储中的SessionId
    /// </summary>
    /// <returns>是否成功</returns>
    public async Task<bool> ClearSessionIdAsync()
    {
        return await SafeExecuteAsync(async () =>
        {
            var localStorage = _serviceProvider.GetService<ProtectedLocalStorage>();
            if (localStorage == null)
            {
                LogFailure("ProtectedLocalStorage服务不可用");
                return false;
            }

            try
            {
                await localStorage.DeleteAsync(SessionStorageKey);
                Logger.LogDebug("🗑️ 本地存储策略 - SessionId已清除");
                return true;
            }
            catch (JSDisconnectedException ex)
            {
                LogFailure("Blazor Circuit已断开，无法清除本地存储", ex);
                return false;
            }
            catch (InvalidOperationException ex)
            {
                LogFailure("预渲染阶段，无法清除本地存储", ex);
                return false;
            }
            catch (JSException ex)
            {
                LogFailure("JavaScript互操作异常", ex);
                return false;
            }
        }, false, "本地存储SessionId清除");
    }

    public override string GetDescription()
    {
        return $"{base.GetDescription()} - 从Blazor ProtectedLocalStorage获取SessionId";
    }
}

/// <summary>
/// 传统RedisAuthenticationStateProvider SessionId获取策略
/// 用于向后兼容和降级处理
/// </summary>
public class LegacyProviderSessionIdStrategy : SessionIdRetrievalStrategy
{
    private readonly IServiceProvider _serviceProvider;

    public LegacyProviderSessionIdStrategy(
        IServiceProvider serviceProvider,
        ILogger<LegacyProviderSessionIdStrategy> logger) : base(logger)
    {
        _serviceProvider = serviceProvider;
    }

    public override SessionIdRetrievalStrategyType StrategyType => SessionIdRetrievalStrategyType.LegacyProvider;

    public override int Priority => 4;

    public override string StrategyName => "传统提供者策略";

    public override async Task<bool> IsAvailableAsync(HttpContext? httpContext = null)
    {
        return await SafeExecuteAsync(async () =>
        {
            var legacyProvider = _serviceProvider.GetService<RedisAuthenticationStateProvider>();
            return legacyProvider != null;
        }, false, "传统提供者可用性检查");
    }

    public override async Task<string?> GetSessionIdAsync(HttpContext? httpContext = null)
    {
        return await SafeExecuteAsync(async () =>
        {
            var legacyProvider = _serviceProvider.GetService<RedisAuthenticationStateProvider>();
            if (legacyProvider == null)
            {
                LogFailure("RedisAuthenticationStateProvider服务不可用");
                return null;
            }

            // 使用反射调用私有方法GetCurrentSessionIdAsync
            var method = typeof(RedisAuthenticationStateProvider).GetMethod("GetCurrentSessionIdAsync");
            if (method != null)
            {
                var task = (Task<string?>)method.Invoke(legacyProvider, null);
                var sessionId = await task;
                
                if (!string.IsNullOrEmpty(sessionId))
                {
                    LogSuccess(sessionId);
                    return sessionId;
                }
            }

            LogFailure("传统提供者无法获取SessionId");
            return null;
        }, null, "传统提供者SessionId获取");
    }

    public override string GetDescription()
    {
        return $"{base.GetDescription()} - 从传统RedisAuthenticationStateProvider获取SessionId";
    }
}
