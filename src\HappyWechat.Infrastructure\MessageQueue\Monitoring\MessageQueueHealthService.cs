using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageQueue.Monitoring;

/// <summary>
/// 消息队列健康检查服务
/// 监控队列积压、处理速率、错误率等关键指标
/// </summary>
public class MessageQueueHealthService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MessageQueueHealthService> _logger;
    private readonly ConcurrentDictionary<string, QueueHealthMetrics> _queueMetrics = new();
    
    // 健康检查阈值
    private const int QUEUE_BACKLOG_WARNING_THRESHOLD = 1000;
    private const int QUEUE_BACKLOG_CRITICAL_THRESHOLD = 5000;
    private const double ERROR_RATE_WARNING_THRESHOLD = 0.05; // 5%
    private const double ERROR_RATE_CRITICAL_THRESHOLD = 0.15; // 15%
    
    public MessageQueueHealthService(
        IServiceProvider serviceProvider,
        ILogger<MessageQueueHealthService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🔍 消息队列健康检查服务启动");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthCheckAsync(stoppingToken);
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // 每分钟检查一次
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 消息队列健康检查异常");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // 异常时等待5分钟
            }
        }
        
        _logger.LogInformation("消息队列健康检查服务已停止");
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    private async Task PerformHealthCheckAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var database = scope.ServiceProvider.GetRequiredService<IDatabase>();
        
        // 检查各种队列类型
        var queueTypes = new[] { "send_text", "send_image", "file_send", "send_voice", "send_video" };
        var activeAccounts = await GetActiveAccountsAsync();
        
        foreach (var accountId in activeAccounts)
        {
            foreach (var queueType in queueTypes)
            {
                await CheckQueueHealthAsync(database, accountId, queueType);
            }
        }
        
        // 检查流式架构健康状况
        await CheckStreamingArchitectureHealthAsync();
        
        // 生成健康报告
        await GenerateHealthReportAsync();
    }

    /// <summary>
    /// 检查单个队列健康状况
    /// </summary>
    private async Task CheckQueueHealthAsync(IDatabase database, Guid accountId, string queueType)
    {
        try
        {
            var queueName = $"wx_queue:{accountId}:{queueType}";
            var queueLength = await database.ListLengthAsync(queueName);
            
            var metricsKey = $"{accountId}:{queueType}";
            var metrics = _queueMetrics.GetOrAdd(metricsKey, _ => new QueueHealthMetrics
            {
                AccountId = accountId,
                QueueType = queueType
            });
            
            // 更新指标
            metrics.CurrentBacklog = (int)queueLength;
            metrics.LastCheckTime = DateTime.UtcNow;
            
            // 检查积压情况
            if (queueLength > QUEUE_BACKLOG_CRITICAL_THRESHOLD)
            {
                _logger.LogError("🚨 队列严重积压 - Account: {AccountId}, Queue: {QueueType}, Backlog: {Backlog}",
                    accountId, queueType, queueLength);
                metrics.HealthStatus = QueueHealthStatus.Critical;
            }
            else if (queueLength > QUEUE_BACKLOG_WARNING_THRESHOLD)
            {
                _logger.LogWarning("⚠️ 队列积压警告 - Account: {AccountId}, Queue: {QueueType}, Backlog: {Backlog}",
                    accountId, queueType, queueLength);
                metrics.HealthStatus = QueueHealthStatus.Warning;
            }
            else
            {
                metrics.HealthStatus = QueueHealthStatus.Healthy;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查队列健康状况异常 - Account: {AccountId}, Queue: {QueueType}",
                accountId, queueType);
        }
    }

    /// <summary>
    /// 检查流式架构健康状况
    /// </summary>
    private async Task CheckStreamingArchitectureHealthAsync()
    {
        // TODO: 实现流式架构健康检查
        // 检查Channel容量使用率、处理速率等
        await Task.CompletedTask;
    }

    /// <summary>
    /// 生成健康报告
    /// </summary>
    private async Task GenerateHealthReportAsync()
    {
        var totalQueues = _queueMetrics.Count;
        var healthyQueues = _queueMetrics.Values.Count(m => m.HealthStatus == QueueHealthStatus.Healthy);
        var warningQueues = _queueMetrics.Values.Count(m => m.HealthStatus == QueueHealthStatus.Warning);
        var criticalQueues = _queueMetrics.Values.Count(m => m.HealthStatus == QueueHealthStatus.Critical);
        
        if (criticalQueues > 0)
        {
            _logger.LogError("🚨 消息队列健康报告 - 总计: {Total}, 健康: {Healthy}, 警告: {Warning}, 严重: {Critical}",
                totalQueues, healthyQueues, warningQueues, criticalQueues);
        }
        else if (warningQueues > 0)
        {
            _logger.LogWarning("⚠️ 消息队列健康报告 - 总计: {Total}, 健康: {Healthy}, 警告: {Warning}, 严重: {Critical}",
                totalQueues, healthyQueues, warningQueues, criticalQueues);
        }
        else
        {
            _logger.LogInformation("✅ 消息队列健康报告 - 总计: {Total}, 全部健康",
                totalQueues);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取活跃账号列表
    /// </summary>
    private async Task<List<Guid>> GetActiveAccountsAsync()
    {
        // TODO: 从数据库获取活跃账号列表
        // 这里返回示例数据
        return new List<Guid> { Guid.Parse("980a47bc-1452-446c-8e78-72f7bb0c5144") };
    }
}

/// <summary>
/// 队列健康指标
/// </summary>
public class QueueHealthMetrics
{
    public Guid AccountId { get; set; }
    public string QueueType { get; set; } = string.Empty;
    public int CurrentBacklog { get; set; }
    public QueueHealthStatus HealthStatus { get; set; } = QueueHealthStatus.Healthy;
    public DateTime LastCheckTime { get; set; } = DateTime.UtcNow;
    public long TotalProcessed { get; set; }
    public long TotalErrors { get; set; }
    
    public double ErrorRate => TotalProcessed > 0 ? (double)TotalErrors / TotalProcessed : 0;
}

/// <summary>
/// 队列健康状态
/// </summary>
public enum QueueHealthStatus
{
    Healthy,
    Warning,
    Critical
}
