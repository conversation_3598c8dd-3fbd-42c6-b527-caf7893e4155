/**
 * 统一同步通知处理器
 * 提供统一、可靠的前端同步通知处理机制
 */
class UnifiedSyncNotificationHandler {
    constructor() {
        this.connection = null;
        this.isInitialized = false;
        this.callbacks = new Map();
        this.statistics = {
            totalReceived: 0,
            contactNotifications: 0,
            groupNotifications: 0,
            failedNotifications: 0,
            lastReceived: null
        };
        
        // 防抖配置
        this.debounceTimers = new Map();
        this.debounceInterval = 500; // 500ms防抖
        
        // 重连配置
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 2000;
        
        this.init();
    }

    /**
     * 初始化通知处理器
     */
    async init() {
        try {
            console.log('🚀 初始化统一同步通知处理器...');
            
            // 等待SignalR连接就绪
            await this.waitForSignalRConnection();
            
            // 设置事件处理器
            this.setupEventHandlers();
            
            // 设置连接状态监听
            this.setupConnectionMonitoring();
            
            this.isInitialized = true;
            console.log('✅ 统一同步通知处理器初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('initialized', { timestamp: new Date() });
            
        } catch (error) {
            console.error('❌ 统一同步通知处理器初始化失败:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * 🔧 等待SignalR连接就绪 - 增强认证支持
     */
    async waitForSignalRConnection() {
        return new Promise((resolve, reject) => {
            const checkConnection = () => {
                // 优先使用联系人同步连接
                if (window.contactSyncManager && window.contactSyncManager.connection) {
                    this.connection = window.contactSyncManager.connection;
                    console.log('✅ 使用联系人同步SignalR连接');
                    this.ensureConnectionAuthentication();
                    resolve();
                    return;
                }
                
                // 备用：使用简化通知处理器连接
                if (window.simplifiedNotificationHandler && window.simplifiedNotificationHandler.connection) {
                    this.connection = window.simplifiedNotificationHandler.connection;
                    console.log('✅ 使用简化通知处理器SignalR连接');
                    this.ensureConnectionAuthentication();
                    resolve();
                    return;
                }
                
                // 🔧 备用方案：创建带认证的新连接
                if (this.shouldCreateNewConnection()) {
                    this.createAuthenticatedConnection().then(() => {
                        console.log('✅ 创建了新的认证SignalR连接');
                        resolve();
                    }).catch(reject);
                    return;
                }
                
                // 继续等待
                setTimeout(checkConnection, 100);
            };
            
            checkConnection();
            
            // 超时处理
            setTimeout(() => {
                if (!this.connection) {
                    reject(new Error('SignalR连接超时'));
                }
            }, 10000);
        });
    }

    /**
     * 🔧 确保连接具有认证信息
     */
    ensureConnectionAuthentication() {
        if (!this.connection) return;

        // 获取sessionId
        const sessionId = this.getSessionId();
        if (!sessionId) {
            console.warn('⚠️ 未找到SessionId，SignalR可能无法认证');
            return;
        }

        // 检查连接URL是否包含access_token
        if (this.connection.connection && this.connection.connection.connectionState === 'Connected') {
            console.log('✅ SignalR连接已建立，SessionId:', sessionId.substring(0, 8) + '...');
        }
    }

    /**
     * 🔧 获取SessionId - 统一接口
     */
    async getSessionIdUnified() {
        try {
            // Priority 1: 使用统一认证状态管理器
            if (window.authStateManager) {
                const sessionId = await window.authStateManager.getSessionId();
                if (sessionId) {
                    return sessionId;
                }
            }

            // Priority 2: 降级到传统方法
            return this.getSessionIdLegacy();
        } catch (error) {
            console.error('❌ 获取SessionId失败:', error);
            return this.getSessionIdLegacy();
        }
    }

    /**
     * 🔧 获取SessionId - 传统方法（保持向后兼容）
     */
    getSessionId() {
        return this.getSessionIdLegacy();
    }

    /**
     * 🔧 获取SessionId - 传统实现
     */
    getSessionIdLegacy() {
        // 从Redis认证管理器获取
        if (window.redisAuthManager && typeof window.redisAuthManager.getSessionId === 'function') {
            const sessionId = window.redisAuthManager.getSessionId();
            if (sessionId) return sessionId;
        }

        // 从统一会话管理器获取
        if (window.unifiedSessionManager && typeof window.unifiedSessionManager.getSessionId === 'function') {
            const sessionId = window.unifiedSessionManager.getSessionId();
            if (sessionId) return sessionId;
        }

        // 从localStorage获取
        try {
            const stored = localStorage.getItem('hw_session_id');
            if (stored) return stored;
        } catch (error) {
            console.warn('⚠️ 无法从localStorage获取sessionId:', error);
        }

        // 从Cookie获取
        const value = `; ${document.cookie}`;
        const parts = value.split(`; HW_SessionId=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }

        return null;
    }

    /**
     * 🔧 判断是否应该创建新连接
     */
    shouldCreateNewConnection() {
        // 如果有sessionId但没有可用连接，则创建新连接
        const sessionId = this.getSessionId();
        return sessionId && !this.connection;
    }

    /**
     * 🔧 创建带认证的SignalR连接
     */
    async createAuthenticatedConnection() {
        try {
            console.log('🔗 开始创建认证SignalR连接...');

            // 使用统一的SignalR认证提供者
            if (window.signalRAuthProvider) {
                const connection = await window.signalRAuthProvider.createAuthenticatedConnection('/hubs/contactSync', {
                    logLevel: signalR.LogLevel.Information,
                    connectionOptions: {
                        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling
                    }
                });

                // 设置连接事件处理器
                this.setupConnectionEventHandlers(connection);

                this.connection = connection;
                console.log('✅ 认证SignalR连接创建成功');
                return connection;
            }

            // 降级：使用传统方式创建连接
            return await this.createConnectionFallback();

        } catch (error) {
            console.error('❌ 创建认证SignalR连接失败:', error);
            throw error;
        }
    }

    /**
     * 🔧 降级连接创建方法
     */
    async createConnectionFallback() {
        console.log('🔄 使用降级方式创建SignalR连接...');

        const sessionId = await this.getSessionIdUnified();
        if (!sessionId) {
            throw new Error('无法创建认证连接：缺少SessionId');
        }

        // 创建连接构建器
        const connectionBuilder = new signalR.HubConnectionBuilder()
            .withUrl('/hubs/contactSync', {
                skipNegotiation: false,
                transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
                accessTokenFactory: () => this.getSessionIdUnified(),
                timeout: 30000
            })
            .withAutomaticReconnect([0, 2000, 5000, 10000, 30000])
            .configureLogging(signalR.LogLevel.Information);

        const connection = connectionBuilder.build();

        // 设置连接事件处理器
        this.setupConnectionEventHandlers(connection);

        // 启动连接
        await connection.start();

        console.log('✅ 降级SignalR连接创建成功');
        return connection;
    }

    /**
     * 🔧 设置连接事件处理器
     */
    setupConnectionEventHandlers(connection) {
        // 连接关闭事件
        connection.onclose((error) => {
            console.log('🔌 统一同步通知处理器连接关闭', error);
        });

        // 重连事件
        connection.onreconnecting((error) => {
            console.log('🔄 统一同步通知处理器重连中', error);
        });

        // 重连成功事件
        connection.onreconnected((connectionId) => {
            console.log('✅ 统一同步通知处理器重连成功', connectionId);
        });

        // 认证错误处理
        connection.on('AuthenticationError', (errorData) => {
            console.error('🚫 统一同步通知处理器认证错误', errorData);
            this.handleAuthenticationError(errorData);
        });

        // 连接确认
        connection.on('ConnectionConfirmed', (confirmData) => {
            console.log('✅ 统一同步通知处理器连接确认', confirmData);
        });
    }

    /**
     * 🔧 处理认证错误
     */
    handleAuthenticationError(errorData) {
        console.error('🚫 处理认证错误:', errorData);

        // 发布认证错误事件
        if (window.authEventBus) {
            window.authEventBus.publish('signalrAuthError', {
                source: 'unifiedSyncNotificationHandler',
                errorData,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 🔧 更新认证信息
     */
    updateAuthentication(sessionId) {
        try {
            console.log('🔄 更新统一同步通知处理器的认证信息...');
            
            if (!sessionId) {
                console.warn('⚠️ SessionId为空，无法更新认证');
                return;
            }

            // 如果已有连接，检查是否需要重新连接
            if (this.connection && this.connection.connectionState === 'Connected') {
                console.log('✅ SignalR连接已存在且正常，使用新的SessionId');
                // 这里可以根据需要决定是否重新连接
            } else {
                console.log('⚠️ SignalR连接不存在或异常，准备重新初始化');
                // 重新初始化连接
                this.reconnectAttempts = 0;
                this.init().catch(error => {
                    console.error('❌ 重新初始化失败:', error);
                });
            }
        } catch (error) {
            console.error('❌ 更新认证信息失败:', error);
        }
    }

    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        if (!this.connection) {
            console.error('❌ SignalR连接未就绪，无法设置事件处理器');
            return;
        }

        console.log('🔧 设置统一同步通知事件处理器...');

        // 联系人同步进度
        this.connection.on('ContactSyncProgress', (data) => {
            this.handleContactSyncProgress(data);
        });

        // 联系人同步完成
        this.connection.on('ContactSyncCompleted', (data) => {
            this.handleContactSyncCompleted(data);
        });

        // 群组同步进度
        this.connection.on('GroupSyncProgress', (data) => {
            this.handleGroupSyncProgress(data);
        });

        // 群组同步完成
        this.connection.on('GroupSyncCompleted', (data) => {
            this.handleGroupSyncCompleted(data);
        });

        // 同步失败
        this.connection.on('SyncFailed', (data) => {
            this.handleSyncFailed(data);
        });

        // 数据变更通知
        this.connection.on('DataChanged', (data) => {
            this.handleDataChanged(data);
        });

        console.log('✅ 统一同步通知事件处理器设置完成');
    }

    /**
     * 设置连接状态监听
     */
    setupConnectionMonitoring() {
        if (!this.connection) return;

        this.connection.onreconnecting(() => {
            console.log('🔄 SignalR连接重连中...');
            this.dispatchEvent('reconnecting');
        });

        this.connection.onreconnected(() => {
            console.log('✅ SignalR连接重连成功');
            this.reconnectAttempts = 0;
            this.dispatchEvent('reconnected');
        });

        this.connection.onclose(() => {
            console.log('❌ SignalR连接已关闭');
            this.dispatchEvent('disconnected');
            this.scheduleReconnect();
        });
    }

    /**
     * 处理联系人同步进度
     */
    handleContactSyncProgress(data) {
        console.log('📊 收到联系人同步进度:', data);
        
        this.updateStatistics('contact');
        
        // 防抖处理
        this.debounceCallback('contactProgress', () => {
            this.executeCallbacks('contactSyncProgress', data);
            this.dispatchEvent('contactSyncProgress', data);
        });
    }

    /**
     * 处理联系人同步完成
     */
    handleContactSyncCompleted(data) {
        console.log('🎉 收到联系人同步完成通知:', data);
        
        this.updateStatistics('contact');
        
        // 立即执行，不防抖
        this.executeCallbacks('contactSyncCompleted', data);
        this.dispatchEvent('contactSyncCompleted', data);
        
        // 触发页面刷新
        this.triggerPageRefresh('contact', data.WxManagerId || data.wxManagerId);
    }

    /**
     * 处理群组同步进度
     */
    handleGroupSyncProgress(data) {
        console.log('📊 收到群组同步进度:', data);
        
        this.updateStatistics('group');
        
        // 防抖处理
        this.debounceCallback('groupProgress', () => {
            this.executeCallbacks('groupSyncProgress', data);
            this.dispatchEvent('groupSyncProgress', data);
        });
    }

    /**
     * 处理群组同步完成
     */
    handleGroupSyncCompleted(data) {
        console.log('🎉 收到群组同步完成通知:', data);
        
        this.updateStatistics('group');
        
        // 立即执行，不防抖
        this.executeCallbacks('groupSyncCompleted', data);
        this.dispatchEvent('groupSyncCompleted', data);
        
        // 触发页面刷新
        this.triggerPageRefresh('group', data.WxManagerId || data.wxManagerId);
    }

    /**
     * 处理同步失败
     */
    handleSyncFailed(data) {
        console.error('❌ 收到同步失败通知:', data);
        
        this.statistics.failedNotifications++;
        
        this.executeCallbacks('syncFailed', data);
        this.dispatchEvent('syncFailed', data);
        
        // 显示错误提示
        this.showErrorNotification(data);
    }

    /**
     * 处理数据变更通知
     */
    handleDataChanged(data) {
        console.log('🔄 收到数据变更通知:', data);
        
        // 使用简化刷新管理器处理
        if (window.simplifiedRefreshManager) {
            window.simplifiedRefreshManager.handleDataChanged(data);
        }
        
        this.executeCallbacks('dataChanged', data);
        this.dispatchEvent('dataChanged', data);
    }

    /**
     * 注册回调函数
     */
    registerCallback(eventType, callback, callbackId = null) {
        if (typeof callback !== 'function') {
            console.error('❌ 回调函数必须是function类型');
            return null;
        }

        const id = callbackId || this.generateCallbackId();
        
        if (!this.callbacks.has(eventType)) {
            this.callbacks.set(eventType, new Map());
        }
        
        this.callbacks.get(eventType).set(id, callback);
        
        console.log(`✅ 注册回调函数 - EventType: ${eventType}, CallbackId: ${id}`);
        return id;
    }

    /**
     * 注销回调函数
     */
    unregisterCallback(eventType, callbackId) {
        if (this.callbacks.has(eventType)) {
            const removed = this.callbacks.get(eventType).delete(callbackId);
            if (removed) {
                console.log(`🗑️ 注销回调函数 - EventType: ${eventType}, CallbackId: ${callbackId}`);
            }
            return removed;
        }
        return false;
    }

    /**
     * 执行回调函数
     */
    executeCallbacks(eventType, data) {
        if (!this.callbacks.has(eventType)) return;
        
        const callbacks = this.callbacks.get(eventType);
        callbacks.forEach((callback, callbackId) => {
            try {
                callback(data);
            } catch (error) {
                console.error(`❌ 回调函数执行失败 - EventType: ${eventType}, CallbackId: ${callbackId}`, error);
            }
        });
    }

    /**
     * 防抖回调
     */
    debounceCallback(key, callback) {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }
        
        const timer = setTimeout(() => {
            callback();
            this.debounceTimers.delete(key);
        }, this.debounceInterval);
        
        this.debounceTimers.set(key, timer);
    }

    /**
     * 触发页面刷新
     */
    triggerPageRefresh(dataType, wxManagerId) {
        try {
            // 根据数据类型调用相应的Blazor组件方法
            if (dataType === 'contact' && window.blazorContactComponent) {
                window.blazorContactComponent.invokeMethodAsync('OnSyncCompleted');
            } else if (dataType === 'group' && window.blazorGroupComponent) {
                window.blazorGroupComponent.invokeMethodAsync('OnSyncCompleted');
            }

            console.log(`🔄 触发页面刷新 - DataType: ${dataType}, WxManagerId: ${wxManagerId}`);
        } catch (error) {
            console.error('❌ 触发页面刷新失败:', error);
        }
    }

    /**
     * 显示错误通知
     */
    showErrorNotification(data) {
        try {
            const message = `同步失败: ${data.ErrorMessage || data.errorMessage || '未知错误'}`;

            // 尝试使用MudBlazor的Snackbar
            if (window.mudBlazorSnackbar) {
                window.mudBlazorSnackbar.add(message, 'error');
            }
            // 备用：使用浏览器通知
            else if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('同步失败', { body: message, icon: '/favicon.ico' });
            }
            // 最后备用：控制台输出
            else {
                console.error('🚨 同步失败通知:', message);
            }
        } catch (error) {
            console.error('❌ 显示错误通知失败:', error);
        }
    }

    /**
     * 调度重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ 达到最大重连次数，停止重连');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // 指数退避

        console.log(`🔄 计划重连 - 尝试次数: ${this.reconnectAttempts}/${this.maxReconnectAttempts}, 延迟: ${delay}ms`);

        setTimeout(() => {
            this.init();
        }, delay);
    }

    /**
     * 更新统计信息
     */
    updateStatistics(type) {
        this.statistics.totalReceived++;
        this.statistics.lastReceived = new Date();

        if (type === 'contact') {
            this.statistics.contactNotifications++;
        } else if (type === 'group') {
            this.statistics.groupNotifications++;
        }
    }

    /**
     * 生成回调ID
     */
    generateCallbackId() {
        return 'callback_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * 分发自定义事件
     */
    dispatchEvent(eventType, data = null) {
        try {
            const event = new CustomEvent(`unifiedSync:${eventType}`, {
                detail: data,
                bubbles: true,
                cancelable: true
            });

            document.dispatchEvent(event);
        } catch (error) {
            console.error('❌ 分发自定义事件失败:', error);
        }
    }

    /**
     * 获取统计信息
     */
    getStatistics() {
        return { ...this.statistics };
    }

    /**
     * 重置统计信息
     */
    resetStatistics() {
        this.statistics = {
            totalReceived: 0,
            contactNotifications: 0,
            groupNotifications: 0,
            failedNotifications: 0,
            lastReceived: null
        };
        console.log('🔄 统计信息已重置');
    }

    /**
     * 清理资源
     */
    dispose() {
        try {
            // 清理防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            // 清理回调函数
            this.callbacks.clear();

            // 重置状态
            this.isInitialized = false;
            this.connection = null;

            console.log('🗑️ 统一同步通知处理器已清理');
        } catch (error) {
            console.error('❌ 清理统一同步通知处理器失败:', error);
        }
    }

    /**
     * 手动触发重新初始化
     */
    async reinitialize() {
        console.log('🔄 手动重新初始化统一同步通知处理器...');
        this.dispose();
        this.reconnectAttempts = 0;
        await this.init();
    }
}

// 全局实例
window.unifiedSyncNotificationHandler = new UnifiedSyncNotificationHandler();

// 添加缺失的clearUnifiedFrontendCache函数
window.clearUnifiedFrontendCache = function(wxManagerId, dataType) {
    try {
        console.log(`🧹 清理统一前端缓存 - ManagerId: ${wxManagerId}, DataType: ${dataType}`);

        // 使用统一缓存管理器清理
        if (window.unifiedCacheManager) {
            return window.unifiedCacheManager.clearCache(wxManagerId, dataType);
        }

        // 备用：使用简化刷新管理器
        if (window.simplifiedRefreshManager) {
            window.simplifiedRefreshManager.clearAllCache();
        }

        console.log('✅ 统一前端缓存清理完成');
        return Promise.resolve({ success: true });
    } catch (error) {
        console.error('❌ 清理统一前端缓存失败:', error);
        return Promise.resolve({ success: false, error: error.message });
    }
};

// 导出到全局
window.UnifiedSyncNotificationHandler = UnifiedSyncNotificationHandler;
