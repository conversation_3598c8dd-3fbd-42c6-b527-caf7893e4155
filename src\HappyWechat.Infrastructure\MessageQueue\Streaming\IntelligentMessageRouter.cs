using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageQueue.Streaming;

/// <summary>
/// 智能消息路由器 - 替代阻塞式EYunCallbackProcessor
/// 实现毫秒级响应，永不阻塞EYun回调
/// </summary>
public interface IIntelligentMessageRouter
{
    /// <summary>
    /// 路由消息 - 永不阻塞，毫秒级响应
    /// </summary>
    Task<MessageRoutingResult> RouteMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 获取路由统计
    /// </summary>
    Task<RouterStats> GetStatsAsync();
}

public class IntelligentMessageRouter : IIntelligentMessageRouter
{
    private readonly StreamingMessageArchitecture _streamingArchitecture;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<IntelligentMessageRouter> _logger;
    
    // 消息预分类缓存 - 避免重复计算
    private readonly ConcurrentDictionary<string, MessageCategory> _messageTypeCache = new();
    
    // 路由统计
    private readonly ConcurrentDictionary<string, long> _routingStats = new();
    private readonly object _statsLock = new();

    public IntelligentMessageRouter(
        StreamingMessageArchitecture streamingArchitecture,
        IServiceProvider serviceProvider,
        ILogger<IntelligentMessageRouter> logger)
    {
        _streamingArchitecture = streamingArchitecture;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 路由消息 - 核心方法，毫秒级响应
    /// </summary>
    public async Task<MessageRoutingResult> RouteMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        var routingStart = DateTime.UtcNow;

        try
        {
            // 第一步：快速验证（1-2ms）
            var validationResult = await QuickValidateAsync(callbackMessage, processingId);
            if (!validationResult.IsValid)
            {
                UpdateStats("validation_failed");
                return MessageRoutingResult.CreateFailure(validationResult.ErrorMessage);
            }

            // 第二步：消息分类（1ms）
            var category = ClassifyMessage(callbackMessage);
            UpdateStats($"category_{category}");

            // 第三步：特殊消息预处理（2-3ms）
            var preprocessResult = await PreprocessSpecialMessagesAsync(callbackMessage, processingId, category);
            if (preprocessResult.ShouldStop)
            {
                UpdateStats("preprocessed_handled");
                return MessageRoutingResult.CreateSuccess("消息已预处理完成");
            }

            // 第四步：路由到流式架构（1ms）
            var enqueueSuccess = await _streamingArchitecture.EnqueueMessageAsync(callbackMessage, processingId);
            
            var routingTime = DateTime.UtcNow - routingStart;
            
            if (enqueueSuccess)
            {
                UpdateStats("routed_success");
                _logger.LogDebug("🚀 消息路由成功 - ProcessingId: {ProcessingId}, Category: {Category}, Duration: {Duration}ms",
                    processingId, category, routingTime.TotalMilliseconds);
                return MessageRoutingResult.CreateSuccess($"消息已路由到{category}通道");
            }
            else
            {
                // 降级处理：如果流式架构满了，使用同步降级处理
                UpdateStats("degraded_processing");
                var degradedResult = await ProcessDegradedAsync(callbackMessage, processingId, category);
                return degradedResult;
            }
        }
        catch (Exception ex)
        {
            var routingTime = DateTime.UtcNow - routingStart;
            UpdateStats("routing_error");
            _logger.LogError(ex, "❌ 消息路由异常 - ProcessingId: {ProcessingId}, Duration: {Duration}ms",
                processingId, routingTime.TotalMilliseconds);
            return MessageRoutingResult.CreateFailure($"路由异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 快速验证 - 1-2ms完成
    /// </summary>
    private async Task<ValidationResult> QuickValidateAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        // 基本字段验证
        if (string.IsNullOrEmpty(callbackMessage.WxManagerId))
            return ValidationResult.Invalid("WxManagerId不能为空");

        if (string.IsNullOrEmpty(callbackMessage.MessageType))
            return ValidationResult.Invalid("MessageType不能为空");

        // 账号存在性快速检查 - 使用缓存
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var wxManagerRepository = scope.ServiceProvider.GetService<IWxManagerRepository>();
            
            if (wxManagerRepository != null)
            {
                var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
                var exists = await wxManagerRepository.ExistsAsync(wxManagerId);
                if (!exists)
                    return ValidationResult.Invalid($"微信管理器不存在: {wxManagerId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 账号验证异常，跳过验证 - ProcessingId: {ProcessingId}", processingId);
            // 验证异常时允许通过，避免阻塞
        }

        return ValidationResult.Valid();
    }

    /// <summary>
    /// 消息分类 - 基于缓存的快速分类
    /// </summary>
    private MessageCategory ClassifyMessage(WxCallbackMessageDto callbackMessage)
    {
        var cacheKey = $"{callbackMessage.MessageType}_{!string.IsNullOrEmpty(callbackMessage.Data?.FromGroup)}";
        
        return _messageTypeCache.GetOrAdd(cacheKey, _ =>
        {
            var messageType = callbackMessage.MessageType;
            var isGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup);

            return messageType switch
            {
                "80001" => MessageCategory.PriorityAtMessage,
                "60001" when !isGroupMessage => MessageCategory.PrivateTextFast,
                "60001" when isGroupMessage => MessageCategory.GroupTextFast,
                "60002" or "60004" or "60009" or "60010" => MessageCategory.MediaSlow,
                "50001" => MessageCategory.FriendRequest,
                "50002" => MessageCategory.GroupInvite,
                _ => MessageCategory.SystemMessage
            };
        });
    }

    /// <summary>
    /// 特殊消息预处理 - 某些消息可以立即处理完毕
    /// </summary>
    private async Task<PreprocessResult> PreprocessSpecialMessagesAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        MessageCategory category)
    {
        switch (category)
        {
            case MessageCategory.FriendRequest:
                return await HandleFriendRequestAsync(callbackMessage, processingId);

            case MessageCategory.GroupInvite:
                return await HandleGroupInviteAsync(callbackMessage, processingId);

            case MessageCategory.SystemMessage:
                // 系统消息直接忽略
                _logger.LogDebug("🔧 系统消息已忽略 - ProcessingId: {ProcessingId}, MessageType: {MessageType}",
                    processingId, callbackMessage.MessageType);
                return PreprocessResult.Handled();

            default:
                return PreprocessResult.Continue();
        }
    }

    /// <summary>
    /// 处理好友请求 - 可以立即处理
    /// </summary>
    private async Task<PreprocessResult> HandleFriendRequestAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var friendRequestProcessor = scope.ServiceProvider.GetService<IFriendRequestProcessor>();
            var configManager = scope.ServiceProvider.GetService<ISystemConfigManager>();
            
            if (friendRequestProcessor != null && configManager != null)
            {
                // 异步处理好友请求，不等待结果
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // 创建默认全局配置（简化版）
                        var globalConfig = new HappyWechat.Infrastructure.MessageProcessing.Services.GlobalRobotConfig
                        {
                            EnableAutoAcceptFriend = false,
                            FriendAcceptKeywords = new List<string>(),
                            DailyFriendAcceptLimit = 50
                        };
                        var result = await friendRequestProcessor.ProcessFriendRequestAsync(callbackMessage, globalConfig, processingId);
                        _logger.LogInformation("✅ 好友请求处理完成 - ProcessingId: {ProcessingId}, Success: {Success}",
                            processingId, result.Success);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ 好友请求异步处理失败 - ProcessingId: {ProcessingId}", processingId);
                    }
                });
                
                return PreprocessResult.Handled();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 好友请求预处理异常 - ProcessingId: {ProcessingId}", processingId);
        }

        return PreprocessResult.Continue();
    }

    /// <summary>
    /// 处理群邀请 - 可以立即处理
    /// </summary>
    private async Task<PreprocessResult> HandleGroupInviteAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        // 群邀请处理逻辑
        _logger.LogDebug("👥 群邀请消息已预处理 - ProcessingId: {ProcessingId}", processingId);
        return PreprocessResult.Handled();
    }

    /// <summary>
    /// 降级处理 - 当流式架构过载时的备用方案
    /// </summary>
    private async Task<MessageRoutingResult> ProcessDegradedAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        MessageCategory category)
    {
        _logger.LogWarning("⚠️ 启用降级处理 - ProcessingId: {ProcessingId}, Category: {Category}", processingId, category);

        try
        {
            using var scope = _serviceProvider.CreateScope();

            switch (category)
            {
                case MessageCategory.PriorityAtMessage:
                    // @消息优先处理，即使在降级模式下也要保证处理
                    var processor = scope.ServiceProvider.GetService<IUnifiedMessageProcessor>();
                    if (processor != null)
                    {
                        var result = await processor.ProcessMessageAsync(callbackMessage, processingId);
                        return result.Success 
                            ? MessageRoutingResult.CreateSuccess("优先级消息降级处理成功")
                            : MessageRoutingResult.CreateFailure("优先级消息降级处理失败");
                    }
                    break;

                case MessageCategory.PrivateTextFast:
                case MessageCategory.GroupTextFast:
                    // 文本消息降级：记录但不处理，避免丢失重要消息
                    _logger.LogWarning("⚠️ 文本消息降级：已记录但暂缓处理 - ProcessingId: {ProcessingId}", processingId);
                    // 可以考虑存储到数据库等持久化存储中
                    return MessageRoutingResult.CreateSuccess("文本消息已记录，降级处理中");

                case MessageCategory.MediaSlow:
                    // 媒体消息降级：直接丢弃，因为处理成本太高
                    _logger.LogWarning("⚠️ 媒体消息降级：已丢弃 - ProcessingId: {ProcessingId}", processingId);
                    return MessageRoutingResult.CreateSuccess("媒体消息已丢弃（系统过载）");

                default:
                    return MessageRoutingResult.CreateSuccess("未知消息类型已忽略");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 降级处理异常 - ProcessingId: {ProcessingId}", processingId);
        }

        return MessageRoutingResult.CreateFailure("降级处理失败");
    }

    /// <summary>
    /// 获取路由统计
    /// </summary>
    public async Task<RouterStats> GetStatsAsync()
    {
        lock (_statsLock)
        {
            return new RouterStats
            {
                TotalRouted = _routingStats.Values.Sum(),
                CategoryStats = _routingStats.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                GeneratedAt = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 更新统计
    /// </summary>
    private void UpdateStats(string category)
    {
        _routingStats.AddOrUpdate(category, 1, (key, count) => count + 1);
    }
}

#region 辅助类型

/// <summary>
/// 消息路由结果
/// </summary>
public class MessageRoutingResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    public static MessageRoutingResult CreateSuccess(string message) =>
        new() { Success = true, Message = message };

    public static MessageRoutingResult CreateFailure(string message) =>
        new() { Success = false, Message = message };
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;

    public static ValidationResult Valid() => new() { IsValid = true };
    public static ValidationResult Invalid(string error) => new() { IsValid = false, ErrorMessage = error };
}

/// <summary>
/// 预处理结果
/// </summary>
public class PreprocessResult
{
    public bool ShouldStop { get; set; }

    public static PreprocessResult Continue() => new() { ShouldStop = false };
    public static PreprocessResult Handled() => new() { ShouldStop = true };
}

/// <summary>
/// 消息分类
/// </summary>
public enum MessageCategory
{
    PriorityAtMessage,    // @消息，最高优先级
    PrivateTextFast,      // 私聊文本，快速通道
    GroupTextFast,        // 群聊文本，快速通道
    MediaSlow,            // 媒体消息，慢速通道
    FriendRequest,        // 好友请求，立即处理
    GroupInvite,          // 群邀请，立即处理
    SystemMessage         // 系统消息，忽略
}

/// <summary>
/// 路由器统计
/// </summary>
public class RouterStats
{
    public long TotalRouted { get; set; }
    public Dictionary<string, long> CategoryStats { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

#endregion