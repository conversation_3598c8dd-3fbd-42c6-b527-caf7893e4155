namespace HappyWechat.Application.DTOs.Monitoring;

/// <summary>
/// 配置同步状态DTO
/// </summary>
public class ConfigSyncStatusDto
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }
    
    /// <summary>
    /// 状态描述
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; }
    
    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();
    
    /// <summary>
    /// 性能指标
    /// </summary>
    public ConfigSyncPerformanceDto Performance { get; set; } = new();
    
    /// <summary>
    /// 统计信息
    /// </summary>
    public ConfigSyncStatisticsDto Statistics { get; set; } = new();
    
    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 配置同步性能DTO
/// </summary>
public class ConfigSyncPerformanceDto
{
    /// <summary>
    /// 平均同步时间（毫秒）
    /// </summary>
    public double AverageSyncTime { get; set; }
    
    /// <summary>
    /// 最大同步时间（毫秒）
    /// </summary>
    public double MaxSyncTime { get; set; }
    
    /// <summary>
    /// 最小同步时间（毫秒）
    /// </summary>
    public double MinSyncTime { get; set; }
    
    /// <summary>
    /// 总同步操作数
    /// </summary>
    public long TotalSyncOperations { get; set; }
    
    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRate { get; set; }
    
    /// <summary>
    /// 每分钟吞吐量
    /// </summary>
    public double ThroughputPerMinute { get; set; }
    
    /// <summary>
    /// 指标收集时间
    /// </summary>
    public DateTime MetricsCollectedAt { get; set; }
}

/// <summary>
/// 配置同步统计DTO
/// </summary>
public class ConfigSyncStatisticsDto
{
    /// <summary>
    /// 总同步次数
    /// </summary>
    public long TotalSyncs { get; set; }
    
    /// <summary>
    /// 成功同步次数
    /// </summary>
    public long SuccessfulSyncs { get; set; }
    
    /// <summary>
    /// 失败同步次数
    /// </summary>
    public long FailedSyncs { get; set; }
    
    /// <summary>
    /// 按配置类型分组的同步次数
    /// </summary>
    public Dictionary<string, int> SyncsByConfigType { get; set; } = new();
    
    /// <summary>
    /// 按类型分组的错误次数
    /// </summary>
    public Dictionary<string, int> ErrorsByType { get; set; } = new();
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    public DateTime LastSyncTime { get; set; }
    
    /// <summary>
    /// 平均处理时间
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }
}

/// <summary>
/// 配置同步事件DTO
/// </summary>
public class ConfigSyncEventDto
{
    /// <summary>
    /// 事件ID
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public string ConfigType { get; set; } = string.Empty;
    
    /// <summary>
    /// 实体ID
    /// </summary>
    public string EntityId { get; set; } = string.Empty;
    
    /// <summary>
    /// 操作类型
    /// </summary>
    public string Operation { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 配置同步错误DTO
/// </summary>
public class ConfigSyncErrorDto
{
    /// <summary>
    /// 错误ID
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public string ConfigType { get; set; } = string.Empty;
    
    /// <summary>
    /// 实体ID
    /// </summary>
    public string EntityId { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 堆栈跟踪
    /// </summary>
    public string StackTrace { get; set; } = string.Empty;
    
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 严重性
    /// </summary>
    public string Severity { get; set; } = string.Empty;
    
    /// <summary>
    /// 上下文信息
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 配置类型同步状态DTO
/// </summary>
public class ConfigTypeSyncStatusDto
{
    /// <summary>
    /// 配置类型
    /// </summary>
    public string ConfigType { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    public DateTime LastSyncTime { get; set; }
    
    /// <summary>
    /// 待处理操作数
    /// </summary>
    public int PendingOperations { get; set; }
    
    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();
    
    /// <summary>
    /// 指标
    /// </summary>
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// 配置同步健康状态DTO
/// </summary>
public class ConfigSyncHealthStatusDto
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }
    
    /// <summary>
    /// 状态描述
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; }
    
    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();
    
    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}