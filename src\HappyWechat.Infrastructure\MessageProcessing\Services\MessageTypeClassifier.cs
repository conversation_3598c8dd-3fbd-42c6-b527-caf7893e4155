using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息类型分类器接口
/// </summary>
public interface IMessageTypeClassifier
{
    /// <summary>
    /// 分类消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>分类结果</returns>
    MessageClassificationResult ClassifyMessage(WxCallbackMessageDto message);
}

/// <summary>
/// 消息类型分类器实现
/// </summary>
public class MessageTypeClassifier : IMessageTypeClassifier
{
    private readonly ILogger<MessageTypeClassifier> _logger;

    public MessageTypeClassifier(ILogger<MessageTypeClassifier> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 分类消息
    /// </summary>
    public MessageClassificationResult ClassifyMessage(WxCallbackMessageDto message)
    {
        var result = message.MessageType switch
        {
            // 好友请求
            "30001" => new MessageClassificationResult 
            { 
                Type = Models.MessageType.FriendRequest,
                Priority = Models.MessagePriority.High,
                RequiresImmediateProcessing = true,
                Description = "好友添加请求"
            },
            
            // 离线通知
            "30000" => new MessageClassificationResult
            {
                Type = Models.MessageType.OfflineNotification,
                Priority = Models.MessagePriority.High,
                RequiresImmediateProcessing = true,
                Description = "离线通知"
            },

            // 私聊消息
            "60001" => new MessageClassificationResult
            {
                Type = Models.MessageType.PrivateText,
                Priority = Models.MessagePriority.Normal,
                RequiresAiProcessing = true,
                Description = "私聊文本消息"
            },
            "60002" => new MessageClassificationResult
            {
                Type = Models.MessageType.PrivateImage,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                Description = "私聊图片消息"
            },
            "60004" => new MessageClassificationResult
            {
                Type = Models.MessageType.PrivateVoice,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                RequiresVoiceConversion = true,
                Description = "私聊语音消息"
            },
            // 🔧 重构：60008已废弃，使用60009处理文件发送完成消息
            "60009" => new MessageClassificationResult
            {
                Type = Models.MessageType.PrivateFile,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                Description = "私聊文件发送完成消息"
            },

            // 群聊消息
            "80001" => new MessageClassificationResult
            {
                Type = Models.MessageType.GroupText,
                Priority = Models.MessagePriority.Normal,
                RequiresAiProcessing = true,
                RequiresMentionCheck = true,
                Description = "群聊文本消息"
            },
            "80002" => new MessageClassificationResult
            {
                Type = Models.MessageType.GroupImage,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                RequiresMentionCheck = true,
                Description = "群聊图片消息"
            },
            "80004" => new MessageClassificationResult
            {
                Type = Models.MessageType.GroupVoice,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                RequiresMentionCheck = true,
                RequiresVoiceConversion = true,
                Description = "群聊语音消息"
            },
            // 🔧 重构：80008已废弃，使用80009处理文件发送完成消息
            "80009" => new MessageClassificationResult
            {
                Type = Models.MessageType.GroupFile,
                Priority = Models.MessagePriority.Normal,
                RequiresFileDownload = true,
                RequiresAiProcessing = true,
                RequiresMentionCheck = true,
                Description = "群聊文件发送完成消息"
            },

            _ => new MessageClassificationResult
            {
                Type = Models.MessageType.Unknown,
                Priority = Models.MessagePriority.Low,
                Description = $"未知消息类型: {message.MessageType}"
            }
        };

        _logger.LogDebug("消息分类完成 - MessageType: {MessageType}, Type: {Type}, Priority: {Priority}, Description: {Description}",
            message.MessageType, result.Type, result.Priority, result.Description);

        return result;
    }
}
