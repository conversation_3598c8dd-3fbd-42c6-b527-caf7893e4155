using System.ComponentModel.DataAnnotations;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis认证系统配置选项
/// </summary>
public class RedisAuthenticationOptions
{
    public const string SectionName = "RedisAuthentication";

    /// <summary>
    /// 会话过期时间（分钟）
    /// </summary>
    [Range(5, 1440)]
    public int SessionExpirationMinutes { get; set; } = 480; // 8小时

    /// <summary>
    /// 会话续期时间（分钟）
    /// </summary>
    [Range(1, 60)]
    public int SessionRenewalMinutes { get; set; } = 30;

    /// <summary>
    /// 最大并发会话数（每用户）
    /// </summary>
    [Range(1, 10)]
    public int MaxConcurrentSessions { get; set; } = 3;

    /// <summary>
    /// 是否启用自动会话续期
    /// </summary>
    public bool EnableAutoRenewal { get; set; } = true;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 会话清理间隔（分钟）
    /// </summary>
    [Range(5, 60)]
    public int SessionCleanupIntervalMinutes { get; set; } = 15;

    /// <summary>
    /// Redis键前缀
    /// </summary>
    public string KeyPrefix { get; set; } = "auth";

    /// <summary>
    /// 是否启用权限缓存
    /// </summary>
    public bool EnablePermissionCache { get; set; } = true;

    /// <summary>
    /// 权限缓存过期时间（分钟）
    /// </summary>
    [Range(5, 120)]
    public int PermissionCacheExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// 是否启用会话统计
    /// </summary>
    public bool EnableSessionStatistics { get; set; } = true;

    /// <summary>
    /// 强制登出不活跃会话的时间（小时）
    /// </summary>
    [Range(1, 72)]
    public int ForceLogoutInactiveHours { get; set; } = 24;
}
