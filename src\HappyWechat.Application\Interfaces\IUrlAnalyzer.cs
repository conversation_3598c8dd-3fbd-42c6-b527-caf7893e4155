using HappyWechat.Application.DTOs.MessageProcess;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// URL分析服务接口 - 支持短链接转换和URL类型识别
/// </summary>
public interface IUrlAnalyzer
{
    /// <summary>
    /// 分析URL并确定消息类型
    /// </summary>
    /// <param name="url">要分析的URL</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>URL分析结果</returns>
    Task<UrlAnalysisResult> AnalyzeUrlAsync(string url, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量分析URL
    /// </summary>
    /// <param name="urls">URL列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>URL分析结果列表</returns>
    Task<List<UrlAnalysisResult>> AnalyzeUrlsAsync(List<string> urls, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 解析短链接获取真实URL
    /// </summary>
    /// <param name="shortUrl">短链接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>真实URL</returns>
    Task<string?> ResolveShortUrlAsync(string shortUrl, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 判断是否为短链接
    /// </summary>
    /// <param name="url">URL</param>
    /// <returns>是否为短链接</returns>
    bool IsShortUrl(string url);
    
    /// <summary>
    /// 根据URL确定消息类型
    /// </summary>
    /// <param name="url">URL</param>
    /// <param name="contentType">内容类型（可选）</param>
    /// <returns>消息类型</returns>
    string DetermineMessageType(string url, string? contentType = null);
}

/// <summary>
/// URL分析结果
/// </summary>
public class UrlAnalysisResult
{
    /// <summary>
    /// 原始URL
    /// </summary>
    public string OriginalUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 解析后的真实URL
    /// </summary>
    public string ResolvedUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型（text、image、file）
    /// </summary>
    public string MessageType { get; set; } = "text";
    
    /// <summary>
    /// 内容类型
    /// </summary>
    public string? ContentType { get; set; }
    
    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string? FileExtension { get; set; }
    
    /// <summary>
    /// 是否为短链接
    /// </summary>
    public bool IsShortUrl { get; set; }
    
    /// <summary>
    /// 分析是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 分析耗时（毫秒）
    /// </summary>
    public long AnalysisTimeMs { get; set; }
    
    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static UrlAnalysisResult CreateSuccess(string originalUrl, string resolvedUrl, string messageType, 
        string? contentType = null, string? fileExtension = null, bool isShortUrl = false, long analysisTimeMs = 0)
    {
        return new UrlAnalysisResult
        {
            OriginalUrl = originalUrl,
            ResolvedUrl = resolvedUrl,
            MessageType = messageType,
            ContentType = contentType,
            FileExtension = fileExtension,
            IsShortUrl = isShortUrl,
            Success = true,
            AnalysisTimeMs = analysisTimeMs
        };
    }
    
    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static UrlAnalysisResult CreateFailure(string originalUrl, string errorMessage, long analysisTimeMs = 0)
    {
        return new UrlAnalysisResult
        {
            OriginalUrl = originalUrl,
            ResolvedUrl = originalUrl, // 失败时使用原始URL
            MessageType = "text", // 默认为文本类型
            Success = false,
            ErrorMessage = errorMessage,
            AnalysisTimeMs = analysisTimeMs
        };
    }
}
