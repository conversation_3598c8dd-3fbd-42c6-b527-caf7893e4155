namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 当前用户上下文接口
/// 提供统一的用户身份信息访问方式，替代直接从HTTP请求头获取用户信息
/// </summary>
public interface ICurrentUserContext
{
    /// <summary>
    /// 当前用户ID
    /// </summary>
    Guid UserId { get; }
    
    /// <summary>
    /// 当前用户名
    /// </summary>
    string? Username { get; }
    
    /// <summary>
    /// 当前用户显示名称
    /// </summary>
    string? DisplayName { get; }
    
    /// <summary>
    /// 当前用户邮箱
    /// </summary>
    string? Email { get; }
    
    /// <summary>
    /// 当前用户角色列表
    /// </summary>
    IReadOnlyList<string> Roles { get; }
    
    /// <summary>
    /// 用户是否已认证
    /// </summary>
    bool IsAuthenticated { get; }
    
    /// <summary>
    /// 用户是否有指定角色
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <returns>是否有该角色</returns>
    bool HasRole(string role);
    
    /// <summary>
    /// 用户是否有任一指定角色
    /// </summary>
    /// <param name="roles">角色名称列表</param>
    /// <returns>是否有任一角色</returns>
    bool HasAnyRole(params string[] roles);
    
    /// <summary>
    /// 用户是否有所有指定角色
    /// </summary>
    /// <param name="roles">角色名称列表</param>
    /// <returns>是否有所有角色</returns>
    bool HasAllRoles(params string[] roles);
    
    /// <summary>
    /// 确保用户已认证，否则抛出异常
    /// </summary>
    /// <exception cref="UnauthorizedAccessException">用户未认证时抛出</exception>
    void EnsureAuthenticated();
    
    /// <summary>
    /// 确保用户有指定角色，否则抛出异常
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <exception cref="UnauthorizedAccessException">用户没有该角色时抛出</exception>
    void EnsureRole(string role);
}