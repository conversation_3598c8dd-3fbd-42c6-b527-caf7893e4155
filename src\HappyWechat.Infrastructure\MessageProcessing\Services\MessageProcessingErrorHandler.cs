using System.Text.Json;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息处理错误处理器接口
/// </summary>
public interface IMessageProcessingErrorHandler
{
    /// <summary>
    /// 处理处理错误
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="processingStage">处理阶段</param>
    /// <param name="processingId">处理ID</param>
    /// <returns>错误处理结果</returns>
    Task<ErrorHandlingResult> HandleProcessingErrorAsync(
        Exception exception, 
        WxCallbackMessageDto message, 
        string processingStage,
        string processingId);
}

/// <summary>
/// 消息处理错误处理器实现
/// </summary>
public class MessageProcessingErrorHandler : IMessageProcessingErrorHandler
{
    private readonly ILogger<MessageProcessingErrorHandler> _logger;
    private readonly IDatabase _redis;

    public MessageProcessingErrorHandler(
        ILogger<MessageProcessingErrorHandler> logger,
        IDatabase redis)
    {
        _logger = logger;
        _redis = redis;
    }

    /// <summary>
    /// 处理处理错误
    /// </summary>
    public async Task<ErrorHandlingResult> HandleProcessingErrorAsync(
        Exception exception, 
        WxCallbackMessageDto message, 
        string processingStage,
        string processingId)
    {
        var errorId = Guid.NewGuid().ToString();
        
        try
        {
            // 记录详细错误信息
            _logger.LogError(exception, 
                "消息处理错误 - ErrorId: {ErrorId}, ProcessingId: {ProcessingId}, Stage: {Stage}, MessageType: {MessageType}, WxManagerId: {WxManagerId}",
                errorId, processingId, processingStage, message.MessageType, message.WxManagerId);
            
            // 保存错误信息到Redis（用于监控和分析）
            await SaveErrorInfoAsync(errorId, processingId, processingStage, message, exception);
            
            // 根据错误类型决定处理策略
            var result = DetermineErrorHandlingStrategy(exception);
            result.ErrorId = errorId;
            
            _logger.LogDebug("错误处理策略确定 - ErrorId: {ErrorId}, ShouldRetry: {ShouldRetry}, Category: {Category}",
                errorId, result.ShouldRetry, result.ErrorCategory);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理错误时发生异常 - ErrorId: {ErrorId}", errorId);
            return new ErrorHandlingResult 
            { 
                ErrorId = errorId,
                ShouldRetry = false, 
                ErrorCategory = ErrorCategory.Unknown,
                ErrorMessage = $"处理错误时发生异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 保存错误信息到Redis
    /// </summary>
    private async Task SaveErrorInfoAsync(
        string errorId, 
        string processingId, 
        string processingStage, 
        WxCallbackMessageDto message, 
        Exception exception)
    {
        try
        {
            var errorInfo = new
            {
                ErrorId = errorId,
                ProcessingId = processingId,
                Stage = processingStage,
                MessageType = message.MessageType,
                WxManagerId = message.WxManagerId,
                WcId = message.WcId,
                ExceptionType = exception.GetType().Name,
                ExceptionMessage = exception.Message,
                StackTrace = exception.StackTrace,
                InnerException = exception.InnerException?.Message,
                Timestamp = DateTime.UtcNow,
                MachineName = Environment.MachineName
            };
            
            var key = $"errors:processing:{DateTime.UtcNow:yyyy-MM-dd}";
            await _redis.HashSetAsync(key, errorId, JsonSerializer.Serialize(errorInfo));
            
            // 设置过期时间（保留7天）
            await _redis.KeyExpireAsync(key, TimeSpan.FromDays(7));

            // 更新错误计数
            var countKey = $"errors:count:{DateTime.UtcNow:yyyy-MM-dd-HH}";
            await _redis.StringIncrementAsync(countKey);
            await _redis.KeyExpireAsync(countKey, TimeSpan.FromDays(1));
            
            _logger.LogDebug("错误信息已保存到Redis - ErrorId: {ErrorId}", errorId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存错误信息到Redis失败 - ErrorId: {ErrorId}", errorId);
        }
    }

    /// <summary>
    /// 确定错误处理策略
    /// </summary>
    private ErrorHandlingResult DetermineErrorHandlingStrategy(Exception exception)
    {
        return exception switch
        {
            TimeoutException => new ErrorHandlingResult 
            { 
                ShouldRetry = true, 
                RetryDelayMs = 5000,
                MaxRetries = 3,
                ErrorCategory = ErrorCategory.Temporary,
                ErrorMessage = "请求超时，将重试"
            },
            HttpRequestException => new ErrorHandlingResult 
            { 
                ShouldRetry = true, 
                RetryDelayMs = 10000,
                MaxRetries = 2,
                ErrorCategory = ErrorCategory.Network,
                ErrorMessage = "网络请求失败，将重试"
            },
            TaskCanceledException => new ErrorHandlingResult 
            { 
                ShouldRetry = true, 
                RetryDelayMs = 3000,
                MaxRetries = 2,
                ErrorCategory = ErrorCategory.Temporary,
                ErrorMessage = "任务被取消，将重试"
            },
            ArgumentException => new ErrorHandlingResult 
            { 
                ShouldRetry = false, 
                ShouldLog = true,
                ErrorCategory = ErrorCategory.DataValidation,
                ErrorMessage = "参数错误，不重试"
            },
            JsonException => new ErrorHandlingResult 
            { 
                ShouldRetry = false, 
                ShouldLog = true,
                ErrorCategory = ErrorCategory.DataFormat,
                ErrorMessage = "JSON格式错误，不重试"
            },
            UnauthorizedAccessException => new ErrorHandlingResult 
            { 
                ShouldRetry = false, 
                ShouldLog = true,
                ErrorCategory = ErrorCategory.Authorization,
                ErrorMessage = "权限不足，不重试"
            },
            _ => new ErrorHandlingResult 
            { 
                ShouldRetry = true, 
                RetryDelayMs = 3000, 
                MaxRetries = 3,
                ErrorCategory = ErrorCategory.Unknown,
                ErrorMessage = "未知错误，将重试"
            }
        };
    }
}
