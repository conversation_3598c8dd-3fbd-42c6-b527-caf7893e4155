using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MediaProcessing.Models;

namespace HappyWechat.Infrastructure.MediaProcessing;

/// <summary>
/// 媒体处理降级策略服务
/// 当主要的媒体处理流程失败时，提供各种降级方案确保系统可用性
/// </summary>
public interface IMediaProcessingFallbackService
{
    /// <summary>
    /// 创建降级处理结果
    /// </summary>
    Task<MediaProcessingResult> CreateFallbackResultAsync(
        WxCallbackMessageDto callbackMessage,
        string originalError,
        string processingId);

    /// <summary>
    /// 生成友好的错误提示消息
    /// </summary>
    string GenerateFriendlyErrorMessage(string messageType, string originalError);

    /// <summary>
    /// 检查是否可以尝试备用下载方案
    /// </summary>
    bool CanTryAlternativeDownload(string messageType, string errorMessage);
}

/// <summary>
/// 媒体处理降级策略服务实现
/// </summary>
public class MediaProcessingFallbackService : IMediaProcessingFallbackService
{
    private readonly ILogger<MediaProcessingFallbackService> _logger;
    
    /// <summary>
    /// 消息类型描述映射
    /// </summary>
    private static readonly Dictionary<string, string> MessageTypeDescriptions = new()
    {
        { "60002", "图片" }, { "80002", "图片" },
        { "60004", "语音" }, { "80004", "语音" },
        { "60009", "文件" }, { "80009", "文件" }
    };

    /// <summary>
    /// 友好的错误提示模板
    /// </summary>
    private static readonly Dictionary<string, string> FriendlyMessageTemplates = new()
    {
        { "60002", "抱歉，图片处理遇到了问题。您可以尝试重新发送图片，或者直接告诉我图片的内容，我会尽力帮助您。" },
        { "80002", "抱歉，图片处理遇到了问题。您可以尝试重新发送图片，或者直接描述图片内容，我会尽力帮助您。" },
        { "60004", "抱歉，语音处理遇到了问题。您可以尝试重新发送语音，或者直接打字告诉我您想表达的内容。" },
        { "80004", "抱歉，语音处理遇到了问题。您可以尝试重新发送语音，或者直接打字告诉我您想表达的内容。" },
        { "60009", "抱歉，文件处理遇到了问题。您可以尝试重新发送文件，或者告诉我文件的主要内容，我会尽力帮助您。" },
        { "80009", "抱歉，文件处理遇到了问题。您可以尝试重新发送文件，或者告诉我文件的主要内容，我会尽力帮助您。" }
    };

    public MediaProcessingFallbackService(ILogger<MediaProcessingFallbackService> logger)
    {
        _logger = logger;
    }

    public async Task<MediaProcessingResult> CreateFallbackResultAsync(
        WxCallbackMessageDto callbackMessage,
        string originalError,
        string processingId)
    {
        await Task.CompletedTask; // 异步方法，当前不需要异步操作

        var messageType = callbackMessage.MessageType ?? "unknown";
        var mediaTypeDescription = GetMediaTypeDescription(messageType);
        
        _logger.LogInformation("[{ProcessingId}] 创建媒体处理降级结果 - MessageType: {MessageType}, Error: {Error}",
            processingId, messageType, originalError);

        // 生成友好的错误消息
        var friendlyMessage = GenerateFriendlyErrorMessage(messageType, originalError);
        
        // 生成降级URL（用于标识这是降级处理）
        var fallbackUrl = $"fallback://media-processing-failed/{messageType}/{processingId}";
        
        // 生成降级文件名
        var fallbackFileName = $"fallback_{mediaTypeDescription}_{DateTime.Now:yyyyMMddHHmmss}";

        var result = MediaProcessingResult.CreateSuccess(
            publicUrl: fallbackUrl,
            fileName: fallbackFileName,
            fileSize: 0,
            mediaType: "fallback",
            messageType: messageType,
            fallbackMessage: friendlyMessage
        );

        // 添加降级处理的元数据
        result.Metadata["IsFallback"] = true;
        result.Metadata["OriginalError"] = originalError;
        result.Metadata["FallbackStrategy"] = "FriendlyMessage";
        result.Metadata["ProcessingId"] = processingId;
        result.Metadata["CreatedAt"] = DateTime.UtcNow;

        _logger.LogInformation("[{ProcessingId}] 降级结果创建完成 - Type: {Type}, Message: {Message}",
            processingId, mediaTypeDescription, friendlyMessage);

        return result;
    }

    public string GenerateFriendlyErrorMessage(string messageType, string originalError)
    {
        // 使用预定义的友好消息模板
        if (FriendlyMessageTemplates.TryGetValue(messageType, out var template))
        {
            return template;
        }

        // 根据错误类型生成动态消息
        var mediaTypeDescription = GetMediaTypeDescription(messageType);
        var errorCategory = CategorizeError(originalError);

        return errorCategory switch
        {
            ErrorCategory.Network => $"网络连接不稳定，{mediaTypeDescription}处理暂时失败。请稍后重试，或者直接描述{mediaTypeDescription}内容。",
            ErrorCategory.Format => $"{mediaTypeDescription}格式可能不支持，请尝试其他格式，或者直接描述{mediaTypeDescription}内容。",
            ErrorCategory.Permission => $"没有权限访问该{mediaTypeDescription}，请检查{mediaTypeDescription}是否仍然可用。",
            ErrorCategory.Size => $"{mediaTypeDescription}可能过大，请尝试压缩后重新发送，或者直接描述{mediaTypeDescription}内容。",
            ErrorCategory.Timeout => $"{mediaTypeDescription}处理超时，可能是因为{mediaTypeDescription}较大。请稍后重试，或者直接描述{mediaTypeDescription}内容。",
            _ => $"抱歉，{mediaTypeDescription}处理遇到了问题。您可以尝试重新发送，或者直接告诉我{mediaTypeDescription}的内容。"
        };
    }

    public bool CanTryAlternativeDownload(string messageType, string errorMessage)
    {
        if (string.IsNullOrEmpty(errorMessage))
            return true;

        var lowerError = errorMessage.ToLowerInvariant();
        
        // 网络相关错误可以尝试备用方案
        var networkErrors = new[] { "timeout", "connection", "network", "连接", "超时", "网络" };
        if (networkErrors.Any(keyword => lowerError.Contains(keyword)))
        {
            _logger.LogDebug("网络错误，可以尝试备用下载方案 - Error: {Error}", errorMessage);
            return true;
        }

        // 服务器错误可以尝试备用方案
        var serverErrors = new[] { "502", "503", "504", "server error", "服务器" };
        if (serverErrors.Any(keyword => lowerError.Contains(keyword)))
        {
            _logger.LogDebug("服务器错误，可以尝试备用下载方案 - Error: {Error}", errorMessage);
            return true;
        }

        // 权限或格式错误通常不适合备用方案
        var nonRetriableErrors = new[] { "forbidden", "unauthorized", "invalid", "corrupt", "权限", "无效", "损坏" };
        if (nonRetriableErrors.Any(keyword => lowerError.Contains(keyword)))
        {
            _logger.LogDebug("权限或格式错误，不适合备用下载方案 - Error: {Error}", errorMessage);
            return false;
        }

        return true; // 默认可以尝试
    }

    /// <summary>
    /// 获取媒体类型描述
    /// </summary>
    private string GetMediaTypeDescription(string messageType)
    {
        return MessageTypeDescriptions.TryGetValue(messageType, out var description) 
            ? description 
            : "媒体";
    }

    /// <summary>
    /// 错误分类枚举
    /// </summary>
    private enum ErrorCategory
    {
        Network,
        Format,
        Permission,
        Size,
        Timeout,
        Unknown
    }

    /// <summary>
    /// 根据错误消息对错误进行分类
    /// </summary>
    private ErrorCategory CategorizeError(string errorMessage)
    {
        if (string.IsNullOrEmpty(errorMessage))
            return ErrorCategory.Unknown;

        var lowerError = errorMessage.ToLowerInvariant();

        if (new[] { "timeout", "超时", "time out" }.Any(keyword => lowerError.Contains(keyword)))
            return ErrorCategory.Timeout;

        if (new[] { "network", "connection", "连接", "网络", "dns", "socket" }.Any(keyword => lowerError.Contains(keyword)))
            return ErrorCategory.Network;

        if (new[] { "format", "invalid", "corrupt", "格式", "无效", "损坏", "解析" }.Any(keyword => lowerError.Contains(keyword)))
            return ErrorCategory.Format;

        if (new[] { "permission", "forbidden", "unauthorized", "权限", "禁止", "未授权" }.Any(keyword => lowerError.Contains(keyword)))
            return ErrorCategory.Permission;

        if (new[] { "size", "large", "big", "大小", "过大", "超出" }.Any(keyword => lowerError.Contains(keyword)))
            return ErrorCategory.Size;

        return ErrorCategory.Unknown;
    }
}