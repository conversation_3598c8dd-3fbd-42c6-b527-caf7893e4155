namespace HappyWechat.Infrastructure.MessageProcessing.Configuration;

/// <summary>
/// 消息处理配置
/// </summary>
public class MessageProcessingConfiguration
{
    /// <summary>
    /// 是否启用消息处理
    /// </summary>
    public bool EnableMessageProcessing { get; set; } = true;

    /// <summary>
    /// 最大并发处理数
    /// </summary>
    public int MaxConcurrentProcessing { get; set; } = 10;

    /// <summary>
    /// 消息处理超时时间（秒）
    /// </summary>
    public int ProcessingTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试配置
    /// </summary>
    public RetryConfiguration Retry { get; set; } = new();

    /// <summary>
    /// 队列配置
    /// </summary>
    public QueueConfiguration Queue { get; set; } = new();

    /// <summary>
    /// AI处理配置
    /// </summary>
    public AiProcessingConfiguration AiProcessing { get; set; } = new();

    /// <summary>
    /// 媒体处理配置
    /// </summary>
    public MediaProcessingConfiguration MediaProcessing { get; set; } = new();

    /// <summary>
    /// 敏感词检测配置
    /// </summary>
    public SensitiveWordConfiguration SensitiveWord { get; set; } = new();
}

/// <summary>
/// 重试配置
/// </summary>
public class RetryConfiguration
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用指数退避
    /// </summary>
    public bool EnableExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 退避倍数
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;
}

/// <summary>
/// 队列配置
/// </summary>
public class QueueConfiguration
{
    /// <summary>
    /// 队列名称前缀
    /// </summary>
    public string QueueNamePrefix { get; set; } = "wx_message";

    /// <summary>
    /// 批处理大小
    /// </summary>
    public int BatchSize { get; set; } = 10;

    /// <summary>
    /// 消息TTL（秒）
    /// </summary>
    public int MessageTtlSeconds { get; set; } = 3600;

    /// <summary>
    /// 死信队列配置
    /// </summary>
    public bool EnableDeadLetterQueue { get; set; } = true;
}

/// <summary>
/// AI处理配置
/// </summary>
public class AiProcessingConfiguration
{
    /// <summary>
    /// 是否启用AI处理
    /// </summary>
    public bool EnableAiProcessing { get; set; } = true;

    /// <summary>
    /// AI处理超时时间（秒）
    /// </summary>
    public int AiProcessingTimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// 最大消息长度
    /// </summary>
    public int MaxMessageLength { get; set; } = 2000;

    /// <summary>
    /// 是否启用上下文记忆
    /// </summary>
    public bool EnableContextMemory { get; set; } = true;

    /// <summary>
    /// 上下文记忆条数
    /// </summary>
    public int ContextMemoryCount { get; set; } = 10;
}

/// <summary>
/// 媒体处理配置
/// </summary>
public class MediaProcessingConfiguration
{
    /// <summary>
    /// 是否启用媒体处理
    /// </summary>
    public bool EnableMediaProcessing { get; set; } = true;

    /// <summary>
    /// 最大文件大小（MB）
    /// </summary>
    public int MaxFileSizeMb { get; set; } = 100;

    /// <summary>
    /// 支持的图片格式
    /// </summary>
    public List<string> SupportedImageFormats { get; set; } = new() { "jpg", "jpeg", "png", "gif", "webp" };

    /// <summary>
    /// 支持的音频格式
    /// </summary>
    public List<string> SupportedAudioFormats { get; set; } = new() { "mp3", "wav", "silk", "amr" };

    /// <summary>
    /// 支持的视频格式
    /// </summary>
    public List<string> SupportedVideoFormats { get; set; } = new() { "mp4", "avi", "mov" };

    /// <summary>
    /// 媒体处理超时时间（秒）
    /// </summary>
    public int MediaProcessingTimeoutSeconds { get; set; } = 120;
}

/// <summary>
/// 敏感词检测配置
/// </summary>
public class SensitiveWordConfiguration
{
    /// <summary>
    /// 是否启用敏感词检测
    /// </summary>
    public bool EnableSensitiveWordDetection { get; set; } = true;

    /// <summary>
    /// 敏感词处理方式
    /// </summary>
    public SensitiveWordAction Action { get; set; } = SensitiveWordAction.Replace;

    /// <summary>
    /// 替换字符
    /// </summary>
    public string ReplacementChar { get; set; } = "*";

    /// <summary>
    /// 是否记录敏感词日志
    /// </summary>
    public bool LogSensitiveWords { get; set; } = true;
}

/// <summary>
/// 敏感词处理方式
/// </summary>
public enum SensitiveWordAction
{
    /// <summary>
    /// 忽略
    /// </summary>
    Ignore = 0,

    /// <summary>
    /// 替换
    /// </summary>
    Replace = 1,

    /// <summary>
    /// 阻止
    /// </summary>
    Block = 2,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 3
}
