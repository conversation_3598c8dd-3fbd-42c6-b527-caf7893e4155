using Microsoft.AspNetCore.Http;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// SessionId获取服务接口
/// 定义统一的多源获取契约，支持优先级和降级机制
/// </summary>
public interface ISessionIdRetrievalService
{
    /// <summary>
    /// 获取SessionId - 按优先级从多个来源获取
    /// </summary>
    /// <param name="httpContext">HTTP上下文（可选）</param>
    /// <returns>SessionId或null</returns>
    Task<string?> GetSessionIdAsync(HttpContext? httpContext = null);

    /// <summary>
    /// 从指定策略获取SessionId
    /// </summary>
    /// <param name="strategyType">策略类型</param>
    /// <param name="httpContext">HTTP上下文（可选）</param>
    /// <returns>SessionId或null</returns>
    Task<string?> GetSessionIdFromStrategyAsync(SessionIdRetrievalStrategyType strategyType, HttpContext? httpContext = null);

    /// <summary>
    /// 验证SessionId是否有效
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateSessionIdAsync(string sessionId);

    /// <summary>
    /// 获取所有可用的获取策略统计信息
    /// </summary>
    /// <returns>策略统计信息</returns>
    Task<SessionIdRetrievalStats> GetRetrievalStatsAsync();
}

/// <summary>
/// SessionId获取策略类型
/// </summary>
public enum SessionIdRetrievalStrategyType
{
    /// <summary>
    /// 内存缓存
    /// </summary>
    MemoryCache = 1,

    /// <summary>
    /// HTTP上下文（Header、Cookie、查询参数）
    /// </summary>
    HttpContext = 2,

    /// <summary>
    /// Blazor ProtectedLocalStorage
    /// </summary>
    ProtectedLocalStorage = 3,

    /// <summary>
    /// 传统RedisAuthenticationStateProvider
    /// </summary>
    LegacyProvider = 4,

    /// <summary>
    /// JavaScript互操作
    /// </summary>
    JavaScriptInterop = 5
}

/// <summary>
/// SessionId获取统计信息
/// </summary>
public class SessionIdRetrievalStats
{
    /// <summary>
    /// 总获取次数
    /// </summary>
    public int TotalRetrievals { get; set; }

    /// <summary>
    /// 成功获取次数
    /// </summary>
    public int SuccessfulRetrievals { get; set; }

    /// <summary>
    /// 失败获取次数
    /// </summary>
    public int FailedRetrievals { get; set; }

    /// <summary>
    /// 各策略的使用统计
    /// </summary>
    public Dictionary<SessionIdRetrievalStrategyType, StrategyUsageStats> StrategyStats { get; set; } = new();

    /// <summary>
    /// 最后一次成功获取的策略
    /// </summary>
    public SessionIdRetrievalStrategyType? LastSuccessfulStrategy { get; set; }

    /// <summary>
    /// 最后一次获取时间
    /// </summary>
    public DateTime? LastRetrievalTime { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalRetrievals > 0 ? (double)SuccessfulRetrievals / TotalRetrievals : 0;
}

/// <summary>
/// 策略使用统计
/// </summary>
public class StrategyUsageStats
{
    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTimeMs { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => UsageCount > 0 ? (double)SuccessCount / UsageCount : 0;
}
