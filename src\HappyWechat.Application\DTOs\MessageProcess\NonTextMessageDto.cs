namespace HappyWechat.Application.DTOs.MessageProcess;

/// <summary>
/// 非文本消息DTO
/// </summary>
public class NonTextMessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 微信实例ID
    /// </summary>
    public string WId { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者微信ID
    /// </summary>
    public string FromUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者昵称
    /// </summary>
    public string FromUserNickName { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public NonTextMessageType MessageType { get; set; }
    
    /// <summary>
    /// 原始文件URL（微信服务器地址）
    /// </summary>
    public string OriginalUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 本地文件路径
    /// </summary>
    public string? LocalFilePath { get; set; }
    
    /// <summary>
    /// 存储系统中的文件路径
    /// </summary>
    public string? StorageFilePath { get; set; }
    
    /// <summary>
    /// 存储系统中的访问URL
    /// </summary>
    public string? StorageUrl { get; set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string FileExtension { get; set; } = string.Empty;
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string MimeType { get; set; } = string.Empty;
    
    /// <summary>
    /// 处理状态
    /// </summary>
    public MessageProcessStatus ProcessStatus { get; set; } = MessageProcessStatus.Pending;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 下载开始时间
    /// </summary>
    public DateTime? DownloadStartTime { get; set; }
    
    /// <summary>
    /// 下载完成时间
    /// </summary>
    public DateTime? DownloadCompletedTime { get; set; }
    
    /// <summary>
    /// 上传开始时间
    /// </summary>
    public DateTime? UploadStartTime { get; set; }
    
    /// <summary>
    /// 上传完成时间
    /// </summary>
    public DateTime? UploadCompletedTime { get; set; }
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object>? ExtendedProperties { get; set; }
    
    /// <summary>
    /// 下载耗时（毫秒）
    /// </summary>
    public long DownloadDurationMs => DownloadCompletedTime.HasValue && DownloadStartTime.HasValue 
        ? (long)(DownloadCompletedTime.Value - DownloadStartTime.Value).TotalMilliseconds 
        : 0;
    
    /// <summary>
    /// 上传耗时（毫秒）
    /// </summary>
    public long UploadDurationMs => UploadCompletedTime.HasValue && UploadStartTime.HasValue 
        ? (long)(UploadCompletedTime.Value - UploadStartTime.Value).TotalMilliseconds 
        : 0;
    
    /// <summary>
    /// 总处理耗时（毫秒）
    /// </summary>
    public long TotalProcessDurationMs => UploadCompletedTime.HasValue && DownloadStartTime.HasValue 
        ? (long)(UploadCompletedTime.Value - DownloadStartTime.Value).TotalMilliseconds 
        : 0;
}

/// <summary>
/// 非文本消息类型
/// </summary>
public enum NonTextMessageType
{
    /// <summary>
    /// 图片
    /// </summary>
    Image = 1,
    
    /// <summary>
    /// 语音
    /// </summary>
    Voice = 2,
    
    /// <summary>
    /// 视频
    /// </summary>
    Video = 3,
    
    /// <summary>
    /// 文件
    /// </summary>
    File = 4,
    
    /// <summary>
    /// 表情包
    /// </summary>
    Emoji = 5,
    
    /// <summary>
    /// 位置信息
    /// </summary>
    Location = 6
}

/// <summary>
/// 消息处理状态
/// </summary>
public enum MessageProcessStatus
{
    /// <summary>
    /// 等待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在下载
    /// </summary>
    Downloading = 1,
    
    /// <summary>
    /// 下载完成
    /// </summary>
    Downloaded = 2,
    
    /// <summary>
    /// 正在上传
    /// </summary>
    Uploading = 3,
    
    /// <summary>
    /// 处理完成
    /// </summary>
    Completed = 4,
    
    /// <summary>
    /// 处理失败
    /// </summary>
    Failed = 5,
    
    /// <summary>
    /// 已跳过（不需要处理）
    /// </summary>
    Skipped = 6
}

/// <summary>
/// 消息处理配置
/// </summary>
public class MessageProcessOptions
{
    /// <summary>
    /// 最大文件大小（字节，默认50MB）
    /// </summary>
    public long MaxFileSize { get; set; } = 50 * 1024 * 1024;
    
    /// <summary>
    /// 支持的图片格式
    /// </summary>
    public List<string> SupportedImageFormats { get; set; } = new() { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
    
    /// <summary>
    /// 支持的视频格式
    /// </summary>
    public List<string> SupportedVideoFormats { get; set; } = new() { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv" };
    
    /// <summary>
    /// 支持的音频格式
    /// </summary>
    public List<string> SupportedAudioFormats { get; set; } = new() { ".mp3", ".wav", ".flac", ".aac", ".m4a", ".amr" };
    
    /// <summary>
    /// 支持的文档格式
    /// </summary>
    public List<string> SupportedDocumentFormats { get; set; } = new() { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt" };
    
    /// <summary>
    /// 下载超时时间（秒）
    /// </summary>
    public int DownloadTimeoutSeconds { get; set; } = 60;
    
    /// <summary>
    /// 上传超时时间（秒）
    /// </summary>
    public int UploadTimeoutSeconds { get; set; } = 120;
    
    /// <summary>
    /// 是否启用文件压缩
    /// </summary>
    public bool EnableCompression { get; set; } = true;
    
    /// <summary>
    /// 图片压缩质量（1-100）
    /// </summary>
    public int ImageCompressionQuality { get; set; } = 85;
    
    /// <summary>
    /// 临时文件保存目录
    /// </summary>
    public string TempDirectory { get; set; } = Path.Combine(Path.GetTempPath(), "HappyWechat", "NonTextMessages");
    
    /// <summary>
    /// 文件保存天数（超过后自动清理）
    /// </summary>
    public int FileRetentionDays { get; set; } = 30;
}

/// <summary>
/// 消息处理结果
/// </summary>
public class MessageProcessResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 处理后的消息
    /// </summary>
    public NonTextMessageDto? ProcessedMessage { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ProcessDurationMs { get; set; }
    
    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static MessageProcessResult Success(NonTextMessageDto processedMessage, long durationMs = 0)
    {
        return new MessageProcessResult
        {
            IsSuccess = true,
            ProcessedMessage = processedMessage,
            ProcessDurationMs = durationMs
        };
    }
    
    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static MessageProcessResult Failure(string errorMessage, long durationMs = 0)
    {
        return new MessageProcessResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ProcessDurationMs = durationMs
        };
    }
}
