using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace HappyWechat.Infrastructure.MessageQueue.Unified;

public class UnifiedMessageQueue : IUnifiedMessageQueue
{
    private readonly ISimplifiedQueueService _queueService;
    private readonly ILogger<UnifiedMessageQueue> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string AI_REQUEST_QUEUE = "ai_request";
    private const string MEDIA_PROCESSING_QUEUE = "media_processing";

    public UnifiedMessageQueue(
        ISimplifiedQueueService queueService,
        ILogger<UnifiedMessageQueue> logger)
    {
        _queueService = queueService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// 入队AI请求
    /// </summary>
    public async Task<string> EnqueueAiRequestAsync(Guid wxManagerId, string aiTemplate, WxCallbackMessageDto originalMessage, string processingId, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new HappyWechat.Application.Interfaces.UnifiedAiRequest
            {
                Id = Guid.NewGuid().ToString("N"),
                WxManagerId = wxManagerId,
                AiTemplate = aiTemplate,
                OriginalMessage = originalMessage,
                ProcessingId = processingId,
                CreatedAt = DateTime.UtcNow,
                Priority = DeterminePriority(originalMessage)
            };

            var messageId = await _queueService.EnqueueAsync(wxManagerId, AI_REQUEST_QUEUE, request, request.Priority, 3, null, cancellationToken);
            
            _logger.LogInformation("[{ProcessingId}] 📝 AI请求已入队 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, Priority: {Priority}",
                processingId, messageId, wxManagerId, request.Priority);

            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ AI请求入队失败 - WxManagerId: {WxManagerId}", processingId, wxManagerId);
            throw;
        }
    }

    /// <summary>
    /// 入队媒体处理请求
    /// </summary>
    public async Task<string> EnqueueMediaProcessingAsync(Guid wxManagerId, WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new HappyWechat.Application.Interfaces.UnifiedMediaRequest
            {
                Id = Guid.NewGuid().ToString("N"),
                WxManagerId = wxManagerId,
                CallbackMessage = callbackMessage,
                ProcessingId = processingId,
                CreatedAt = DateTime.UtcNow,
                Priority = DeterminePriority(callbackMessage)
            };

            var messageId = await _queueService.EnqueueAsync(wxManagerId, MEDIA_PROCESSING_QUEUE, request, request.Priority, 3, null, cancellationToken);
            
            _logger.LogInformation("[{ProcessingId}] 📁 媒体处理请求已入队 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, MessageType: {MessageType}",
                processingId, messageId, wxManagerId, callbackMessage.MessageType);

            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 媒体处理请求入队失败 - WxManagerId: {WxManagerId}", processingId, wxManagerId);
            throw;
        }
    }

    /// <summary>
    /// 出队AI请求
    /// </summary>
    public async Task<List<UnifiedAiRequest>> DequeueAiRequestsAsync(Guid wxManagerId, int maxCount = 5, CancellationToken cancellationToken = default)
    {
        try
        {
            var results = new List<UnifiedAiRequest>();
            
            for (int i = 0; i < maxCount; i++)
            {
                var message = await _queueService.DequeueAsync<HappyWechat.Application.Interfaces.UnifiedAiRequest>(wxManagerId, AI_REQUEST_QUEUE, 0, cancellationToken);
                if (message?.Data == null)
                    break;

                results.Add(message.Data);
            }

            if (results.Any())
            {
                _logger.LogDebug("📦 出队AI请求 - WxManagerId: {WxManagerId}, Count: {Count}", wxManagerId, results.Count);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 出队AI请求失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new List<UnifiedAiRequest>();
        }
    }

    /// <summary>
    /// 出队媒体处理请求
    /// </summary>
    public async Task<List<UnifiedMediaRequest>> DequeueMediaRequestsAsync(Guid wxManagerId, int maxCount = 3, CancellationToken cancellationToken = default)
    {
        try
        {
            var results = new List<UnifiedMediaRequest>();
            
            for (int i = 0; i < maxCount; i++)
            {
                var message = await _queueService.DequeueAsync<HappyWechat.Application.Interfaces.UnifiedMediaRequest>(wxManagerId, MEDIA_PROCESSING_QUEUE, 0, cancellationToken);
                if (message?.Data == null)
                    break;

                results.Add(message.Data);
            }

            if (results.Any())
            {
                _logger.LogDebug("📦 出队媒体处理请求 - WxManagerId: {WxManagerId}, Count: {Count}", wxManagerId, results.Count);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 出队媒体处理请求失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new List<UnifiedMediaRequest>();
        }
    }

    /// <summary>
    /// 标记任务完成
    /// </summary>
    public async Task MarkTaskCompletedAsync(Guid wxManagerId, string taskId, bool success, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var statusKey = $"task_status:{taskId}";
            var status = new
            {
                TaskId = taskId,
                WxManagerId = wxManagerId,
                Success = success,
                ErrorMessage = errorMessage,
                CompletedAt = DateTime.UtcNow
            };

            await _queueService.SetKeyAsync(statusKey, JsonSerializer.Serialize(status, _jsonOptions), TimeSpan.FromHours(24), cancellationToken);
            
            if (success)
            {
                _logger.LogDebug("✅ 任务标记为完成 - TaskId: {TaskId}", taskId);
            }
            else
            {
                _logger.LogWarning("❌ 任务标记为失败 - TaskId: {TaskId}, Error: {Error}", taskId, errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 标记任务状态失败 - TaskId: {TaskId}", taskId);
        }
    }

    /// <summary>
    /// 获取队列状态
    /// </summary>
    public async Task<UnifiedQueueStatus> GetQueueStatusAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var aiQueueName = _queueService.GenerateQueueName(wxManagerId, AI_REQUEST_QUEUE);
            var mediaQueueName = _queueService.GenerateQueueName(wxManagerId, MEDIA_PROCESSING_QUEUE);

            var aiQueueExists = await _queueService.QueueExistsAsync(aiQueueName);
            var mediaQueueExists = await _queueService.QueueExistsAsync(mediaQueueName);

            return new UnifiedQueueStatus
            {
                WxManagerId = wxManagerId,
                AiRequestQueueLength = aiQueueExists ? await GetQueueLengthAsync(aiQueueName) : 0,
                MediaProcessingQueueLength = mediaQueueExists ? await GetQueueLengthAsync(mediaQueueName) : 0,
                LastCheckedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列状态失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new UnifiedQueueStatus { WxManagerId = wxManagerId };
        }
    }

    /// <summary>
    /// 确定消息优先级
    /// </summary>
    private int DeterminePriority(WxCallbackMessageDto callbackMessage)
    {
        // 群聊@消息优先级最高
        if (callbackMessage.MessageType == "80001")
            return 10;

        // 私聊文本消息优先级较高
        if (callbackMessage.MessageType == "60001" && string.IsNullOrEmpty(callbackMessage.Data?.FromGroup))
            return 8;

        // 群聊文本消息优先级中等
        if (callbackMessage.MessageType == "60001")
            return 6;

        // 语音消息优先级较高（需要及时处理）
        if (callbackMessage.MessageType == "60004")
            return 7;

        // 图片消息优先级中等
        if (callbackMessage.MessageType == "60002")
            return 5;

        // 文件消息优先级较低
        if (callbackMessage.MessageType == "60009")
            return 3;

        // 默认优先级
        return 5;
    }

    /// <summary>
    /// 获取队列长度（需要实现具体逻辑）
    /// </summary>
    private async Task<int> GetQueueLengthAsync(string queueName)
    {
        // 这里需要调用底层Redis获取队列长度
        // 暂时返回0，后续实现
        return 0;
    }
}


/// <summary>
/// 统一队列状态
/// </summary>
public class UnifiedQueueStatus
{
    public Guid WxManagerId { get; set; }
    public int AiRequestQueueLength { get; set; }
    public int MediaProcessingQueueLength { get; set; }
    public DateTime LastCheckedAt { get; set; }
}


