using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageQueue.Unified;

/// <summary>
/// 统一消息消费者
/// 账号级别隔离，真正并行处理，完全替代所有旧消费者
/// </summary>
public class UnifiedMessageConsumer : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<UnifiedMessageConsumer> _logger;
    private readonly ConcurrentDictionary<Guid, AccountProcessingContext> _accountContexts = new();
    private readonly ConcurrentDictionary<Guid, SemaphoreSlim> _accountSemaphores = new();
    private readonly Timer _accountHealthCheckTimer;

    public UnifiedMessageConsumer(
        IServiceProvider serviceProvider,
        ILogger<UnifiedMessageConsumer> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        // 账号健康检查定时器
        _accountHealthCheckTimer = new Timer(PerformAccountHealthCheck, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAllAccountsInParallelAsync(stoppingToken);
                await Task.Delay(500, stoppingToken); // 减少轮询间隔
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("🛑 统一消息消费者收到停止信号");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 统一消息消费者全局异常");
                await Task.Delay(2000, stoppingToken);
            }
        }

        await ShutdownAllAccountsAsync();
        _logger.LogInformation("🛑 统一消息消费者已停止");
    }

    /// <summary>
    /// 真正的并行处理所有账号 - 每个账号独立Task
    /// </summary>
    private async Task ProcessAllAccountsInParallelAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var activeWxManagerIds = await GetActiveWxManagerIdsAsync(scope.ServiceProvider, cancellationToken);

            if (!activeWxManagerIds.Any())
            {
                _logger.LogDebug("🔍 未发现活跃的微信管理器，跳过本轮处理");
                return;
            }

            _logger.LogDebug("📋 发现 {Count} 个活跃微信管理器，开始并行处理AI和媒体请求", activeWxManagerIds.Count);

            // 为每个账号创建或获取处理上下文
            var accountTasks = new List<Task>();
            var healthyAccountCount = 0;

            foreach (var wxManagerId in activeWxManagerIds)
            {
                var context = _accountContexts.GetOrAdd(wxManagerId, id => new AccountProcessingContext
                {
                    WxManagerId = id,
                    IsHealthy = true,
                    LastProcessedAt = DateTime.UtcNow,
                    ProcessingErrors = new ConcurrentQueue<AccountError>(),
                    CircuitBreakerState = CircuitBreakerState.Closed
                });

                // 跳过不健康的账号
                if (!context.IsHealthy || context.CircuitBreakerState == CircuitBreakerState.Open)
                {
                    _logger.LogDebug("⚠️ 跳过不健康账号 - WxManagerId: {WxManagerId}, IsHealthy: {IsHealthy}, CircuitBreakerState: {State}",
                        wxManagerId, context.IsHealthy, context.CircuitBreakerState);
                    continue;
                }

                healthyAccountCount++;
                // 每个账号独立的处理任务
                var accountTask = ProcessSingleAccountAsync(wxManagerId, context, cancellationToken);
                accountTasks.Add(accountTask);
            }

            if (healthyAccountCount > 0)
            {
                _logger.LogDebug("🚀 启动 {Count} 个健康账号的并行处理任务", healthyAccountCount);
            }

            // 真正的并行处理 - 所有账号同时执行
            if (accountTasks.Any())
            {
                await Task.WhenAll(accountTasks);
            }

            // 清理已下线的账号
            await CleanupInactiveAccountsAsync(activeWxManagerIds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 并行处理所有账号时发生异常");
        }
    }

    /// <summary>
    /// 处理单个账号 - 完全隔离的错误处理和熔断机制
    /// </summary>
    private async Task ProcessSingleAccountAsync(
        Guid wxManagerId, 
        AccountProcessingContext context, 
        CancellationToken cancellationToken)
    {
        var semaphore = _accountSemaphores.GetOrAdd(wxManagerId, _ => new SemaphoreSlim(1, 1));
        
        if (!await semaphore.WaitAsync(50, cancellationToken))
        {
            return; // 避免同一账号重复处理
        }

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 检查熔断器状态
            if (await ShouldSkipDueToCircuitBreakerAsync(context))
            {
                return;
            }

            var unifiedMessageQueue = scope.ServiceProvider.GetRequiredService<IUnifiedMessageQueue>();
            var mediaToAiProcessor = scope.ServiceProvider.GetRequiredService<IMediaToAiProcessor>();
            var aiAgentService = scope.ServiceProvider.GetRequiredService<IAiAgentService>();
            var unifiedAiResponseProcessor = scope.ServiceProvider.GetRequiredService<IUnifiedAiResponseProcessor>();

            var processedCount = 0;

            // 1. 处理媒体消息（账号级别隔离）
            var mediaCount = await ProcessAccountMediaMessagesAsync(
                unifiedMessageQueue, mediaToAiProcessor, wxManagerId, context, cancellationToken);
            processedCount += mediaCount;

            if (mediaCount > 0)
            {
                _logger.LogInformation("📱 账号媒体消息处理完成 - WxManagerId: {WxManagerId}, 处理数量: {Count}",
                    wxManagerId, mediaCount);
            }

            // 2. 处理AI请求（账号级别隔离）
            var aiCount = await ProcessAccountAiRequestsAsync(
                unifiedMessageQueue, aiAgentService, unifiedAiResponseProcessor, wxManagerId, context, cancellationToken);
            processedCount += aiCount;

            if (aiCount > 0)
            {
                _logger.LogInformation("🤖 账号AI请求处理完成 - WxManagerId: {WxManagerId}, 处理数量: {Count}",
                    wxManagerId, aiCount);
            }

            // 更新处理状态
            context.LastProcessedAt = DateTime.UtcNow;
            context.ProcessedMessageCount += processedCount;
            context.LastProcessingDuration = stopwatch.Elapsed;

            // 成功处理后重置错误计数
            if (processedCount > 0)
            {
                await ResetCircuitBreakerAsync(context);
            }

            if (processedCount > 0)
            {
                _logger.LogDebug("✅ 账号处理完成 - WxManagerId: {WxManagerId}, 总处理数量: {Count}, 耗时: {Duration}ms",
                    wxManagerId, processedCount, stopwatch.ElapsedMilliseconds);
            }
        }
        catch (Exception ex)
        {
            await HandleAccountErrorAsync(wxManagerId, context, ex);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// 处理账号的媒体消息 - 隔离错误处理，增强队列监控
    /// </summary>
    private async Task<int> ProcessAccountMediaMessagesAsync(
        IUnifiedMessageQueue unifiedMessageQueue,
        IMediaToAiProcessor mediaToAiProcessor,
        Guid wxManagerId,
        AccountProcessingContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var mediaRequests = await unifiedMessageQueue.DequeueMediaRequestsAsync(wxManagerId, 5, cancellationToken);
            var processedCount = 0;

            if (mediaRequests.Any())
            {
                _logger.LogDebug("📦 获取到媒体处理请求 - WxManagerId: {WxManagerId}, Count: {Count}, Types: [{Types}]",
                    wxManagerId, mediaRequests.Count, 
                    string.Join(", ", mediaRequests.Select(r => r.CallbackMessage?.MessageType ?? "Unknown")));
            }

            foreach (var mediaRequest in mediaRequests)
            {
                try
                {
                    await ProcessSingleMediaRequestAsync(mediaToAiProcessor, unifiedMessageQueue, mediaRequest, cancellationToken);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    var messageType = mediaRequest.CallbackMessage?.MessageType ?? "Unknown";
                    _logger.LogError(ex, "❌ 单个媒体请求处理失败 - WxManagerId: {WxManagerId}, RequestId: {RequestId}, MessageType: {MessageType}",
                        wxManagerId, mediaRequest.Id, messageType);
                    
                    await RecordAccountErrorAsync(context, $"媒体处理失败[{messageType}]: {ex.Message}");
                    await unifiedMessageQueue.MarkTaskCompletedAsync(wxManagerId, mediaRequest.Id, false, ex.Message, cancellationToken);
                }
            }

            return processedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 账号媒体消息处理异常 - WxManagerId: {WxManagerId}", wxManagerId);
            await RecordAccountErrorAsync(context, $"媒体处理全局异常: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 处理单个媒体请求 - 增强60002等媒体消息的日志记录
    /// </summary>
    private async Task ProcessSingleMediaRequestAsync(
        IMediaToAiProcessor mediaToAiProcessor,
        IUnifiedMessageQueue unifiedMessageQueue,
        UnifiedMediaRequest mediaRequest,
        CancellationToken cancellationToken)
    {
        var messageType = mediaRequest.CallbackMessage?.MessageType ?? "Unknown";
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 📁 开始处理媒体请求 - MessageType: {MessageType}, RequestId: {RequestId}, WxManagerId: {WxManagerId}",
                mediaRequest.ProcessingId, messageType, mediaRequest.Id, mediaRequest.WxManagerId);

            // 特别关注60002图片消息
            if (messageType == "60002")
            {
                _logger.LogInformation("[{ProcessingId}] 🖼️ 60002图片消息开始处理 - FromUser: {FromUser}, MsgId: {MsgId}",
                    mediaRequest.ProcessingId, 
                    mediaRequest.CallbackMessage?.Data?.FromUser,
                    mediaRequest.CallbackMessage?.Data?.MsgId);
            }

            var result = await mediaToAiProcessor.ProcessMediaToAiAsync(mediaRequest, cancellationToken);
            var duration = DateTime.UtcNow - startTime;

            if (result.Success)
            {
                await unifiedMessageQueue.MarkTaskCompletedAsync(mediaRequest.WxManagerId, mediaRequest.Id, true, null, cancellationToken);
                _logger.LogInformation("[{ProcessingId}] ✅ 媒体请求处理完成 - MessageType: {MessageType}, AiProcessed: {AiProcessed}, Duration: {Duration}ms",
                    mediaRequest.ProcessingId, messageType, result.AiProcessed, duration.TotalMilliseconds);
                    
                // 特别记录60002的处理结果
                if (messageType == "60002")
                {
                    _logger.LogInformation("[{ProcessingId}] 🖼️ 60002图片消息处理完成 - AiProcessed: {AiProcessed}, PublicUrl: {PublicUrl}",
                        mediaRequest.ProcessingId, result.AiProcessed, result.MediaResult?.PublicUrl);
                }
            }
            else
            {
                await unifiedMessageQueue.MarkTaskCompletedAsync(mediaRequest.WxManagerId, mediaRequest.Id, false, result.ErrorMessage, cancellationToken);
                _logger.LogError("[{ProcessingId}] ❌ 媒体请求处理失败 - MessageType: {MessageType}, Error: {Error}, Duration: {Duration}ms",
                    mediaRequest.ProcessingId, messageType, result.ErrorMessage, duration.TotalMilliseconds);
                    
                // 特别记录60002的失败原因
                if (messageType == "60002")
                {
                    _logger.LogError("[{ProcessingId}] 🖼️ 60002图片消息处理失败 - Error: {Error}, MediaResult: {MediaResult}",
                        mediaRequest.ProcessingId, result.ErrorMessage, result.MediaResult?.Success);
                }
            }
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "[{ProcessingId}] ❌ 媒体请求处理异常 - MessageType: {MessageType}, Duration: {Duration}ms", 
                mediaRequest.ProcessingId, messageType, duration.TotalMilliseconds);
                
            // 特别记录60002的异常
            if (messageType == "60002")
            {
                _logger.LogError(ex, "[{ProcessingId}] 🖼️ 60002图片消息处理异常 - Exception: {Exception}", 
                    mediaRequest.ProcessingId, ex.Message);
            }
            
            await unifiedMessageQueue.MarkTaskCompletedAsync(mediaRequest.WxManagerId, mediaRequest.Id, false, ex.Message, cancellationToken);
        }
    }

    /// <summary>
    /// 处理账号的AI请求 - 隔离错误处理
    /// </summary>
    private async Task<int> ProcessAccountAiRequestsAsync(
        IUnifiedMessageQueue unifiedMessageQueue,
        IAiAgentService aiAgentService,
        IUnifiedAiResponseProcessor unifiedAiResponseProcessor,
        Guid wxManagerId,
        AccountProcessingContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var aiRequests = await unifiedMessageQueue.DequeueAiRequestsAsync(wxManagerId, 8, cancellationToken);
            var processedCount = 0;

            foreach (var aiRequest in aiRequests)
            {
                try
                {
                    await ProcessSingleAiRequestAsync(aiAgentService, unifiedAiResponseProcessor, unifiedMessageQueue, aiRequest, cancellationToken);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 单个AI请求处理失败 - WxManagerId: {WxManagerId}, RequestId: {RequestId}",
                        wxManagerId, aiRequest.Id);
                    
                    await RecordAccountErrorAsync(context, $"AI处理失败: {ex.Message}");
                    await unifiedMessageQueue.MarkTaskCompletedAsync(wxManagerId, aiRequest.Id, false, ex.Message, cancellationToken);
                }
            }

            return processedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 账号AI请求处理异常 - WxManagerId: {WxManagerId}", wxManagerId);
            await RecordAccountErrorAsync(context, $"AI处理全局异常: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 处理单个AI请求
    /// </summary>
    private async Task ProcessSingleAiRequestAsync(
        IAiAgentService aiAgentService,
        IUnifiedAiResponseProcessor unifiedAiResponseProcessor,
        IUnifiedMessageQueue unifiedMessageQueue,
        UnifiedAiRequest aiRequest,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤖 开始处理AI请求 - Template: {Template}",
                aiRequest.ProcessingId, aiRequest.AiTemplate);

            // 1. 获取AI智能体配置并调用AI服务
            var originalMessage = aiRequest.OriginalMessage;
            if (originalMessage?.Data == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 原始消息数据为空", aiRequest.ProcessingId);
                await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, false, "原始消息数据为空", cancellationToken);
                return;
            }

            // 获取AI智能体ID（这里需要根据实际业务逻辑获取）
            var aiAgentId = await GetAiAgentIdAsync(aiRequest, cancellationToken);
            if (aiAgentId == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 未找到AI智能体配置", aiRequest.ProcessingId);
                await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, false, "未找到AI智能体配置", cancellationToken);
                return;
            }

            var aiResponse = await aiAgentService.ProcessMessageAsync(aiAgentId.Value, aiRequest.AiTemplate, cancellationToken);

            if (string.IsNullOrEmpty(aiResponse))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ AI响应为空", aiRequest.ProcessingId);
                await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, false, "AI响应为空", cancellationToken);
                return;
            }

            // 2. 处理AI响应

            var wId = originalMessage.Data.WId ?? "";
            var toUser = originalMessage.Data.FromUser ?? "";
            var toGroup = originalMessage.Data.FromGroup;
            var atUsers = DetermineAtUsers(originalMessage);

            var responseResult = await unifiedAiResponseProcessor.ProcessAiResponseAsync(
                aiResponse, aiRequest.WxManagerId, wId, toUser, toGroup, atUsers, cancellationToken);

            if (responseResult.Success)
            {
                await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, true, null, cancellationToken);
                _logger.LogInformation("[{ProcessingId}] ✅ AI请求处理完成 - BatchId: {BatchId}, MessageCount: {MessageCount}",
                    aiRequest.ProcessingId, responseResult.BatchId, responseResult.MessageCount);
            }
            else
            {
                await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, false, responseResult.ErrorMessage, cancellationToken);
                _logger.LogError("[{ProcessingId}] ❌ AI响应处理失败 - Error: {Error}",
                    aiRequest.ProcessingId, responseResult.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ AI请求处理异常", aiRequest.ProcessingId);
            await unifiedMessageQueue.MarkTaskCompletedAsync(aiRequest.WxManagerId, aiRequest.Id, false, ex.Message, cancellationToken);
        }
    }

    /// <summary>
    /// 确定@用户
    /// </summary>
    private string? DetermineAtUsers(WxCallbackMessageDto originalMessage)
    {
        // 如果是群聊@消息，需要@回复发送者
        if (originalMessage.MessageType == "80001" && !string.IsNullOrEmpty(originalMessage.Data?.FromUser))
        {
            // 在群聊中，FromUser就是群聊中的发送者
            return originalMessage.Data.FromUser;
        }

        return null;
    }

    /// <summary>
    /// 获取AI智能体ID
    /// </summary>
    private async Task<Guid?> GetAiAgentIdAsync(UnifiedAiRequest aiRequest, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var contactAiChecker = scope.ServiceProvider.GetRequiredService<IContactAiConfigChecker>();

            var originalMessage = aiRequest.OriginalMessage;
            if (originalMessage?.Data == null) return null;

            var wxManagerId = aiRequest.WxManagerId;
            var fromUser = originalMessage.Data.FromUser ?? "";

            var aiConfig = await contactAiChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);

            return aiConfig.AiAgentId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 获取AI智能体ID失败", aiRequest.ProcessingId);
            return null;
        }
    }

    /// <summary>
    /// 获取活跃的微信账号ID列表
    /// </summary>
    private async Task<List<Guid>> GetActiveWxManagerIdsAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var wxManagerRepository = serviceProvider.GetRequiredService<IWxManagerRepository>();
            var allManagers = await wxManagerRepository.GetActiveManagersAsync();

            return allManagers.Select(m => m.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取活跃微信账号失败");
            return new List<Guid>();
        }
    }

    /// <summary>
    /// 账号级别错误处理
    /// </summary>
    private async Task HandleAccountErrorAsync(Guid wxManagerId, AccountProcessingContext context, Exception ex)
    {
        _logger.LogError(ex, "❌ 账号处理异常 - WxManagerId: {WxManagerId}", wxManagerId);
        
        await RecordAccountErrorAsync(context, ex.Message);
        
        // 检查是否需要触发熔断器
        await CheckAndTriggerCircuitBreakerAsync(context);
    }

    /// <summary>
    /// 记录账号错误
    /// </summary>
    private async Task RecordAccountErrorAsync(AccountProcessingContext context, string errorMessage)
    {
        var error = new AccountError
        {
            Timestamp = DateTime.UtcNow,
            Message = errorMessage
        };
        
        context.ProcessingErrors.Enqueue(error);
        context.ConsecutiveErrorCount++;
        
        // 保留最近50个错误
        while (context.ProcessingErrors.Count > 50)
        {
            context.ProcessingErrors.TryDequeue(out _);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 检查并触发熔断器
    /// </summary>
    private async Task CheckAndTriggerCircuitBreakerAsync(AccountProcessingContext context)
    {
        const int errorThreshold = 10; // 连续错误阈值
        const int timeWindowMinutes = 5; // 时间窗口
        
        if (context.ConsecutiveErrorCount >= errorThreshold)
        {
            context.CircuitBreakerState = CircuitBreakerState.Open;
            context.CircuitBreakerOpenedAt = DateTime.UtcNow;
            context.IsHealthy = false;
            
            _logger.LogWarning("🔥 账号熔断器开启 - WxManagerId: {WxManagerId}, 连续错误数: {ErrorCount}",
                context.WxManagerId, context.ConsecutiveErrorCount);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 检查是否应该跳过处理（熔断器开启）
    /// </summary>
    private async Task<bool> ShouldSkipDueToCircuitBreakerAsync(AccountProcessingContext context)
    {
        if (context.CircuitBreakerState != CircuitBreakerState.Open)
        {
            return false;
        }
        
        // 熔断器开启后5分钟尝试半开状态
        var timeSinceOpened = DateTime.UtcNow - (context.CircuitBreakerOpenedAt ?? DateTime.UtcNow);
        if (timeSinceOpened.TotalMinutes > 5)
        {
            context.CircuitBreakerState = CircuitBreakerState.HalfOpen;
            _logger.LogInformation("🔄 账号熔断器进入半开状态 - WxManagerId: {WxManagerId}", context.WxManagerId);
            return false;
        }
        
        return true;
    }

    /// <summary>
    /// 重置熔断器
    /// </summary>
    private async Task ResetCircuitBreakerAsync(AccountProcessingContext context)
    {
        if (context.CircuitBreakerState != CircuitBreakerState.Closed)
        {
            context.CircuitBreakerState = CircuitBreakerState.Closed;
            context.ConsecutiveErrorCount = 0;
            context.CircuitBreakerOpenedAt = null;
            context.IsHealthy = true;
            
            _logger.LogInformation("✅ 账号熔断器重置 - WxManagerId: {WxManagerId}", context.WxManagerId);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 清理不活跃的账号
    /// </summary>
    private async Task CleanupInactiveAccountsAsync(List<Guid> activeWxManagerIds)
    {
        var inactiveAccounts = _accountContexts.Keys.Except(activeWxManagerIds).ToList();
        
        foreach (var inactiveAccountId in inactiveAccounts)
        {
            if (_accountContexts.TryRemove(inactiveAccountId, out var context))
            {
                _logger.LogInformation("🧹 清理不活跃账号上下文 - WxManagerId: {WxManagerId}", inactiveAccountId);
            }
            
            if (_accountSemaphores.TryRemove(inactiveAccountId, out var semaphore))
            {
                semaphore.Dispose();
            }
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 账号健康检查
    /// </summary>
    private async void PerformAccountHealthCheck(object? state)
    {
        try
        {
            var unhealthyAccounts = _accountContexts.Values
                .Where(ctx => !ctx.IsHealthy || 
                             (DateTime.UtcNow - ctx.LastProcessedAt).TotalMinutes > 10)
                .ToList();

            foreach (var context in unhealthyAccounts)
            {
                _logger.LogWarning("⚠️ 发现不健康账号 - WxManagerId: {WxManagerId}, LastProcessed: {LastProcessed}, CircuitBreaker: {CircuitBreaker}",
                    context.WxManagerId, context.LastProcessedAt, context.CircuitBreakerState);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 账号健康检查异常");
        }
    }

    /// <summary>
    /// 优雅关闭所有账号处理
    /// </summary>
    private async Task ShutdownAllAccountsAsync()
    {
        _logger.LogInformation("🔄 开始优雅关闭所有账号处理...");
        
        // 释放所有信号量
        foreach (var semaphore in _accountSemaphores.Values)
        {
            semaphore.Dispose();
        }
        
        _accountSemaphores.Clear();
        _accountContexts.Clear();
        _accountHealthCheckTimer?.Dispose();
        
        _logger.LogInformation("✅ 所有账号处理已优雅关闭");
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("统一消息消费者正在停止...");
        await ShutdownAllAccountsAsync();
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("统一消息消费者已完全停止");
    }
}

/// <summary>
/// 账号处理上下文 - 完全隔离的错误处理和状态管理
/// </summary>
public class AccountProcessingContext
{
    public Guid WxManagerId { get; set; }
    public bool IsHealthy { get; set; } = true;
    public DateTime LastProcessedAt { get; set; } = DateTime.UtcNow;
    public long ProcessedMessageCount { get; set; }
    public TimeSpan LastProcessingDuration { get; set; }
    public int ConsecutiveErrorCount { get; set; }
    public CircuitBreakerState CircuitBreakerState { get; set; } = CircuitBreakerState.Closed;
    public DateTime? CircuitBreakerOpenedAt { get; set; }
    public ConcurrentQueue<AccountError> ProcessingErrors { get; set; } = new();
}

/// <summary>
/// 熔断器状态
/// </summary>
public enum CircuitBreakerState
{
    Closed,    // 正常状态
    Open,      // 熔断开启
    HalfOpen   // 半开状态，尝试恢复
}

/// <summary>
/// 账号错误记录
/// </summary>
public class AccountError
{
    public DateTime Timestamp { get; set; }
    public string Message { get; set; } = string.Empty;
}
