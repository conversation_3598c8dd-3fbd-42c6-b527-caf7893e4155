/**
 * SignalR认证提供者
 * 专门处理SignalR连接的认证逻辑，实现createAuthenticatedConnection方法
 * 
 * 功能特性：
 * - 统一的SignalR连接认证
 * - 自动重连机制
 * - 认证失败处理
 * - 连接状态监控
 */
class SignalRAuthenticationProvider {
    constructor() {
        this.connections = new Map();
        this.connectionConfig = {
            serverTimeoutInMilliseconds: 60000,
            keepAliveIntervalInMilliseconds: 15000,
            reconnectDelays: [0, 2000, 5000, 10000, 30000]
        };
        
        console.log('🔗 SignalR认证提供者已初始化');
    }

    /**
     * 创建带认证的SignalR连接
     * @param {string} hubUrl Hub URL
     * @param {Object} options 连接选项
     * @returns {Promise<signalR.HubConnection>} SignalR连接
     */
    async createAuthenticatedConnection(hubUrl, options = {}) {
        try {
            console.log(`🔗 创建认证SignalR连接: ${hubUrl}`);

            // 获取SessionId
            const sessionId = await this.getSessionId();
            if (!sessionId) {
                throw new Error('无法创建认证连接：缺少SessionId');
            }

            // 构建连接配置
            const connectionOptions = {
                skipNegotiation: false,
                transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
                accessTokenFactory: () => this.getSessionId(),
                timeout: options.timeout || 30000,
                ...options.connectionOptions
            };

            // 创建连接构建器
            const connectionBuilder = new signalR.HubConnectionBuilder()
                .withUrl(hubUrl, connectionOptions)
                .withAutomaticReconnect(this.connectionConfig.reconnectDelays)
                .configureLogging(options.logLevel || signalR.LogLevel.Information);

            // 构建连接
            const connection = connectionBuilder.build();

            // 应用连接配置
            connection.serverTimeoutInMilliseconds = this.connectionConfig.serverTimeoutInMilliseconds;
            connection.keepAliveIntervalInMilliseconds = this.connectionConfig.keepAliveIntervalInMilliseconds;

            // 设置连接事件处理器
            this.setupConnectionEvents(connection, hubUrl);

            // 启动连接
            await connection.start();

            // 存储连接引用
            const connectionId = this.generateConnectionId(hubUrl);
            this.connections.set(connectionId, {
                connection,
                hubUrl,
                createdAt: Date.now(),
                lastActivity: Date.now()
            });

            console.log(`✅ SignalR认证连接创建成功: ${hubUrl}`);
            return connection;

        } catch (error) {
            console.error(`❌ 创建SignalR认证连接失败: ${hubUrl}`, error);
            throw error;
        }
    }

    /**
     * 获取SessionId - 使用统一认证状态管理器
     * @returns {Promise<string|null>} SessionId
     */
    async getSessionId() {
        try {
            // 使用统一认证状态管理器
            if (window.authStateManager) {
                return await window.authStateManager.getSessionId();
            }

            // 降级：直接从localStorage获取
            return localStorage.getItem('hw_session_id');
        } catch (error) {
            console.error('❌ 获取SessionId失败:', error);
            return null;
        }
    }

    /**
     * 设置连接事件处理器
     * @param {signalR.HubConnection} connection SignalR连接
     * @param {string} hubUrl Hub URL
     */
    setupConnectionEvents(connection, hubUrl) {
        // 连接关闭事件
        connection.onclose((error) => {
            console.log(`🔌 SignalR连接关闭: ${hubUrl}`, error);
            this.handleConnectionClosed(connection, hubUrl, error);
        });

        // 重连事件
        connection.onreconnecting((error) => {
            console.log(`🔄 SignalR重连中: ${hubUrl}`, error);
        });

        // 重连成功事件
        connection.onreconnected((connectionId) => {
            console.log(`✅ SignalR重连成功: ${hubUrl}, ConnectionId: ${connectionId}`);
            this.updateConnectionActivity(hubUrl);
        });

        // 认证错误处理
        connection.on('AuthenticationError', (errorData) => {
            console.error(`🚫 SignalR认证错误: ${hubUrl}`, errorData);
            this.handleAuthenticationError(connection, hubUrl, errorData);
        });

        // 连接确认
        connection.on('ConnectionConfirmed', (confirmData) => {
            console.log(`✅ SignalR连接确认: ${hubUrl}`, confirmData);
            this.updateConnectionActivity(hubUrl);
        });
    }

    /**
     * 处理连接关闭
     * @param {signalR.HubConnection} connection SignalR连接
     * @param {string} hubUrl Hub URL
     * @param {Error} error 错误信息
     */
    handleConnectionClosed(connection, hubUrl, error) {
        // 从连接映射中移除
        const connectionId = this.generateConnectionId(hubUrl);
        this.connections.delete(connectionId);

        // 如果是认证错误，通知认证状态管理器
        if (error && error.message && error.message.includes('认证')) {
            this.notifyAuthenticationFailure(hubUrl, error);
        }
    }

    /**
     * 处理认证错误
     * @param {signalR.HubConnection} connection SignalR连接
     * @param {string} hubUrl Hub URL
     * @param {Object} errorData 错误数据
     */
    async handleAuthenticationError(connection, hubUrl, errorData) {
        console.error(`🚫 处理SignalR认证错误: ${hubUrl}`, errorData);

        // 尝试刷新认证状态
        if (window.authStateManager) {
            try {
                await window.authStateManager.restoreAuthenticationState();
                
                // 如果获取到新的SessionId，尝试重新连接
                const newSessionId = await window.authStateManager.getSessionId();
                if (newSessionId) {
                    console.log(`🔄 尝试使用新SessionId重新连接: ${hubUrl}`);
                    // 这里可以实现重新连接逻辑
                }
            } catch (error) {
                console.error('❌ 刷新认证状态失败:', error);
            }
        }

        // 发布认证错误事件
        if (window.authEventBus) {
            window.authEventBus.publish('signalrAuthError', {
                hubUrl,
                errorData,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 通知认证失败
     * @param {string} hubUrl Hub URL
     * @param {Error} error 错误信息
     */
    notifyAuthenticationFailure(hubUrl, error) {
        if (window.authEventBus) {
            window.authEventBus.publish('signalrAuthFailure', {
                hubUrl,
                error: error.message,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 更新连接活动时间
     * @param {string} hubUrl Hub URL
     */
    updateConnectionActivity(hubUrl) {
        const connectionId = this.generateConnectionId(hubUrl);
        const connectionInfo = this.connections.get(connectionId);
        if (connectionInfo) {
            connectionInfo.lastActivity = Date.now();
        }
    }

    /**
     * 生成连接ID
     * @param {string} hubUrl Hub URL
     * @returns {string} 连接ID
     */
    generateConnectionId(hubUrl) {
        return `signalr_${hubUrl.replace(/[^a-zA-Z0-9]/g, '_')}`;
    }

    /**
     * 获取连接信息
     * @param {string} hubUrl Hub URL
     * @returns {Object|null} 连接信息
     */
    getConnectionInfo(hubUrl) {
        const connectionId = this.generateConnectionId(hubUrl);
        return this.connections.get(connectionId) || null;
    }

    /**
     * 获取所有连接统计
     * @returns {Object} 连接统计信息
     */
    getConnectionStats() {
        const stats = {
            totalConnections: this.connections.size,
            connections: []
        };

        for (const [connectionId, info] of this.connections) {
            stats.connections.push({
                connectionId,
                hubUrl: info.hubUrl,
                state: info.connection.state,
                createdAt: info.createdAt,
                lastActivity: info.lastActivity,
                uptime: Date.now() - info.createdAt
            });
        }

        return stats;
    }

    /**
     * 关闭指定连接
     * @param {string} hubUrl Hub URL
     */
    async closeConnection(hubUrl) {
        const connectionInfo = this.getConnectionInfo(hubUrl);
        if (connectionInfo && connectionInfo.connection) {
            try {
                await connectionInfo.connection.stop();
                console.log(`🔌 SignalR连接已关闭: ${hubUrl}`);
            } catch (error) {
                console.error(`❌ 关闭SignalR连接失败: ${hubUrl}`, error);
            }
        }
    }

    /**
     * 关闭所有连接
     */
    async closeAllConnections() {
        const closePromises = [];
        
        for (const [connectionId, info] of this.connections) {
            closePromises.push(this.closeConnection(info.hubUrl));
        }

        await Promise.allSettled(closePromises);
        this.connections.clear();
        
        console.log('🔌 所有SignalR连接已关闭');
    }

    /**
     * 销毁认证提供者
     */
    async destroy() {
        await this.closeAllConnections();
        console.log('💥 SignalR认证提供者已销毁');
    }
}

// 创建全局单例实例
if (!window.signalRAuthProvider) {
    window.signalRAuthProvider = new SignalRAuthenticationProvider();
}

console.log('✅ SignalRAuthenticationProvider已加载并注册到window.signalRAuthProvider');
