using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.MessageProcessing.Pipeline;

namespace HappyWechat.Infrastructure.MessageProcessing.Routing;

/// <summary>
/// 消息类型路由器
/// 根据消息类型和业务规则决定使用哪个处理流水线
/// </summary>
public interface IMessageTypeRouter
{
    /// <summary>
    /// 路由消息到合适的流水线
    /// </summary>
    Task<RoutingResult> RouteMessageAsync(WxCallbackMessageDto message, CancellationToken cancellationToken = default);
}

/// <summary>
/// 路由结果
/// </summary>
public class RoutingResult
{
    public PipelineType PipelineType { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool RequiresCoordination { get; set; } // 是否需要消息协调
    public Dictionary<string, object> Metadata { get; set; } = new();
    
    public static RoutingResult CreateFast(string reason)
    {
        return new RoutingResult
        {
            PipelineType = PipelineType.Fast,
            Reason = reason
        };
    }
    
    public static RoutingResult CreateSlow(string reason)
    {
        return new RoutingResult
        {
            PipelineType = PipelineType.Slow,
            Reason = reason
        };
    }
    
    public static RoutingResult CreateCoordination(string reason)
    {
        return new RoutingResult
        {
            PipelineType = PipelineType.Coordination,
            Reason = reason,
            RequiresCoordination = true
        };
    }
}

public class MessageTypeRouter : IMessageTypeRouter
{
    private readonly ILogger<MessageTypeRouter> _logger;
    
    // 快速处理的消息类型（文本消息）
    private static readonly HashSet<string> FastProcessingTypes = new()
    {
        MessageTypeConstants.PRIVATE_TEXT,  // 60001
        MessageTypeConstants.GROUP_TEXT     // 80001
    };
    
    // 慢速处理的消息类型（媒体文件）
    private static readonly HashSet<string> SlowProcessingTypes = new()
    {
        MessageTypeConstants.PRIVATE_IMAGE, // 60002
        MessageTypeConstants.PRIVATE_VOICE, // 60004
        MessageTypeConstants.PRIVATE_FILE,  // 60009
        MessageTypeConstants.GROUP_IMAGE,   // 80002
        MessageTypeConstants.GROUP_VOICE,   // 80004
        MessageTypeConstants.GROUP_FILE     // 80009
    };
    
    // 需要协调处理的消息类型（群聊中可能需要组合的消息）
    private static readonly HashSet<string> CoordinationTypes = new()
    {
        MessageTypeConstants.GROUP_TEXT,    // 80001 - 可能需要等待媒体
        MessageTypeConstants.GROUP_IMAGE,   // 80002 - 可能需要等待文本
        MessageTypeConstants.GROUP_FILE     // 80009 - 可能需要等待文本
    };
    
    public MessageTypeRouter(ILogger<MessageTypeRouter> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// 路由消息到合适的流水线
    /// </summary>
    public async Task<RoutingResult> RouteMessageAsync(WxCallbackMessageDto message, CancellationToken cancellationToken = default)
    {
        var messageType = message.MessageType;
        var isGroupMessage = IsGroupMessage(messageType);
        var isPrivateMessage = IsPrivateMessage(messageType);
        
        _logger.LogDebug("🔀 开始消息路由 - MessageType: {MessageType}, IsGroup: {IsGroup}, IsPrivate: {IsPrivate}",
            messageType, isGroupMessage, isPrivateMessage);
        
        try
        {
            // 1. 私聊消息路由逻辑
            if (isPrivateMessage)
            {
                return RoutePrivateMessage(messageType);
            }
            
            // 2. 群聊消息路由逻辑
            if (isGroupMessage)
            {
                return await RouteGroupMessageAsync(message, cancellationToken);
            }
            
            // 3. 其他消息类型（系统消息等）
            return RoutingResult.CreateFast($"未知消息类型，使用快速流水线: {messageType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 消息路由异常 - MessageType: {MessageType}", messageType);
            // 异常情况下默认使用快速流水线
            return RoutingResult.CreateFast($"路由异常，使用快速流水线: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 路由私聊消息
    /// </summary>
    private RoutingResult RoutePrivateMessage(string messageType)
    {
        if (FastProcessingTypes.Contains(messageType))
        {
            return RoutingResult.CreateFast($"私聊文本消息，使用快速流水线");
        }
        
        if (SlowProcessingTypes.Contains(messageType))
        {
            return RoutingResult.CreateSlow($"私聊媒体消息，使用慢速流水线");
        }
        
        return RoutingResult.CreateFast($"私聊未知类型，使用快速流水线");
    }
    
    /// <summary>
    /// 路由群聊消息
    /// </summary>
    private async Task<RoutingResult> RouteGroupMessageAsync(WxCallbackMessageDto message, CancellationToken cancellationToken)
    {
        var messageType = message.MessageType;
        
        // 检查是否需要协调处理（消息组合逻辑）
        if (CoordinationTypes.Contains(messageType))
        {
            var needsCoordination = await CheckIfNeedsCoordinationAsync(message, cancellationToken);
            
            if (needsCoordination)
            {
                return RoutingResult.CreateCoordination($"群聊消息需要协调处理: {messageType}");
            }
        }
        
        // 根据消息类型选择流水线
        if (messageType == MessageTypeConstants.GROUP_TEXT)
        {
            return RoutingResult.CreateFast("群聊文本消息，使用快速流水线");
        }
        
        if (messageType == MessageTypeConstants.GROUP_VOICE)
        {
            // 语音消息的特殊处理逻辑
            return RouteGroupVoiceMessage(message);
        }
        
        if (SlowProcessingTypes.Contains(messageType))
        {
            return RoutingResult.CreateSlow($"群聊媒体消息，使用慢速流水线");
        }
        
        return RoutingResult.CreateFast($"群聊未知类型，使用快速流水线");
    }
    
    /// <summary>
    /// 路由群聊语音消息
    /// </summary>
    private RoutingResult RouteGroupVoiceMessage(WxCallbackMessageDto message)
    {
        // 根据群聊模式决定语音消息的处理方式
        // 这里需要检查群组配置，但为了简化，先使用基本逻辑
        
        var fromGroup = message.Data?.FromGroup;
        
        _logger.LogDebug("🎵 路由群聊语音消息 - FromGroup: {FromGroup}", fromGroup);
        
        // TODO: 这里应该查询群组配置，判断是"回复所有消息"还是"仅@后回复"模式
        // 暂时使用慢速流水线处理
        
        return RoutingResult.CreateSlow("群聊语音消息，使用慢速流水线");
    }
    
    /// <summary>
    /// 检查是否需要协调处理
    /// </summary>
    private async Task<bool> CheckIfNeedsCoordinationAsync(WxCallbackMessageDto message, CancellationToken cancellationToken)
    {
        var messageType = message.MessageType;
        var fromGroup = message.Data?.FromGroup;
        var fromUser = message.Data?.FromUser;
        
        // 检查是否是"仅@后回复"模式的群组
        // TODO: 这里应该查询群组配置
        
        // 简化逻辑：如果是图片或文件消息，可能需要等待文本消息
        if (messageType == MessageTypeConstants.GROUP_IMAGE || 
            messageType == MessageTypeConstants.GROUP_FILE)
        {
            _logger.LogDebug("🔗 媒体消息可能需要协调处理 - MessageType: {MessageType}, FromGroup: {FromGroup}",
                messageType, fromGroup);
            return true;
        }
        
        // 如果是文本消息，检查是否有等待的媒体消息
        if (messageType == MessageTypeConstants.GROUP_TEXT)
        {
            // TODO: 检查Redis缓存中是否有等待的媒体消息
            _logger.LogDebug("📝 文本消息检查是否需要协调 - FromGroup: {FromGroup}, FromUser: {FromUser}",
                fromGroup, fromUser);
            return false; // 暂时返回false，具体逻辑在协调流水线中实现
        }
        
        return false;
    }
    
    /// <summary>
    /// 判断是否为群聊消息
    /// </summary>
    private bool IsGroupMessage(string messageType)
    {
        return messageType.StartsWith("80");
    }
    
    /// <summary>
    /// 判断是否为私聊消息
    /// </summary>
    private bool IsPrivateMessage(string messageType)
    {
        return messageType.StartsWith("60");
    }
}
