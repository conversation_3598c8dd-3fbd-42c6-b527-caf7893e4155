namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 联系人AI配置项
/// </summary>
public class ContactAiConfigItem
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 联系人ID
    /// </summary>
    public Guid ContactId { get; set; }
    
    /// <summary>
    /// 联系人名称
    /// </summary>
    public string ContactName { get; set; } = string.Empty;
    
    /// <summary>
    /// 联系人微信号
    /// </summary>
    public string ContactWcId { get; set; } = string.Empty;
    
    /// <summary>
    /// AI智能体ID
    /// </summary>
    public Guid AiAgentId { get; set; }
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string AiAgentName { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 配置描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 账号名称
    /// </summary>
    public string AccountName { get; set; } = string.Empty;
}
