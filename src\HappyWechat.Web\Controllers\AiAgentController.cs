using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.AiAgent;
using HappyWechat.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace HappyWechat.Web.Controllers;

/// <summary>
/// AI智能体控制器 - 使用用户上下文注入的新架构版本
/// </summary>
[Route("api/ai-agent")]
public class AiAgentController : BaseAuthenticatedController
{
    private readonly IAiAgentService _aiAgentService;

    public AiAgentController(
        IAiAgentService aiAgentService,
        ICurrentUserContext userContext,
        ILogger<AiAgentController> logger) : base(userContext, logger)
    {
        _aiAgentService = aiAgentService;
    }

    /// <summary>
    /// 获取AI智能体分页列表
    /// </summary>
    [HttpPost("list")]
    public async Task<ActionResult<ApiResponse<PageResponse<AiAgentDto>>>> GetPagedList(
        AiAgentQuery query)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("GetPagedList", new { query.PageQuery?.Page, query.PageQuery?.PageSize, query.SearchKeyword });

            var result = await _aiAgentService.GetPagedListAsync(CurrentUserId, query);

            // 🔧 注释冗余的成功日志 - 减少日志噪音
            // Logger.LogInformation("获取AI智能体列表成功 - UserId: {UserId}, Count: {Count}, Total: {Total}",
            //     CurrentUserId, result.Items.Count, result.TotalCount);
            
            return result;
        });
    }

    /// <summary>
    /// 创建AI智能体
    /// </summary>
    [HttpPost("create")]
    public async Task<ActionResult<ApiResponse<AiAgentDto>>> Create(CreateAiAgentCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("CreateAiAgent", new { command.Name, command.ProviderType });
            
            var result = await _aiAgentService.CreateAsync(CurrentUserId, command);
            
            Logger.LogInformation("创建AI智能体成功 - UserId: {UserId}, AgentId: {AgentId}, Name: {Name}", 
                CurrentUserId, result.Id, result.Name);
            
            return result;
        });
    }

    /// <summary>
    /// 更新AI智能体
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<ApiResponse<AiAgentDto>>> Update(Guid id, UpdateAiAgentCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("UpdateAiAgent", new { AgentId = id, command.Name });
            
            command.Id = id;
            var result = await _aiAgentService.UpdateAsync(CurrentUserId, command);
            
            Logger.LogInformation("更新AI智能体成功 - UserId: {UserId}, AgentId: {AgentId}, Name: {Name}", 
                CurrentUserId, result.Id, result.Name);
            
            return result;
        });
    }

    /// <summary>
    /// 删除AI智能体
    /// </summary>
    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ApiResponse<bool>>> Delete(Guid id)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("DeleteAiAgent", new { AgentId = id });
            
            await _aiAgentService.DeleteAsync(CurrentUserId, id);
            
            Logger.LogInformation("删除AI智能体成功 - UserId: {UserId}, AgentId: {AgentId}", 
                CurrentUserId, id);
            
            return true;
        });
    }

    /// <summary>
    /// 获取AI智能体详情
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<ApiResponse<AiAgentDto>>> GetById(Guid id)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetAiAgentById", new { AgentId = id });
            
            var result = await _aiAgentService.GetByIdAsync(CurrentUserId, id);
            
            if (result == null)
            {
                Logger.LogWarning("AI智能体不存在 - UserId: {UserId}, AgentId: {AgentId}", 
                    CurrentUserId, id);
                throw new InvalidOperationException("指定的AI智能体不存在");
            }
            
            return result;
        });
    }

    /// <summary>
    /// 切换AI智能体启用状态
    /// </summary>
    [HttpPatch("{id:guid}/toggle")]
    public async Task<ActionResult<ApiResponse<bool>>> ToggleEnabled(Guid id, [FromBody] bool enabled)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("ToggleAiAgentEnabled", new { AgentId = id, Enabled = enabled });
            
            await _aiAgentService.ToggleEnabledAsync(CurrentUserId, id, enabled);
            
            Logger.LogInformation("切换AI智能体状态成功 - UserId: {UserId}, AgentId: {AgentId}, Enabled: {Enabled}", 
                CurrentUserId, id, enabled);
            
            return true;
        });
    }

    /// <summary>
    /// 测试AI智能体 - 需要管理员权限
    /// </summary>
    [HttpPost("{id:guid}/test")]
    public async Task<ActionResult<ApiResponse<string>>> Test(Guid id, [FromBody] string testMessage)
    {
        // 使用角色验证的示例
        return await ExecuteRoleBasedOperation("Admin", async () =>
        {
            LogOperation("TestAiAgent", new { AgentId = id, TestMessage = testMessage });
            
            // 这里应该调用实际的测试逻辑
            var result = $"测试消息: {testMessage} - 已发送到智能体 {id}";
            
            Logger.LogInformation("测试AI智能体成功 - UserId: {UserId}, AgentId: {AgentId}", 
                CurrentUserId, id);
            
            return result;
        });
    }

    /// <summary>
    /// 获取用户的智能体统计信息
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<ApiResponse<object>>> GetStatistics()
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetAiAgentStatistics");
            
            // 示例统计数据
            var statistics = new
            {
                TotalAgents = await _aiAgentService.GetCountAsync(CurrentUserId),
                EnabledAgents = await _aiAgentService.GetEnabledCountAsync(CurrentUserId),
                UserId = CurrentUserId,
                Username = CurrentUsername,
                UserDisplayName = CurrentUserDisplayName
            };
            
            Logger.LogInformation("获取AI智能体统计信息成功 - UserId: {UserId}, TotalAgents: {TotalAgents}", 
                CurrentUserId, statistics.TotalAgents);
            
            return (object)statistics;
        });
    }
}