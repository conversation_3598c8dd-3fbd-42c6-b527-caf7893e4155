using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息长度处理器接口 - 新架构版本
/// </summary>
public interface IMessageLengthProcessor
{
    /// <summary>
    /// 检查和处理消息长度
    /// </summary>
    Task<MessageLengthProcessResult> ProcessMessageLengthAsync(string content, int maxLength, bool enableAutoSplit, string processingId);

    /// <summary>
    /// 分割长消息
    /// </summary>
    List<string> SplitLongMessage(string content, int maxLength);

    /// <summary>
    /// 批量处理消息长度
    /// </summary>
    Task<List<MessageLengthProcessResult>> ProcessMessageLengthBatchAsync(List<string> contents, int maxLength, bool enableAutoSplit, string processingId);
}

/// <summary>
/// 消息长度处理结果 - 新架构版本
/// </summary>
public class MessageLengthProcessResult
{
    /// <summary>
    /// 原始内容
    /// </summary>
    public string OriginalContent { get; set; } = string.Empty;

    /// <summary>
    /// 处理后的内容列表
    /// </summary>
    public List<string> ProcessedContents { get; set; } = new();

    /// <summary>
    /// 是否超过长度限制
    /// </summary>
    public bool ExceedsLimit { get; set; }

    /// <summary>
    /// 是否进行了分割
    /// </summary>
    public bool WasSplit { get; set; }

    /// <summary>
    /// 原始长度
    /// </summary>
    public int OriginalLength { get; set; }

    /// <summary>
    /// 最大允许长度
    /// </summary>
    public int MaxLength { get; set; }

    /// <summary>
    /// 分割后的片段数量
    /// </summary>
    public int SegmentCount { get; set; }

    /// <summary>
    /// 处理成功
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 扩展数据
    /// </summary>
    public Dictionary<string, object> ExtendedData { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static MessageLengthProcessResult CreateSuccess(string originalContent, List<string> processedContents, bool exceedsLimit, bool wasSplit, int maxLength)
    {
        return new MessageLengthProcessResult
        {
            OriginalContent = originalContent,
            ProcessedContents = processedContents,
            ExceedsLimit = exceedsLimit,
            WasSplit = wasSplit,
            OriginalLength = originalContent.Length,
            MaxLength = maxLength,
            SegmentCount = processedContents.Count,
            Success = true
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static MessageLengthProcessResult CreateFailure(string originalContent, string errorMessage)
    {
        return new MessageLengthProcessResult
        {
            OriginalContent = originalContent,
            ProcessedContents = new List<string> { originalContent },
            OriginalLength = originalContent.Length,
            SegmentCount = 1,
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 消息长度处理器实现 - 新架构版本
/// </summary>
public class MessageLengthProcessor : IMessageLengthProcessor
{
    private readonly ILogger<MessageLengthProcessor> _logger;

    public MessageLengthProcessor(ILogger<MessageLengthProcessor> logger)
    {
        _logger = logger;
    }

    public async Task<MessageLengthProcessResult> ProcessMessageLengthAsync(string content, int maxLength, bool enableAutoSplit, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔍 开始消息长度检查 - Length: {Length}, MaxLength: {MaxLength}, AutoSplit: {AutoSplit}", 
                processingId, content?.Length ?? 0, maxLength, enableAutoSplit);

            if (string.IsNullOrEmpty(content))
            {
                _logger.LogDebug("[{ProcessingId}] ⏭️ 消息内容为空，跳过长度检查", processingId);
                return MessageLengthProcessResult.CreateSuccess("", new List<string> { "" }, false, false, maxLength);
            }

            if (content.Length <= maxLength)
            {
                // 未超过长度限制
                _logger.LogDebug("[{ProcessingId}] ✅ 消息长度检查通过 - 未超过限制", processingId);
                return MessageLengthProcessResult.CreateSuccess(content, new List<string> { content }, false, false, maxLength);
            }

            // 超过长度限制
            _logger.LogInformation("[{ProcessingId}] ⚠️ 消息超过长度限制 - Length: {Length}, MaxLength: {MaxLength}", 
                processingId, content.Length, maxLength);

            if (enableAutoSplit)
            {
                // 自动分割
                var segments = SplitLongMessage(content, maxLength);
                
                _logger.LogInformation("[{ProcessingId}] ✂️ 消息已自动分割 - 原长度: {OriginalLength}, 分割为: {SegmentCount} 段", 
                    processingId, content.Length, segments.Count);

                var result = MessageLengthProcessResult.CreateSuccess(content, segments, true, true, maxLength);
                result.ExtendedData["SplitTime"] = DateTime.UtcNow;
                result.ExtendedData["ProcessingId"] = processingId;
                
                return result;
            }
            else
            {
                // 不分割，保持原内容
                _logger.LogWarning("[{ProcessingId}] ⚠️ 消息超过长度限制但未启用自动分割 - Length: {Length}", 
                    processingId, content.Length);
                
                return MessageLengthProcessResult.CreateSuccess(content, new List<string> { content }, true, false, maxLength);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息长度检查异常", processingId);
            return MessageLengthProcessResult.CreateFailure(content ?? "", $"消息长度检查异常: {ex.Message}");
        }
    }

    public List<string> SplitLongMessage(string content, int maxLength)
    {
        if (string.IsNullOrEmpty(content) || content.Length <= maxLength)
        {
            return new List<string> { content ?? "" };
        }

        var segments = new List<string>();
        var remaining = content;

        while (remaining.Length > maxLength)
        {
            // 尝试在合适的位置分割（优先在句号、换行符等处分割）
            var splitIndex = FindBestSplitPoint(remaining, maxLength);
            
            if (splitIndex <= 0)
            {
                // 找不到合适的分割点，强制分割
                splitIndex = maxLength;
            }

            var segment = remaining.Substring(0, splitIndex).Trim();
            if (!string.IsNullOrEmpty(segment))
            {
                segments.Add(segment);
            }

            remaining = remaining.Substring(splitIndex).Trim();
        }

        // 添加剩余部分
        if (!string.IsNullOrEmpty(remaining))
        {
            segments.Add(remaining);
        }

        return segments;
    }

    public async Task<List<MessageLengthProcessResult>> ProcessMessageLengthBatchAsync(List<string> contents, int maxLength, bool enableAutoSplit, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔍 开始批量消息长度检查 - Count: {Count}, MaxLength: {MaxLength}", 
                processingId, contents?.Count ?? 0, maxLength);

            if (contents == null || !contents.Any())
            {
                _logger.LogDebug("[{ProcessingId}] ⏭️ 消息内容列表为空，跳过长度检查", processingId);
                return new List<MessageLengthProcessResult>();
            }

            var results = new List<MessageLengthProcessResult>();
            var tasks = new List<Task<MessageLengthProcessResult>>();

            // 并行处理多个内容
            for (int i = 0; i < contents.Count; i++)
            {
                var content = contents[i];
                var itemProcessingId = $"{processingId}-{i}";
                tasks.Add(ProcessMessageLengthAsync(content, maxLength, enableAutoSplit, itemProcessingId));
            }

            // 等待所有任务完成
            var taskResults = await Task.WhenAll(tasks);
            results.AddRange(taskResults);

            // 统计结果
            var totalCount = results.Count;
            var exceedsLimitCount = results.Count(r => r.ExceedsLimit);
            var splitCount = results.Count(r => r.WasSplit);
            var totalSegments = results.Sum(r => r.SegmentCount);

            _logger.LogInformation("[{ProcessingId}] ✅ 批量消息长度检查完成 - Total: {Total}, ExceedsLimit: {ExceedsLimit}, Split: {Split}, TotalSegments: {TotalSegments}", 
                processingId, totalCount, exceedsLimitCount, splitCount, totalSegments);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 批量消息长度检查异常", processingId);
            
            // 返回失败结果
            return contents?.Select(content => 
                MessageLengthProcessResult.CreateFailure(content, $"批量长度检查异常: {ex.Message}")
            ).ToList() ?? new List<MessageLengthProcessResult>();
        }
    }

    /// <summary>
    /// 找到最佳分割点
    /// </summary>
    private int FindBestSplitPoint(string content, int maxLength)
    {
        if (content.Length <= maxLength)
            return content.Length;

        // 优先级：句号 > 换行符 > 感叹号 > 问号 > 逗号 > 分号 > 空格
        var splitChars = new char[] { '。', '\n', '！', '？', '，', '；', ' ', '.', '!', '?', ',', ';' };
        
        // 从最大长度向前搜索，但不要搜索太远（至少保留一半长度）
        for (int i = Math.Min(maxLength - 1, content.Length - 1); i >= maxLength / 2; i--)
        {
            if (splitChars.Contains(content[i]))
            {
                return i + 1; // 包含分割符
            }
        }

        // 如果找不到合适的分割点，尝试在单词边界分割（针对英文）
        for (int i = Math.Min(maxLength - 1, content.Length - 1); i >= maxLength / 2; i--)
        {
            if (char.IsWhiteSpace(content[i]))
            {
                return i + 1;
            }
        }

        return -1; // 找不到合适的分割点
    }
}
