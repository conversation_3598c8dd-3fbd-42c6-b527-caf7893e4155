namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 配置模板DTO
/// </summary>
public class ConfigTemplateDto
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public BatchConfigType ConfigType { get; set; }
    
    /// <summary>
    /// 模板配置（JSON格式）
    /// </summary>
    public string ConfigJson { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否为系统预置模板
    /// </summary>
    public bool IsSystemTemplate { get; set; } = false;
    
    /// <summary>
    /// 是否为默认模板
    /// </summary>
    public bool IsDefault { get; set; } = false;
    
    /// <summary>
    /// 创建者用户ID
    /// </summary>
    public Guid? CreatedBy { get; set; }
    
    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }
    
    /// <summary>
    /// 标签（用于分类和搜索）
    /// </summary>
    public List<string> Tags { get; set; } = new();
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// 模板预览信息
    /// </summary>
    public ConfigTemplatePreview? Preview { get; set; }
}

/// <summary>
/// 配置模板预览信息
/// </summary>
public class ConfigTemplatePreview
{
    /// <summary>
    /// AI智能体数量
    /// </summary>
    public int AiAgentCount { get; set; }
    
    /// <summary>
    /// 是否启用自动回复
    /// </summary>
    public bool? IsAutoReplyEnabled { get; set; }
    
    /// <summary>
    /// 是否启用敏感词过滤
    /// </summary>
    public bool? IsSensitiveWordFilterEnabled { get; set; }
    
    
    /// <summary>
    /// 特殊功能列表
    /// </summary>
    public List<string> SpecialFeatures { get; set; } = new();
}

/// <summary>
/// 预置配置模板类型
/// </summary>
public enum PresetTemplateType
{
    /// <summary>
    /// 客服助手
    /// </summary>
    CustomerService = 1,
    
    /// <summary>
    /// 营销推广
    /// </summary>
    Marketing = 2,
    
    /// <summary>
    /// 知识问答
    /// </summary>
    KnowledgeQA = 3,
    
    /// <summary>
    /// 娱乐互动
    /// </summary>
    Entertainment = 4,
    
    /// <summary>
    /// 教育培训
    /// </summary>
    Education = 5,
    
    /// <summary>
    /// 社交助手
    /// </summary>
    SocialAssistant = 6
}