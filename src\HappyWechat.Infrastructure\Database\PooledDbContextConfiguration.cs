using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 独立的池化DbContext配置管理器
/// 完全独立于AddDbContext配置，避免生命周期冲突
/// 专为高性能场景设计的池化数据库访问
/// </summary>
public static class PooledDbContextConfiguration
{
    /// <summary>
    /// 添加独立的池化DbContextFactory配置
    /// 这个配置完全独立，不会与Identity系统的AddDbContext产生冲突
    /// </summary>
    public static IServiceCollection AddHappyWechatPooledDbContextFactory<TContext>(
        this IServiceCollection services, 
        IConfiguration configuration) where TContext : DbContext
    {
        // 验证配置完整性
        ValidateConfiguration(configuration);
        
        // 创建独立的配置管道
        var pooledDbOptions = CreatePooledDbOptions(configuration);
        
        // 注册池化DbContextFactory（完全独立的Singleton配置）
        var result = services.AddPooledDbContextFactory<TContext>(options =>
        {
            ConfigurePooledDbContext(options, pooledDbOptions);
        }, poolSize: pooledDbOptions.PoolSize);
        
        // 记录配置信息
        var serviceProvider = services.BuildServiceProvider();
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        var logger = loggerFactory?.CreateLogger("PooledDbContextConfiguration");
        logger?.LogInformation("独立池化DbContextFactory配置完成 - 池大小: {PoolSize}, 连接超时: {CommandTimeout}s", 
            pooledDbOptions.PoolSize, pooledDbOptions.CommandTimeout);
            
        return result;
    }
    
    /// <summary>
    /// 验证配置完整性
    /// </summary>
    private static void ValidateConfiguration(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection") ??
                              configuration["Database:WriteConnectionString"];
                              
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "池化DbContext配置失败：未找到有效的数据库连接字符串。请检查 ConnectionStrings:DefaultConnection 或 Database:WriteConnectionString 配置。");
        }
        
        // 验证池大小配置
        var poolSize = configuration.GetValue<int>("Database:ConnectionPool:PoolSize", 128);
        if (poolSize <= 0 || poolSize > 1000)
        {
            throw new InvalidOperationException(
                $"池化DbContext配置失败：池大小 {poolSize} 无效，必须在 1-1000 之间。");
        }
    }
    
    /// <summary>
    /// 创建独立的池化数据库配置选项
    /// </summary>
    private static PooledDbOptions CreatePooledDbOptions(IConfiguration configuration)
    {
        return new PooledDbOptions
        {
            ConnectionString = GetConnectionString(configuration),
            PoolSize = configuration.GetValue<int>("Database:ConnectionPool:PoolSize", 128),
            CommandTimeout = configuration.GetValue<int>("Database:CommandTimeoutSeconds", 60),
            QueryTimeout = configuration.GetValue<int>("Database:QueryTimeoutSeconds", 30),
            EnableSensitiveDataLogging = configuration.GetValue<bool>("Database:EnableSensitiveDataLogging", false),
            EnableDetailedErrors = configuration.GetValue<bool>("Database:EnableDetailedErrors", false),
            RetryPolicy = CreateRetryPolicy(configuration)
        };
    }
    
    /// <summary>
    /// 获取连接字符串（优先级：Write > Default > 环境变量）
    /// </summary>
    private static string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("DefaultConnection") ??
               configuration["Database:WriteConnectionString"] ??
               Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING") ??
               throw new InvalidOperationException("池化DbContext数据库连接字符串未配置");
    }
    
    /// <summary>
    /// 配置池化DbContext选项
    /// </summary>
    private static void ConfigurePooledDbContext(DbContextOptionsBuilder options, PooledDbOptions config)
    {
        options.UseMySql(config.ConnectionString, ServerVersion.AutoDetect(config.ConnectionString), mysqlOptions =>
        {
            mysqlOptions.CommandTimeout(config.CommandTimeout);
            
            if (config.RetryPolicy.EnableRetry)
            {
                mysqlOptions.EnableRetryOnFailure(
                    maxRetryCount: config.RetryPolicy.MaxRetryCount,
                    maxRetryDelay: TimeSpan.FromMilliseconds(config.RetryPolicy.MaxDelayMs),
                    errorNumbersToAdd: null);
            }
        });

        // 性能优化配置
        if (config.EnableSensitiveDataLogging)
        {
            options.EnableSensitiveDataLogging();
        }

        if (config.EnableDetailedErrors)
        {
            options.EnableDetailedErrors();
        }

        // 池化特定优化
        options.EnableServiceProviderCaching();
        
        // 池化性能优化 - 禁用详细日志记录
        options.UseLoggerFactory(LoggerFactory.Create(builder =>
        {
            builder.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.Warning);
            builder.AddFilter("Microsoft.EntityFrameworkCore.Database.Connection", LogLevel.Warning);
            builder.AddFilter("Microsoft.EntityFrameworkCore.Query", LogLevel.Warning);
        }));
        
        // 禁用自动事务以提升池化场景性能（业务层手动管理事务）
        options.EnableSensitiveDataLogging(false);  // 生产环境禁用敏感数据日志
    }
    
    /// <summary>
    /// 创建重试策略配置
    /// </summary>
    private static PooledRetryPolicy CreateRetryPolicy(IConfiguration configuration)
    {
        var retrySection = configuration.GetSection("Database:RetryPolicy");
        return new PooledRetryPolicy
        {
            EnableRetry = retrySection.GetValue<bool>("EnableRetry", true),
            MaxRetryCount = retrySection.GetValue<int>("MaxRetryCount", 3),
            BaseDelayMs = retrySection.GetValue<int>("BaseDelayMs", 1000),
            MaxDelayMs = retrySection.GetValue<int>("MaxDelayMs", 30000),
            DelayMultiplier = retrySection.GetValue<double>("DelayMultiplier", 2.0)
        };
    }
}