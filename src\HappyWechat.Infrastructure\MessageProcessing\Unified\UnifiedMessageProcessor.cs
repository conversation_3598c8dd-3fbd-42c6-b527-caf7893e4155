using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageQueue.Unified;
using HappyWechat.Infrastructure.MediaProcessing;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace HappyWechat.Infrastructure.MessageProcessing.Unified;

/// <summary>
/// 统一消息处理器
/// 整合快速/慢速流水线功能，统一所有消息处理入口
/// </summary>
public interface IUnifiedMessageProcessor
{
    /// <summary>
    /// 处理微信回调消息
    /// </summary>
    Task<UnifiedProcessResult> ProcessMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

public class UnifiedMessageProcessor : IUnifiedMessageProcessor
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IUnifiedAiMessageTemplateService _aiMessageTemplateService;
    private readonly IUnifiedMessageQueue _unifiedMessageQueue;
    private readonly ILogger<UnifiedMessageProcessor> _logger;

    public UnifiedMessageProcessor(
        IServiceProvider serviceProvider,
        IUnifiedAiMessageTemplateService aiMessageTemplateService,
        IUnifiedMessageQueue unifiedMessageQueue,
        ILogger<UnifiedMessageProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _aiMessageTemplateService = aiMessageTemplateService;
        _unifiedMessageQueue = unifiedMessageQueue;
        _logger = logger;
    }

    /// <summary>
    /// 处理微信回调消息
    /// </summary>
    public async Task<UnifiedProcessResult> ProcessMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 🚀 统一消息处理器开始处理 - MessageType: {MessageType}, WxManagerId: {WxManagerId}",
                processingId, callbackMessage.MessageType, callbackMessage.WxManagerId);

            // 1. 消息分类和路由
            var messageCategory = CategorizeMessage(callbackMessage);
            
            // 2. 根据消息类型执行相应处理
            var result = messageCategory switch
            {
                MessageCategory.PrivateText => await ProcessPrivateTextMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageCategory.GroupText => await ProcessGroupTextMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageCategory.PrivateMedia => await ProcessPrivateMediaMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageCategory.GroupMedia => await ProcessGroupMediaMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageCategory.System => await ProcessSystemMessageAsync(callbackMessage, processingId, cancellationToken),
                _ => UnifiedProcessResult.CreateFailure($"未支持的消息类型: {callbackMessage.MessageType}")
            };

            var duration = DateTime.UtcNow - startTime;
            
            if (result.Success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 统一消息处理完成 - Category: {Category}, Duration: {Duration}ms",
                    processingId, messageCategory, duration.TotalMilliseconds);
            }
            else
            {
                _logger.LogError("[{ProcessingId}] ❌ 统一消息处理失败 - Category: {Category}, Error: {Error}",
                    processingId, messageCategory, result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "[{ProcessingId}] ❌ 统一消息处理异常 - Duration: {Duration}ms", processingId, duration.TotalMilliseconds);
            return UnifiedProcessResult.CreateFailure($"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 消息分类
    /// </summary>
    private MessageCategory CategorizeMessage(WxCallbackMessageDto callbackMessage)
    {
        var messageType = callbackMessage.MessageType;
        var isGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup);

        return messageType switch
        {
            "60001" when isGroupMessage => MessageCategory.GroupText,
            "60001" when !isGroupMessage => MessageCategory.PrivateText,
            "80001" => MessageCategory.GroupText, // 群聊@消息
            "60002" or "60004" or "60009" or "60010" when isGroupMessage => MessageCategory.GroupMedia,
            "60002" or "60004" or "60009" or "60010" when !isGroupMessage => MessageCategory.PrivateMedia,
            _ => MessageCategory.System
        };
    }

    /// <summary>
    /// 处理私聊文本消息
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessPrivateTextMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📱 处理私聊文本消息", processingId);

            // 1. 检查AI配置
            var contactAiChecker = _serviceProvider.GetRequiredService<IContactAiConfigChecker>();
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            var fromUser = callbackMessage.Data?.FromUser ?? "";
            var aiConfig = await contactAiChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);

            if (!aiConfig.IsAiConfigured || !aiConfig.IsEnabled)
            {
                _logger.LogDebug("[{ProcessingId}] 私聊消息不需要AI回复 - 联系人: {ContactName}, 配置状态: {IsConfigured}",
                    processingId, aiConfig.ContactName, aiConfig.IsAiConfigured);
                return UnifiedProcessResult.CreateSuccess(processingId);
            }

            // 2. 构建AI消息模板
            var aiTemplate = _aiMessageTemplateService.BuildTextMessageTemplate(callbackMessage);
            
            // 3. 提交到AI处理队列
            await _unifiedMessageQueue.EnqueueAiRequestAsync(wxManagerId, aiTemplate, callbackMessage, processingId, cancellationToken);

            return UnifiedProcessResult.CreateSuccess(processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 私聊文本消息处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"私聊文本消息处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理群聊文本消息
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessGroupTextMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 👥 处理群聊文本消息", processingId);

            // 1. 检查群聊AI触发条件
            var groupMessageProcessor = _serviceProvider.GetRequiredService<IEnhancedGroupMessageProcessor>();
            var groupResult = await groupMessageProcessor.CheckGroupMessageAiReplyTriggerAsync(callbackMessage);
            
            if (!groupResult.ShouldReply)
            {
                _logger.LogDebug("[{ProcessingId}] 群聊消息不需要AI回复 - 原因: {Reason}", processingId, groupResult.Reason);
                return UnifiedProcessResult.CreateSuccess(processingId);
            }

            // 2. 获取发送者昵称
            var senderNickname = await GetSenderNicknameAsync(callbackMessage);
            
            // 3. 构建AI消息模板
            var aiTemplate = _aiMessageTemplateService.BuildGroupMessageTemplate(callbackMessage, senderNickname);
            
            // 4. 提交到AI处理队列
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            await _unifiedMessageQueue.EnqueueAiRequestAsync(wxManagerId, aiTemplate, callbackMessage, processingId, cancellationToken);

            return UnifiedProcessResult.CreateSuccess(processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群聊文本消息处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"群聊文本消息处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理私聊媒体消息
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessPrivateMediaMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📁 处理私聊媒体消息", processingId);

            // 1. 提交到媒体处理队列
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            await _unifiedMessageQueue.EnqueueMediaProcessingAsync(wxManagerId, callbackMessage, processingId, cancellationToken);

            return UnifiedProcessResult.CreateSuccess(processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 私聊媒体消息处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"私聊媒体消息处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理群聊媒体消息
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessGroupMediaMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📁 处理群聊媒体消息", processingId);

            // 1. 提交到媒体处理队列
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            await _unifiedMessageQueue.EnqueueMediaProcessingAsync(wxManagerId, callbackMessage, processingId, cancellationToken);

            return UnifiedProcessResult.CreateSuccess(processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群聊媒体消息处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"群聊媒体消息处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理系统消息
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessSystemMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔧 处理系统消息 - MessageType: {MessageType}", processingId, callbackMessage.MessageType);

            // 根据消息类型处理不同的系统消息
            var messageType = callbackMessage.MessageType;

            switch (messageType)
            {
                case "30001": // 好友请求
                    return await ProcessFriendRequestAsync(callbackMessage, processingId, cancellationToken);

                case "30000": // 离线通知
                    return await ProcessOfflineNotificationAsync(callbackMessage, processingId, cancellationToken);

                case "60999": // 其他系统消息
                    _logger.LogInformation("[{ProcessingId}] 📋 系统消息已记录 - MessageType: {MessageType}", processingId, messageType);
                    return UnifiedProcessResult.CreateSuccess(processingId);

                default:
                    _logger.LogDebug("[{ProcessingId}] 📋 未知系统消息类型 - MessageType: {MessageType}", processingId, messageType);
                    return UnifiedProcessResult.CreateSuccess(processingId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 系统消息处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"系统消息处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理好友请求
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessFriendRequestAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 处理好友请求", processingId);

            // 复用现有的好友请求处理器
            var friendRequestProcessor = _serviceProvider.GetRequiredService<IFriendRequestProcessor>();
            var configManager = _serviceProvider.GetRequiredService<ISystemConfigManager>();

            // 创建全局机器人配置（从系统配置中获取）
            var riskControlConfig = await configManager.GetRiskControlConfigAsync();
            var globalConfig = new GlobalRobotConfig
            {
                EnableAutoAcceptFriend = false, // 默认关闭，可以从riskControlConfig中获取
                FriendAcceptKeywords = new List<string>(),
                DailyFriendAcceptLimit = 50,
                FriendWelcomeMessage = "",
                EnableMessageLengthCheck = true,
                MaxMessageLength = 2000,
                EnableAutoSplit = true
            };

            var result = await friendRequestProcessor.ProcessFriendRequestAsync(callbackMessage, globalConfig, processingId, cancellationToken);

            return result.Success
                ? UnifiedProcessResult.CreateSuccess(processingId)
                : UnifiedProcessResult.CreateFailure(result.ErrorMessage ?? "好友请求处理失败");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 好友请求处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"好友请求处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理离线通知
    /// </summary>
    private async Task<UnifiedProcessResult> ProcessOfflineNotificationAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 📴 处理离线通知", processingId);

            // 离线通知主要用于记录，不需要特殊处理
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            _logger.LogInformation("[{ProcessingId}] 📴 微信账号离线通知 - WxManagerId: {WxManagerId}", processingId, wxManagerId);

            return UnifiedProcessResult.CreateSuccess(processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 离线通知处理异常", processingId);
            return UnifiedProcessResult.CreateFailure($"离线通知处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取发送者昵称
    /// </summary>
    private async Task<string> GetSenderNicknameAsync(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            var contactNicknameService = _serviceProvider.GetRequiredService<IContactNicknameService>();
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            // 在群聊中，FromUser就是群聊中的发送者
            var senderWcId = callbackMessage.Data?.FromUser ?? "";

            return await contactNicknameService.GetContactNicknameAsync(wxManagerId, senderWcId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取发送者昵称失败，使用默认值");
            return "未知用户";
        }
    }
}

/// <summary>
/// 消息分类枚举
/// </summary>
public enum MessageCategory
{
    PrivateText,    // 私聊文本
    GroupText,      // 群聊文本
    PrivateMedia,   // 私聊媒体
    GroupMedia,     // 群聊媒体
    System          // 系统消息
}

/// <summary>
/// 统一处理结果
/// </summary>
public class UnifiedProcessResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public TimeSpan ProcessingTime { get; set; }

    public static UnifiedProcessResult CreateSuccess(string processingId)
    {
        return new UnifiedProcessResult
        {
            Success = true,
            ProcessingId = processingId
        };
    }

    public static UnifiedProcessResult CreateFailure(string errorMessage)
    {
        return new UnifiedProcessResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
