using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 联系人同步队列消息
/// </summary>
public class ContactSyncQueueMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString("N")[..8];

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 联系人类型
    /// </summary>
    public WxContactListType ListType { get; set; }

    /// <summary>
    /// 联系人ID列表
    /// </summary>
    public List<string> ContactIds { get; set; } = new();

    /// <summary>
    /// 批次索引（从0开始）
    /// </summary>
    public int BatchIndex { get; set; }

    /// <summary>
    /// 总批次数
    /// </summary>
    public int TotalBatches { get; set; }

    /// <summary>
    /// 当前批次大小
    /// </summary>
    public int BatchSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 优先级（0-255）
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 延时配置（毫秒）
    /// </summary>
    public DelayConfig Delay { get; set; } = new();

    /// <summary>
    /// 重试配置
    /// </summary>
    public RetryConfig Retry { get; set; } = new();

    /// <summary>
    /// 同步会话ID（用于跟踪整个同步过程）
    /// </summary>
    public string SyncSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为最后一个批次
    /// </summary>
    public bool IsLastBatch => BatchIndex == TotalBatches - 1;

    /// <summary>
    /// 获取批次描述
    /// </summary>
    public string GetBatchDescription()
    {
        var contactTypeName = ListType == WxContactListType.Friends ? "个人" : "企业";
        return $"{contactTypeName}联系人批次 {BatchIndex + 1}/{TotalBatches} (本批 {BatchSize} 个)";
    }
}

/// <summary>
/// 延时配置
/// </summary>
public class DelayConfig
{
    /// <summary>
    /// 最小延时（毫秒）
    /// </summary>
    public int MinDelayMs { get; set; } = 300;

    /// <summary>
    /// 最大延时（毫秒）
    /// </summary>
    public int MaxDelayMs { get; set; } = 1500;

    /// <summary>
    /// 是否启用随机延时
    /// </summary>
    public bool EnableRandomDelay { get; set; } = true;
}

/// <summary>
/// 重试配置
/// </summary>
public class RetryConfig
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; } = 0;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 5000;

    /// <summary>
    /// 是否可以重试
    /// </summary>
    public bool CanRetry => CurrentRetryCount < MaxRetryCount;

    /// <summary>
    /// 增加重试次数
    /// </summary>
    public void IncrementRetry()
    {
        CurrentRetryCount++;
    }
}
