namespace HappyWechat.Infrastructure.MessageProcessing.Constants;

/// <summary>
/// 消息处理常量
/// </summary>
public static class MessageProcessingConstants
{
    #region 队列名称常量

    /// <summary>
    /// AI消息处理队列前缀
    /// </summary>
    public const string AiMessageQueuePrefix = "ai_message";

    /// <summary>
    /// 文件处理队列前缀
    /// </summary>
    public const string FileProcessingQueuePrefix = "file_processing";

    /// <summary>
    /// 发送消息队列前缀
    /// </summary>
    public const string SendMessageQueuePrefix = "send_message";

    /// <summary>
    /// 好友请求处理队列前缀
    /// </summary>
    public const string FriendRequestQueuePrefix = "friend_request";

    /// <summary>
    /// 离线通知队列前缀
    /// </summary>
    public const string OfflineNotificationQueuePrefix = "offline_notification";

    #endregion

    #region 处理状态常量

    /// <summary>
    /// 处理成功状态
    /// </summary>
    public const string ProcessingStatusSuccess = "success";

    /// <summary>
    /// 处理失败状态
    /// </summary>
    public const string ProcessingStatusFailed = "failed";

    /// <summary>
    /// 处理中状态
    /// </summary>
    public const string ProcessingStatusInProgress = "in_progress";

    /// <summary>
    /// 等待处理状态
    /// </summary>
    public const string ProcessingStatusPending = "pending";

    /// <summary>
    /// 已取消状态
    /// </summary>
    public const string ProcessingStatusCancelled = "cancelled";

    #endregion

    #region 缓存键常量

    /// <summary>
    /// 实体存在性缓存键前缀
    /// </summary>
    public const string EntityExistsCachePrefix = "entity_exists";

    /// <summary>
    /// 配置缓存键前缀
    /// </summary>
    public const string ConfigCachePrefix = "config";

    /// <summary>
    /// 消息处理统计缓存键前缀
    /// </summary>
    public const string ProcessingStatsCachePrefix = "processing_stats";

    #endregion

    #region 超时常量

    /// <summary>
    /// 默认消息处理超时时间（秒）
    /// </summary>
    public const int DefaultProcessingTimeoutSeconds = 30;

    /// <summary>
    /// AI处理超时时间（秒）
    /// </summary>
    public const int AiProcessingTimeoutSeconds = 60;

    /// <summary>
    /// 文件处理超时时间（秒）
    /// </summary>
    public const int FileProcessingTimeoutSeconds = 120;

    /// <summary>
    /// 媒体处理超时时间（秒）
    /// </summary>
    public const int MediaProcessingTimeoutSeconds = 180;

    #endregion

    #region 重试常量

    /// <summary>
    /// 默认最大重试次数
    /// </summary>
    public const int DefaultMaxRetryCount = 3;

    /// <summary>
    /// 默认重试间隔（毫秒）
    /// </summary>
    public const int DefaultRetryIntervalMs = 1000;

    /// <summary>
    /// 指数退避倍数
    /// </summary>
    public const double ExponentialBackoffMultiplier = 2.0;

    #endregion

    #region 限制常量

    /// <summary>
    /// 最大消息长度
    /// </summary>
    public const int MaxMessageLength = 2000;

    /// <summary>
    /// 最大文件大小（MB）
    /// </summary>
    public const int MaxFileSizeMb = 100;

    /// <summary>
    /// 最大并发处理数
    /// </summary>
    public const int MaxConcurrentProcessing = 10;

    /// <summary>
    /// 批处理大小
    /// </summary>
    public const int BatchSize = 10;

    #endregion

    #region 配置键常量

    /// <summary>
    /// 全局机器人配置键
    /// </summary>
    public const string GlobalBotConfigKey = "GlobalBotConfig";

    /// <summary>
    /// 敏感词配置键
    /// </summary>
    public const string SensitiveWordConfigKey = "SensitiveWordConfig";

    /// <summary>
    /// AI配置键
    /// </summary>
    public const string AiConfigKey = "AiConfig";

    /// <summary>
    /// 消息处理配置键
    /// </summary>
    public const string MessageProcessingConfigKey = "MessageProcessingConfig";

    #endregion

    #region 错误代码常量

    /// <summary>
    /// 实体不存在错误代码
    /// </summary>
    public const string ErrorCodeEntityNotExists = "ENTITY_NOT_EXISTS";

    /// <summary>
    /// 配置无效错误代码
    /// </summary>
    public const string ErrorCodeInvalidConfig = "INVALID_CONFIG";

    /// <summary>
    /// 处理超时错误代码
    /// </summary>
    public const string ErrorCodeProcessingTimeout = "PROCESSING_TIMEOUT";

    /// <summary>
    /// 队列满错误代码
    /// </summary>
    public const string ErrorCodeQueueFull = "QUEUE_FULL";

    /// <summary>
    /// 权限不足错误代码
    /// </summary>
    public const string ErrorCodeInsufficientPermission = "INSUFFICIENT_PERMISSION";

    #endregion

    #region 日志模板常量

    /// <summary>
    /// 消息处理开始日志模板
    /// </summary>
    public const string LogTemplateProcessingStart = "[{ProcessingId}] 🚀 开始处理消息 - MessageType: {MessageType}, WxManagerId: {WxManagerId}";

    /// <summary>
    /// 消息处理完成日志模板
    /// </summary>
    public const string LogTemplateProcessingComplete = "[{ProcessingId}] ✅ 消息处理完成 - Duration: {Duration}ms, Status: {Status}";

    /// <summary>
    /// 消息处理失败日志模板
    /// </summary>
    public const string LogTemplateProcessingFailed = "[{ProcessingId}] ❌ 消息处理失败 - Error: {Error}, Duration: {Duration}ms";

    /// <summary>
    /// 实体验证日志模板
    /// </summary>
    public const string LogTemplateEntityValidation = "[{ProcessingId}] 🔍 实体验证 - EntityType: {EntityType}, EntityId: {EntityId}, Exists: {Exists}";

    #endregion

    #region 性能指标常量

    /// <summary>
    /// 缓存命中率阈值
    /// </summary>
    public const double CacheHitRateThreshold = 0.8;

    /// <summary>
    /// 处理时间警告阈值（毫秒）
    /// </summary>
    public const int ProcessingTimeWarningThresholdMs = 5000;

    /// <summary>
    /// 队列长度警告阈值
    /// </summary>
    public const int QueueLengthWarningThreshold = 100;

    #endregion
}
