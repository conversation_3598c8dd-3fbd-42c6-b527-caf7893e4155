<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HappyWechat - 微信自动化管理平台</title>
    <base href="~/" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="css/layout.css" rel="stylesheet" />
    <link href="css/auth.css" rel="stylesheet" />
    <link href="css/custom-overrides.css" rel="stylesheet" />
    <link href="HappyWechat.Web.styles.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.ico"/>
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">重新加载</a>
        <a class="dismiss">🗙</a>
    </div>

    <!-- 🚀 Blazor Server 基础脚本 -->
    <script src="_framework/blazor.server.js"></script>
    
    <!-- 🚀 MudBlazor 组件库 -->
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>

    <!-- 🚀 SignalR 客户端库 -->
    <script src="https://unpkg.com/@@microsoft/signalr@@latest/dist/browser/signalr.min.js"></script>

    <!-- 🚀 应用核心脚本 -->
    <script src="js/debugLogger.js"></script>

    <!-- 🔧 统一认证状态管理器 - 核心认证架构 -->
    <script src="js/core/AuthenticationEventBus.js"></script>
    <script src="js/core/AuthenticationStateManager.js"></script>
    <script src="js/core/SignalRAuthenticationProvider.js"></script>

    <!-- 🔧 传统认证管理器 - 保持向后兼容 -->
    <script src="js/unifiedSessionManager.js"></script>
    <script src="js/redisAuthManager.js"></script>

    <!-- 🔧 Blazor认证助手 - 确保SignalR连接认证 -->
    <script src="js/blazorAuthHelper.js"></script>

    <!-- 🚀 SignalR 和同步相关脚本 -->
    <script src="js/contactSync.js"></script>
    <script src="js/simplifiedNotificationHandler.js"></script>

    <!-- 🔧 统一同步通知处理器 -->
    <script src="js/unifiedSyncNotificationHandler.js"></script>

    <!-- 🚀 页面刷新管理脚本 - 确保在Blazor初始化前加载 -->
    <script src="js/simplifiedRefreshManager.js"></script>
    <script src="js/blazor-startup.js"></script>
    
    <!-- 🚀 应用程序状态监控 -->
    <script>
        // 监控Blazor启动
        window.addEventListener('DOMContentLoaded', function() {
            // 🚀 监听NetworkMonitor初始化完成事件，然后测试后端连接
            window.addEventListener('networkMonitorReady', function(event) {
                DebugLogger.info('AppLoad', 'NetworkMonitor已就绪，开始测试后端连接', event.detail);
                
                NetworkMonitor.testBackendConnection().then(result => {
                    // Connection test completed
                }).catch(error => {
                    DebugLogger.error('AppLoad', 'NetworkMonitor连接测试失败', error);
                });
            });

            // 监控Blazor启动状态
            const checkBlazorStartup = () => {
                if (window.Blazor) {
                    // Blazor started successfully
                } else {
                    setTimeout(checkBlazorStartup, 100);
                }
            };
            checkBlazorStartup();
        });

        window.addEventListener('load', function() {
            // 强制认证检查
            setTimeout(async function() {
                await performForceAuthCheck();
            }, 2000); // 延迟2秒，确保所有服务已初始化
        });

        // 全局认证状态检查函数（供其他组件使用）
        window.checkAuthenticationStatus = async function() {
            try {
                // 优先使用统一会话管理器检查状态
                if (window.unifiedSessionManager) {
                    const isAuthenticated = await window.unifiedSessionManager.isAuthenticated();
                    if (isAuthenticated) {
                        return true;
                    }
                }

                // 使用Redis认证管理器检查状态
                if (window.redisAuthManager) {
                    return await window.redisAuthManager.checkAuthStatus();
                }

                // 降级到API检查
                const sessionId = await (window.unifiedSessionManager ? 
                    window.unifiedSessionManager.getSessionId() : 
                    localStorage.getItem('hw_session_id'));
                const response = await fetch('/api/auth/status', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache',
                        'X-Session-Id': sessionId || ''
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const isAuthenticated = data?.isSuccess && data?.data?.isAuthenticated === true;
                    const userId = data?.data?.userId;

                    if (isAuthenticated && userId && !userId.includes('anonymous')) {
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.warn('认证状态检查失败:', error);
                return false;
            }
        };

        // 强制认证检查函数
        async function performForceAuthCheck() {
            try {
                // 检查是否在登录页面
                if (window.location.pathname === '/login') {
                    DebugLogger.info('Auth', '当前在登录页面，跳过认证检查');
                    return;
                }

                // 使用Redis认证管理器检查状态
                if (window.redisAuthManager) {
                    const isAuthenticated = await window.redisAuthManager.checkAuthStatus();
                    if (isAuthenticated) {
                        const userInfo = window.redisAuthManager.getUserInfo();
                        DebugLogger.info('Auth', '✅ Redis认证检查成功 - 用户ID: ' + userInfo?.userId);
                    } else {
                        DebugLogger.info('Auth', '⚠️ Redis认证检查失败，用户未认证');
                    }
                } else {
                    DebugLogger.warn('Auth', '⚠️ Redis认证管理器未初始化');
                }

            } catch (error) {
                DebugLogger.error('Auth', '❌ 强制认证检查异常: ' + error.message);
            }
        }
    </script>
</body>
</html>