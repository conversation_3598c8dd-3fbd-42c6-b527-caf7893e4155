using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.Commands.Wx;

/// <summary>
/// 完整群组添加命令
/// </summary>
public class CompleteGroupAdditionCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 群组ID
    /// </summary>
    public string GroupId { get; set; } = string.Empty;

    /// <summary>
    /// 是否强制刷新群组信息
    /// </summary>
    public bool ForceRefresh { get; set; } = false;

    /// <summary>
    /// 是否包含成员信息
    /// </summary>
    public bool IncludeMembers { get; set; } = true;

    /// <summary>
    /// 操作超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// 群组添加结果
/// </summary>
public class GroupAdditionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 群组信息
    /// </summary>
    public GroupAdditionInfo? GroupInfo { get; set; }

    /// <summary>
    /// 成员数量
    /// </summary>
    public int MemberCount { get; set; }

    /// <summary>
    /// 处理详情
    /// </summary>
    public string? ProcessingDetails { get; set; }

    /// <summary>
    /// 完成的步骤
    /// </summary>
    public List<string> CompletedSteps { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static GroupAdditionResult CreateSuccess(GroupAdditionInfo groupInfo, int memberCount, List<string> completedSteps)
    {
        return new GroupAdditionResult
        {
            Success = true,
            GroupInfo = groupInfo,
            MemberCount = memberCount,
            CompletedSteps = completedSteps,
            ProcessingDetails = $"群组添加成功 - 群组: {groupInfo.NickName}, 成员数: {memberCount}"
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static GroupAdditionResult CreateFailure(string errorMessage, List<string>? completedSteps = null)
    {
        return new GroupAdditionResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            CompletedSteps = completedSteps ?? new List<string>(),
            ProcessingDetails = $"群组添加失败: {errorMessage}"
        };
    }
}

/// <summary>
/// 群组添加信息
/// </summary>
public class GroupAdditionInfo
{
    /// <summary>
    /// 群组ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 群组聊天室ID
    /// </summary>
    public string ChatRoomId { get; set; } = string.Empty;

    /// <summary>
    /// 群组昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 群主
    /// </summary>
    public string? ChatRoomOwner { get; set; }

    /// <summary>
    /// 成员数量
    /// </summary>
    public int MemberCount { get; set; }

    /// <summary>
    /// 大头像URL
    /// </summary>
    public string? BigHeadImgUrl { get; set; }

    /// <summary>
    /// 小头像URL
    /// </summary>
    public string? SmallHeadImgUrl { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
