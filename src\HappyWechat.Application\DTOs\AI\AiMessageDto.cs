namespace HappyWechat.Application.DTOs.AI;

/// <summary>
/// AI消息DTO
/// </summary>
public class AiMessageDto
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string? MessageType { get; set; }

    /// <summary>
    /// 发送者
    /// </summary>
    public string? FromUser { get; set; }

    /// <summary>
    /// 接收者
    /// </summary>
    public string? ToUser { get; set; }

    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// 群ID
    /// </summary>
    public string? GroupId { get; set; }

    /// <summary>
    /// 上下文信息
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}