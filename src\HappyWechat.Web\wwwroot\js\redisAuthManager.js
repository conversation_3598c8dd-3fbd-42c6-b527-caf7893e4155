/**
 * Redis认证管理器 - 统一的前端认证状态管理
 */
class RedisAuthManager {
    constructor() {
        this.sessionId = null;
        this.userInfo = null;
        this.isAuthenticated = false;
        this.sessionStorageKey = 'hw_session_id';
        this.userInfoKey = 'hw_user_info';
        this.authCheckInterval = null;
        this.authCheckIntervalMs = 60000; // 1分钟检查一次
        this.renewalThresholdMs = 30 * 60 * 1000; // 30分钟续期阈值
        
        this.init();
    }

    /**
     * 初始化认证管理器
     */
    async init() {
        try {
            console.log('🔐 初始化Redis认证管理器...');
            
            // 从存储中恢复会话
            await this.restoreSession();
            
            // 启动定期认证检查
            this.startAuthCheck();
            
            // 监听页面可见性变化
            this.setupVisibilityListener();
            
            console.log('✅ Redis认证管理器初始化完成');
        } catch (error) {
            console.error('❌ Redis认证管理器初始化失败:', error);
        }
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        try {
            console.log('🔐 开始登录...');
            
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Username: username,
                    Password: password
                })
            });

            if (!response.ok) {
                throw new Error(`登录请求失败: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.isSuccess && result.data) {
                const loginData = result.data;
                
                // 🔧 多渠道获取SessionId，确保可靠性
                this.sessionId = this.extractSessionId(response, loginData);
                
                this.userInfo = {
                    userId: loginData.userId,
                    username: loginData.username,
                    roles: loginData.roles || [],
                    permissions: loginData.permissions || [],
                    expiresAt: loginData.expiresAt
                };
                this.isAuthenticated = true;

                // 持久化存储
                await this.persistSession();
                
                // 🔧 立即设置Authorization头用于后续请求
                this.setAuthorizationHeader();
                
                // 🔧 使用统一认证状态管理器进行状态同步
                if (window.authStateManager) {
                    const success = await window.authStateManager.updateAuthenticationState(this.sessionId, this.userInfo, 'redisAuthManager');
                    if (!success) {
                        console.warn('⚠️ 统一认证状态管理器更新失败，使用降级方案');
                        await this.syncAuthenticationStateLegacy();
                    }
                } else {
                    // 降级：使用传统方法
                    await this.syncAuthenticationStateLegacy();
                }
                
                console.log('✅ 登录成功 - UserId:', this.userInfo.userId, 'SessionId:', this.sessionId?.substring(0, 8) + '...');
                return { success: true, data: loginData };
            } else {
                console.error('❌ 登录失败:', result.message);
                return { success: false, error: result.message || '登录失败' };
            }
        } catch (error) {
            console.error('❌ 登录异常:', error);
            return { success: false, error: error.message || '登录异常' };
        }
    }

    /**
     * 🔧 多渠道提取SessionId
     */
    extractSessionId(response, loginData) {
        // 1. 优先从响应体获取
        if (loginData && loginData.sessionId) {
            console.log('✅ 从响应体获取SessionId');
            return loginData.sessionId;
        }
        
        // 2. 从响应Header获取
        const headerSessionId = response.headers.get('X-Session-Id');
        if (headerSessionId) {
            console.log('✅ 从响应Header获取SessionId');
            return headerSessionId;
        }
        
        // 3. 从Cookie获取
        const cookieSessionId = this.getCookie('HW_SessionId');
        if (cookieSessionId) {
            console.log('✅ 从Cookie获取SessionId');
            return cookieSessionId;
        }
        
        console.warn('⚠️ 未能从任何渠道获取到SessionId');
        return null;
    }

    /**
     * 🔧 设置Authorization头用于后续请求
     */
    setAuthorizationHeader() {
        if (this.sessionId) {
            // 设置全局请求默认Header
            if (window.fetch.defaults) {
                window.fetch.defaults.headers = window.fetch.defaults.headers || {};
                window.fetch.defaults.headers['Authorization'] = `Bearer ${this.sessionId}`;
            }
            console.log('✅ 已设置Authorization头');
        }
    }

    /**
     * 🔧 获取Cookie值
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            console.log('🔐 开始登出...');
            
            if (this.sessionId) {
                // 调用登出API
                try {
                    await fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Session-Id': this.sessionId
                        }
                    });
                } catch (error) {
                    console.warn('⚠️ 登出API调用失败:', error);
                }
            }

            // 清除本地状态
            await this.clearSession();
            
            // 使用统一会话管理器进行完整的状态清除和通知
            if (window.unifiedSessionManager) {
                await window.unifiedSessionManager.clearSessionId();
            } else {
                // 降级：直接通知Blazor
                await this.notifyBlazorAuthStateChanged();
            }
            
            console.log('✅ 登出完成');
            return { success: true };
        } catch (error) {
            console.error('❌ 登出异常:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 检查认证状态
     */
    async checkAuthStatus() {
        try {
            if (!this.sessionId) {
                this.isAuthenticated = false;
                return false;
            }

            const response = await fetch('/api/auth/status', {
                method: 'GET',
                headers: {
                    'X-Session-Id': this.sessionId
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.isSuccess && result.data && result.data.isAuthenticated) {
                    this.isAuthenticated = true;
                    
                    // 更新用户信息
                    if (result.data.userInfo) {
                        this.userInfo = result.data.userInfo;
                        await this.persistSession();
                    }
                    
                    return true;
                } else {
                    await this.clearSession();
                    return false;
                }
            } else {
                await this.clearSession();
                return false;
            }
        } catch (error) {
            console.error('❌ 检查认证状态异常:', error);
            await this.clearSession();
            return false;
        }
    }

    /**
     * 🔧 获取当前会话ID - 多渠道支持
     */
    getSessionId() {
        // 1. 从内存中获取
        if (this.sessionId) {
            return this.sessionId;
        }
        
        // 2. 从localStorage获取
        try {
            const stored = localStorage.getItem(this.sessionStorageKey);
            if (stored) {
                this.sessionId = stored;
                return this.sessionId;
            }
        } catch (error) {
            console.warn('⚠️ 无法从localStorage获取sessionId:', error);
        }
        
        // 3. 从Cookie获取
        const cookieSessionId = this.getCookie('HW_SessionId');
        if (cookieSessionId) {
            this.sessionId = cookieSessionId;
            return this.sessionId;
        }
        
        console.warn('⚠️ 无法从任何渠道获取到有效的SessionId');
        return null;
    }

    /**
     * 获取当前用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 检查是否已认证
     */
    isUserAuthenticated() {
        return this.isAuthenticated && this.sessionId && this.userInfo;
    }

    /**
     * 检查用户是否具有指定角色
     */
    hasRole(role) {
        return this.userInfo?.roles?.includes(role) || false;
    }

    /**
     * 检查用户是否具有指定权限
     */
    hasPermission(permission) {
        return this.userInfo?.permissions?.includes(permission) || false;
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        if (this.sessionId) {
            return {
                'X-Session-Id': this.sessionId
            };
        }
        return {};
    }

    /**
     * 从存储中恢复会话
     */
    async restoreSession() {
        try {
            const sessionId = localStorage.getItem(this.sessionStorageKey);
            const userInfoJson = localStorage.getItem(this.userInfoKey);
            
            if (sessionId && userInfoJson) {
                this.sessionId = sessionId;
                this.userInfo = JSON.parse(userInfoJson);
                
                // 验证会话是否仍然有效
                const isValid = await this.checkAuthStatus();
                if (!isValid) {
                    await this.clearSession();
                }
            }
        } catch (error) {
            console.error('❌ 恢复会话失败:', error);
            await this.clearSession();
        }
    }

    /**
     * 持久化会话
     */
    async persistSession() {
        try {
            if (this.sessionId && this.userInfo) {
                localStorage.setItem(this.sessionStorageKey, this.sessionId);
                localStorage.setItem(this.userInfoKey, JSON.stringify(this.userInfo));
            }
        } catch (error) {
            console.error('❌ 持久化会话失败:', error);
        }
    }

    /**
     * 清除会话
     */
    async clearSession() {
        this.sessionId = null;
        this.userInfo = null;
        this.isAuthenticated = false;
        
        try {
            localStorage.removeItem(this.sessionStorageKey);
            localStorage.removeItem(this.userInfoKey);
        } catch (error) {
            console.error('❌ 清除本地存储失败:', error);
        }
    }

    /**
     * 🔧 传统认证状态同步方法 - 保持向后兼容
     */
    async syncAuthenticationStateLegacy() {
        try {
            console.log('🔄 开始同步认证状态到所有组件（传统方法）...');

            // 1. 通知Blazor认证助手
            if (window.blazorAuthHelper && typeof window.blazorAuthHelper.updateAuthState === 'function') {
                window.blazorAuthHelper.updateAuthState(this.sessionId);
            }

            // 2. 通知所有SignalR连接管理器
            if (window.unifiedSyncNotificationHandler) {
                window.unifiedSyncNotificationHandler.updateAuthentication(this.sessionId);
            }

            // 3. 使用统一会话管理器进行完整的状态同步和通知
            if (window.unifiedSessionManager) {
                await window.unifiedSessionManager.setSessionId(this.sessionId);
                window.unifiedSessionManager.setUserInfo(this.userInfo);
            } else {
                // 降级：直接通知Blazor
                await this.notifyBlazorAuthStateChanged();
            }

            // 4. 更新页面认证状态显示
            this.updatePageAuthDisplay();

            // 5. 触发自定义认证状态变更事件
            this.dispatchAuthStateChangedEvent();

            console.log('✅ 传统认证状态同步完成');
        } catch (error) {
            console.error('❌ 传统认证状态同步失败:', error);
        }
    }

    /**
     * 🔧 同步认证状态到所有组件 - 适配器方法
     */
    async syncAuthenticationState() {
        try {
            // Priority 1: 使用统一认证状态管理器 (推荐)
            if (window.authStateManager) {
                const success = await window.authStateManager.updateAuthenticationState(this.sessionId, this.userInfo, 'redisAuthManager');
                if (success) {
                    console.log('✅ 使用统一认证状态管理器同步成功');
                    return;
                }
            }

            // Priority 2: 降级到传统方法
            await this.syncAuthenticationStateLegacy();
            
            console.log('✅ 认证状态同步完成');
        } catch (error) {
            console.error('❌ 同步认证状态失败:', error);
        }
    }

    /**
     * 🔧 更新页面认证状态显示
     */
    updatePageAuthDisplay() {
        try {
            // 更新所有带有认证状态的元素
            const authElements = document.querySelectorAll('[data-auth-state]');
            authElements.forEach(element => {
                if (this.isAuthenticated) {
                    element.setAttribute('data-auth-state', 'authenticated');
                    element.classList.add('authenticated');
                    element.classList.remove('unauthenticated');
                } else {
                    element.setAttribute('data-auth-state', 'unauthenticated');
                    element.classList.add('unauthenticated');
                    element.classList.remove('authenticated');
                }
            });
        } catch (error) {
            console.warn('⚠️ 更新页面认证状态显示失败:', error);
        }
    }

    /**
     * 🔧 触发认证状态变更事件
     */
    dispatchAuthStateChangedEvent() {
        try {
            const event = new CustomEvent('authStateChanged', {
                detail: {
                    isAuthenticated: this.isAuthenticated,
                    sessionId: this.sessionId,
                    userInfo: this.userInfo,
                    timestamp: new Date()
                }
            });
            
            window.dispatchEvent(event);
            document.dispatchEvent(event);
        } catch (error) {
            console.warn('⚠️ 触发认证状态变更事件失败:', error);
        }
    }

    /**
     * 通知Blazor认证状态变更
     */
    async notifyBlazorAuthStateChanged() {
        try {
            if (window.blazorAuthHelper && window.blazorAuthHelper.invokeMethodAsync) {
                await window.blazorAuthHelper.invokeMethodAsync('NotifyAuthenticationStateChanged', this.sessionId);
            }
        } catch (error) {
            console.warn('⚠️ 通知Blazor认证状态变更失败:', error);
        }
    }

    /**
     * 启动定期认证检查
     */
    startAuthCheck() {
        if (this.authCheckInterval) {
            clearInterval(this.authCheckInterval);
        }

        this.authCheckInterval = setInterval(async () => {
            if (this.sessionId) {
                await this.checkAuthStatus();
            }
        }, this.authCheckIntervalMs);
    }

    /**
     * 停止定期认证检查
     */
    stopAuthCheck() {
        if (this.authCheckInterval) {
            clearInterval(this.authCheckInterval);
            this.authCheckInterval = null;
        }
    }

    /**
     * 设置页面可见性监听器
     */
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', async () => {
            if (!document.hidden && this.sessionId) {
                // 页面变为可见时检查认证状态
                await this.checkAuthStatus();
            }
        });
    }

    /**
     * 销毁认证管理器
     */
    destroy() {
        this.stopAuthCheck();
        this.clearSession();
    }
}

// 创建全局实例
window.redisAuthManager = new RedisAuthManager();

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RedisAuthManager;
}

console.log('🔑 RedisAuthManager已注册到window.redisAuthManager');
