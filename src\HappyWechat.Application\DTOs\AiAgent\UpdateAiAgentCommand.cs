using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiAgent;

/// <summary>
/// 更新AI智能体命令
/// </summary>
public class UpdateAiAgentCommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// AI提供商类型
    /// </summary>
    public AiProviderType ProviderType { get; set; }
    
    /// <summary>
    /// 智能体名称
    /// </summary>
    public required string Name { get; set; }
    
    /// <summary>
    /// 智能体描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
    
    /// <summary>
    /// 配置信息
    /// </summary>
    public required AiAgentConfigDto Config { get; set; }
}
