using HappyWechat.Application.Interfaces;

namespace HappyWechat.Web.Middlewares;

/// <summary>
/// 用户上下文验证中间件
/// 在需要认证的API路径上验证用户上下文的完整性
/// </summary>
public class UserContextValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserContextValidationMiddleware> _logger;

    public UserContextValidationMiddleware(RequestDelegate next, ILogger<UserContextValidationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ICurrentUserContext userContext)
    {
        // 只对API请求进行用户上下文验证
        if (context.Request.Path.StartsWithSegments("/api"))
        {
            // 检查是否为需要认证的API路径
            if (IsProtectedApiPath(context.Request.Path))
            {
                try
                {
                    // 验证用户认证状态
                    if (userContext.IsAuthenticated)
                    {
                        // 验证用户ID是否有效
                        if (userContext.UserId == Guid.Empty)
                        {
                            _logger.LogWarning("⚠️ 用户已认证但用户ID无效 - Path: {Path}, Username: {Username}", 
                                context.Request.Path, userContext.Username);
                        }
                        else
                        {
                            _logger.LogDebug("✅ 用户上下文验证通过 - UserId: {UserId}, Username: {Username}, Path: {Path}", 
                                userContext.UserId, userContext.Username, context.Request.Path);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("🔒 受保护的API路径但用户未认证 - Path: {Path}", context.Request.Path);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 用户上下文验证异常 - Path: {Path}", context.Request.Path);
                }
            }
        }

        await _next(context);
    }

    /// <summary>
    /// 判断是否为需要认证的API路径
    /// </summary>
    /// <param name="path">请求路径</param>
    /// <returns>是否为受保护路径</returns>
    private static bool IsProtectedApiPath(PathString path)
    {
        // 需要认证的API路径
        var protectedPaths = new[]
        {
            "/api/wx/",
            "/api/ai-agent/",
            "/api/ai-config/",
            "/api/schedule/",
            "/api/user/",
            "/api/config/",
            "/api/message/"
        };

        // 不需要认证的API路径
        var publicPaths = new[]
        {
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/status",
            "/api/health"
        };

        // 如果是公开路径，则不需要认证
        if (publicPaths.Any(publicPath => path.StartsWithSegments(publicPath)))
        {
            return false;
        }

        // 如果是受保护路径，则需要认证
        return protectedPaths.Any(protectedPath => path.StartsWithSegments(protectedPath));
    }
}