namespace HappyWechat.Application.DTOs.Monitoring;

/// <summary>
/// 系统指标DTO
/// </summary>
public class SystemMetricsDto
{
    /// <summary>
    /// 收集时间
    /// </summary>
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsagePercentage { get; set; }
    
    /// <summary>
    /// 内存使用情况
    /// </summary>
    public MemoryUsageDto MemoryUsage { get; set; } = new();
    
    /// <summary>
    /// 磁盘使用情况
    /// </summary>
    public List<DiskUsageDto> DiskUsage { get; set; } = new();
    
    /// <summary>
    /// 网络使用情况
    /// </summary>
    public NetworkUsageDto NetworkUsage { get; set; } = new();
    
    /// <summary>
    /// 应用程序指标
    /// </summary>
    public ApplicationMetricsDto ApplicationMetrics { get; set; } = new();
    
    /// <summary>
    /// 数据库指标
    /// </summary>
    public DatabaseMetricsDto DatabaseMetrics { get; set; } = new();
    
    /// <summary>
    /// 系统健康状态
    /// </summary>
    public SystemHealthStatus HealthStatus { get; set; } = SystemHealthStatus.Unknown;
    
    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// 内存使用DTO
/// </summary>
public class MemoryUsageDto
{
    /// <summary>
    /// 总内存（字节）
    /// </summary>
    public long TotalMemoryBytes { get; set; }
    
    /// <summary>
    /// 已使用内存（字节）
    /// </summary>
    public long UsedMemoryBytes { get; set; }
    
    /// <summary>
    /// 可用内存（字节）
    /// </summary>
    public long AvailableMemoryBytes { get; set; }
    
    /// <summary>
    /// 内存使用率（百分比）
    /// </summary>
    public double UsagePercentage { get; set; }
    
    /// <summary>
    /// 应用程序内存使用（字节）
    /// </summary>
    public long ApplicationMemoryBytes { get; set; }
    
    /// <summary>
    /// GC堆内存（字节）
    /// </summary>
    public long GcHeapMemoryBytes { get; set; }
    
    /// <summary>
    /// GC收集次数
    /// </summary>
    public GcCollectionCountDto GcCollectionCount { get; set; } = new();
}

/// <summary>
/// GC收集次数DTO
/// </summary>
public class GcCollectionCountDto
{
    public int Generation0 { get; set; }
    public int Generation1 { get; set; }
    public int Generation2 { get; set; }
}

/// <summary>
/// 磁盘使用DTO
/// </summary>
public class DiskUsageDto
{
    /// <summary>
    /// 驱动器名称
    /// </summary>
    public string DriveName { get; set; } = string.Empty;
    
    /// <summary>
    /// 总空间（字节）
    /// </summary>
    public long TotalSpaceBytes { get; set; }
    
    /// <summary>
    /// 已使用空间（字节）
    /// </summary>
    public long UsedSpaceBytes { get; set; }
    
    /// <summary>
    /// 可用空间（字节）
    /// </summary>
    public long AvailableSpaceBytes { get; set; }
    
    /// <summary>
    /// 使用率（百分比）
    /// </summary>
    public double UsagePercentage { get; set; }
    
    /// <summary>
    /// 文件系统类型
    /// </summary>
    public string FileSystemType { get; set; } = string.Empty;
}

/// <summary>
/// 网络使用DTO
/// </summary>
public class NetworkUsageDto
{
    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; }
    
    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; }
    
    /// <summary>
    /// 发送速率（字节/秒）
    /// </summary>
    public long SendRate { get; set; }
    
    /// <summary>
    /// 接收速率（字节/秒）
    /// </summary>
    public long ReceiveRate { get; set; }
    
    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }
}

/// <summary>
/// 应用程序指标DTO
/// </summary>
public class ApplicationMetricsDto
{
    /// <summary>
    /// 应用程序启动时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }
    
    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; }
    
    /// <summary>
    /// 处理的HTTP请求总数
    /// </summary>
    public long TotalHttpRequests { get; set; }
    
    /// <summary>
    /// 当前活跃HTTP请求数
    /// </summary>
    public int ActiveHttpRequests { get; set; }
    
    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTimeMs { get; set; }
    
    /// <summary>
    /// 错误率（百分比）
    /// </summary>
    public double ErrorRate { get; set; }
    
    /// <summary>
    /// 微信账号数量
    /// </summary>
    public int WeChatAccountCount { get; set; }
    
    /// <summary>
    /// 在线微信账号数量
    /// </summary>
    public int OnlineWeChatAccountCount { get; set; }
    
    /// <summary>
    /// 今日处理消息数
    /// </summary>
    public long TodayProcessedMessages { get; set; }
    
    /// <summary>
    /// AI响应平均时间（毫秒）
    /// </summary>
    public double AverageAiResponseTimeMs { get; set; }
}

/// <summary>
/// 数据库指标DTO
/// </summary>
public class DatabaseMetricsDto
{
    /// <summary>
    /// 连接池状态
    /// </summary>
    public ConnectionPoolStatusDto ConnectionPool { get; set; } = new();
    
    /// <summary>
    /// 查询性能
    /// </summary>
    public QueryPerformanceDto QueryPerformance { get; set; } = new();
    
    /// <summary>
    /// 数据库大小（字节）
    /// </summary>
    public long DatabaseSizeBytes { get; set; }
    
    /// <summary>
    /// 表数量
    /// </summary>
    public int TableCount { get; set; }
    
    /// <summary>
    /// 索引数量
    /// </summary>
    public int IndexCount { get; set; }
}

/// <summary>
/// 连接池状态DTO
/// </summary>
public class ConnectionPoolStatusDto
{
    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; }
    
    /// <summary>
    /// 当前活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }
    
    /// <summary>
    /// 空闲连接数
    /// </summary>
    public int IdleConnections { get; set; }
    
    /// <summary>
    /// 等待连接数
    /// </summary>
    public int WaitingConnections { get; set; }
}

/// <summary>
/// 查询性能DTO
/// </summary>
public class QueryPerformanceDto
{
    /// <summary>
    /// 平均查询时间（毫秒）
    /// </summary>
    public double AverageQueryTimeMs { get; set; }
    
    /// <summary>
    /// 慢查询数量
    /// </summary>
    public int SlowQueryCount { get; set; }
    
    /// <summary>
    /// 查询错误数量
    /// </summary>
    public int QueryErrorCount { get; set; }
    
    /// <summary>
    /// 每秒查询数
    /// </summary>
    public double QueriesPerSecond { get; set; }
}

/// <summary>
/// 系统健康状态
/// </summary>
public enum SystemHealthStatus
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 健康
    /// </summary>
    Healthy = 1,
    
    /// <summary>
    /// 警告
    /// </summary>
    Warning = 2,
    
    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy = 3,
    
    /// <summary>
    /// 严重错误
    /// </summary>
    Critical = 4
}
