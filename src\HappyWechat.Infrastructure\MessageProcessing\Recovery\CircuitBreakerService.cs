using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageProcessing.Recovery;

/// <summary>
/// 断路器服务
/// 实现断路器模式，防止级联失败和系统雪崩
/// </summary>
public interface ICircuitBreakerService
{
    /// <summary>
    /// 执行操作（带断路器保护）
    /// </summary>
    Task<T> ExecuteAsync<T>(string circuitName, Func<Task<T>> operation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 执行操作（带断路器保护，无返回值）
    /// </summary>
    Task ExecuteAsync(string circuitName, Func<Task> operation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取断路器状态
    /// </summary>
    CircuitBreakerState GetCircuitState(string circuitName);
    
    /// <summary>
    /// 手动重置断路器
    /// </summary>
    void ResetCircuit(string circuitName);
    
    /// <summary>
    /// 手动打开断路器
    /// </summary>
    void OpenCircuit(string circuitName, string reason);
}

/// <summary>
/// 断路器状态
/// </summary>
public enum CircuitBreakerState
{
    /// <summary>
    /// 关闭状态 - 正常工作
    /// </summary>
    Closed,
    
    /// <summary>
    /// 打开状态 - 阻止请求
    /// </summary>
    Open,
    
    /// <summary>
    /// 半开状态 - 尝试恢复
    /// </summary>
    HalfOpen
}

/// <summary>
/// 断路器配置
/// </summary>
public class CircuitBreakerConfig
{
    public int FailureThreshold { get; set; } = 5;
    public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(1);
    public TimeSpan OperationTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public int SampleSize { get; set; } = 10;
    public double FailureRate { get; set; } = 0.5; // 50%失败率
}

/// <summary>
/// 断路器实例
/// </summary>
public class CircuitBreaker
{
    private readonly CircuitBreakerConfig _config;
    private readonly ILogger _logger;
    private readonly object _lock = new();
    
    private CircuitBreakerState _state = CircuitBreakerState.Closed;
    private int _failureCount = 0;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private DateTime _lastSuccessTime = DateTime.UtcNow;
    private readonly Queue<bool> _recentResults = new();
    
    public string Name { get; }
    public CircuitBreakerState State => _state;
    public int FailureCount => _failureCount;
    public DateTime LastFailureTime => _lastFailureTime;
    
    public CircuitBreaker(string name, CircuitBreakerConfig config, ILogger logger)
    {
        Name = name;
        _config = config;
        _logger = logger;
    }
    
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (!CanExecute())
        {
            throw new CircuitBreakerOpenException($"断路器 {Name} 处于打开状态");
        }
        
        try
        {
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(_config.OperationTimeout);
            
            var result = await operation().ConfigureAwait(false);
            
            OnSuccess();
            return result;
        }
        catch (Exception ex)
        {
            OnFailure(ex);
            throw;
        }
    }
    
    public async Task ExecuteAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(async () =>
        {
            await operation().ConfigureAwait(false);
            return true;
        }, cancellationToken).ConfigureAwait(false);
    }
    
    private bool CanExecute()
    {
        lock (_lock)
        {
            switch (_state)
            {
                case CircuitBreakerState.Closed:
                    return true;
                
                case CircuitBreakerState.Open:
                    if (DateTime.UtcNow - _lastFailureTime >= _config.OpenTimeout)
                    {
                        _state = CircuitBreakerState.HalfOpen;
                        _logger.LogInformation("🔄 断路器 {Name} 进入半开状态", Name);
                        return true;
                    }
                    return false;
                
                case CircuitBreakerState.HalfOpen:
                    return true;
                
                default:
                    return false;
            }
        }
    }
    
    private void OnSuccess()
    {
        lock (_lock)
        {
            _recentResults.Enqueue(true);
            if (_recentResults.Count > _config.SampleSize)
            {
                _recentResults.Dequeue();
            }
            
            _lastSuccessTime = DateTime.UtcNow;
            
            if (_state == CircuitBreakerState.HalfOpen)
            {
                _state = CircuitBreakerState.Closed;
                _failureCount = 0;
                _logger.LogInformation("✅ 断路器 {Name} 恢复到关闭状态", Name);
            }
        }
    }
    
    private void OnFailure(Exception ex)
    {
        lock (_lock)
        {
            _recentResults.Enqueue(false);
            if (_recentResults.Count > _config.SampleSize)
            {
                _recentResults.Dequeue();
            }
            
            _failureCount++;
            _lastFailureTime = DateTime.UtcNow;
            
            if (ShouldOpen())
            {
                _state = CircuitBreakerState.Open;
                _logger.LogWarning("⚠️ 断路器 {Name} 打开 - 失败次数: {FailureCount}, 异常: {Exception}",
                    Name, _failureCount, ex.Message);
            }
        }
    }
    
    private bool ShouldOpen()
    {
        if (_state == CircuitBreakerState.Open)
            return false;
        
        // 检查失败次数阈值
        if (_failureCount >= _config.FailureThreshold)
            return true;
        
        // 检查失败率
        if (_recentResults.Count >= _config.SampleSize)
        {
            var failureRate = _recentResults.Count(r => !r) / (double)_recentResults.Count;
            if (failureRate >= _config.FailureRate)
                return true;
        }
        
        return false;
    }
    
    public void Reset()
    {
        lock (_lock)
        {
            _state = CircuitBreakerState.Closed;
            _failureCount = 0;
            _recentResults.Clear();
            _logger.LogInformation("🔄 断路器 {Name} 已手动重置", Name);
        }
    }
    
    public void Open(string reason)
    {
        lock (_lock)
        {
            _state = CircuitBreakerState.Open;
            _lastFailureTime = DateTime.UtcNow;
            _logger.LogWarning("⚠️ 断路器 {Name} 已手动打开 - 原因: {Reason}", Name, reason);
        }
    }
}

public class CircuitBreakerService : ICircuitBreakerService
{
    private readonly ILogger<CircuitBreakerService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, CircuitBreaker> _circuitBreakers = new();
    
    public CircuitBreakerService(
        ILogger<CircuitBreakerService> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }
    
    public async Task<T> ExecuteAsync<T>(string circuitName, Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        var circuit = GetOrCreateCircuit(circuitName);
        return await circuit.ExecuteAsync(operation, cancellationToken);
    }
    
    public async Task ExecuteAsync(string circuitName, Func<Task> operation, CancellationToken cancellationToken = default)
    {
        var circuit = GetOrCreateCircuit(circuitName);
        await circuit.ExecuteAsync(operation, cancellationToken);
    }
    
    public CircuitBreakerState GetCircuitState(string circuitName)
    {
        if (_circuitBreakers.TryGetValue(circuitName, out var circuit))
        {
            return circuit.State;
        }
        return CircuitBreakerState.Closed;
    }
    
    public void ResetCircuit(string circuitName)
    {
        if (_circuitBreakers.TryGetValue(circuitName, out var circuit))
        {
            circuit.Reset();
        }
    }
    
    public void OpenCircuit(string circuitName, string reason)
    {
        var circuit = GetOrCreateCircuit(circuitName);
        circuit.Open(reason);
    }
    
    private CircuitBreaker GetOrCreateCircuit(string circuitName)
    {
        return _circuitBreakers.GetOrAdd(circuitName, name =>
        {
            var config = GetCircuitConfig(name);
            return new CircuitBreaker(name, config, _logger);
        });
    }
    
    private CircuitBreakerConfig GetCircuitConfig(string circuitName)
    {
        var section = _configuration.GetSection($"CircuitBreaker:{circuitName}");
        if (section.Exists())
        {
            return section.Get<CircuitBreakerConfig>() ?? new CircuitBreakerConfig();
        }
        
        // 返回默认配置
        return new CircuitBreakerConfig();
    }
}

/// <summary>
/// 断路器打开异常
/// </summary>
public class CircuitBreakerOpenException : Exception
{
    public CircuitBreakerOpenException(string message) : base(message)
    {
    }
    
    public CircuitBreakerOpenException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
