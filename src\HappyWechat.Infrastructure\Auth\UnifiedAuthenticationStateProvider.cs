using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using HappyWechat.Application.Interfaces;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// 统一认证状态提供者实现
/// 整合现有的Redis认证逻辑，提供线程安全的状态管理
/// </summary>
public class UnifiedAuthenticationStateProvider : AuthenticationStateProvider, IUnifiedAuthenticationStateProvider, IDisposable
{
    private readonly IRedisAuthenticationService _authService;
    private readonly ISessionIdRetrievalService _sessionIdRetrievalService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<UnifiedAuthenticationStateProvider> _logger;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    
    private AuthenticationState? _currentAuthState;
    private string? _currentSessionId;
    private DateTime? _lastUpdated;
    private readonly ClaimsPrincipal _anonymous = new(new ClaimsIdentity());

    /// <summary>
    /// 认证状态变更事件
    /// </summary>
    public event Func<AuthenticationStateChangedEventArgs, Task>? AuthenticationStateChanged;

    public UnifiedAuthenticationStateProvider(
        IRedisAuthenticationService authService,
        ISessionIdRetrievalService sessionIdRetrievalService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<UnifiedAuthenticationStateProvider> logger)
    {
        _authService = authService;
        _sessionIdRetrievalService = sessionIdRetrievalService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前认证状态
    /// </summary>
    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            // 如果已有缓存的认证状态，直接返回
            if (_currentAuthState != null && IsStateValid())
            {
                return _currentAuthState;
            }

            // 获取当前会话ID
            var sessionId = await GetCurrentSessionIdInternalAsync();
            if (string.IsNullOrEmpty(sessionId))
            {
                _currentAuthState = new AuthenticationState(_anonymous);
                _currentSessionId = null;
                _lastUpdated = DateTime.UtcNow;
                return _currentAuthState;
            }

            // 从认证服务获取认证状态
            _currentAuthState = await _authService.GetAuthenticationStateAsync(sessionId);
            _currentSessionId = sessionId;
            _lastUpdated = DateTime.UtcNow;

            return _currentAuthState;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取认证状态异常");
            return new AuthenticationState(_anonymous);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 获取当前会话ID
    /// </summary>
    public async Task<string?> GetCurrentSessionIdAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            if (!string.IsNullOrEmpty(_currentSessionId) && IsStateValid())
            {
                return _currentSessionId;
            }

            return await GetCurrentSessionIdInternalAsync();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 内部获取会话ID逻辑 - 使用统一的SessionId获取服务
    /// </summary>
    private async Task<string?> GetCurrentSessionIdInternalAsync()
    {
        try
        {
            _logger.LogDebug("🔍 开始内部获取SessionId...");

            // Priority 1: 如果内存中已有有效的SessionId，直接返回
            if (!string.IsNullOrEmpty(_currentSessionId) && IsStateValid())
            {
                _logger.LogDebug("✅ 从内存缓存获取SessionId: {SessionId}",
                    _currentSessionId.Length > 8 ? _currentSessionId.Substring(0, 8) + "..." : _currentSessionId);
                return _currentSessionId;
            }

            // Priority 2: 使用统一的SessionId获取服务
            var httpContext = _httpContextAccessor.HttpContext;
            var sessionId = await _sessionIdRetrievalService.GetSessionIdAsync(httpContext);

            if (!string.IsNullOrEmpty(sessionId))
            {
                _logger.LogDebug("✅ 通过SessionId获取服务获取到SessionId: {SessionId}",
                    sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);

                // 更新内存缓存
                _currentSessionId = sessionId;
                _lastUpdated = DateTime.UtcNow;

                return sessionId;
            }

            _logger.LogDebug("⚠️ 所有SessionId获取方式都失败");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取会话ID异常");
            return null;
        }
    }

    /// <summary>
    /// 通知认证状态已更改
    /// </summary>
    public async Task<bool> NotifyAuthenticationStateChangedAsync(string? sessionId = null)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("🔔 认证状态变更通知 - SessionId: {SessionId}", sessionId ?? "null");

            var oldSessionId = _currentSessionId;
            
            // 清除缓存的认证状态
            _currentAuthState = null;
            _currentSessionId = sessionId;
            _lastUpdated = DateTime.UtcNow;

            AuthenticationState newAuthState;
            if (!string.IsNullOrEmpty(sessionId))
            {
                // 获取新的认证状态
                newAuthState = await _authService.GetAuthenticationStateAsync(sessionId);
                _currentAuthState = newAuthState;
            }
            else
            {
                // 登出状态
                newAuthState = new AuthenticationState(_anonymous);
                _currentAuthState = newAuthState;
            }

            // 通知Blazor组件认证状态已更改
            NotifyAuthenticationStateChanged(Task.FromResult(newAuthState));

            // 触发认证状态变更事件
            await TriggerAuthenticationStateChangedEvent(sessionId, oldSessionId);

            _logger.LogInformation("✅ 认证状态变更完成 - IsAuthenticated: {IsAuthenticated}", 
                newAuthState.User.Identity?.IsAuthenticated == true);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 通知认证状态变更异常");
            return false;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 标记用户为已认证状态
    /// </summary>
    public async Task<bool> MarkUserAsAuthenticatedAsync(string sessionId, object? userInfo = null)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            _logger.LogWarning("⚠️ SessionId为空，无法标记为已认证");
            return false;
        }

        return await NotifyAuthenticationStateChangedAsync(sessionId);
    }

    /// <summary>
    /// 标记用户为已登出状态
    /// </summary>
    public async Task<bool> MarkUserAsLoggedOutAsync()
    {
        return await NotifyAuthenticationStateChangedAsync(null);
    }

    /// <summary>
    /// 验证会话是否有效
    /// </summary>
    public async Task<bool> ValidateSessionAsync(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            return false;
        }

        try
        {
            var authState = await _authService.GetAuthenticationStateAsync(sessionId);
            return authState?.User?.Identity?.IsAuthenticated == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 验证会话异常 - SessionId: {SessionId}", sessionId);
            return false;
        }
    }

    /// <summary>
    /// 刷新认证状态
    /// </summary>
    public async Task<AuthenticationState> RefreshAuthenticationStateAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            // 清除缓存，强制重新获取
            _currentAuthState = null;
            _lastUpdated = null;
            
            return await GetAuthenticationStateAsync();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 获取用户Claims
    /// </summary>
    public async Task<IEnumerable<Claim>> GetUserClaimsAsync(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            return Enumerable.Empty<Claim>();
        }

        try
        {
            var authState = await _authService.GetAuthenticationStateAsync(sessionId);
            return authState?.User?.Claims ?? Enumerable.Empty<Claim>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取用户Claims异常 - SessionId: {SessionId}", sessionId);
            return Enumerable.Empty<Claim>();
        }
    }

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    public async Task<bool> IsInRoleAsync(string sessionId, string role)
    {
        var claims = await GetUserClaimsAsync(sessionId);
        return claims.Any(c => c.Type == ClaimTypes.Role && c.Value == role);
    }

    /// <summary>
    /// 检查用户是否具有指定权限
    /// </summary>
    public async Task<bool> HasPermissionAsync(string sessionId, string permission)
    {
        var claims = await GetUserClaimsAsync(sessionId);
        return claims.Any(c => c.Type == "permission" && c.Value == permission);
    }

    /// <summary>
    /// 获取认证状态统计信息
    /// </summary>
    public async Task<AuthenticationStateStats> GetStatsAsync()
    {
        var authState = await GetAuthenticationStateAsync();
        var sessionId = await GetCurrentSessionIdAsync();

        var stats = new AuthenticationStateStats
        {
            IsAuthenticated = authState.User.Identity?.IsAuthenticated == true,
            CurrentSessionId = sessionId,
            LastUpdated = _lastUpdated,
            Source = "UnifiedAuthenticationStateProvider"
        };

        if (authState.User.Identity?.IsAuthenticated == true)
        {
            stats.UserId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            stats.Username = authState.User.FindFirst(ClaimTypes.Name)?.Value;
            stats.Roles = authState.User.FindAll(ClaimTypes.Role).Select(c => c.Value);
        }

        return stats;
    }

    /// <summary>
    /// 检查状态是否有效
    /// </summary>
    private bool IsStateValid()
    {
        // 检查状态缓存是否有效（5分钟内）
        return _lastUpdated.HasValue &&
               DateTime.UtcNow - _lastUpdated.Value < TimeSpan.FromMinutes(5) &&
               !string.IsNullOrEmpty(_currentSessionId);
    }

    /// <summary>
    /// 强制刷新SessionId缓存
    /// </summary>
    public async Task<string?> RefreshSessionIdAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogDebug("🔄 强制刷新SessionId缓存...");

            // 清除缓存
            _currentSessionId = null;
            _lastUpdated = null;

            // 重新获取
            var sessionId = await GetCurrentSessionIdInternalAsync();

            _logger.LogDebug("✅ SessionId缓存刷新完成: {SessionId}",
                sessionId != null ? sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId : "null");

            return sessionId;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 获取SessionId获取服务的统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public async Task<SessionIdRetrievalStats> GetSessionIdRetrievalStatsAsync()
    {
        return await _sessionIdRetrievalService.GetRetrievalStatsAsync();
    }

    /// <summary>
    /// 触发认证状态变更事件
    /// </summary>
    private async Task TriggerAuthenticationStateChangedEvent(string? newSessionId, string? oldSessionId)
    {
        if (AuthenticationStateChanged != null)
        {
            var eventArgs = new AuthenticationStateChangedEventArgs
            {
                NewSessionId = newSessionId,
                OldSessionId = oldSessionId,
                Source = "UnifiedAuthenticationStateProvider",
                ChangedAt = DateTime.UtcNow
            };

            try
            {
                await AuthenticationStateChanged.Invoke(eventArgs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 触发认证状态变更事件异常");
            }
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public new void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _semaphore?.Dispose();
        }
    }
}
