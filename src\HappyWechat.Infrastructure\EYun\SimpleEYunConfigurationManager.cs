using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Interfaces;

namespace HappyWechat.Infrastructure.EYun;

/// <summary>
/// 简化的EYun配置管理器 - 直接从IConfiguration读取，避免生命周期冲突
/// 专门用于解决Singleton服务依赖Scoped服务的问题
/// </summary>
public class SimpleEYunConfigurationManager : IEYunConfigurationManager
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SimpleEYunConfigurationManager> _logger;
    private EYunConfigDto? _cachedConfig;
    private readonly object _lock = new();

    public event EventHandler<EYunConfigDto>? ConfigurationChanged;

    public SimpleEYunConfigurationManager(
        IConfiguration configuration,
        ILogger<SimpleEYunConfigurationManager> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<EYunConfigDto> GetCurrentConfigAsync()
    {
        lock (_lock)
        {
            if (_cachedConfig != null)
            {
                return _cachedConfig;
            }
        }

        try
        {
            // 直接从appsettings.json读取EYun配置
            var eyunSection = _configuration.GetSection("EYun");
            var config = new EYunConfigDto
            {
                Account = eyunSection["Account"] ?? "",
                Password = eyunSection["Password"] ?? "",
                WxEYunBaseUrl = eyunSection["WxEYunBaseUrl"] ?? "",
                CallBackUrl = eyunSection["CallBackUrl"] ?? "",
                Token = eyunSection["Token"] ?? ""
            };

            // 验证配置完整性
            ValidateConfig(config);

            lock (_lock)
            {
                _cachedConfig = config;
            }

            _logger.LogDebug("EYun配置加载成功，Token: {TokenMask}", MaskToken(config.Token));
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取EYun配置失败");
            throw;
        }
    }

    public async Task RefreshConfigurationAsync()
    {
        try
        {
            _logger.LogInformation("强制刷新EYun配置");
            
            // 重新从配置读取
            var eyunSection = _configuration.GetSection("EYun");
            var config = new EYunConfigDto
            {
                Account = eyunSection["Account"] ?? "",
                Password = eyunSection["Password"] ?? "",
                WxEYunBaseUrl = eyunSection["WxEYunBaseUrl"] ?? "",
                CallBackUrl = eyunSection["CallBackUrl"] ?? "",
                Token = eyunSection["Token"] ?? ""
            };

            ValidateConfig(config);

            EYunConfigDto? oldConfig;
            lock (_lock)
            {
                oldConfig = _cachedConfig;
                _cachedConfig = config;
            }

            // 检查配置是否有变化
            if (oldConfig != null && !ConfigEquals(oldConfig, config))
            {
                _logger.LogInformation("检测到EYun配置变化，触发变更事件");
                ConfigurationChanged?.Invoke(this, config);
            }

            _logger.LogInformation("EYun配置已刷新，Token: {TokenMask}", MaskToken(config.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新EYun配置失败");
            throw;
        }
    }

    public async Task UpdateConfigurationAsync(EYunConfigDto config)
    {
        try
        {
            lock (_lock)
            {
                _cachedConfig = config;
            }
            
            // 触发配置变更事件
            ConfigurationChanged?.Invoke(this, config);
            
            _logger.LogInformation("EYun配置已更新并通知相关服务");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新EYun配置失败");
            throw;
        }
    }

    private void ValidateConfig(EYunConfigDto config)
    {
        if (string.IsNullOrEmpty(config.WxEYunBaseUrl))
        {
            _logger.LogWarning("EYun BaseUrl未配置");
        }

        if (string.IsNullOrEmpty(config.Token))
        {
            _logger.LogWarning("EYun Token未配置");
        }

        if (string.IsNullOrEmpty(config.Account))
        {
            _logger.LogWarning("EYun Account未配置");
        }
    }

    private static bool ConfigEquals(EYunConfigDto config1, EYunConfigDto config2)
    {
        return config1.Account == config2.Account &&
               config1.Password == config2.Password &&
               config1.WxEYunBaseUrl == config2.WxEYunBaseUrl &&
               config1.CallBackUrl == config2.CallBackUrl &&
               config1.Token == config2.Token;
    }

    private static string MaskToken(string? token)
    {
        if (string.IsNullOrEmpty(token) || token.Length <= 8)
            return "***";
        
        return $"{token[..4]}***{token[^4..]}";
    }
}
