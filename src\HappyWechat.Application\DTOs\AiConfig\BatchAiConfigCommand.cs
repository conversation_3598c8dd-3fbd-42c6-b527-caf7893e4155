using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 批量AI配置命令
/// </summary>
public class BatchAiConfigCommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public BatchConfigType ConfigType { get; set; }
    
    /// <summary>
    /// 目标对象ID列表（联系人或群组的微信ID）
    /// </summary>
    public List<string> TargetIds { get; set; } = new();
    
    /// <summary>
    /// 批量配置设置
    /// </summary>
    public BatchConfigSettings Settings { get; set; } = new();
}

/// <summary>
/// 批量配置类型
/// </summary>
public enum BatchConfigType
{
    /// <summary>
    /// 联系人配置
    /// </summary>
    Contact = 1,
    
    /// <summary>
    /// 群组配置
    /// </summary>
    Group = 2,
    
    /// <summary>
    /// 视频号配置
    /// </summary>
    VideoAccount = 3,
    
    /// <summary>
    /// 朋友圈配置
    /// </summary>
    Moments = 4
}

/// <summary>
/// 批量配置设置
/// </summary>
public class BatchConfigSettings
{
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool? IsEnabled { get; set; }
    

    
    /// <summary>
    /// 关联的AI智能体ID
    /// </summary>
    public Guid? AiAgentId { get; set; }
    
    /// <summary>
    /// 群组特有：仅在@时回复（仅当ConfigType为Group时有效）
    /// </summary>
    public bool? OnlyReplyWhenMentioned { get; set; }
    
    /// <summary>
    /// 群组特有：触发条件（仅当ConfigType为Group时有效）
    /// </summary>
    public GroupTriggerCondition? TriggerCondition { get; set; }
    
    #region 视频号特有配置
    
    /// <summary>
    /// 视频号：是否启用评论回复（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public bool? IsCommentReplyEnabled { get; set; }
    
    /// <summary>
    /// 视频号：是否启用私信回复（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public bool? IsDirectMessageReplyEnabled { get; set; }
    
    /// <summary>
    /// 视频号：是否启用内容生成（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public bool? IsContentGenerationEnabled { get; set; }
    
    /// <summary>
    /// 视频号：评论回复AI智能体ID（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public Guid? CommentReplyAiAgentId { get; set; }
    
    /// <summary>
    /// 视频号：内容生成AI智能体ID（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public Guid? ContentGenerationAiAgentId { get; set; }
    
    /// <summary>
    /// 视频号：评论回复自定义提示词（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public string? CommentReplyPrompt { get; set; }
    
    /// <summary>
    /// 视频号：内容生成自定义提示词（仅当ConfigType为VideoAccount时有效）
    /// </summary>
    public string? ContentGenerationPrompt { get; set; }
    
    #endregion
    
    #region 朋友圈特有配置
    
    /// <summary>
    /// 朋友圈：配置名称（仅当ConfigType为Moments时有效）
    /// </summary>
    public string? ConfigName { get; set; }
    
    /// <summary>
    /// 朋友圈：是否启用自动点赞（仅当ConfigType为Moments时有效）
    /// </summary>
    public bool? IsAutoLikeEnabled { get; set; }
    
    /// <summary>
    /// 朋友圈：评论回复AI智能体ID（仅当ConfigType为Moments时有效）
    /// </summary>
    public Guid? MomentsCommentReplyAiAgentId { get; set; }
    
    /// <summary>
    /// 朋友圈：内容生成AI智能体ID（仅当ConfigType为Moments时有效）
    /// </summary>
    public Guid? MomentsContentGenerationAiAgentId { get; set; }
    
    /// <summary>
    /// 朋友圈：评论回复提示词（仅当ConfigType为Moments时有效）
    /// </summary>
    public string? MomentsCommentReplyPrompt { get; set; }
    
    /// <summary>
    /// 朋友圈：内容生成提示词（仅当ConfigType为Moments时有效）
    /// </summary>
    public string? MomentsContentGenerationPrompt { get; set; }
    
    #endregion
}

/// <summary>
/// 批量配置结果
/// </summary>
public class BatchConfigResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 总数
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 创建数量
    /// </summary>
    public int CreatedCount { get; set; }
    
    /// <summary>
    /// 更新数量
    /// </summary>
    public int UpdatedCount { get; set; }
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount { get; set; }
    
    /// <summary>
    /// 错误消息（总体错误）
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 失败详情
    /// </summary>
    public List<BatchConfigError> Errors { get; set; } = new();
    
    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public long ProcessDurationMs { get; set; }
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public BatchConfigType ConfigType { get; set; }
}

/// <summary>
/// 批量配置错误信息
/// </summary>
public class BatchConfigError
{
    /// <summary>
    /// 目标ID
    /// </summary>
    public string TargetId { get; set; } = string.Empty;
    
    /// <summary>
    /// 目标名称（如果可用）
    /// </summary>
    public string? TargetName { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误类型
    /// </summary>
    public BatchConfigErrorType ErrorType { get; set; } = BatchConfigErrorType.Unknown;
    
    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime ErrorTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 批量配置错误类型
/// </summary>
public enum BatchConfigErrorType
{
    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 目标不存在
    /// </summary>
    TargetNotFound = 1,
    
    /// <summary>
    /// 验证失败
    /// </summary>
    ValidationFailed = 2,
    
    /// <summary>
    /// 权限不足
    /// </summary>
    AccessDenied = 3,
    
    /// <summary>
    /// 数据库错误
    /// </summary>
    DatabaseError = 4,
    
    /// <summary>
    /// 配置冲突
    /// </summary>
    ConfigConflict = 5
}
