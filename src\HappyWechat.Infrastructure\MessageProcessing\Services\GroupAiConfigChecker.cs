using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Domain.Entities;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Application.Commons;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 群聊回复模式
/// </summary>
public enum GroupReplyMode
{
    /// <summary>
    /// 仅@后回复
    /// </summary>
    OnlyWhenMentioned = 0,
    
    /// <summary>
    /// 所有消息回复
    /// </summary>
    AllMessages = 1
}

/// <summary>
/// 群聊AI配置检查结果
/// </summary>
public class GroupAiConfigResult
{
    public bool IsAiConfigured { get; set; }
    public bool IsEnabled { get; set; }
    public GroupReplyMode ReplyMode { get; set; }
    public bool IsMentioned { get; set; }
    public Guid? AiAgentId { get; set; }
    public string? GroupName { get; set; }
    public string? ErrorMessage { get; set; }
    public bool GroupExists { get; set; } = true;
    public MessageDiscardReason DiscardReason { get; set; } = MessageDiscardReason.None;
    public bool OnlyReplyWhenMentioned { get; set; }
}

/// <summary>
/// 群聊AI配置检查服务接口
/// </summary>
public interface IGroupAiConfigChecker
{
    /// <summary>
    /// 检查指定群聊的AI配置
    /// </summary>
    Task<GroupAiConfigResult> CheckGroupAiConfigAsync(Guid wxManagerId, string fromGroup, MessageData messageData);

    /// <summary>
    /// 检查群聊是否应该触发AI回复
    /// </summary>
    Task<bool> ShouldTriggerAiReplyAsync(Guid wxManagerId, string fromGroup, MessageData messageData);

    /// <summary>
    /// 检查消息是否@了机器人
    /// </summary>
    bool IsMessageMentioned(MessageData messageData, string botWcId);

    /// <summary>
    /// 验证群组消息是否应该处理（包含实体存在性检查）
    /// </summary>
    Task<MessageValidationResult> ValidateGroupMessageAsync(Guid wxManagerId, string fromGroup, MessageData messageData, string processingId);
}

/// <summary>
/// 群聊AI配置检查服务实现
/// </summary>
public class GroupAiConfigChecker : IGroupAiConfigChecker
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<GroupAiConfigChecker> _logger;

    public GroupAiConfigChecker(
        ApplicationDbContext dbContext,
        ILogger<GroupAiConfigChecker> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<GroupAiConfigResult> CheckGroupAiConfigAsync(Guid wxManagerId, string fromGroup, MessageData messageData)
    {
        try
        {
            _logger.LogDebug("检查群聊AI配置 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}", 
                wxManagerId, fromGroup);

            // 查找群组实体
            var group = await _dbContext.WxGroupEntities
                .Where(g => g.WxManagerId == wxManagerId && g.ChatRoomId == fromGroup)
                .Select(g => new
                {
                    g.Id,
                    g.NickName,
                    g.IsAiEnabled,
                    g.OnlyReplyWhenMentioned,
                    g.AiAgentId
                })
                .FirstOrDefaultAsync();

            if (group == null)
            {
                _logger.LogDebug("群组不存在 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}",
                    wxManagerId, fromGroup);

                return new GroupAiConfigResult
                {
                    IsAiConfigured = false,
                    IsEnabled = false,
                    ReplyMode = GroupReplyMode.OnlyWhenMentioned,
                    IsMentioned = false,
                    GroupName = fromGroup,
                    ErrorMessage = "群组不存在",
                    GroupExists = false,
                    DiscardReason = MessageDiscardReason.GroupNotExists
                };
            }

            // 🔧 修复：使用正确的机器人ID获取逻辑
            // 在群聊消息中，机器人的wcId应该从外层消息获取，而不是messageData.ToUser
            var botWcId = messageData.ToUser; // 先尝试使用ToUser

            // 🔧 修复：如果ToUser为空或不合理，使用默认处理
            if (string.IsNullOrEmpty(botWcId) || botWcId.Contains("@chatroom"))
            {
                // 对于群聊消息，ToUser可能是群ID，这种情况下@检测可能不准确
                _logger.LogDebug("⚠️ ToUser不是有效的机器人ID: {ToUser}，@检测可能不准确", botWcId);
                // 这种情况下，我们依赖AtList中的内容进行检测
                botWcId = ""; // 清空，依赖AtList检测
            }

            // 🔧 修复：添加详细的@检测日志
            _logger.LogDebug("🔍 开始@检测 - BotWcId: {BotWcId}, AtList: [{AtList}], Content: '{Content}'",
                botWcId,
                messageData.Atlist != null ? string.Join(",", messageData.Atlist) : "null",
                messageData.Content?.Length > 50 ? messageData.Content.Substring(0, 50) + "..." : messageData.Content ?? "null");

            var isMentioned = IsMessageMentioned(messageData, botWcId);

            _logger.LogDebug("🔍 @检测结果 - IsMentioned: {IsMentioned}, BotWcId: {BotWcId}",
                isMentioned, botWcId);

            // 简化群组AI配置判断逻辑：需要同时满足启用和有代理
            var isFullyConfigured = group.IsAiEnabled && group.AiAgentId.HasValue;

            var result = new GroupAiConfigResult
            {
                IsAiConfigured = isFullyConfigured,
                IsEnabled = group.IsAiEnabled, // 使用IsAiEnabled字段
                ReplyMode = group.OnlyReplyWhenMentioned ? GroupReplyMode.OnlyWhenMentioned : GroupReplyMode.AllMessages,
                IsMentioned = isMentioned,
                AiAgentId = group.AiAgentId,
                GroupName = group.NickName ?? fromGroup,
                OnlyReplyWhenMentioned = group.OnlyReplyWhenMentioned
            };

            _logger.LogDebug("群聊AI配置检查完成 - Group: {GroupName}, IsAiEnabled: {IsAiEnabled}, ReplyMode: {ReplyMode}, IsMentioned: {IsMentioned}, AiAgentId: {AiAgentId}, IsFullyConfigured: {IsFullyConfigured}",
                result.GroupName, group.IsAiEnabled, result.ReplyMode, result.IsMentioned, result.AiAgentId, isFullyConfigured);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群聊AI配置失败 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}", 
                wxManagerId, fromGroup);
            
            return new GroupAiConfigResult
            {
                IsAiConfigured = false,
                IsEnabled = false,
                ReplyMode = GroupReplyMode.OnlyWhenMentioned,
                IsMentioned = false,
                GroupName = fromGroup,
                ErrorMessage = $"检查失败: {ex.Message}"
            };
        }
    }

    public async Task<bool> ShouldTriggerAiReplyAsync(Guid wxManagerId, string fromGroup, MessageData messageData)
    {
        try
        {
            var config = await CheckGroupAiConfigAsync(wxManagerId, fromGroup, messageData);

            // 简化判断逻辑：只要启用AI且有AI代理就可能触发AI回复
            if (!config.IsAiConfigured || !config.IsEnabled)
            {
                _logger.LogDebug("群聊AI未配置或未启用 - Group: {GroupName}, IsConfigured: {IsConfigured}, IsEnabled: {IsEnabled}",
                    config.GroupName, config.IsAiConfigured, config.IsEnabled);
                return false;
            }

            // 根据@回复模式判断
            bool shouldTrigger = config.OnlyReplyWhenMentioned ? config.IsMentioned : true;

            _logger.LogDebug("群聊AI回复触发检查 - Group: {GroupName}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}, ShouldTrigger: {ShouldTrigger}",
                config.GroupName, config.OnlyReplyWhenMentioned, config.IsMentioned, shouldTrigger);
            
            return shouldTrigger;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群聊AI回复触发条件失败 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}", 
                wxManagerId, fromGroup);
            return false;
        }
    }

    public bool IsMessageMentioned(MessageData messageData, string botWcId)
    {
        try
        {
            var content = messageData.Content ?? "";
            var atList = messageData.Atlist ?? new List<string>();

            _logger.LogDebug("🔍 检查@状态 - BotWcId: {BotWcId}, AtList: [{AtList}], Content: '{Content}'",
                botWcId,
                string.Join(",", atList),
                content.Length > 50 ? content.Substring(0, 50) + "..." : content);

            // 🔧 增强@检测逻辑：支持多种@检测方式
            var isMentioned = false;

            // 方式1：检查@列表中是否包含机器人WcId
            if (atList.Contains(botWcId))
            {
                isMentioned = true;
                _logger.LogInformation("✅ @检测成功 - 方式1：@列表包含机器人WcId - BotWcId: {BotWcId}", botWcId);
            }

            // 🔧 删除硬编码的特殊目标检测，统一使用动态机器人ID
            // 方式2：检查消息内容是否包含@所有人
            if (!isMentioned && content.Contains("@所有人"))
            {
                isMentioned = true;
                _logger.LogInformation("✅ @检测成功 - 方式2：消息内容包含@所有人");
            }

            // 方式4：检查消息内容中是否包含@机器人的文本或@所有人
            if (!isMentioned && (content.Contains($"@{botWcId}") || content.Contains("@所有人")))
            {
                isMentioned = true;
                _logger.LogInformation("✅ @检测成功 - 方式4：消息内容包含@机器人或@所有人 - BotWcId: {BotWcId}", botWcId);
            }

            if (isMentioned)
            {
                return true;
            }

            _logger.LogDebug("❌ 消息未@机器人 - BotWcId: {BotWcId}", botWcId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查消息@状态失败 - BotWcId: {BotWcId}", botWcId);
            return false;
        }
    }

    public async Task<MessageValidationResult> ValidateGroupMessageAsync(Guid wxManagerId, string fromGroup, MessageData messageData, string processingId)
    {
        try
        {
            _logger.LogDebug("验证群组消息 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}, ProcessingId: {ProcessingId}",
                wxManagerId, fromGroup, processingId);

            var config = await CheckGroupAiConfigAsync(wxManagerId, fromGroup, messageData);

            // 如果群组不存在，直接丢弃消息
            if (!config.GroupExists)
            {
                _logger.LogWarning("⚠️ 群组不存在，消息将被丢弃 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}",
                    wxManagerId, fromGroup);

                return MessageValidationResult.CreateDiscard(processingId, MessageDiscardReason.GroupNotExists,
                    $"群组 {fromGroup} 在数据库中不存在");
            }

            // 群组存在，返回成功结果
            var result = MessageValidationResult.CreateSuccess(processingId);
            result.ProcessingDetails = $"群组验证通过 - Group: {config.GroupName}, AI配置: {(config.IsAiConfigured ? "已启用" : "未启用")}";

            _logger.LogDebug("✅ 群组消息验证通过 - Group: {GroupName}, ProcessingId: {ProcessingId}",
                config.GroupName, processingId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证群组消息失败 - WxManagerId: {WxManagerId}, FromGroup: {FromGroup}, ProcessingId: {ProcessingId}",
                wxManagerId, fromGroup, processingId);

            return MessageValidationResult.CreateFailure(processingId, $"验证群组消息异常: {ex.Message}");
        }
    }
}
