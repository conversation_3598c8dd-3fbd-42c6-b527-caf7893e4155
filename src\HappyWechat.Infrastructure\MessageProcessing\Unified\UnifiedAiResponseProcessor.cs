using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageProcessing.Handlers;
using HappyWechat.Infrastructure.MessageQueue.Unified;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.MessageProcessing.Unified;

/// <summary>
/// 统一AI响应处理器
/// 整合所有AI响应处理逻辑，包含markdown过滤、拆分、队列入队
/// </summary>
public interface IUnifiedAiResponseProcessor
{
    /// <summary>
    /// 处理AI响应
    /// </summary>
    Task<UnifiedAiResponseResult> ProcessAiResponseAsync(
        string aiResponse,
        Guid wxManagerId,
        string wId,
        string toUser,
        string? toGroup = null,
        string? atUsers = null,
        CancellationToken cancellationToken = default);
}

public class UnifiedAiResponseProcessor : IUnifiedAiResponseProcessor
{
    private readonly IMarkdownProcessor _markdownProcessor;
    private readonly IStreamingAiResponseHandler _streamingHandler;
    private readonly IUnifiedEYunSendQueue _unifiedSendQueue;
    private readonly IUrlAnalyzer _urlAnalyzer;
    private readonly ILogger<UnifiedAiResponseProcessor> _logger;

    public UnifiedAiResponseProcessor(
        IMarkdownProcessor markdownProcessor,
        IStreamingAiResponseHandler streamingHandler,
        IUnifiedEYunSendQueue unifiedSendQueue,
        IUrlAnalyzer urlAnalyzer,
        ILogger<UnifiedAiResponseProcessor> logger)
    {
        _markdownProcessor = markdownProcessor;
        _streamingHandler = streamingHandler;
        _unifiedSendQueue = unifiedSendQueue;
        _urlAnalyzer = urlAnalyzer;
        _logger = logger;
    }

    /// <summary>
    /// 处理AI响应
    /// </summary>
    public async Task<UnifiedAiResponseResult> ProcessAiResponseAsync(
        string aiResponse,
        Guid wxManagerId,
        string wId,
        string toUser,
        string? toGroup = null,
        string? atUsers = null,
        CancellationToken cancellationToken = default)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("[{ProcessingId}] 🤖 开始统一AI响应处理 - Length: {Length}, ToUser: {ToUser}, ToGroup: {ToGroup}",
                processingId, aiResponse.Length, toUser, toGroup);

            // 1. Markdown过滤
            var filteredContent = await _markdownProcessor.ProcessMarkdownAsync(aiResponse);
            _logger.LogInformation("[{ProcessingId}] 📝 Markdown过滤完成 - 原长度: {Original}, 过滤后: {Filtered}",
                processingId, aiResponse.Length, filteredContent.Length);

            // 2. 增强的内容拆分和解析（支持URL分析）
            var mixedContent = await ParseMixedContentWithUrlAnalysisAsync(filteredContent, processingId);
            _logger.LogInformation("[{ProcessingId}] 🔍 内容解析完成 - 拆分为: {Count}个片段",
                processingId, mixedContent.Count);

            // 3. 转换为发送命令
            var sendCommands = CreateSendCommands(mixedContent, wxManagerId, wId, toUser, toGroup, atUsers);
            _logger.LogInformation("[{ProcessingId}] 📝 AI响应拆分完成 - 原始长度: {OriginalLength}, 拆分为: {Count}条消息",
                processingId, aiResponse.Length, sendCommands.Count);

            // 4. 批量入队到统一发送队列
            string batchId = string.Empty;
            if (sendCommands.Any())
            {
                batchId = await _unifiedSendQueue.EnqueueBatchAsync(wxManagerId, sendCommands, cancellationToken);
                _logger.LogInformation("[{ProcessingId}] ✅ AI响应已入队统一发送队列 - BatchId: {BatchId}, 消息数: {Count}",
                    processingId, batchId, sendCommands.Count);
            }

            var duration = DateTime.UtcNow - startTime;
            return UnifiedAiResponseResult.CreateSuccess(processingId, batchId, sendCommands.Count, duration);
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "[{ProcessingId}] ❌ 统一AI响应处理异常 - Duration: {Duration}ms", processingId, duration.TotalMilliseconds);
            return UnifiedAiResponseResult.CreateFailure(processingId, ex.Message, duration);
        }
    }

    /// <summary>
    /// 增强的混合内容解析（支持URL分析和短链接转换）
    /// </summary>
    private async Task<List<MixedContentItem>> ParseMixedContentWithUrlAnalysisAsync(string content, string processingId)
    {
        try
        {
            var items = new List<MixedContentItem>();
            var currentPosition = 0;

            // 查找所有URL（包括短链接）
            var urlPattern = @"https?://[^\s\n\r]+";
            var urlMatches = Regex.Matches(content, urlPattern, RegexOptions.IgnoreCase);

            if (!urlMatches.Any())
            {
                // 没有URL，直接返回文本
                if (!string.IsNullOrWhiteSpace(content))
                {
                    items.Add(new MixedContentItem
                    {
                        Type = "text",
                        Content = content.Trim(),
                        Order = 1
                    });
                }
                return items;
            }

            _logger.LogDebug("[{ProcessingId}] 🔗 发现 {Count} 个URL，开始分析", processingId, urlMatches.Count);

            // 分析所有URL
            var urls = urlMatches.Select(m => m.Value).ToList();
            var urlAnalysisResults = await _urlAnalyzer.AnalyzeUrlsAsync(urls);

            // 按位置排序处理
            var urlInfos = urlMatches.Cast<Match>()
                .Select((match, index) => new
                {
                    Match = match,
                    Analysis = urlAnalysisResults[index],
                    Index = index
                })
                .OrderBy(x => x.Match.Index)
                .ToList();

            foreach (var urlInfo in urlInfos)
            {
                var match = urlInfo.Match;
                var analysis = urlInfo.Analysis;

                // 添加URL前的文本
                if (match.Index > currentPosition)
                {
                    var textContent = content.Substring(currentPosition, match.Index - currentPosition).Trim();
                    if (!string.IsNullOrEmpty(textContent))
                    {
                        items.Add(new MixedContentItem
                        {
                            Type = "text",
                            Content = textContent,
                            Order = items.Count + 1
                        });
                    }
                }

                // 添加URL内容（根据分析结果确定类型）
                var urlToUse = analysis.Success ? analysis.ResolvedUrl : analysis.OriginalUrl;
                items.Add(new MixedContentItem
                {
                    Type = analysis.MessageType,
                    Content = urlToUse,
                    Order = items.Count + 1
                });

                _logger.LogDebug("[{ProcessingId}] 🔗 URL分析完成 - 原始: {Original}, 类型: {Type}, 解析: {Resolved}",
                    processingId, analysis.OriginalUrl, analysis.MessageType, urlToUse);

                currentPosition = match.Index + match.Length;
            }

            // 添加最后剩余的文本
            if (currentPosition < content.Length)
            {
                var remainingText = content.Substring(currentPosition).Trim();
                if (!string.IsNullOrEmpty(remainingText))
                {
                    items.Add(new MixedContentItem
                    {
                        Type = "text",
                        Content = remainingText,
                        Order = items.Count + 1
                    });
                }
            }

            return items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] URL解析异常，降级到基础解析", processingId);
            return ParseMixedContent(content);
        }
    }

    /// <summary>
    /// 基础混合内容解析（保留原有逻辑作为降级方案）
    /// </summary>
    private List<MixedContentItem> ParseMixedContent(string content)
    {
        var items = new List<MixedContentItem>();
        var currentPosition = 0;
        
        // 查找所有媒体内容的位置
        var mediaMatches = new List<(int Start, int End, string Type, string Url, string OriginalText)>();
        
        // 查找图片链接 - 支持多种格式
        var imagePatterns = new[]
        {
            @"图片链接[：:]\s*(https?://[^\s\n]+)",
            @"!\[.*?\]\((https?://[^\s)]+)\)",
            @"<img[^>]+src=[""']([^""']+)[""'][^>]*>",
            @"(https?://[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp)(?:\?[^\s]*)?)"
        };

        foreach (var pattern in imagePatterns)
        {
            var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in regex.Matches(content))
            {
                var url = match.Groups[1].Success ? match.Groups[1].Value : match.Groups[0].Value;
                mediaMatches.Add((match.Index, match.Index + match.Length, "image", url, match.Value));
            }
        }
        
        // 查找文件链接
        var filePatterns = new[]
        {
            @"文件链接[：:]\s*(https?://[^\s\n]+)",
            @"(https?://[^\s]+\.(?:pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar)(?:\?[^\s]*)?)"
        };

        foreach (var pattern in filePatterns)
        {
            var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in regex.Matches(content))
            {
                var url = match.Groups[1].Success ? match.Groups[1].Value : match.Groups[0].Value;
                mediaMatches.Add((match.Index, match.Index + match.Length, "file", url, match.Value));
            }
        }
        
        // 按位置排序
        mediaMatches = mediaMatches.OrderBy(m => m.Start).ToList();
        
        // 拆分内容
        foreach (var media in mediaMatches)
        {
            // 添加媒体前的文本
            if (media.Start > currentPosition)
            {
                var textContent = content.Substring(currentPosition, media.Start - currentPosition).Trim();
                if (!string.IsNullOrEmpty(textContent))
                {
                    items.Add(new MixedContentItem
                    {
                        Type = "text",
                        Content = textContent,
                        Order = items.Count + 1
                    });
                }
            }
            
            // 添加媒体内容
            items.Add(new MixedContentItem
            {
                Type = media.Type,
                Content = media.Url,
                Order = items.Count + 1
            });
            
            currentPosition = media.End;
        }
        
        // 添加剩余文本
        if (currentPosition < content.Length)
        {
            var remainingText = content.Substring(currentPosition).Trim();
            if (!string.IsNullOrEmpty(remainingText))
            {
                items.Add(new MixedContentItem
                {
                    Type = "text",
                    Content = remainingText,
                    Order = items.Count + 1
                });
            }
        }
        
        // 如果没有找到任何媒体内容，整个内容作为文本处理
        if (!items.Any())
        {
            items.Add(new MixedContentItem
            {
                Type = "text",
                Content = content.Trim(),
                Order = 1
            });
        }
        
        return items;
    }

    /// <summary>
    /// 创建发送命令列表 - 使用强类型Command类替代匿名对象
    /// </summary>
    private List<object> CreateSendCommands(
        List<MixedContentItem> mixedContent,
        Guid wxManagerId,
        string wId,
        string toUser,
        string? toGroup,
        string? atUsers)
    {
        var commands = new List<object>();

        foreach (var item in mixedContent)
        {
            object? command = item.Type switch
            {
                "text" => new WxSendTextMessageCommand
                {
                    WId = wId,
                    WcId = string.IsNullOrEmpty(toGroup) ? toUser : toGroup,
                    Content = item.Content,
                    At = atUsers,
                    FromGroup = toGroup,
                    FromGroupUser = string.IsNullOrEmpty(toGroup) ? null : toUser
                },
                "image" => new WxSendImageMessageCommand
                {
                    WId = wId,
                    WcId = string.IsNullOrEmpty(toGroup) ? toUser : toGroup,
                    ImageUrl = item.Content,
                    Content = item.Content
                },
                "file" => new WxSendFileMessageCommand
                {
                    WId = wId,
                    WcId = string.IsNullOrEmpty(toGroup) ? toUser : toGroup,
                    Path = item.Content,
                    FileName = ExtractFileName(item.Content),
                    FilePath = item.Content
                },
                _ => null
            };

            if (command != null)
            {
                commands.Add(command);
            }
        }

        return commands;
    }

    /// <summary>
    /// 从URL中提取文件名
    /// </summary>
    private string ExtractFileName(string url)
    {
        try
        {
            var uri = new Uri(url);
            var fileName = Path.GetFileName(uri.LocalPath);
            return string.IsNullOrEmpty(fileName) ? "未知文件" : fileName;
        }
        catch
        {
            return "未知文件";
        }
    }
}

/// <summary>
/// 混合内容项
/// </summary>
public class MixedContentItem
{
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
}

/// <summary>
/// 统一AI响应处理结果
/// </summary>
public class UnifiedAiResponseResult
{
    public bool Success { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public string? BatchId { get; set; }
    public int MessageCount { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ProcessingTime { get; set; }

    public static UnifiedAiResponseResult CreateSuccess(string processingId, string batchId, int messageCount, TimeSpan processingTime)
    {
        return new UnifiedAiResponseResult
        {
            Success = true,
            ProcessingId = processingId,
            BatchId = batchId,
            MessageCount = messageCount,
            ProcessingTime = processingTime
        };
    }

    public static UnifiedAiResponseResult CreateFailure(string processingId, string errorMessage, TimeSpan processingTime)
    {
        return new UnifiedAiResponseResult
        {
            Success = false,
            ProcessingId = processingId,
            ErrorMessage = errorMessage,
            ProcessingTime = processingTime
        };
    }
}
