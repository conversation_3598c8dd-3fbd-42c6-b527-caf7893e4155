namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 消息基类
/// </summary>
public abstract class MessageBase
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 优先级
    /// </summary>
    public byte Priority { get; set; } = 0;
    
    /// <summary>
    /// 延迟时间（毫秒）
    /// </summary>
    public int? DelayMs { get; set; }
    
    /// <summary>
    /// 消息来源
    /// </summary>
    public string Source { get; set; } = string.Empty;
    
    /// <summary>
    /// 关联ID（如微信账号ID）
    /// </summary>
    public string? CorrelationId { get; set; }
}

/// <summary>
/// 泛型消息包装器
/// </summary>
/// <typeparam name="T">消息数据类型</typeparam>
public class QueueMessage<T> : MessageBase where T : class
{
    /// <summary>
    /// 消息数据
    /// </summary>
    public T Data { get; set; } = default!;
    
    public QueueMessage()
    {
        MessageType = typeof(T).Name;
    }
    
    public QueueMessage(T data) : this()
    {
        Data = data;
    }
}