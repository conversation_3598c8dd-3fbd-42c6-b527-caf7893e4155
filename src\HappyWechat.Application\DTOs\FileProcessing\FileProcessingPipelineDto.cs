using HappyWechat.Application.DTOs.MessageProcess;
using HappyWechat.Application.DTOs.MessageQueue;

namespace HappyWechat.Application.DTOs.FileProcessing;

/// <summary>
/// 文件处理管道请求DTO
/// </summary>
public class FileProcessingRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>
    public FileProcessingType ProcessingType { get; set; }
    
    /// <summary>
    /// 输入文件列表
    /// </summary>
    public List<FileProcessingInput> InputFiles { get; set; } = new();
    
    /// <summary>
    /// 处理选项
    /// </summary>
    public FileProcessingOptions? Options { get; set; }
    
    /// <summary>
    /// 优先级
    /// </summary>
    public ProcessingPriority Priority { get; set; } = ProcessingPriority.Normal;
    
    /// <summary>
    /// 回调配置
    /// </summary>
    public CallbackConfiguration? Callback { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 超时时间（分钟）
    /// </summary>
    public int TimeoutMinutes { get; set; } = 30;
}

/// <summary>
/// 文件处理输入
/// </summary>
public class FileProcessingInput
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public string FileId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 文件URL或路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public NonTextMessageType FileType { get; set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long? FileSize { get; set; }
    
    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string? FileExtension { get; set; }
    
    /// <summary>
    /// 源消息ID（如果来自消息）
    /// </summary>
    public string? SourceMessageId { get; set; }
    
    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// 文件处理管道结果DTO
/// </summary>
public class FileProcessingResult
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 处理状态
    /// </summary>
    public PipelineStatus Status { get; set; }

    /// <summary>
    /// 文件处理结果列表
    /// </summary>
    public List<FileProcessingItemResult> FileResults { get; set; } = new();

    /// <summary>
    /// 总处理时间（毫秒）
    /// </summary>
    public long TotalProcessingTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理统计信息
    /// </summary>
    public ProcessingStatistics Statistics { get; set; } = new();

    /// <summary>
    /// 处理策略
    /// </summary>
    public ProcessingStrategy? ProcessingStrategy { get; set; }

    /// <summary>
    /// 队列消息ID（当使用队列处理时）
    /// </summary>
    public string? QueueMessageId { get; set; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 使用的处理策略
    /// </summary>
    public ProcessingStrategy UsedStrategy { get; set; }
}

/// <summary>
/// 单个文件处理结果
/// </summary>
public class FileProcessingItemResult
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public string FileId { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始文件路径
    /// </summary>
    public string OriginalPath { get; set; } = string.Empty;
    
    /// <summary>
    /// 本地文件路径
    /// </summary>
    public string? LocalPath { get; set; }
    
    /// <summary>
    /// 存储文件路径
    /// </summary>
    public string? StoragePath { get; set; }
    
    /// <summary>
    /// 访问URL
    /// </summary>
    public string? AccessUrl { get; set; }
    
    /// <summary>
    /// 处理状态
    /// </summary>
    public FileProcessingStatus Status { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// 处理步骤记录
    /// </summary>
    public List<ProcessingStep> ProcessingSteps { get; set; } = new();
}

/// <summary>
/// 处理步骤记录
/// </summary>
public class ProcessingStep
{
    /// <summary>
    /// 步骤名称
    /// </summary>
    public string StepName { get; set; } = string.Empty;
    
    /// <summary>
    /// 步骤状态
    /// </summary>
    public StepStatus Status { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    public long DurationMs => EndTime.HasValue ? (long)(EndTime.Value - StartTime).TotalMilliseconds : 0;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 额外信息
    /// </summary>
    public Dictionary<string, object>? AdditionalInfo { get; set; }
}

/// <summary>
/// 处理统计信息
/// </summary>
public class ProcessingStatistics
{
    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }
    
    /// <summary>
    /// 成功处理数
    /// </summary>
    public int SuccessfulFiles { get; set; }
    
    /// <summary>
    /// 失败数
    /// </summary>
    public int FailedFiles { get; set; }
    
    /// <summary>
    /// 跳过数
    /// </summary>
    public int SkippedFiles { get; set; }
    
    /// <summary>
    /// 总下载字节数
    /// </summary>
    public long TotalDownloadedBytes { get; set; }
    
    /// <summary>
    /// 总上传字节数
    /// </summary>
    public long TotalUploadedBytes { get; set; }
    
    /// <summary>
    /// 平均处理时间（毫秒）
    /// </summary>
    public double AverageProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 并发处理数峰值
    /// </summary>
    public int PeakConcurrency { get; set; }
    
    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalFiles > 0 ? (double)SuccessfulFiles / TotalFiles * 100 : 0;
}

/// <summary>
/// 回调配置
/// </summary>
public class CallbackConfiguration
{
    /// <summary>
    /// 回调URL
    /// </summary>
    public string? CallbackUrl { get; set; }
    
    /// <summary>
    /// 回调方法
    /// </summary>
    public string CallbackMethod { get; set; } = "POST";
    
    /// <summary>
    /// 是否在处理完成时回调
    /// </summary>
    public bool CallbackOnComplete { get; set; } = true;
    
    /// <summary>
    /// 是否在处理失败时回调
    /// </summary>
    public bool CallbackOnError { get; set; } = true;
    
    /// <summary>
    /// 是否发送进度更新
    /// </summary>
    public bool SendProgressUpdates { get; set; } = false;
    
    /// <summary>
    /// 进度更新间隔（秒）
    /// </summary>
    public int ProgressIntervalSeconds { get; set; } = 30;
}

#region Enums

/// <summary>
/// 文件处理类型
/// </summary>
public enum FileProcessingType
{
    /// <summary>
    /// 单个文件处理
    /// </summary>
    SingleFile = 1,
    
    /// <summary>
    /// 批量文件处理
    /// </summary>
    BatchFiles = 2,
    
    /// <summary>
    /// 消息中的混合内容
    /// </summary>
    MixedContent = 3,
    
    /// <summary>
    /// 定时任务处理
    /// </summary>
    ScheduledTask = 4
}

/// <summary>
/// 处理优先级
/// </summary>
public enum ProcessingPriority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 2,
    
    /// <summary>
    /// 高优先级
    /// </summary>
    High = 3,
    
    /// <summary>
    /// 紧急优先级
    /// </summary>
    Urgent = 4
}

/// <summary>
/// 管道状态
/// </summary>
public enum PipelineStatus
{
    /// <summary>
    /// 等待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在分析
    /// </summary>
    Analyzing = 1,
    
    /// <summary>
    /// 正在处理
    /// </summary>
    Processing = 2,
    
    /// <summary>
    /// 处理完成
    /// </summary>
    Completed = 3,
    
    /// <summary>
    /// 处理失败
    /// </summary>
    Failed = 4,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 5,
    
    /// <summary>
    /// 超时
    /// </summary>
    Timeout = 6
}

/// <summary>
/// 文件处理状态
/// </summary>
public enum FileProcessingStatus
{
    /// <summary>
    /// 等待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在分析
    /// </summary>
    Analyzing = 1,
    
    /// <summary>
    /// 正在下载
    /// </summary>
    Downloading = 2,
    
    /// <summary>
    /// 正在上传
    /// </summary>
    Uploading = 3,
    
    /// <summary>
    /// 处理完成
    /// </summary>
    Completed = 4,
    
    /// <summary>
    /// 处理失败
    /// </summary>
    Failed = 5,
    
    /// <summary>
    /// 已跳过（重复文件）
    /// </summary>
    Skipped = 6
}

/// <summary>
/// 步骤状态
/// </summary>
public enum StepStatus
{
    /// <summary>
    /// 等待开始
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在执行
    /// </summary>
    Running = 1,
    
    /// <summary>
    /// 执行完成
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// 执行失败
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// 已跳过
    /// </summary>
    Skipped = 4
}

/// <summary>
/// 处理策略
/// </summary>
public enum ProcessingStrategy
{
    /// <summary>
    /// 直接处理
    /// </summary>
    Direct = 1,
    
    /// <summary>
    /// 队列处理
    /// </summary>
    Queue = 2,
    
    /// <summary>
    /// 混合处理
    /// </summary>
    Hybrid = 3,
    
    /// <summary>
    /// 智能选择
    /// </summary>
    Smart = 4
}

#endregion