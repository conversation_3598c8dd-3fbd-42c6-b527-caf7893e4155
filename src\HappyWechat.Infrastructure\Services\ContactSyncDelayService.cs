using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 联系人同步延时服务 - 实现随机延时机制
/// </summary>
public class ContactSyncDelayService
{
    private readonly ILogger<ContactSyncDelayService> _logger;
    private readonly Random _random;

    public ContactSyncDelayService(ILogger<ContactSyncDelayService> logger)
    {
        _logger = logger;
        _random = new Random();
    }

    /// <summary>
    /// 获取随机延时时间（300ms-1500ms）
    /// </summary>
    /// <returns>延时毫秒数</returns>
    public int GetRandomDelay()
    {
        var delay = _random.Next(300, 1501); // 300-1500ms
        _logger.LogDebug("生成随机延时: {Delay}ms", delay);
        return delay;
    }

    /// <summary>
    /// 执行随机延时
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task DelayAsync(CancellationToken cancellationToken = default)
    {
        var delay = GetRandomDelay();
        _logger.LogDebug("开始执行延时: {Delay}ms", delay);
        await Task.Delay(delay, cancellationToken);
        _logger.LogDebug("延时执行完成: {Delay}ms", delay);
    }

    /// <summary>
    /// 根据联系人类型获取延时时间
    /// </summary>
    /// <param name="isEnterprise">是否为企业联系人</param>
    /// <returns>延时毫秒数</returns>
    public int GetDelayByContactType(bool isEnterprise)
    {
        // 企业联系人和个人联系人使用相同的延时策略
        return GetRandomDelay();
    }

    /// <summary>
    /// 执行指定类型的延时
    /// </summary>
    /// <param name="isEnterprise">是否为企业联系人</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task DelayByContactTypeAsync(bool isEnterprise, CancellationToken cancellationToken = default)
    {
        var delay = GetDelayByContactType(isEnterprise);
        _logger.LogDebug("开始执行{ContactType}联系人延时: {Delay}ms", 
            isEnterprise ? "企业" : "个人", delay);
        await Task.Delay(delay, cancellationToken);
        _logger.LogDebug("{ContactType}联系人延时执行完成: {Delay}ms", 
            isEnterprise ? "企业" : "个人", delay);
    }
}
