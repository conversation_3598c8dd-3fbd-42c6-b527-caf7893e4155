namespace HappyWechat.Infrastructure.MessageProcessing.Models;

/// <summary>
/// AI处理消息
/// </summary>
public class AiProcessMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 原始消息ID
    /// </summary>
    public string OriginalMessageId { get; set; } = string.Empty;

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// AI智能体ID
    /// </summary>
    public string AiAgentId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息内容
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// 联系人/群组ID
    /// </summary>
    public string ContactId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// 发送者微信ID
    /// </summary>
    public string FromUser { get; set; } = string.Empty;

    /// <summary>
    /// 发送者昵称
    /// </summary>
    public string FromUserNickname { get; set; } = string.Empty;

    /// <summary>
    /// 群组ID（如果是群消息）
    /// </summary>
    public string? FromGroup { get; set; }

    /// <summary>
    /// 群组ID（兼容性属性）
    /// </summary>
    public string? GroupId { get; set; }

    /// <summary>
    /// 群组名称（如果是群消息）
    /// </summary>
    public string? FromGroupName { get; set; }

    /// <summary>
    /// 消息内容（兼容性属性）
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 文件URL列表
    /// </summary>
    public List<string> FileUrls { get; set; } = new();

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 原始消息ID
    /// </summary>
    public long OriginalMsgId { get; set; }

    /// <summary>
    /// 新消息ID
    /// </summary>
    public long NewMsgId { get; set; }

    /// <summary>
    /// 消息时间戳
    /// </summary>
    public long Timestamp { get; set; }

    /// <summary>
    /// 处理ID
    /// </summary>
    public string ProcessingId { get; set; } = string.Empty;

    /// <summary>
    /// 上下文信息
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 媒体文件信息
    /// </summary>
    public List<MediaFileInfo> MediaFiles { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 创建AI处理消息
    /// </summary>
    public static AiProcessMessage Create(
        string wxManagerId,
        string aiAgentId,
        string userMessage,
        string contactId,
        bool isGroupMessage,
        string fromUser,
        string processingId)
    {
        return new AiProcessMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            WxManagerId = wxManagerId,
            AiAgentId = aiAgentId,
            UserMessage = userMessage,
            ContactId = contactId,
            IsGroupMessage = isGroupMessage,
            FromUser = fromUser,
            ProcessingId = processingId
        };
    }

    /// <summary>
    /// 添加媒体文件
    /// </summary>
    public void AddMediaFile(string fileType, string filePath, string? originalUrl = null)
    {
        MediaFiles.Add(new MediaFileInfo
        {
            FileType = fileType,
            FilePath = filePath,
            OriginalUrl = originalUrl
        });
    }

    /// <summary>
    /// 设置群组信息
    /// </summary>
    public void SetGroupInfo(string fromGroup, string? fromGroupName = null)
    {
        FromGroup = fromGroup;
        FromGroupName = fromGroupName;
    }

    /// <summary>
    /// 设置消息元数据
    /// </summary>
    public void SetMessageMetadata(string messageType, long originalMsgId, long newMsgId, long timestamp)
    {
        MessageType = messageType;
        OriginalMsgId = originalMsgId;
        NewMsgId = newMsgId;
        Timestamp = timestamp;
    }
}

/// <summary>
/// 媒体文件信息
/// </summary>
public class MediaFileInfo
{
    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 原始URL
    /// </summary>
    public string? OriginalUrl { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
