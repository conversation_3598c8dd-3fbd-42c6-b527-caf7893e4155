namespace HappyWechat.Application.DTOs.FileStorage;

/// <summary>
/// 文件下载结果
/// </summary>
public class FileDownloadResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 文件流
    /// </summary>
    public Stream? FileStream { get; set; }
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string? FileName { get; set; }
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string? MimeType { get; set; }
    
    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime? LastModified { get; set; }
}
