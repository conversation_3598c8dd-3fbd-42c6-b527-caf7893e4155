namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 消息间隔配置DTO
/// </summary>
public class MessageIntervalConfig
{
    /// <summary>
    /// 发送消息最小间隔（毫秒）- 流程图要求1.5秒
    /// </summary>
    public int SendMessageMinInterval { get; set; } = 1500;

    /// <summary>
    /// 发送消息最大间隔（毫秒）- 流程图要求2秒
    /// </summary>
    public int SendMessageMaxInterval { get; set; } = 2000;

    /// <summary>
    /// 批量操作最小间隔（毫秒）
    /// </summary>
    public int BatchOperationMinInterval { get; set; } = 300;

    /// <summary>
    /// 批量操作最大间隔（毫秒）
    /// </summary>
    public int BatchOperationMaxInterval { get; set; } = 1500;

    /// <summary>
    /// 联系人操作最小间隔（毫秒）
    /// </summary>
    public int ContactOperationMinInterval { get; set; } = 300;

    /// <summary>
    /// 联系人操作最大间隔（毫秒）
    /// </summary>
    public int ContactOperationMaxInterval { get; set; } = 800;

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    public bool IsValid()
    {
        return SendMessageMinInterval > 0 
            && SendMessageMaxInterval > SendMessageMinInterval
            && BatchOperationMinInterval > 0
            && BatchOperationMaxInterval > BatchOperationMinInterval
            && ContactOperationMinInterval > 0
            && ContactOperationMaxInterval > ContactOperationMinInterval;
    }

    /// <summary>
    /// 获取默认配置
    /// </summary>
    public static MessageIntervalConfig GetDefault()
    {
        return new MessageIntervalConfig();
    }
}
