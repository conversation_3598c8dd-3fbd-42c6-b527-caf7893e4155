using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 群组AI配置DTO
/// </summary>
public class GroupAiConfigDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 群组ID
    /// </summary>
    public Guid GroupId { get; set; }
    
    /// <summary>
    /// 群组信息
    /// </summary>
    public GroupInfo? Group { get; set; }
    
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool IsEnabled { get; set; } = false;
    

    
    /// <summary>
    /// 关联的AI智能体ID
    /// </summary>
    public Guid? AiAgentId { get; set; }
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string? AiAgentName { get; set; }
    
    /// <summary>
    /// 自定义提示词（从AI智能体获取）
    /// </summary>
    public string? CustomPrompt { get; set; }
    
    /// <summary>
    /// 回复延迟时间（秒）（从AI智能体获取）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 3;
    
    /// <summary>
    /// 是否启用敏感词过滤（从AI智能体获取）
    /// </summary>
    public bool EnableSensitiveWordFilter { get; set; } = true;

    /// <summary>
    /// 是否仅在@后回复（群组特有功能）
    /// </summary>
    public bool OnlyReplyWhenMentioned { get; set; } = true;

    /// <summary>
    /// 回复模式
    /// </summary>
    public string ReplyMode { get; set; } = "OnlyAtReply";

    /// <summary>
    /// 触发条件
    /// </summary>
    public GroupTriggerCondition TriggerCondition { get; set; } = new();
}

/// <summary>
/// 群组信息
/// </summary>
public class GroupInfo
{
    /// <summary>
    /// 群组聊天室ID
    /// </summary>
    public string ChatRoomId { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组大头像URL
    /// </summary>
    public string BigHeadImgUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 群成员数量
    /// </summary>
    public int MemberCount { get; set; }
}

/// <summary>
/// 群组触发条件
/// </summary>
public class GroupTriggerCondition
{
    /// <summary>
    /// 是否需要@机器人
    /// </summary>
    public bool RequireAtBot { get; set; } = true;
    
    /// <summary>
    /// 触发关键词列表
    /// </summary>
    public List<string> TriggerKeywords { get; set; } = new();
    
    /// <summary>
    /// 是否回复所有消息
    /// </summary>
    public bool ReplyToAllMessages { get; set; } = false;
    
    /// <summary>
    /// 排除的发送者微信ID列表
    /// </summary>
    public List<string> ExcludedSenders { get; set; } = new();
    
    /// <summary>
    /// 最小消息间隔（秒）- 防止频繁回复
    /// </summary>
    public int MinMessageInterval { get; set; } = 30;
}
