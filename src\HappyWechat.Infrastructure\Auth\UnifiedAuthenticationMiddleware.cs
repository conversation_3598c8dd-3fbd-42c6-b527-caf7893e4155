using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// 统一认证中间件 - 处理Redis会话认证
/// </summary>
public class UnifiedAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UnifiedAuthenticationMiddleware> _logger;
    private readonly HashSet<string> _excludePaths;

    public UnifiedAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<UnifiedAuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
        
        // 不需要认证的路径
        _excludePaths = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "/login",
            "/register",
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/status",
            "/_blazor/negotiate", // 🔧 允许SignalR握手，但处理连接认证
            "/_framework",
            "/css",
            "/js",
            "/images",
            "/favicon.ico",
            "/swagger",
            "/api-docs"
        };
    }

    public async Task InvokeAsync(HttpContext context, IRedisAuthenticationService authService)
    {
        try
        {
            // 检查是否需要认证
            if (ShouldSkipAuthentication(context))
            {
                await _next(context);
                return;
            }

            // 获取会话ID
            var sessionId = authService.GetSessionIdFromContext(context);
            
            if (string.IsNullOrEmpty(sessionId))
            {
                // 没有会话ID，设置为匿名用户
                await SetAnonymousUserAsync(context);
                await _next(context);
                return;
            }

            // 验证会话
            var sessionInfo = await ValidateSessionAsync(authService, sessionId);
            if (sessionInfo == null)
            {
                // 会话无效，清除会话并设置为匿名用户
                authService.ClearSessionIdFromContext(context);
                await SetAnonymousUserAsync(context);
                await _next(context);
                return;
            }

            // 设置认证用户
            await SetAuthenticatedUserAsync(context, sessionInfo);
            
            // 续期会话（如果启用自动续期）
            _ = Task.Run(async () =>
            {
                try
                {
                    await authService.RenewSessionAsync(sessionId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ 会话续期失败 - SessionId: {SessionId}", sessionId);
                }
            });

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 认证中间件处理异常 - Path: {Path}", context.Request.Path);
            await SetAnonymousUserAsync(context);
            await _next(context);
        }
    }

    private bool ShouldSkipAuthentication(HttpContext context)
    {
        var path = context.Request.Path.Value ?? string.Empty;
        
        // 检查排除路径
        if (_excludePaths.Any(excludePath => path.StartsWith(excludePath, StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        // 检查静态文件
        if (IsStaticFile(path))
        {
            return true;
        }

        // 检查API文档相关路径
        if (path.Contains("/swagger", StringComparison.OrdinalIgnoreCase) ||
            path.Contains("/api-docs", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        return false;
    }

    private static bool IsStaticFile(string path)
    {
        var staticExtensions = new[] { ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot" };
        return staticExtensions.Any(ext => path.EndsWith(ext, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<RedisSessionInfo?> ValidateSessionAsync(IRedisAuthenticationService authService, string sessionId)
    {
        try
        {
            var authState = await authService.GetAuthenticationStateAsync(sessionId);
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                // 从认证状态重构会话信息
                var user = authState.User;
                return new RedisSessionInfo
                {
                    SessionId = sessionId,
                    UserId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty,
                    Username = user.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty,
                    Roles = user.FindAll(ClaimTypes.Role).Select(c => c.Value),
                    Permissions = user.FindAll("permission").Select(c => c.Value),
                    LastAccessAt = DateTime.UtcNow
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 验证会话异常 - SessionId: {SessionId}", sessionId);
            return null;
        }
    }

    private async Task SetAuthenticatedUserAsync(HttpContext context, RedisSessionInfo sessionInfo)
    {
        try
        {
            var claimsPrincipal = sessionInfo.ToClaimsPrincipal();
            
            // 设置HttpContext.User
            context.User = claimsPrincipal;

            // 设置认证结果
            var authResult = AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipal, "RedisAuthentication"));
            context.Features.Set<IAuthenticateResultFeature>(new AuthenticateResultFeature { AuthenticateResult = authResult });

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug("✅ 设置认证用户 - UserId: {UserId}, SessionId: {SessionId}", 
                    sessionInfo.UserId, sessionInfo.SessionId);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 设置认证用户异常");
        }
    }

    private async Task SetAnonymousUserAsync(HttpContext context)
    {
        try
        {
            var anonymous = new ClaimsPrincipal(new ClaimsIdentity());
            context.User = anonymous;

            var authResult = AuthenticateResult.NoResult();
            context.Features.Set<IAuthenticateResultFeature>(new AuthenticateResultFeature { AuthenticateResult = authResult });

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 设置匿名用户异常");
        }
    }
}

/// <summary>
/// 认证结果特性实现
/// </summary>
internal class AuthenticateResultFeature : IAuthenticateResultFeature
{
    public AuthenticateResult? AuthenticateResult { get; set; }
}

/// <summary>
/// 统一认证中间件扩展方法
/// </summary>
public static class UnifiedAuthenticationMiddlewareExtensions
{
    /// <summary>
    /// 使用统一认证中间件
    /// </summary>
    /// <param name="builder">应用构建器</param>
    /// <returns>应用构建器</returns>
    public static IApplicationBuilder UseUnifiedAuthentication(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<UnifiedAuthenticationMiddleware>();
    }
}
