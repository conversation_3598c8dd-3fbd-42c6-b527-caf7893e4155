using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.MessageProcessing;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息路由服务 - 负责消息类型识别和路由分发
/// </summary>
public class MessageRoutingService : IMessageRoutingService
{
    private readonly ILogger<MessageRoutingService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public MessageRoutingService(
        ILogger<MessageRoutingService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 根据消息类型分发处理
    /// </summary>
    public async Task<MessageProcessingResult> DispatchMessageProcessingAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 开始消息路由分发 - MessageType: {MessageType}, FromUser: {FromUser}", 
                processingId, callbackMessage.Data?.MsgType, callbackMessage.Data?.FromUser);

            // 1. 识别消息类型
            var messageType = IdentifyMessageType(callbackMessage, processingId);
            
            // 2. 根据消息类型分发到相应处理器
            var result = await DispatchToHandlerAsync(messageType, callbackMessage, processingId, cancellationToken);
            
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            
            _logger.LogInformation("[{ProcessingId}] 消息路由分发完成 - 类型: {MessageType}, 结果: {Success}, 耗时: {ElapsedMs}ms", 
                processingId, messageType, result.Success, stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 消息路由分发异常", processingId);
            return MessageProcessingResult.Failed(
                $"消息路由异常: {ex.Message}",
                $"ProcessingId: {processingId}, ElapsedMs: {stopwatch.ElapsedMilliseconds}");
        }
    }

    /// <summary>
    /// 识别消息类型
    /// </summary>
    private MessageTypeCategory IdentifyMessageType(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            // 根据消息类型和内容判断消息类别
            var messageType = callbackMessage.Data?.MsgType switch
            {
                1 => IdentifyTextMessageType(callbackMessage), // 文本消息
                3 => MessageTypeCategory.Image, // 图片消息
                34 => MessageTypeCategory.Audio, // 语音消息
                43 => MessageTypeCategory.Video, // 视频消息
                47 => MessageTypeCategory.Emoji, // 表情消息
                49 => IdentifyAppMessageType(callbackMessage), // 应用消息
                62 => MessageTypeCategory.VideoCall, // 视频通话
                10000 => MessageTypeCategory.System, // 系统消息
                _ => MessageTypeCategory.Unknown
            };

            _logger.LogDebug("[{ProcessingId}] 识别消息类型 - 原始类型: {OriginalType}, 分类: {Category}", 
                processingId, callbackMessage.Data?.MsgType, messageType);

            return messageType;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 识别消息类型异常", processingId);
            return MessageTypeCategory.Unknown;
        }
    }

    /// <summary>
    /// 识别文本消息类型
    /// </summary>
    private MessageTypeCategory IdentifyTextMessageType(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage)
    {
        // 检查是否为群组消息
        if (!string.IsNullOrWhiteSpace(callbackMessage.Data?.FromGroup))
        {
            return MessageTypeCategory.GroupText;
        }

        // 检查是否为私聊消息
        if (!string.IsNullOrWhiteSpace(callbackMessage.Data?.FromUser))
        {
            return MessageTypeCategory.PrivateText;
        }

        return MessageTypeCategory.Text;
    }

    /// <summary>
    /// 识别应用消息类型
    /// </summary>
    private MessageTypeCategory IdentifyAppMessageType(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage)
    {
        // 应用消息可能包含文件、链接、小程序等
        var content = callbackMessage.Data?.Content ?? string.Empty;
        
        if (content.Contains("url") || content.Contains("link"))
        {
            return MessageTypeCategory.Link;
        }
        
        if (content.Contains("file") || content.Contains("attach"))
        {
            return MessageTypeCategory.File;
        }
        
        if (content.Contains("miniprogram"))
        {
            return MessageTypeCategory.MiniProgram;
        }
        
        return MessageTypeCategory.App;
    }

    /// <summary>
    /// 根据消息类型分发到相应处理器
    /// </summary>
    private async Task<MessageProcessingResult> DispatchToHandlerAsync(
        MessageTypeCategory messageType, 
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        try
        {
            return messageType switch
            {
                MessageTypeCategory.PrivateText => await HandlePrivateTextMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.GroupText => await HandleGroupTextMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Image => await HandleImageMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Audio => await HandleAudioMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Video => await HandleVideoMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.File => await HandleFileMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Link => await HandleLinkMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.MiniProgram => await HandleMiniProgramMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.System => await HandleSystemMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.VideoCall => await HandleVideoCallMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Emoji => await HandleEmojiMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.App => await HandleAppMessageAsync(callbackMessage, processingId, cancellationToken),
                MessageTypeCategory.Unknown => await HandleUnknownMessageAsync(callbackMessage, processingId, cancellationToken),
                _ => MessageProcessingResult.Failed($"未支持的消息类型: {messageType}", $"ProcessingId: {processingId}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 消息处理器分发异常 - 消息类型: {MessageType}", processingId, messageType);
            return MessageProcessingResult.Failed(
                $"消息处理器异常: {ex.Message}",
                $"ProcessingId: {processingId}, MessageType: {messageType}");
        }
    }

    /// <summary>
    /// 处理私聊文本消息
    /// </summary>
    private async Task<MessageProcessingResult> HandlePrivateTextMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理私聊文本消息", processingId);
            
            // 获取私聊消息处理器
            var privateMessageHandler = _serviceProvider.GetRequiredService<IPrivateMessageHandler>();
            return await privateMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理私聊文本消息异常", processingId);
            return MessageProcessingResult.Failed($"私聊消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理群组文本消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleGroupTextMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理群组文本消息 - GroupId: {GroupId}", 
                processingId, callbackMessage.Data?.FromGroup);
            
            // 获取群组消息处理器
            var groupMessageHandler = _serviceProvider.GetRequiredService<IGroupMessageHandler>();
            return await groupMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理群组文本消息异常", processingId);
            return MessageProcessingResult.Failed($"群组消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理图片消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleImageMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理图片消息", processingId);
            
            // 获取文件消息处理器
            var fileMessageHandler = _serviceProvider.GetRequiredService<IFileMessageHandler>();
            return await fileMessageHandler.ProcessImageAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理图片消息异常", processingId);
            return MessageProcessingResult.Failed($"图片消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理语音消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleAudioMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理语音消息", processingId);
            
            // 获取文件消息处理器
            var fileMessageHandler = _serviceProvider.GetRequiredService<IFileMessageHandler>();
            return await fileMessageHandler.ProcessAudioAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理语音消息异常", processingId);
            return MessageProcessingResult.Failed($"语音消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理视频消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleVideoMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理视频消息", processingId);
            
            // 获取文件消息处理器
            var fileMessageHandler = _serviceProvider.GetRequiredService<IFileMessageHandler>();
            return await fileMessageHandler.ProcessVideoAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理视频消息异常", processingId);
            return MessageProcessingResult.Failed($"视频消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理文件消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleFileMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理文件消息", processingId);
            
            // 获取文件消息处理器
            var fileMessageHandler = _serviceProvider.GetRequiredService<IFileMessageHandler>();
            return await fileMessageHandler.ProcessFileAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理文件消息异常", processingId);
            return MessageProcessingResult.Failed($"文件消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理链接消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleLinkMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理链接消息", processingId);
            
            // 获取链接消息处理器
            var linkMessageHandler = _serviceProvider.GetRequiredService<ILinkMessageHandler>();
            return await linkMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理链接消息异常", processingId);
            return MessageProcessingResult.Failed($"链接消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理小程序消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleMiniProgramMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理小程序消息", processingId);
            
            // 获取小程序消息处理器
            var miniProgramHandler = _serviceProvider.GetRequiredService<IMiniProgramMessageHandler>();
            return await miniProgramHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理小程序消息异常", processingId);
            return MessageProcessingResult.Failed($"小程序消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理系统消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleSystemMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理系统消息", processingId);
            
            // 获取系统消息处理器
            var systemMessageHandler = _serviceProvider.GetRequiredService<ISystemMessageHandler>();
            return await systemMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理系统消息异常", processingId);
            return MessageProcessingResult.Failed($"系统消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理视频通话消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleVideoCallMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理视频通话消息", processingId);
            
            // 视频通话消息通常只记录，不进行AI处理
            return MessageProcessingResult.Skipped("视频通话消息不需要处理", $"ProcessingId: {processingId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理视频通话消息异常", processingId);
            return MessageProcessingResult.Failed($"视频通话消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理表情消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleEmojiMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理表情消息", processingId);
            
            // 获取表情消息处理器
            var emojiMessageHandler = _serviceProvider.GetRequiredService<IEmojiMessageHandler>();
            return await emojiMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理表情消息异常", processingId);
            return MessageProcessingResult.Failed($"表情消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理应用消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleAppMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 处理应用消息", processingId);
            
            // 获取应用消息处理器
            var appMessageHandler = _serviceProvider.GetRequiredService<IAppMessageHandler>();
            return await appMessageHandler.ProcessAsync(callbackMessage, processingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理应用消息异常", processingId);
            return MessageProcessingResult.Failed($"应用消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
    }

    /// <summary>
    /// 处理未知消息
    /// </summary>
    private async Task<MessageProcessingResult> HandleUnknownMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogWarning("[{ProcessingId}] 收到未知类型消息 - MessageType: {MessageType}", 
                processingId, callbackMessage.Data?.MsgType);
            
            // 未知消息记录日志但不处理
            return MessageProcessingResult.Skipped($"未知消息类型: {callbackMessage.Data?.MsgType}", $"ProcessingId: {processingId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理未知消息异常", processingId);
            return MessageProcessingResult.Failed($"未知消息处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 检查是否需要优先处理
    /// </summary>
    public bool ShouldPrioritize(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage)
    {
        try
        {
            // 系统消息优先处理
            if (callbackMessage.Data?.MsgType == 10000)
            {
                return true;
            }

            // 视频通话消息优先处理
            if (callbackMessage.Data?.MsgType == 62)
            {
                return true;
            }

            // 群组@消息优先处理
            if (!string.IsNullOrWhiteSpace(callbackMessage.Data?.FromGroup) && 
                callbackMessage.Data?.Content?.Contains("@") == true)
            {
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查消息优先级异常");
            return false;
        }
    }
}

/// <summary>
/// 消息类型分类
/// </summary>
public enum MessageTypeCategory
{
    /// <summary>
    /// 未知类型
    /// </summary>
    Unknown,

    /// <summary>
    /// 文本消息
    /// </summary>
    Text,

    /// <summary>
    /// 私聊文本消息
    /// </summary>
    PrivateText,

    /// <summary>
    /// 群组文本消息
    /// </summary>
    GroupText,

    /// <summary>
    /// 图片消息
    /// </summary>
    Image,

    /// <summary>
    /// 语音消息
    /// </summary>
    Audio,

    /// <summary>
    /// 视频消息
    /// </summary>
    Video,

    /// <summary>
    /// 文件消息
    /// </summary>
    File,

    /// <summary>
    /// 链接消息
    /// </summary>
    Link,

    /// <summary>
    /// 小程序消息
    /// </summary>
    MiniProgram,

    /// <summary>
    /// 系统消息
    /// </summary>
    System,

    /// <summary>
    /// 视频通话消息
    /// </summary>
    VideoCall,

    /// <summary>
    /// 表情消息
    /// </summary>
    Emoji,

    /// <summary>
    /// 应用消息
    /// </summary>
    App
}