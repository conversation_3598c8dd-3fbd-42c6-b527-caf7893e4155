using System.Text.Json.Serialization;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.AiAgent;

/// <summary>
/// AI智能体配置基类
/// </summary>
[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(CoZeConfigDto), "coze")]
[JsonDerivedType(typeof(MaxKBConfigDto), "maxkb")]
[JsonDerivedType(typeof(DifyConfigDto), "dify")]
[JsonDerivedType(typeof(ChatGPTConfigDto), "chatgpt")]
public abstract class AiAgentConfigDto
{
    public abstract AiProviderType ProviderType { get; }
}

/// <summary>
/// 扣子配置
/// </summary>
public class CoZeConfigDto : AiAgentConfigDto
{
    public override AiProviderType ProviderType => AiProviderType.CoZe;
    
    /// <summary>
    /// 基础URL
    /// </summary>
    public required string BaseUrl { get; set; }
    
    /// <summary>
    /// Token
    /// </summary>
    public required string Token { get; set; }
    
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string? WorkflowId { get; set; }
    
    /// <summary>
    /// 机器人ID
    /// </summary>
    public string? BotId { get; set; }
}

/// <summary>
/// MaxKB配置
/// </summary>
public class MaxKBConfigDto : AiAgentConfigDto
{
    public override AiProviderType ProviderType => AiProviderType.MaxKB;

    /// <summary>
    /// 基础URL
    /// </summary>
    public required string BaseUrl { get; set; }

    /// <summary>
    /// Token (格式: application-xxxxx)
    /// </summary>
    public required string Token { get; set; }

    /// <summary>
    /// 应用ID
    /// </summary>
    public string? ApplicationId { get; set; }
}

/// <summary>
/// Dify配置
/// </summary>
public class DifyConfigDto : AiAgentConfigDto
{
    public override AiProviderType ProviderType => AiProviderType.Dify;
    
    /// <summary>
    /// 基础URL
    /// </summary>
    public required string BaseUrl { get; set; }
    
    /// <summary>
    /// Token
    /// </summary>
    public required string Token { get; set; }
}

/// <summary>
/// ChatGPT配置
/// </summary>
public class ChatGPTConfigDto : AiAgentConfigDto
{
    public override AiProviderType ProviderType => AiProviderType.ChatGPT;
    
    /// <summary>
    /// 基础URL
    /// </summary>
    public required string BaseUrl { get; set; }
    
    /// <summary>
    /// Token
    /// </summary>
    public required string Token { get; set; }
    
    /// <summary>
    /// 模型名称
    /// </summary>
    public required string Model { get; set; }
    
    /// <summary>
    /// 系统提示词
    /// </summary>
    public string? SystemPrompt { get; set; }
    
    /// <summary>
    /// 温度参数
    /// </summary>
    public double Temperature { get; set; } = 0.7;
}
