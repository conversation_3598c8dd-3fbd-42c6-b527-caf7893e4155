using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using HappyWechat.Infrastructure.Auth;

namespace HappyWechat.Web.Auth;

/// <summary>
/// Redis认证头处理器 - 自动为HTTP请求添加会话ID
/// </summary>
public class RedisAuthorizationHeaderHandler : DelegatingHandler
{
    private readonly IRedisAuthenticationService _redisAuthService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILogger<RedisAuthorizationHeaderHandler> _logger;

    public RedisAuthorizationHeaderHandler(
        IRedisAuthenticationService redisAuthService,
        IHttpContextAccessor httpContextAccessor,
        AuthenticationStateProvider authStateProvider,
        ILogger<RedisAuthorizationHeaderHandler> logger)
    {
        _redisAuthService = redisAuthService;
        _httpContextAccessor = httpContextAccessor;
        _authStateProvider = authStateProvider;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            string? sessionId = null;

            // 方法1: 从当前HTTP上下文获取会话ID
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                sessionId = _redisAuthService.GetSessionIdFromContext(httpContext);
            }

            // 方法2: 如果HTTP上下文中没有，尝试从认证状态提供者获取
            if (string.IsNullOrEmpty(sessionId) && _authStateProvider is RedisAuthenticationStateProvider redisProvider)
            {
                try
                {
                    var authState = await _authStateProvider.GetAuthenticationStateAsync();
                    if (authState.User.Identity?.IsAuthenticated == true)
                    {
                        // 从Claims中获取会话ID
                        sessionId = authState.User.FindFirst("session_id")?.Value;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "从认证状态提供者获取会话ID失败");
                }
            }

            if (!string.IsNullOrEmpty(sessionId))
            {
                // 添加Authorization头
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", sessionId);
                _logger.LogDebug("已为HTTP请求添加会话ID: {SessionId}", sessionId[..8] + "...");
            }
            else
            {
                _logger.LogDebug("未找到会话ID，跳过认证头添加");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "添加认证头时发生异常");
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
