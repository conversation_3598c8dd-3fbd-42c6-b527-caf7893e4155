using System.Xml.Linq;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 好友请求消息解析器
/// </summary>
public interface IFriendRequestMessageParser
{
    /// <summary>
    /// 解析好友请求消息
    /// </summary>
    /// <param name="callbackMessage">回调消息</param>
    /// <returns>解析后的好友请求信息</returns>
    FriendRequestInfo? ParseFriendRequest(WxCallbackMessageDto callbackMessage);
}

/// <summary>
/// 好友请求消息解析器实现
/// </summary>
public class FriendRequestMessageParser : IFriendRequestMessageParser
{
    private readonly ILogger<FriendRequestMessageParser> _logger;

    public FriendRequestMessageParser(ILogger<FriendRequestMessageParser> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 解析好友请求消息
    /// </summary>
    public FriendRequestInfo? ParseFriendRequest(WxCallbackMessageDto callbackMessage)
    {
        if (callbackMessage.MessageType != "30001")
        {
            _logger.LogWarning("消息类型不是好友请求: {MessageType}", callbackMessage.MessageType);
            return null;
        }

        try
        {
            // 解析XML内容获取关键信息
            var content = callbackMessage.Data?.Content ?? "";
            if (string.IsNullOrEmpty(content))
            {
                _logger.LogWarning("好友请求消息内容为空");
                return null;
            }

            var xmlDoc = XDocument.Parse(content);
            
            var friendRequestInfo = new FriendRequestInfo
            {
                WId = callbackMessage.Data?.WId ?? "",
                FromUser = ExtractFromUsername(xmlDoc),
                FromNickname = ExtractFromNickname(xmlDoc),
                RequestMessage = ExtractRequestMessage(xmlDoc),
                V1 = ExtractV1(xmlDoc),
                V2 = ExtractV2(xmlDoc),
                Scene = ExtractScene(xmlDoc),
                Timestamp = callbackMessage.Data?.Timestamp ?? 0,
                Remark = ExtractRemark(xmlDoc)
            };

            _logger.LogDebug("解析好友请求成功 - FromUser: {FromUser}, FromNickname: {FromNickname}, Scene: {Scene}",
                friendRequestInfo.FromUser, friendRequestInfo.FromNickname, friendRequestInfo.Scene);

            return friendRequestInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析好友请求消息失败 - Content: {Content}", callbackMessage.Data?.Content);
            return null;
        }
    }

    /// <summary>
    /// 提取发送者用户名
    /// </summary>
    private string ExtractFromUsername(XDocument xmlDoc)
    {
        return xmlDoc.Root?.Attribute("fromusername")?.Value ?? "";
    }

    /// <summary>
    /// 提取发送者昵称
    /// </summary>
    private string ExtractFromNickname(XDocument xmlDoc)
    {
        return xmlDoc.Root?.Attribute("fromnickname")?.Value ?? "";
    }

    /// <summary>
    /// 提取请求消息内容
    /// </summary>
    private string ExtractRequestMessage(XDocument xmlDoc)
    {
        return xmlDoc.Root?.Attribute("content")?.Value ?? "";
    }

    /// <summary>
    /// 提取场景值
    /// </summary>
    private int ExtractScene(XDocument xmlDoc)
    {
        var sceneStr = xmlDoc.Root?.Attribute("scene")?.Value ?? "0";
        return int.TryParse(sceneStr, out var scene) ? scene : 0;
    }

    /// <summary>
    /// 提取V1凭证
    /// </summary>
    private string ExtractV1(XDocument xmlDoc)
    {
        try
        {
            return xmlDoc.Root?.Attribute("v1")?.Value ?? "";
        }
        catch
        {
            return "";
        }
    }

    /// <summary>
    /// 提取V2凭证
    /// </summary>
    private string ExtractV2(XDocument xmlDoc)
    {
        try
        {
            return xmlDoc.Root?.Attribute("v2")?.Value ?? "";
        }
        catch
        {
            return "";
        }
    }

    /// <summary>
    /// 提取备注信息
    /// </summary>
    private string ExtractRemark(XDocument xmlDoc)
    {
        try
        {
            return xmlDoc.Root?.Attribute("remark")?.Value ?? "";
        }
        catch
        {
            return "";
        }
    }
}
