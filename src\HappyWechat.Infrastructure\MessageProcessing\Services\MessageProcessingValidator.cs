using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.Caching;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息处理流程验证结果
/// </summary>
public class MessageProcessingValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}

/// <summary>
/// 消息处理流程验证器接口
/// </summary>
public interface IMessageProcessingValidator
{
    /// <summary>
    /// 验证消息处理流程是否符合流程图要求
    /// </summary>
    Task<MessageProcessingValidationResult> ValidateMessageProcessingFlowAsync(WxCallbackMessageDto callbackMessage);
    
    /// <summary>
    /// 验证wcId到WxManagerId转换
    /// </summary>
    Task<bool> ValidateWcIdConversionAsync(string wcId);
    
    /// <summary>
    /// 验证消息队列隔离
    /// </summary>
    Task<bool> ValidateQueueIsolationAsync(Guid wxManagerId);
}

/// <summary>
/// 消息处理流程验证器实现
/// </summary>
public class MessageProcessingValidator : IMessageProcessingValidator
{
    private readonly IWxManagerIdCacheService _wxManagerIdCacheService;
    private readonly IContactAiConfigChecker _contactAiConfigChecker;
    private readonly IGroupAiConfigChecker _groupAiConfigChecker;
    private readonly IMixedContentParser _mixedContentParser;
    private readonly ILogger<MessageProcessingValidator> _logger;

    public MessageProcessingValidator(
        IWxManagerIdCacheService wxManagerIdCacheService,
        IContactAiConfigChecker contactAiConfigChecker,
        IGroupAiConfigChecker groupAiConfigChecker,
        IMixedContentParser mixedContentParser,
        ILogger<MessageProcessingValidator> logger)
    {
        _wxManagerIdCacheService = wxManagerIdCacheService;
        _contactAiConfigChecker = contactAiConfigChecker;
        _groupAiConfigChecker = groupAiConfigChecker;
        _mixedContentParser = mixedContentParser;
        _logger = logger;
    }

    public async Task<MessageProcessingValidationResult> ValidateMessageProcessingFlowAsync(WxCallbackMessageDto callbackMessage)
    {
        var result = new MessageProcessingValidationResult { IsValid = true };

        try
        {
            _logger.LogDebug("开始验证消息处理流程 - WcId: {WcId}, MessageType: {MessageType}", 
                callbackMessage.WcId, callbackMessage.MessageType);

            // 1. 验证基础消息结构
            ValidateBasicMessageStructure(callbackMessage, result);

            // 2. 验证wcId到WxManagerId转换
            await ValidateWcIdToWxManagerIdConversion(callbackMessage, result);

            // 3. 验证消息类型处理
            ValidateMessageTypeHandling(callbackMessage, result);

            // 4. 验证AI配置检查
            await ValidateAiConfigurationCheck(callbackMessage, result);

            // 5. 验证图文混排解析
            await ValidateContentParsing(callbackMessage, result);

            // 6. 验证消息队列路由
            ValidateQueueRouting(callbackMessage, result);

            _logger.LogDebug("消息处理流程验证完成 - IsValid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}", 
                result.IsValid, result.ValidationErrors.Count, result.ValidationWarnings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息处理流程验证异常");
            result.IsValid = false;
            result.ValidationErrors.Add($"验证异常: {ex.Message}");
            return result;
        }
    }

    public async Task<bool> ValidateWcIdConversionAsync(string wcId)
    {
        try
        {
            var wxManagerId = await _wxManagerIdCacheService.GetWxManagerIdAsync(wcId);
            return wxManagerId.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证wcId转换失败 - WcId: {WcId}", wcId);
            return false;
        }
    }

    public async Task<bool> ValidateQueueIsolationAsync(Guid wxManagerId)
    {
        try
        {
            // 这里可以添加队列隔离的验证逻辑
            // 例如检查队列命名是否正确，是否按WxManagerId隔离等
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证队列隔离失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return false;
        }
    }

    private void ValidateBasicMessageStructure(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        if (string.IsNullOrEmpty(callbackMessage.WcId))
        {
            result.IsValid = false;
            result.ValidationErrors.Add("WcId不能为空");
        }

        if (string.IsNullOrEmpty(callbackMessage.MessageType))
        {
            result.IsValid = false;
            result.ValidationErrors.Add("MessageType不能为空");
        }

        if (callbackMessage.Data == null)
        {
            result.IsValid = false;
            result.ValidationErrors.Add("消息数据不能为空");
        }

        result.ValidationDetails["BasicStructureValid"] = result.ValidationErrors.Count == 0;
    }

    private async Task ValidateWcIdToWxManagerIdConversion(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        var conversionSuccess = await ValidateWcIdConversionAsync(callbackMessage.WcId!);
        
        if (!conversionSuccess)
        {
            result.IsValid = false;
            result.ValidationErrors.Add($"wcId到WxManagerId转换失败: {callbackMessage.WcId}");
        }

        result.ValidationDetails["WcIdConversionValid"] = conversionSuccess;
    }

    /// <summary>
    /// 🔧 重构：验证消息类型处理 - 使用60009/80009替代60008/80008
    /// </summary>
    private void ValidateMessageTypeHandling(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        var supportedTypes = new[]
        {
            "10000", "20000", "30000", // 好友请求、群邀请、离线通知
            "60001", "60002", "60004", "60009", // 私聊消息（文件发送完成）
            "80001", "80002", "80004", "80009"  // 群聊消息（文件发送完成）
        };

        var isSupported = supportedTypes.Contains(callbackMessage.MessageType);

        if (!isSupported)
        {
            result.ValidationWarnings.Add($"未知的消息类型: {callbackMessage.MessageType}");
        }

        result.ValidationDetails["MessageTypeSupported"] = isSupported;
    }

    private async Task ValidateAiConfigurationCheck(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        try
        {
            if (!Guid.TryParse(callbackMessage.WxManagerId, out var wxManagerId))
            {
                result.ValidationWarnings.Add("WxManagerId格式无效，无法验证AI配置");
                return;
            }

            bool aiConfigValid = false;

            if (IsPrivateMessage(callbackMessage.MessageType!))
            {
                var fromUser = callbackMessage.Data?.FromUser ?? "";
                var contactConfig = await _contactAiConfigChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);
                aiConfigValid = !string.IsNullOrEmpty(contactConfig.ErrorMessage) == false;
            }
            else if (IsGroupMessage(callbackMessage.MessageType!))
            {
                var fromGroup = callbackMessage.Data?.FromGroup ?? "";
                if (!string.IsNullOrEmpty(fromGroup))
                {
                    var groupConfig = await _groupAiConfigChecker.CheckGroupAiConfigAsync(wxManagerId, fromGroup, callbackMessage.Data!);
                    aiConfigValid = !string.IsNullOrEmpty(groupConfig.ErrorMessage) == false;
                }
            }

            result.ValidationDetails["AiConfigurationValid"] = aiConfigValid;
        }
        catch (Exception ex)
        {
            result.ValidationWarnings.Add($"AI配置验证异常: {ex.Message}");
        }
    }

    private async Task ValidateContentParsing(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        try
        {
            var parseResult = await _mixedContentParser.ParseAsync(callbackMessage);
            var parsingSuccess = parseResult.Items.Count > 0;

            result.ValidationDetails["ContentParsingValid"] = parsingSuccess;
            result.ValidationDetails["ParsedItemCount"] = parseResult.Items.Count;
            result.ValidationDetails["HasMedia"] = parseResult.HasMedia;
        }
        catch (Exception ex)
        {
            result.ValidationWarnings.Add($"内容解析验证异常: {ex.Message}");
        }
    }

    private void ValidateQueueRouting(WxCallbackMessageDto callbackMessage, MessageProcessingValidationResult result)
    {
        var hasWxManagerId = !string.IsNullOrEmpty(callbackMessage.WxManagerId);
        
        if (!hasWxManagerId)
        {
            result.ValidationWarnings.Add("WxManagerId未设置，可能影响队列路由");
        }

        result.ValidationDetails["QueueRoutingValid"] = hasWxManagerId;
    }

    private bool IsPrivateMessage(string messageType)
    {
        return messageType.StartsWith("600");
    }

    private bool IsGroupMessage(string messageType)
    {
        return messageType.StartsWith("800");
    }
}
