namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 消息队列项
/// </summary>
public class MessageQueueItem
{
    /// <summary>
    /// 消息ID（唯一标识）
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 微信实例ID
    /// </summary>
    public string WId { get; set; } = string.Empty;
    
    /// <summary>
    /// 目标用户微信ID（联系人或群组）
    /// </summary>
    public string ToUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageContentType MessageType { get; set; }
    
    /// <summary>
    /// 消息优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件路径（用于图片、视频、文件等）
    /// </summary>
    public string? FilePath { get; set; }
    
    /// <summary>
    /// 文件URL（用于图片、视频、文件等）
    /// </summary>
    public string? FileUrl { get; set; }
    
    /// <summary>
    /// 消息在队列中的顺序
    /// </summary>
    public int Order { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 计划发送时间
    /// </summary>
    public DateTime ScheduledSendTime { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 消息状态
    /// </summary>
    public MessageQueueStatus Status { get; set; } = MessageQueueStatus.Pending;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 扩展属性（JSON格式）
    /// </summary>
    public Dictionary<string, object>? ExtendedProperties { get; set; }
    
    /// <summary>
    /// 兼容性属性：ID（映射到MessageId）
    /// </summary>
    public string Id 
    { 
        get => MessageId; 
        set => MessageId = value; 
    }
    
    /// <summary>
    /// 兼容性属性：消息类型（映射到MessageType）
    /// </summary>
    public MessageContentType Type 
    { 
        get => MessageType; 
        set => MessageType = value; 
    }
    
    /// <summary>
    /// 兼容性属性：目标ID（映射到ToUser）
    /// </summary>
    public string TargetId 
    { 
        get => ToUser; 
        set => ToUser = value; 
    }
    
    /// <summary>
    /// 兼容性属性：时间戳（映射到CreatedAt）
    /// </summary>
    public DateTime Timestamp 
    { 
        get => CreatedAt; 
        set => CreatedAt = value; 
    }
    
    /// <summary>
    /// 兼容性属性：延迟执行时间（映射到ScheduledSendTime）
    /// </summary>
    public DateTime DelayUntil 
    { 
        get => ScheduledSendTime; 
        set => ScheduledSendTime = value; 
    }
    
    /// <summary>
    /// 延迟毫秒数（向后兼容）
    /// </summary>
    public int DelayMs 
    { 
        get => (int)(ScheduledSendTime - CreatedAt).TotalMilliseconds; 
        set => ScheduledSendTime = CreatedAt.AddMilliseconds(value); 
    }
}

/// <summary>
/// 消息内容类型
/// </summary>
public enum MessageContentType
{
    /// <summary>
    /// 文本消息
    /// </summary>
    Text = 1,
    
    /// <summary>
    /// 图片消息
    /// </summary>
    Image = 2,
    
    /// <summary>
    /// 视频消息
    /// </summary>
    Video = 3,
    
    /// <summary>
    /// 语音消息
    /// </summary>
    Voice = 4,
    
    /// <summary>
    /// 文件消息
    /// </summary>
    File = 5,
    
    /// <summary>
    /// 链接消息
    /// </summary>
    Link = 6,
    
    /// <summary>
    /// 表情消息
    /// </summary>
    Emoji = 7,
    
    /// <summary>
    /// 位置消息
    /// </summary>
    Location = 8,

    /// <summary>
    /// 混合内容消息
    /// </summary>
    Mixed = 9,
    
    /// <summary>
    /// 名片消息
    /// </summary>
    Card = 10
}

/// <summary>
/// 消息队列状态
/// </summary>
public enum MessageQueueStatus
{
    /// <summary>
    /// 等待发送
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在发送
    /// </summary>
    Sending = 1,
    
    /// <summary>
    /// 发送成功
    /// </summary>
    Sent = 2,
    
    /// <summary>
    /// 发送失败
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4,
    
    /// <summary>
    /// 已过期
    /// </summary>
    Expired = 5
}

/// <summary>
/// 消息优先级枚举
/// </summary>
public enum MessagePriority : byte
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 64,
    
    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 128,
    
    /// <summary>
    /// 高优先级
    /// </summary>
    High = 192,
    
    /// <summary>
    /// 紧急优先级
    /// </summary>
    Urgent = 255
}
