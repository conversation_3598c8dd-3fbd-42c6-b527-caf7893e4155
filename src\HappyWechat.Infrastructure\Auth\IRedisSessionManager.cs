using System.Security.Claims;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis会话管理器接口
/// </summary>
public interface IRedisSessionManager
{
    /// <summary>
    /// 创建新会话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="username">用户名</param>
    /// <param name="roles">用户角色</param>
    /// <param name="permissions">用户权限</param>
    /// <returns>会话ID</returns>
    Task<string> CreateSessionAsync(string userId, string username, IEnumerable<string> roles, IEnumerable<string>? permissions = null);

    /// <summary>
    /// 验证会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>会话信息，如果无效则返回null</returns>
    Task<RedisSessionInfo?> ValidateSessionAsync(string sessionId);

    /// <summary>
    /// 续期会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否成功续期</returns>
    Task<bool> RenewSessionAsync(string sessionId);

    /// <summary>
    /// 销毁会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否成功销毁</returns>
    Task<bool> DestroySessionAsync(string sessionId);

    /// <summary>
    /// 销毁用户的所有会话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>销毁的会话数量</returns>
    Task<int> DestroyUserSessionsAsync(string userId);

    /// <summary>
    /// 获取用户的所有活跃会话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>会话信息列表</returns>
    Task<IEnumerable<RedisSessionInfo>> GetUserSessionsAsync(string userId);

    /// <summary>
    /// 清理过期会话
    /// </summary>
    /// <returns>清理的会话数量</returns>
    Task<int> CleanupExpiredSessionsAsync();

    /// <summary>
    /// 获取会话统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<RedisSessionStatistics> GetSessionStatisticsAsync();

    /// <summary>
    /// 检查用户是否已认证
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否已认证</returns>
    Task<bool> IsAuthenticatedAsync(string sessionId);

    /// <summary>
    /// 获取用户权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<string>> GetUserPermissionsAsync(string userId);

    /// <summary>
    /// 更新用户权限缓存
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissions">权限列表</param>
    /// <returns>是否成功更新</returns>
    Task<bool> UpdateUserPermissionsAsync(string userId, IEnumerable<string> permissions);
}

/// <summary>
/// Redis会话信息
/// </summary>
public class RedisSessionInfo
{
    public string SessionId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public IEnumerable<string> Roles { get; set; } = new List<string>();
    public IEnumerable<string> Permissions { get; set; } = new List<string>();
    public DateTime CreatedAt { get; set; }
    public DateTime LastAccessAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }

    /// <summary>
    /// 转换为ClaimsPrincipal
    /// </summary>
    /// <returns>ClaimsPrincipal对象</returns>
    public ClaimsPrincipal ToClaimsPrincipal()
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, UserId),
            new(ClaimTypes.Name, Username),
            new("session_id", SessionId),
            new("created_at", CreatedAt.ToString("O")),
            new("last_access_at", LastAccessAt.ToString("O"))
        };

        // 添加角色Claims
        claims.AddRange(Roles.Select(role => new Claim(ClaimTypes.Role, role)));

        // 添加权限Claims
        claims.AddRange(Permissions.Select(permission => new Claim("permission", permission)));

        var identity = new ClaimsIdentity(claims, "RedisAuthentication");
        return new ClaimsPrincipal(identity);
    }
}

/// <summary>
/// Redis会话统计信息
/// </summary>
public class RedisSessionStatistics
{
    public int TotalActiveSessions { get; set; }
    public int TotalUsers { get; set; }
    public DateTime LastCleanupAt { get; set; }
    public int ExpiredSessionsCleanedUp { get; set; }
    public Dictionary<string, int> SessionsByRole { get; set; } = new();
    public TimeSpan AverageSessionDuration { get; set; }
}
