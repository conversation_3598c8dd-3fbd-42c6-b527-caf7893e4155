using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.DataSync;

/// <summary>
/// 数据同步编排器实现
/// 协调页面数据请求与数据同步队列，复用现有业务逻辑
/// </summary>
public class DataSyncOrchestrator : IDataSyncOrchestrator
{
    private readonly ISimplifiedQueueService _queueService;
    private readonly IWxContactService _wxContactService;
    private readonly IWxGroupService _wxGroupService;
    private readonly ILogger<DataSyncOrchestrator> _logger;

    public DataSyncOrchestrator(
        ISimplifiedQueueService queueService,
        IWxContactService wxContactService,
        IWxGroupService wxGroupService,
        ILogger<DataSyncOrchestrator> logger)
    {
        _queueService = queueService;
        _wxContactService = wxContactService;
        _wxGroupService = wxGroupService;
        _logger = logger;
    }

    /// <summary>
    /// 触发联系人数据同步
    /// </summary>
    public async Task<string> TriggerContactSyncAsync(Guid wxManagerId, bool forceRefresh = false, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 触发联系人数据同步 - WxManagerId: {WxManagerId}, ForceRefresh: {ForceRefresh}", 
                wxManagerId, forceRefresh);

            var syncMessage = new ContactSyncMessage
            {
                WxManagerId = wxManagerId,
                SyncType = forceRefresh ? "force_refresh" : "normal",
                RequestedAt = DateTime.UtcNow,
                Priority = forceRefresh ? 1 : 0
            };

            var messageId = await _queueService.EnqueueAsync(
                wxManagerId, 
                "contact_sync", 
                syncMessage, 
                syncMessage.Priority, 
                3, 
                new Dictionary<string, object> { { "ForceRefresh", forceRefresh } },
                cancellationToken);

            _logger.LogInformation("✅ 联系人同步任务已入队 - MessageId: {MessageId}", messageId);
            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 触发联系人数据同步失败 - WxManagerId: {WxManagerId}", wxManagerId);
            throw;
        }
    }

    /// <summary>
    /// 触发群组数据同步
    /// </summary>
    public async Task<string> TriggerGroupSyncAsync(Guid wxManagerId, bool forceRefresh = false, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 触发群组数据同步 - WxManagerId: {WxManagerId}, ForceRefresh: {ForceRefresh}", 
                wxManagerId, forceRefresh);

            var syncMessage = new GroupSyncMessage
            {
                WxManagerId = wxManagerId,
                SyncType = forceRefresh ? "force_refresh" : "normal",
                RequestedAt = DateTime.UtcNow,
                Priority = forceRefresh ? 1 : 0
            };

            var messageId = await _queueService.EnqueueAsync(
                wxManagerId, 
                "group_sync", 
                syncMessage, 
                syncMessage.Priority, 
                3, 
                new Dictionary<string, object> { { "ForceRefresh", forceRefresh } },
                cancellationToken);

            _logger.LogInformation("✅ 群组同步任务已入队 - MessageId: {MessageId}", messageId);
            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 触发群组数据同步失败 - WxManagerId: {WxManagerId}", wxManagerId);
            throw;
        }
    }

    /// <summary>
    /// 触发联系人详情获取（通过EYun API）
    /// </summary>
    public async Task<bool> TriggerContactDetailsAsync(GetContactDetailsCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 触发联系人详情获取 - WxManagerId: {WxManagerId}", command.WxManagerId);

            // 复用现有的WxContactService业务逻辑
            var result = await _wxContactService.GetContactDetailsAsync(command);

            _logger.LogInformation("✅ 联系人详情获取完成 - WxManagerId: {WxManagerId}, Success: {Success}", 
                command.WxManagerId, result != null);

            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 触发联系人详情获取失败 - WxManagerId: {WxManagerId}", command.WxManagerId);
            return false;
        }
    }

    /// <summary>
    /// 触发群组详情获取（通过EYun API）
    /// </summary>
    public async Task<bool> TriggerGroupDetailsAsync(GetGroupDetailsCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 触发群组详情获取 - WxManagerId: {WxManagerId}", command.WxManagerId);

            // 复用现有的WxGroupService业务逻辑
            var result = await _wxGroupService.GetGroupDetailsAsync(command);

            _logger.LogInformation("✅ 群组详情获取完成 - WxManagerId: {WxManagerId}, Success: {Success}", 
                command.WxManagerId, result != null);

            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 触发群组详情获取失败 - WxManagerId: {WxManagerId}", command.WxManagerId);
            return false;
        }
    }

    /// <summary>
    /// 检查数据同步状态
    /// </summary>
    public async Task<DataSyncStatus> GetSyncStatusAsync(Guid wxManagerId, string dataType, CancellationToken cancellationToken = default)
    {
        try
        {
            var statusKey = $"sync_status:{wxManagerId}:{dataType}";
            var statusJson = await _queueService.GetKeyAsync(statusKey, cancellationToken);

            if (string.IsNullOrEmpty(statusJson))
            {
                return new DataSyncStatus
                {
                    IsInProgress = false,
                    Status = "未开始"
                };
            }

            // 这里可以解析状态JSON并返回详细状态
            return new DataSyncStatus
            {
                IsInProgress = false,
                LastSyncTime = DateTime.UtcNow,
                Status = "已完成"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取同步状态失败 - WxManagerId: {WxManagerId}, DataType: {DataType}", 
                wxManagerId, dataType);
            
            return new DataSyncStatus
            {
                IsInProgress = false,
                Status = "错误",
                ErrorMessage = ex.Message
            };
        }
    }
}
