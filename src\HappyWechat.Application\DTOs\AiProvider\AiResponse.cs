namespace HappyWechat.Application.DTOs.AiProvider;

/// <summary>
/// 统一AI响应模型
/// </summary>
public class AiResponse
{
    /// <summary>
    /// 响应是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 响应内容
    /// </summary>
    public string? Content { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 会话ID
    /// </summary>
    public string? ConversationId { get; set; }
    
    /// <summary>
    /// 消息ID
    /// </summary>
    public string? MessageId { get; set; }
    
    /// <summary>
    /// 使用的令牌数量
    /// </summary>
    public int? TokensUsed { get; set; }
    
    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public long ResponseTimeMs { get; set; }
    
    /// <summary>
    /// 原始响应数据
    /// </summary>
    public object? RawResponse { get; set; }
    
    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static AiResponse Success(string content, string? conversationId = null, string? messageId = null)
    {
        return new AiResponse
        {
            IsSuccess = true,
            Content = content,
            ConversationId = conversationId,
            MessageId = messageId
        };
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    public static AiResponse Failure(string errorMessage, object? rawResponse = null)
    {
        return new AiResponse
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            RawResponse = rawResponse
        };
    }
}