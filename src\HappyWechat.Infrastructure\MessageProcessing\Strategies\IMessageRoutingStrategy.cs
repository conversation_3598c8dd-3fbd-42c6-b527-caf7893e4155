using HappyWechat.Infrastructure.MessageProcessing.Models;

namespace HappyWechat.Infrastructure.MessageProcessing.Strategies;

/// <summary>
/// 消息路由策略接口
/// </summary>
public interface IMessageRoutingStrategy
{
    /// <summary>
    /// 策略名称
    /// </summary>
    string StrategyName { get; }

    /// <summary>
    /// 策略优先级
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// 是否支持该消息类型
    /// </summary>
    bool SupportsMessageType(string messageType);

    /// <summary>
    /// 决定消息目标
    /// </summary>
    Task<MessageTargetDecision> DecideTargetAsync(
        string messageType,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context);

    /// <summary>
    /// 获取发送模式
    /// </summary>
    MessageSendMode GetSendMode(string messageType, Dictionary<string, object> context);

    /// <summary>
    /// 计算发送延迟
    /// </summary>
    int CalculateSendDelay(string messageType, MessagePriority priority);

    /// <summary>
    /// 路由消息
    /// </summary>
    Task<string> RouteMessageAsync(
        string messageType,
        string wxManagerId,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context);
}

/// <summary>
/// 基础消息路由策略
/// </summary>
public abstract class BaseMessageRoutingStrategy : IMessageRoutingStrategy
{
    public abstract string StrategyName { get; }
    public virtual int Priority => 0;

    public abstract bool SupportsMessageType(string messageType);

    public abstract Task<MessageTargetDecision> DecideTargetAsync(
        string messageType,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context);

    public virtual MessageSendMode GetSendMode(string messageType, Dictionary<string, object> context)
    {
        return MessageSendMode.Normal;
    }

    public virtual int CalculateSendDelay(string messageType, MessagePriority priority)
    {
        return priority switch
        {
            MessagePriority.Critical => 0,
            MessagePriority.High => 500,
            MessagePriority.Normal => 1500,
            MessagePriority.Low => 3000,
            _ => 1500
        };
    }

    public virtual async Task<string> RouteMessageAsync(
        string messageType,
        string wxManagerId,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context)
    {
        // 默认实现：生成消息ID并记录日志
        var messageId = Guid.NewGuid().ToString();

        // 这里可以添加实际的路由逻辑
        await Task.CompletedTask;

        return messageId;
    }

    /// <summary>
    /// 检查是否为私聊消息
    /// </summary>
    protected bool IsPrivateMessage(string messageType)
    {
        return messageType.StartsWith("6");
    }

    /// <summary>
    /// 检查是否为群聊消息
    /// </summary>
    protected bool IsGroupMessage(string messageType)
    {
        return messageType.StartsWith("8");
    }

    /// <summary>
    /// 检查是否为文本消息
    /// </summary>
    protected bool IsTextMessage(string messageType)
    {
        return messageType.EndsWith("001");
    }

    /// <summary>
    /// 检查是否为媒体消息
    /// </summary>
    protected bool IsMediaMessage(string messageType)
    {
        return !IsTextMessage(messageType) && (IsPrivateMessage(messageType) || IsGroupMessage(messageType));
    }
}

/// <summary>
/// 私聊消息路由策略
/// </summary>
public class PrivateMessageRoutingStrategy : BaseMessageRoutingStrategy
{
    public override string StrategyName => "PrivateMessageRouting";
    public override int Priority => 10;

    public override bool SupportsMessageType(string messageType)
    {
        return IsPrivateMessage(messageType);
    }

    public override async Task<MessageTargetDecision> DecideTargetAsync(
        string messageType,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context)
    {
        // 私聊消息直接回复给发送者
        return MessageTargetDecision.CreateSend(
            MessageTargetType.Contact,
            fromUser,
            context.GetValueOrDefault("FromUserNickname", fromUser).ToString() ?? fromUser,
            "私聊消息回复");
    }
}

/// <summary>
/// 群聊消息路由策略
/// </summary>
public class GroupMessageRoutingStrategy : BaseMessageRoutingStrategy
{
    public override string StrategyName => "GroupMessageRouting";
    public override int Priority => 10;

    public override bool SupportsMessageType(string messageType)
    {
        return IsGroupMessage(messageType);
    }

    public override async Task<MessageTargetDecision> DecideTargetAsync(
        string messageType,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(fromGroup))
        {
            return MessageTargetDecision.CreateNoSend("群聊消息缺少群组信息");
        }

        // 群聊消息回复到群组
        return MessageTargetDecision.CreateSend(
            MessageTargetType.Group,
            fromGroup,
            context.GetValueOrDefault("FromGroupName", fromGroup).ToString() ?? fromGroup,
            "群聊消息回复");
    }
}

/// <summary>
/// 系统消息路由策略
/// </summary>
public class SystemMessageRoutingStrategy : BaseMessageRoutingStrategy
{
    public override string StrategyName => "SystemMessageRouting";
    public override int Priority => 20;

    public override bool SupportsMessageType(string messageType)
    {
        return messageType is "37" or "30000" or "30001";
    }

    public override async Task<MessageTargetDecision> DecideTargetAsync(
        string messageType,
        string fromUser,
        string? fromGroup,
        string content,
        Dictionary<string, object> context)
    {
        return messageType switch
        {
            "37" => MessageTargetDecision.CreateSend(
                MessageTargetType.System,
                "friend_request",
                "好友请求处理",
                "系统处理好友请求",
                MessageSendMode.Priority),
            "30000" => MessageTargetDecision.CreateNoSend("离线通知不需要回复"),
            "30001" => MessageTargetDecision.CreateNoSend("在线通知不需要回复"),
            _ => MessageTargetDecision.CreateNoSend("未知系统消息类型")
        };
    }
}
