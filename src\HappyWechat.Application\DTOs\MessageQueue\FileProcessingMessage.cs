namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 文件处理消息
/// </summary>
public class FileProcessingMessage
{
    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 微信账号ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// 目标格式（如语音转换为silk）
    /// </summary>
    public string? TargetFormat { get; set; }

    /// <summary>
    /// 处理选项
    /// </summary>
    public Dictionary<string, object>? ProcessingOptions { get; set; }
}

/// <summary>
/// 应用层文件处理消息
/// </summary>
public class AppFileProcessingMessage
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// 输入文件列表
    /// </summary>
    public List<FileProcessingInputFile> InputFiles { get; set; } = new();

    /// <summary>
    /// 处理选项
    /// </summary>
    public FileProcessingOptions? Options { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public byte Priority { get; set; } = 5;

    /// <summary>
    /// 入队时间
    /// </summary>
    public DateTime QueuedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 文件处理输入文件
/// </summary>
public class FileProcessingInputFile
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 原始文件名
    /// </summary>
    public string? OriginalFileName { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
}

/// <summary>
/// 文件处理选项
/// </summary>
public class FileProcessingOptions
{
    /// <summary>
    /// 强制策略
    /// </summary>
    public ProcessingStrategy? ForceStrategy { get; set; }

    /// <summary>
    /// 超时分钟数
    /// </summary>
    public int TimeoutMinutes { get; set; } = 5;

    /// <summary>
    /// 转换为消息处理选项
    /// </summary>
    public MessageProcessOptions? ToMessageProcessOptions()
    {
        return new MessageProcessOptions
        {
            TimeoutMinutes = TimeoutMinutes
        };
    }
}

/// <summary>
/// 消息处理选项
/// </summary>
public class MessageProcessOptions
{
    /// <summary>
    /// 超时分钟数
    /// </summary>
    public int TimeoutMinutes { get; set; } = 5;
}

/// <summary>
/// 处理策略枚举
/// </summary>
public enum ProcessingStrategy
{
    /// <summary>
    /// 直接处理
    /// </summary>
    Direct,

    /// <summary>
    /// 队列处理
    /// </summary>
    Queue,

    /// <summary>
    /// 混合处理
    /// </summary>
    Hybrid
}