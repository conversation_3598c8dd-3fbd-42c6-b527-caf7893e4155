using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Application.DTOs.Responses;

namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 联系人同步队列消息
/// </summary>
public class ContactSyncQueueMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 联系人类型列表
    /// </summary>
    public List<WxContactListType> ListTypes { get; set; } = new();
    
    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 20;
    
    /// <summary>
    /// 最小延时（毫秒）
    /// </summary>
    public int MinDelayMs { get; set; } = 300;
    
    /// <summary>
    /// 最大延时（毫秒）
    /// </summary>
    public int MaxDelayMs { get; set; } = 1500;
    
    /// <summary>
    /// 消息创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;
    
    /// <summary>
    /// 优先级（数字越小优先级越高）
    /// </summary>
    public int Priority { get; set; } = 5;
    
    /// <summary>
    /// 同步类型
    /// </summary>
    public SyncType SyncType { get; set; } = SyncType.Full;
    
    /// <summary>
    /// 上次同步时间
    /// </summary>
    public DateTime? LastSyncTime { get; set; }
}

/// <summary>
/// 联系人同步进度消息
/// </summary>
public class ContactSyncProgressMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 同步状态
    /// </summary>
    public SyncStatus Status { get; set; }
    
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 已处理数量
    /// </summary>
    public int ProcessedCount { get; set; }
    
    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 当前处理的联系人
    /// </summary>
    public string? CurrentContact { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 进度百分比
    /// </summary>
    public int ProgressPercentage => TotalCount > 0 ? (ProcessedCount * 100) / TotalCount : 0;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
