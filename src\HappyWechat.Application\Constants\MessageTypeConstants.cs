namespace HappyWechat.Application.Constants;

/// <summary>
/// 消息类型常量定义
/// 基于EYun API和流程图中的消息类型
/// </summary>
public static class MessageTypeConstants
{
    #region 私聊消息类型
    
    /// <summary>
    /// 私聊文本消息
    /// </summary>
    public const string PRIVATE_TEXT = "60001";
    
    /// <summary>
    /// 私聊图片消息
    /// </summary>
    public const string PRIVATE_IMAGE = "60002";
    
    /// <summary>
    /// 私聊语音消息
    /// </summary>
    public const string PRIVATE_VOICE = "60004";
    
    /// <summary>
    /// 🚀 新架构：私聊文件发送完成消息（已彻底移除60008）
    /// </summary>
    public const string PRIVATE_FILE = "60009";
    
    /// <summary>
    /// 私聊视频消息
    /// </summary>
    public const string PRIVATE_VIDEO = "60003";
    
    /// <summary>
    /// 私聊表情消息
    /// </summary>
    public const string PRIVATE_EMOJI = "60005";
    
    /// <summary>
    /// 私聊链接消息
    /// </summary>
    public const string PRIVATE_LINK = "60006";
    
    /// <summary>
    /// 私聊位置消息
    /// </summary>
    public const string PRIVATE_LOCATION = "60007";
    
    #endregion
    
    #region 群聊消息类型
    
    /// <summary>
    /// 群聊文本消息
    /// </summary>
    public const string GROUP_TEXT = "80001";
    
    /// <summary>
    /// 群聊图片消息
    /// </summary>
    public const string GROUP_IMAGE = "80002";
    
    /// <summary>
    /// 群聊语音消息
    /// </summary>
    public const string GROUP_VOICE = "80004";
    
    /// <summary>
    /// 🚀 新架构：群聊文件发送完成消息（已彻底移除80008）
    /// </summary>
    public const string GROUP_FILE = "80009";
    
    /// <summary>
    /// 群聊视频消息
    /// </summary>
    public const string GROUP_VIDEO = "80003";
    
    /// <summary>
    /// 群聊表情消息
    /// </summary>
    public const string GROUP_EMOJI = "80005";
    
    /// <summary>
    /// 群聊链接消息
    /// </summary>
    public const string GROUP_LINK = "80006";
    
    /// <summary>
    /// 群聊位置消息
    /// </summary>
    public const string GROUP_LOCATION = "80007";
    
    #endregion
    
    #region 系统消息类型
    
    /// <summary>
    /// 离线通知
    /// </summary>
    public const string OFFLINE_NOTIFICATION = "30000";
    
    /// <summary>
    /// 好友请求
    /// </summary>
    public const string FRIEND_REQUEST = "10001";
    
    /// <summary>
    /// 群邀请
    /// </summary>
    public const string GROUP_INVITE = "20001";
    
    /// <summary>
    /// 系统通知
    /// </summary>
    public const string SYSTEM_NOTIFICATION = "90001";
    
    #endregion
    
    #region 消息类型分组
    
    /// <summary>
    /// 所有私聊消息类型
    /// </summary>
    public static readonly string[] PRIVATE_MESSAGE_TYPES = 
    {
        PRIVATE_TEXT, PRIVATE_IMAGE, PRIVATE_VOICE, PRIVATE_FILE,
        PRIVATE_VIDEO, PRIVATE_EMOJI, PRIVATE_LINK, PRIVATE_LOCATION
    };
    
    /// <summary>
    /// 所有群聊消息类型
    /// </summary>
    public static readonly string[] GROUP_MESSAGE_TYPES = 
    {
        GROUP_TEXT, GROUP_IMAGE, GROUP_VOICE, GROUP_FILE,
        GROUP_VIDEO, GROUP_EMOJI, GROUP_LINK, GROUP_LOCATION
    };
    
    /// <summary>
    /// 🔧 修复：需要文件下载的消息类型（移除视频类型，不支持处理）
    /// </summary>
    public static readonly string[] MEDIA_MESSAGE_TYPES = 
    {
        PRIVATE_IMAGE, PRIVATE_VOICE, PRIVATE_FILE,
        GROUP_IMAGE, GROUP_VOICE, GROUP_FILE
    };
    
    /// <summary>
    /// 🔧 新增：高优先级消息类型
    /// </summary>
    public static readonly string[] HIGH_PRIORITY_MESSAGE_TYPES = 
    {
        PRIVATE_TEXT, GROUP_TEXT, OFFLINE_NOTIFICATION, 
        FRIEND_REQUEST, GROUP_INVITE, SYSTEM_NOTIFICATION
    };
    
    /// <summary>
    /// 🔧 修复：低优先级媒体消息类型（仅包含文件消息，不包含视频）
    /// </summary>
    public static readonly string[] LOW_PRIORITY_MEDIA_MESSAGE_TYPES = 
    {
        PRIVATE_FILE, GROUP_FILE
    };
    
    /// <summary>
    /// 系统消息类型
    /// </summary>
    public static readonly string[] SYSTEM_MESSAGE_TYPES = 
    {
        OFFLINE_NOTIFICATION, FRIEND_REQUEST, GROUP_INVITE, SYSTEM_NOTIFICATION
    };
    
    #endregion
    
    #region 辅助方法
    
    /// <summary>
    /// 判断是否为私聊消息
    /// </summary>
    public static bool IsPrivateMessage(string messageType)
    {
        return PRIVATE_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否为群聊消息
    /// </summary>
    public static bool IsGroupMessage(string messageType)
    {
        return GROUP_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否需要文件下载
    /// </summary>
    public static bool RequiresMediaDownload(string messageType)
    {
        return MEDIA_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否为系统消息
    /// </summary>
    public static bool IsSystemMessage(string messageType)
    {
        return SYSTEM_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 获取消息类型的描述
    /// </summary>
    public static string GetMessageTypeDescription(string messageType)
    {
        return messageType switch
        {
            PRIVATE_TEXT => "私聊文本",
            PRIVATE_IMAGE => "私聊图片",
            PRIVATE_VOICE => "私聊语音",
            PRIVATE_FILE => "私聊文件",
            PRIVATE_VIDEO => "私聊视频",
            PRIVATE_EMOJI => "私聊表情",
            PRIVATE_LINK => "私聊链接",
            PRIVATE_LOCATION => "私聊位置",
            
            GROUP_TEXT => "群聊文本",
            GROUP_IMAGE => "群聊图片",
            GROUP_VOICE => "群聊语音",
            GROUP_FILE => "群聊文件",
            GROUP_VIDEO => "群聊视频",
            GROUP_EMOJI => "群聊表情",
            GROUP_LINK => "群聊链接",
            GROUP_LOCATION => "群聊位置",
            
            OFFLINE_NOTIFICATION => "离线通知",
            FRIEND_REQUEST => "好友请求",
            GROUP_INVITE => "群邀请",
            SYSTEM_NOTIFICATION => "系统通知",
            
            _ => "未知消息类型"
        };
    }
    
    /// <summary>
    /// 🔧 修复：获取消息类型的队列优先级
    /// 系统消息：最高优先级 (10) - 30000, 30001
    /// 文本消息：高优先级 (5) - 60001, 80001
    /// 语音消息：中高优先级 (4) - 60004, 80004
    /// 图片消息：中优先级 (3) - 60002, 80002
    /// 文件消息：低优先级 (2) - 60009, 80009
    /// 不处理视频类型消息
    /// </summary>
    public static int GetMessagePriority(string messageType)
    {
        return messageType switch
        {
            // 系统消息 - 最高优先级，紧急处理系统通知
            OFFLINE_NOTIFICATION or FRIEND_REQUEST or GROUP_INVITE or SYSTEM_NOTIFICATION => 10,
            
            // 文本消息 - 高优先级，及时响应用户对话
            PRIVATE_TEXT or GROUP_TEXT => 5,
            
            // 语音消息 - 中高优先级，高于图片但低于文本
            PRIVATE_VOICE or GROUP_VOICE => 4,
            
            // 图片消息 - 中等优先级，高于文件但低于语音
            PRIVATE_IMAGE or GROUP_IMAGE => 3,
            
            // 文件消息 - 低优先级，处理耗时且不紧急
            PRIVATE_FILE or GROUP_FILE => 2,
            
            // 表情和链接 - 类似文本但稍低优先级
            PRIVATE_EMOJI or GROUP_EMOJI or PRIVATE_LINK or GROUP_LINK => 3,
            
            // 位置消息 - 中等优先级
            PRIVATE_LOCATION or GROUP_LOCATION => 3,
            
            // 未知消息类型 - 默认优先级
            _ => 1
        };
    }
    
    /// <summary>
    /// 🔧 修复：判断是否为高优先级消息（系统消息和文本消息）
    /// </summary>
    public static bool IsHighPriorityMessage(string messageType)
    {
        return GetMessagePriority(messageType) >= 5;
    }
    
    /// <summary>
    /// 🔧 修复：判断是否为低优先级媒体消息（仅文件消息）
    /// </summary>
    public static bool IsLowPriorityMediaMessage(string messageType)
    {
        return RequiresMediaDownload(messageType) && GetMessagePriority(messageType) == 2;
    }
    
    /// <summary>
    /// 🔧 新增：判断是否为最高优先级系统消息
    /// </summary>
    public static bool IsSystemPriorityMessage(string messageType)
    {
        return GetMessagePriority(messageType) == 10;
    }
    
    #endregion
}
