namespace HappyWechat.Application.DTOs.AiProvider;

/// <summary>
/// 统一AI请求模型
/// </summary>
public class AiRequest
{
    /// <summary>
    /// 消息内容
    /// </summary>
    public required string Message { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }
    
    /// <summary>
    /// 会话ID
    /// </summary>
    public string? ConversationId { get; set; }
    
    /// <summary>
    /// 系统提示词（可选，主要用于ChatGPT）
    /// </summary>
    public string? SystemPrompt { get; set; }
    
    /// <summary>
    /// 温度参数（可选，主要用于ChatGPT）
    /// </summary>
    public double? Temperature { get; set; }
    
    /// <summary>
    /// 附加参数
    /// </summary>
    public Dictionary<string, object>? AdditionalParameters { get; set; }
    
    /// <summary>
    /// 文件列表（用于多模态输入）
    /// </summary>
    public List<AiFileInput>? Files { get; set; }
}

/// <summary>
/// AI文件输入
/// </summary>
public class AiFileInput
{
    /// <summary>
    /// 文件URL或路径
    /// </summary>
    public required string FileUrl { get; set; }
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public required string FileType { get; set; }
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string? FileName { get; set; }
}