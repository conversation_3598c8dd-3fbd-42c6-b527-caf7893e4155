@page "/auth-test"
@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using HappyWechat.Infrastructure.Auth
@inject AuthenticationStateProvider AuthStateProvider
@inject IJSRuntime JSRuntime
@inject ILogger<AuthTest> Logger
@inject IServiceProvider ServiceProvider

<h3>认证状态测试页面</h3>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>Blazor 认证状态</h4>
    <AuthorizeView>
        <Authorized>
            <p style="color: green;">✅ 已认证</p>
            <p><strong>用户名:</strong> @context.User.Identity?.Name</p>
            <p><strong>用户ID:</strong> @context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value</p>
            <p><strong>角色:</strong> 
                @string.Join(", ", context.User.FindAll(ClaimTypes.Role).Select(c => c.Value))
            </p>
            <p><strong>会话ID:</strong> @context.User.FindFirst("session_id")?.Value</p>
        </Authorized>
        <NotAuthorized>
            <p style="color: red;">❌ 未认证</p>
        </NotAuthorized>
    </AuthorizeView>
</div>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>JavaScript 认证状态</h4>
    <div id="js-auth-status">加载中...</div>
    <button @onclick="CheckJavaScriptAuth">刷新 JavaScript 状态</button>
</div>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>SignalR 连接状态</h4>
    <div id="signalr-status">加载中...</div>
    <button @onclick="CheckSignalRStatus">检查 SignalR 状态</button>
</div>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>认证管理器状态</h4>
    <div id="auth-managers-status">加载中...</div>
    <button @onclick="CheckAuthManagers">检查认证管理器</button>
</div>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>SessionId获取服务测试</h4>
    <div id="sessionid-service-status">加载中...</div>
    <button @onclick="TestSessionIdService">测试SessionId获取服务</button>
    <button @onclick="TestSessionIdStrategies">测试各个获取策略</button>
    <button @onclick="GetSessionIdStats">获取统计信息</button>
</div>

<div style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
    <h4>测试操作</h4>
    <button @onclick="TestAuthStateRefresh" style="margin: 5px;">刷新认证状态</button>
    <button @onclick="TestSignalRReconnect" style="margin: 5px;">重连 SignalR</button>
    <button @onclick="ClearAllAuth" style="margin: 5px; background: red; color: white;">清除所有认证</button>
</div>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await CheckJavaScriptAuth();
            await CheckSignalRStatus();
            await CheckAuthManagers();
            await TestSessionIdService();
        }
    }

    private async Task CheckJavaScriptAuth()
    {
        try
        {
            var result = await JSRuntime.InvokeAsync<string>("eval", @"
                (async function() {
                    const results = [];
                    
                    // 检查统一认证状态管理器
                    if (window.authStateManager) {
                        const sessionId = await window.authStateManager.getSessionId();
                        const isAuth = window.authStateManager.isAuthenticated();
                        const stats = window.authStateManager.getStats();
                        results.push('✅ 统一认证状态管理器: ' + (sessionId ? '已认证 (' + sessionId.substring(0, 8) + '...)' : '未认证'));
                        results.push('   - 状态: ' + JSON.stringify(stats));
                    } else {
                        results.push('❌ 统一认证状态管理器: 未加载');
                    }
                    
                    // 检查传统管理器
                    if (window.unifiedSessionManager) {
                        const sessionId = await window.unifiedSessionManager.getSessionId();
                        results.push('📋 统一会话管理器: ' + (sessionId ? '已认证 (' + sessionId.substring(0, 8) + '...)' : '未认证'));
                    }
                    
                    if (window.redisAuthManager) {
                        const sessionId = window.redisAuthManager.getSessionId();
                        results.push('📋 Redis认证管理器: ' + (sessionId ? '已认证 (' + sessionId.substring(0, 8) + '...)' : '未认证'));
                    }
                    
                    return results.join('<br>');
                })()
            ");

            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('js-auth-status').innerHTML = `{result}`;");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查JavaScript认证状态失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('js-auth-status').innerHTML = '❌ 检查失败: {ex.Message}';");
        }
    }

    private async Task CheckSignalRStatus()
    {
        try
        {
            var result = await JSRuntime.InvokeAsync<string>("eval", @"
                (function() {
                    const results = [];
                    
                    // 检查SignalR认证提供者
                    if (window.signalRAuthProvider) {
                        const stats = window.signalRAuthProvider.getConnectionStats();
                        results.push('✅ SignalR认证提供者: 已加载');
                        results.push('   - 连接数: ' + stats.totalConnections);
                        stats.connections.forEach(conn => {
                            results.push('   - ' + conn.hubUrl + ': ' + conn.state);
                        });
                    } else {
                        results.push('❌ SignalR认证提供者: 未加载');
                    }
                    
                    // 检查ContactSync连接
                    if (window.contactSyncClient) {
                        results.push('📡 ContactSync: ' + (window.contactSyncClient.isConnected ? '已连接' : '未连接'));
                    }
                    
                    return results.join('<br>');
                })()
            ");

            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('signalr-status').innerHTML = `{result}`;");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查SignalR状态失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('signalr-status').innerHTML = '❌ 检查失败: {ex.Message}';");
        }
    }

    private async Task CheckAuthManagers()
    {
        try
        {
            var result = await JSRuntime.InvokeAsync<string>("eval", @"
                (function() {
                    const results = [];
                    
                    // 检查事件总线
                    if (window.authEventBus) {
                        const stats = window.authEventBus.getStats();
                        results.push('✅ 认证事件总线: 已加载');
                        results.push('   - 事件类型数: ' + stats.totalEventTypes);
                        results.push('   - 总订阅数: ' + stats.totalSubscriptions);
                    } else {
                        results.push('❌ 认证事件总线: 未加载');
                    }
                    
                    // 检查各种管理器的加载状态
                    const managers = [
                        'authStateManager',
                        'signalRAuthProvider', 
                        'unifiedSessionManager',
                        'redisAuthManager',
                        'blazorAuthHelper'
                    ];
                    
                    managers.forEach(manager => {
                        if (window[manager]) {
                            results.push('✅ ' + manager + ': 已加载');
                        } else {
                            results.push('❌ ' + manager + ': 未加载');
                        }
                    });
                    
                    return results.join('<br>');
                })()
            ");

            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('auth-managers-status').innerHTML = `{result}`;");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查认证管理器失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('auth-managers-status').innerHTML = '❌ 检查失败: {ex.Message}';");
        }
    }

    private async Task TestAuthStateRefresh()
    {
        try
        {
            // 刷新Blazor认证状态
            if (AuthStateProvider is HappyWechat.Infrastructure.Auth.IUnifiedAuthenticationStateProvider unifiedProvider)
            {
                await unifiedProvider.RefreshAuthenticationStateAsync();
            }

            // 刷新JavaScript认证状态
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (window.authStateManager) {
                    window.authStateManager.restoreAuthenticationState();
                }
            ");

            await CheckJavaScriptAuth();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "刷新认证状态失败");
        }
    }

    private async Task TestSignalRReconnect()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (window.contactSyncClient && window.contactSyncClient.reconnect) {
                    window.contactSyncClient.reconnect();
                }
            ");

            await Task.Delay(2000); // 等待重连
            await CheckSignalRStatus();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "重连SignalR失败");
        }
    }

    private async Task TestSessionIdService()
    {
        try
        {
            var sessionIdService = ServiceProvider.GetService<ISessionIdRetrievalService>();
            if (sessionIdService == null)
            {
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('sessionid-service-status').innerHTML = '❌ SessionId获取服务未注册';");
                return;
            }

            var sessionId = await sessionIdService.GetSessionIdAsync();
            var result = sessionId != null
                ? $"✅ SessionId获取服务正常: {sessionId.Substring(0, Math.Min(8, sessionId.Length))}..."
                : "⚠️ SessionId获取服务返回null";

            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '{result}';");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "测试SessionId获取服务失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '❌ 测试失败: {ex.Message}';");
        }
    }

    private async Task TestSessionIdStrategies()
    {
        try
        {
            var sessionIdService = ServiceProvider.GetService<ISessionIdRetrievalService>();
            if (sessionIdService == null)
            {
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('sessionid-service-status').innerHTML = '❌ SessionId获取服务未注册';");
                return;
            }

            var results = new List<string>();
            var strategies = Enum.GetValues<SessionIdRetrievalStrategyType>();

            foreach (var strategy in strategies)
            {
                try
                {
                    var sessionId = await sessionIdService.GetSessionIdFromStrategyAsync(strategy);
                    var result = sessionId != null
                        ? $"✅ {strategy}: {sessionId.Substring(0, Math.Min(8, sessionId.Length))}..."
                        : $"⚠️ {strategy}: null";
                    results.Add(result);
                }
                catch (Exception ex)
                {
                    results.Add($"❌ {strategy}: {ex.Message}");
                }
            }

            var resultHtml = string.Join("<br>", results);
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '{resultHtml}';");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "测试SessionId获取策略失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '❌ 测试失败: {ex.Message}';");
        }
    }

    private async Task GetSessionIdStats()
    {
        try
        {
            var sessionIdService = ServiceProvider.GetService<ISessionIdRetrievalService>();
            if (sessionIdService == null)
            {
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('sessionid-service-status').innerHTML = '❌ SessionId获取服务未注册';");
                return;
            }

            var stats = await sessionIdService.GetRetrievalStatsAsync();
            var result = $@"
                📊 统计信息:<br>
                - 总获取次数: {stats.TotalRetrievals}<br>
                - 成功次数: {stats.SuccessfulRetrievals}<br>
                - 失败次数: {stats.FailedRetrievals}<br>
                - 成功率: {stats.SuccessRate:P}<br>
                - 最后成功策略: {stats.LastSuccessfulStrategy}<br>
                - 最后获取时间: {stats.LastRetrievalTime}
            ";

            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '{result}';");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取SessionId统计信息失败");
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('sessionid-service-status').innerHTML = '❌ 获取统计失败: {ex.Message}';");
        }
    }

    private async Task ClearAllAuth()
    {
        try
        {
            // 清除JavaScript认证状态
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (window.authStateManager) {
                    window.authStateManager.clearAuthenticationState();
                }
                if (window.unifiedSessionManager) {
                    window.unifiedSessionManager.clearSessionId();
                }
                if (window.redisAuthManager) {
                    window.redisAuthManager.logout();
                }
            ");

            // 清除Blazor认证状态
            if (AuthStateProvider is HappyWechat.Infrastructure.Auth.IUnifiedAuthenticationStateProvider unifiedProvider)
            {
                await unifiedProvider.MarkUserAsLoggedOutAsync();
            }

            await CheckJavaScriptAuth();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "清除认证状态失败");
        }
    }
}
