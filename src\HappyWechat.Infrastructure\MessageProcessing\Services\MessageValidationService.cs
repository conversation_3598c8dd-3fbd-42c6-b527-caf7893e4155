using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Commons;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息验证服务 - 负责消息前置验证、敏感词检测和内容过滤
/// </summary>
public class MessageValidationService : IMessageValidationService
{
    private readonly ILogger<MessageValidationService> _logger;
    private readonly ApplicationDbContext _dbContext;
    private readonly ISensitiveWordDetectionService _sensitiveWordService;
    private readonly ISystemConfigManager _systemConfigManager;

    public MessageValidationService(
        ILogger<MessageValidationService> logger,
        ApplicationDbContext dbContext,
        ISensitiveWordDetectionService sensitiveWordService,
        ISystemConfigManager systemConfigManager)
    {
        _logger = logger;
        _dbContext = dbContext;
        _sensitiveWordService = sensitiveWordService;
        _systemConfigManager = systemConfigManager;
    }

    /// <summary>
    /// 执行消息前置验证检查
    /// </summary>
    public async Task<MessageValidationResult> PerformPreProcessingChecksAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new MessageValidationResult
        {
            ProcessingId = processingId,
            Success = true,
            ProcessingDetails = $"ProcessingId: {processingId}"
        };

        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始前置验证检查", processingId);

            // 1. 基础数据验证
            var basicValidation = await ValidateBasicMessageDataAsync(callbackMessage, processingId);
            if (!basicValidation.IsValid)
            {
                result.Success = false;
                result.ErrorMessage = basicValidation.ErrorMessage;
                result.ProcessingDetails = basicValidation.Details;
                return result;
            }

            // 2. 微信管理器验证
            var managerValidation = await ValidateWxManagerAsync(callbackMessage, processingId);
            if (!managerValidation.IsValid)
            {
                result.Success = false;
                result.ErrorMessage = managerValidation.ErrorMessage;
                result.ProcessingDetails = managerValidation.Details;
                return result;
            }

            // 3. 敏感词检测
            var sensitiveWordCheck = await CheckSensitiveWordsAsync(callbackMessage, processingId);
            if (sensitiveWordCheck.IsBlocked)
            {
                result.Success = true; // 验证通过，但被敏感词阻拦
                result.BlockedBySensitiveWords = true;
                result.ProcessingDetails = sensitiveWordCheck.Details;
                return result;
            }

            // 4. 内容安全性检查
            var securityCheck = await PerformSecurityChecksAsync(callbackMessage, processingId);
            if (!securityCheck.IsValid)
            {
                result.Success = false;
                result.ErrorMessage = securityCheck.ErrorMessage;
                result.ProcessingDetails = securityCheck.Details;
                return result;
            }

            _logger.LogDebug("[{ProcessingId}] 前置验证检查通过", processingId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 前置验证检查异常", processingId);
            result.Success = false;
            result.ErrorMessage = $"验证检查异常: {ex.Message}";
            result.ProcessingDetails = $"ProcessingId: {processingId}, Exception: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 验证基础消息数据
    /// </summary>
    private async Task<ValidationResult> ValidateBasicMessageDataAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 验证消息类型
            if (callbackMessage.Data.MsgType == null || callbackMessage.Data.MsgType == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "消息类型不能为空";
                result.Details = $"MsgType: {callbackMessage.Data.MsgType}";
                return result;
            }

            // 验证消息数据
            if (callbackMessage.Data == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "消息数据不能为空";
                result.Details = "WxCallbackMessageDto.Data is null";
                return result;
            }

            // 验证微信ID
            if (string.IsNullOrWhiteSpace(callbackMessage.Data.WId))
            {
                result.IsValid = false;
                result.ErrorMessage = "微信ID不能为空";
                result.Details = $"WId: '{callbackMessage.Data.WId}'";
                return result;
            }

            // 验证发送者信息
            if (string.IsNullOrWhiteSpace(callbackMessage.Data.FromUser))
            {
                result.IsValid = false;
                result.ErrorMessage = "发送者信息不能为空";
                result.Details = $"FromUser: '{callbackMessage.Data.FromUser}'";
                return result;
            }

            // 验证消息内容（文本消息）
            if (callbackMessage.Data.MsgType == 1 && string.IsNullOrWhiteSpace(callbackMessage.Data.Content))
            {
                result.IsValid = false;
                result.ErrorMessage = "文本消息内容不能为空";
                result.Details = $"Content: '{callbackMessage.Data.Content}'";
                return result;
            }

            _logger.LogDebug("[{ProcessingId}] 基础数据验证通过", processingId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 基础数据验证异常", processingId);
            result.IsValid = false;
            result.ErrorMessage = $"基础数据验证异常: {ex.Message}";
            result.Details = $"ProcessingId: {processingId}, Exception: {ex.Message}";
            return result;
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 验证微信管理器
    /// </summary>
    private async Task<ValidationResult> ValidateWxManagerAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 查找微信管理器
            var wxManager = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.WId == callbackMessage.Data!.WId);

            if (wxManager == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "找不到对应的微信管理器";
                result.Details = $"WId: {callbackMessage.Data!.WId}";
                return result;
            }

            // 检查管理器状态
            if (wxManager.WxStatus != WxStatus.AlreadyLogIn)
            {
                result.IsValid = false;
                result.ErrorMessage = $"微信管理器状态不正常: {wxManager.WxStatus}";
                result.Details = $"WxManagerId: {wxManager.Id}, Status: {wxManager.WxStatus}";
                return result;
            }

            _logger.LogDebug("[{ProcessingId}] 微信管理器验证通过 - WxManagerId: {WxManagerId}", 
                processingId, wxManager.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 微信管理器验证异常", processingId);
            result.IsValid = false;
            result.ErrorMessage = $"微信管理器验证异常: {ex.Message}";
            result.Details = $"ProcessingId: {processingId}, Exception: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 敏感词检测
    /// </summary>
    private async Task<SensitiveWordCheckResult> CheckSensitiveWordsAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new SensitiveWordCheckResult { IsBlocked = false };

        try
        {
            // 只对文本消息进行敏感词检测
            if (callbackMessage.Data?.MsgType != 1 || string.IsNullOrWhiteSpace(callbackMessage.Data?.Content))
            {
                return result;
            }

            var content = callbackMessage.Data.Content;
            var detectionResult = await _sensitiveWordService.DetectAsync(content);
            var sensitiveWords = detectionResult.DetectedWords;

            if (sensitiveWords.Any())
            {
                result.IsBlocked = true;
                result.DetectedWords = sensitiveWords;
                result.Details = $"检测到敏感词: {string.Join(", ", sensitiveWords)}, 原文: {content}";

                _logger.LogWarning("[{ProcessingId}] 检测到敏感词 - 敏感词: {SensitiveWords}, 发送者: {FromUser}", 
                    processingId, string.Join(", ", sensitiveWords), callbackMessage.Data.FromUser);
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] 敏感词检测通过", processingId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 敏感词检测异常", processingId);
            // 异常情况下不阻拦消息，记录日志
            result.IsBlocked = false;
            result.Details = $"敏感词检测异常: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 内容安全性检查
    /// </summary>
    private async Task<ValidationResult> PerformSecurityChecksAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 1. 检查消息长度
            if (callbackMessage.Data?.MsgType == 1 && callbackMessage.Data?.Content != null)
            {
                var contentLength = callbackMessage.Data.Content.Length;
                if (contentLength > 10000) // 10KB限制
                {
                    result.IsValid = false;
                    result.ErrorMessage = "消息内容过长";
                    result.Details = $"Content length: {contentLength}";
                    return result;
                }
            }

            // 2. 检查恶意URL
            if (callbackMessage.Data?.MsgType == 1 && callbackMessage.Data?.Content != null)
            {
                var maliciousUrlCheck = await CheckMaliciousUrlsAsync(callbackMessage.Data.Content, processingId);
                if (!maliciousUrlCheck.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = maliciousUrlCheck.ErrorMessage;
                    result.Details = maliciousUrlCheck.Details;
                    return result;
                }
            }

            // 3. 检查频率限制
            var rateLimitCheck = await CheckRateLimitAsync(callbackMessage, processingId);
            if (!rateLimitCheck.IsValid)
            {
                result.IsValid = false;
                result.ErrorMessage = rateLimitCheck.ErrorMessage;
                result.Details = rateLimitCheck.Details;
                return result;
            }

            _logger.LogDebug("[{ProcessingId}] 安全性检查通过", processingId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 安全性检查异常", processingId);
            result.IsValid = false;
            result.ErrorMessage = $"安全性检查异常: {ex.Message}";
            result.Details = $"ProcessingId: {processingId}, Exception: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 检查恶意URL
    /// </summary>
    private async Task<ValidationResult> CheckMaliciousUrlsAsync(string content, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 简单的URL检测
            var urlPattern = @"https?://[^\s/$.?#].[^\s]*";
            var urls = Regex.Matches(content, urlPattern, RegexOptions.IgnoreCase)
                .Cast<Match>()
                .Select(m => m.Value)
                .ToList();

            if (urls.Any())
            {
                // 检查是否包含已知的恶意域名
                var maliciousDomains = await GetMaliciousDomainsAsync();
                var maliciousUrls = urls.Where(url => maliciousDomains.Any(domain => url.Contains(domain))).ToList();

                if (maliciousUrls.Any())
                {
                    result.IsValid = false;
                    result.ErrorMessage = "检测到恶意URL";
                    result.Details = $"Malicious URLs: {string.Join(", ", maliciousUrls)}";
                    return result;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 恶意URL检查异常", processingId);
            // 异常情况下不阻拦消息
            result.IsValid = true;
            result.Details = $"恶意URL检查异常: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 检查频率限制
    /// </summary>
    private async Task<ValidationResult> CheckRateLimitAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 获取用户最近的消息发送记录
            var recentMessageCount = await _dbContext.MessageProcessingLogs
                .CountAsync(m => m.EntityType == "Contact" && 
                                m.CreatedAt > DateTime.UtcNow.AddMinutes(-1));

            // 每分钟最多20条消息
            if (recentMessageCount > 20)
            {
                result.IsValid = false;
                result.ErrorMessage = "消息发送频率过高";
                result.Details = $"Recent message count: {recentMessageCount}";
                return result;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 频率限制检查异常", processingId);
            // 异常情况下不阻拦消息
            result.IsValid = true;
            result.Details = $"频率限制检查异常: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 获取恶意域名列表
    /// </summary>
    private async Task<List<string>> GetMaliciousDomainsAsync()
    {
        try
        {
            // 这里可以从配置文件或数据库获取恶意域名列表
            // 暂时返回一个示例列表
            return new List<string>
            {
                "malicious-site.com",
                "phishing-site.net",
                "spam-domain.org"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取恶意域名列表失败");
            return new List<string>();
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 验证群组消息权限
    /// </summary>
    public async Task<ValidationResult> ValidateGroupMessagePermissionsAsync(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, string processingId)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // 检查是否为群组消息
            if (callbackMessage.Data?.MsgType != 1 || string.IsNullOrWhiteSpace(callbackMessage.Data?.FromGroup))
            {
                return result;
            }

            // 检查群组是否存在
            var groupEntity = await _dbContext.WxGroupEntities
                .FirstOrDefaultAsync(g => g.ChatRoomId == callbackMessage.Data.FromGroup);

            if (groupEntity == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "群组不存在";
                result.Details = $"GroupId: {callbackMessage.Data.FromGroup}";
                return result;
            }

            // 检查用户是否在群组中
            var isMember = await _dbContext.WxGroupMemberEntities
                .AnyAsync(m => m.ChatRoomId == callbackMessage.Data.FromGroup && 
                              m.UserName == callbackMessage.Data.FromUser);

            if (!isMember)
            {
                result.IsValid = false;
                result.ErrorMessage = "用户不在群组中";
                result.Details = $"GroupId: {callbackMessage.Data.FromGroup}, FromUser: {callbackMessage.Data.FromUser}";
                return result;
            }

            _logger.LogDebug("[{ProcessingId}] 群组消息权限验证通过", processingId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 群组消息权限验证异常", processingId);
            result.IsValid = false;
            result.ErrorMessage = $"群组消息权限验证异常: {ex.Message}";
            result.Details = $"ProcessingId: {processingId}, Exception: {ex.Message}";
            return result;
        }
    }
}

/// <summary>
/// 敏感词检查结果
/// </summary>
public class SensitiveWordCheckResult
{
    /// <summary>
    /// 是否被阻拦
    /// </summary>
    public bool IsBlocked { get; set; }

    /// <summary>
    /// 检测到的敏感词
    /// </summary>
    public List<string> DetectedWords { get; set; } = new();

    /// <summary>
    /// 详细信息
    /// </summary>
    public string? Details { get; set; }
}