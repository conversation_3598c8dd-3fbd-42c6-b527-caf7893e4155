using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Infrastructure.Redis;

namespace HappyWechat.Infrastructure.Testing;

/// <summary>
/// 联系人类型查询测试类 - 验证多ContactType支持
/// </summary>
public static class ContactTypeQueryTest
{
    /// <summary>
    /// 测试GetContactOrGroupListQuery的向后兼容性
    /// </summary>
    public static void TestQueryBackwardCompatibility()
    {
        Console.WriteLine("=== 测试GetContactOrGroupListQuery向后兼容性 ===");
        
        // 测试1: 使用旧的ContactType属性
        var query1 = new GetContactOrGroupListQuery
        {
            WxManagerId = Guid.NewGuid(),
            ContactType = WxContactType.Contact
        };
        
        Console.WriteLine($"旧方式设置ContactType: {query1.ContactType}");
        Console.WriteLine($"ContactTypes列表: [{string.Join(", ", query1.ContactTypes)}]");
        Console.WriteLine($"向后兼容性: {(query1.ContactTypes.Count == 1 && query1.ContactTypes[0] == WxContactType.Contact ? "✓ 通过" : "✗ 失败")}");
        
        // 测试2: 使用新的ContactTypes属性
        var query2 = new GetContactOrGroupListQuery
        {
            WxManagerId = Guid.NewGuid(),
            ContactTypes = new List<WxContactType> { WxContactType.Contact, WxContactType.Enterprise }
        };
        
        Console.WriteLine($"\n新方式设置ContactTypes: [{string.Join(", ", query2.ContactTypes)}]");
        Console.WriteLine($"ContactType属性: {query2.ContactType}");
        Console.WriteLine($"新功能支持: {(query2.ContactTypes.Count == 2 ? "✓ 通过" : "✗ 失败")}");
    }
    
    /// <summary>
    /// 测试缓存键生成
    /// </summary>
    public static void TestCacheKeyGeneration()
    {
        Console.WriteLine("\n=== 测试缓存键生成 ===");
        
        var managerId = Guid.NewGuid();
        
        // 测试单个ContactType的缓存键
        var singleKey = CacheKeys.GetQueryResultKey(
            managerId, 
            WxContactType.Contact, 
            "test", 
            "remark", 
            1, 
            20);
        
        Console.WriteLine($"单ContactType缓存键: {singleKey}");
        
        // 测试多个ContactType的缓存键
        var multiKey = CacheKeys.GetQueryResultKey(
            managerId, 
            new List<WxContactType> { WxContactType.Contact, WxContactType.Enterprise }, 
            "test", 
            "remark", 
            1, 
            20);
        
        Console.WriteLine($"多ContactType缓存键: {multiKey}");
        
        // 测试键的一致性（相同参数应该生成相同的键）
        var multiKey2 = CacheKeys.GetQueryResultKey(
            managerId, 
            new List<WxContactType> { WxContactType.Enterprise, WxContactType.Contact }, // 顺序不同
            "test", 
            "remark", 
            1, 
            20);
        
        Console.WriteLine($"顺序不同的多ContactType缓存键: {multiKey2}");
        Console.WriteLine($"键一致性: {(multiKey == multiKey2 ? "✓ 通过" : "✗ 失败")}");
    }
    
    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("开始运行联系人类型查询测试...\n");
        
        try
        {
            TestQueryBackwardCompatibility();
            TestCacheKeyGeneration();
            
            Console.WriteLine("\n=== 测试总结 ===");
            Console.WriteLine("✓ 所有测试完成");
            Console.WriteLine("✓ 向后兼容性保持良好");
            Console.WriteLine("✓ 多ContactType支持正常");
            Console.WriteLine("✓ 缓存键生成正确");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n✗ 测试失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }
}
