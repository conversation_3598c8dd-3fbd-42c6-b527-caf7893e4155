using HappyWechat.Infrastructure.Commons;
using System.Security.Claims;

namespace HappyWechat.Web.Middlewares;

/// <summary>
/// 用户ID请求头设置中间件
/// 负责从认证的用户Claims中提取用户信息，并设置到HTTP请求头中，供API控制器使用
/// </summary>
public class UserIdHeaderMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserIdHeaderMiddleware> _logger;

    public UserIdHeaderMiddleware(RequestDelegate next, ILogger<UserIdHeaderMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 只处理API请求
        if (context.Request.Path.StartsWithSegments("/api"))
        {
            // 检查用户是否已认证
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                // 从Claims中获取用户ID
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                if (!string.IsNullOrEmpty(userIdClaim) && Guid.TryParse(userIdClaim, out var userId))
                {
                    // 设置用户ID到请求头
                    context.Request.Headers[MyHttpHeaders.UserId] = userId.ToString();
                    
                    // 设置用户名到请求头
                    var userName = context.User.FindFirst(ClaimTypes.Name)?.Value;
                    if (!string.IsNullOrEmpty(userName))
                    {
                        context.Request.Headers[MyHttpHeaders.Username] = userName;
                    }
                    
                    // 设置角色到请求头
                    var roles = context.User.FindAll(ClaimTypes.Role).Select(c => c.Value);
                    if (roles.Any())
                    {
                        context.Request.Headers[MyHttpHeaders.Roles] = string.Join(",", roles);
                    }
                    
                    // 设置用户显示名称到请求头
                    var displayName = context.User.FindFirst("DisplayName")?.Value;
                    if (!string.IsNullOrEmpty(displayName))
                    {
                        context.Request.Headers[MyHttpHeaders.DisplayName] = displayName;
                    }
                    
                    _logger.LogDebug("✅ 已设置用户请求头 - UserId: {UserId}, UserName: {UserName}, DisplayName: {DisplayName}, Roles: {Roles}", 
                        userId, userName, displayName, string.Join(",", roles));
                }
                else
                {
                    _logger.LogWarning("⚠️ 无法从Claims中提取有效的用户ID - UserIdClaim: {UserIdClaim}, Path: {Path}", 
                        userIdClaim, context.Request.Path);
                }
            }
            else
            {
                // 对于需要认证的API路径，记录未认证状态
                if (IsProtectedApiPath(context.Request.Path))
                {
                    _logger.LogDebug("🔒 受保护的API路径但用户未认证 - Path: {Path}", context.Request.Path);
                }
            }
        }

        await _next(context);
    }

    /// <summary>
    /// 判断是否为需要认证的API路径
    /// </summary>
    /// <param name="path">请求路径</param>
    /// <returns>是否为受保护路径</returns>
    private static bool IsProtectedApiPath(PathString path)
    {
        // 这些路径通常需要用户认证
        var protectedPaths = new[]
        {
            "/api/wx/",
            "/api/ai-agent/",
            "/api/schedule/",
            "/api/user/",
            "/api/config/"
        };

        return protectedPaths.Any(protectedPath => path.StartsWithSegments(protectedPath));
    }
}