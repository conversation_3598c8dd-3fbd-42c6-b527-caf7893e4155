using System.ComponentModel.DataAnnotations;

namespace HappyWechat.Application.DTOs.Requests.Commands;

public class SetContactAiAgentCommand
{
    [Required]
    public Guid ContactId { get; set; }
    
    [Required]
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// AI机器人名称，空字符串表示清除AI配置
    /// </summary>
    public string AiAgentName { get; set; } = "";
    
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 自定义提示词
    /// </summary>
    public string? CustomPrompt { get; set; }
    
    /// <summary>
    /// 回复延迟时间（秒）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 1;
}