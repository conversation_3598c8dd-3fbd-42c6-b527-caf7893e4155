using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息处理链路日志增强器 - 提供完整的处理轨迹追踪
/// </summary>
public interface IMessageProcessingLogger
{
    /// <summary>
    /// 记录消息接收日志
    /// </summary>
    void LogMessageReceived(string processingId, WxCallbackMessageDto callbackMessage);

    /// <summary>
    /// 记录消息类型路由日志
    /// </summary>
    void LogMessageTypeRouting(string processingId, string messageType, bool isGroup, string targetProcessor);

    /// <summary>
    /// 记录AI配置检查日志
    /// </summary>
    void LogAiConfigCheck(string processingId, string entityType, string entityId, bool isConfigured, string reason);

    /// <summary>
    /// 记录AI处理开始日志
    /// </summary>
    void LogAiProcessingStart(string processingId, string aiAgentId, string content);

    /// <summary>
    /// 记录AI处理完成日志
    /// </summary>
    void LogAiProcessingComplete(string processingId, bool success, int responseLength, long durationMs);

    /// <summary>
    /// 记录消息发送日志
    /// </summary>
    void LogMessageSending(string processingId, string wId, string wcId, string content, bool isGroup, string? atUser = null);

    /// <summary>
    /// 记录完整处理链路摘要
    /// </summary>
    void LogProcessingSummary(string processingId, string messageType, bool success, long totalDurationMs, string summary);
}

/// <summary>
/// 消息处理链路日志增强器实现
/// </summary>
public class MessageProcessingLogger : IMessageProcessingLogger
{
    private readonly ILogger<MessageProcessingLogger> _logger;

    public MessageProcessingLogger(ILogger<MessageProcessingLogger> logger)
    {
        _logger = logger;
    }

    public void LogMessageReceived(string processingId, WxCallbackMessageDto callbackMessage)
    {
        var messageType = callbackMessage.MessageType;
        var fromUser = callbackMessage.Data?.FromUser;
        var fromGroup = callbackMessage.Data?.FromGroup;
        var content = callbackMessage.Data?.Content?.Substring(0, Math.Min(100, callbackMessage.Data.Content?.Length ?? 0));

        _logger.LogInformation(
            "[{ProcessingId}] 📥 消息接收 | " +
            "MessageType: {MessageType} | " +
            "FromUser: {FromUser} | " +
            "FromGroup: {FromGroup} | " +
            "Content: {Content} | " +
            "WxManagerId: {WxManagerId}",
            processingId, messageType, fromUser, fromGroup, content, callbackMessage.WxManagerId);
    }

    public void LogMessageTypeRouting(string processingId, string messageType, bool isGroup, string targetProcessor)
    {
        _logger.LogInformation(
            "[{ProcessingId}] 🎯 消息路由 | " +
            "MessageType: {MessageType} | " +
            "IsGroup: {IsGroup} | " +
            "TargetProcessor: {TargetProcessor}",
            processingId, messageType, isGroup, targetProcessor);
    }

    public void LogAiConfigCheck(string processingId, string entityType, string entityId, bool isConfigured, string reason)
    {
        var level = isConfigured ? LogLevel.Information : LogLevel.Warning;
        var icon = isConfigured ? "✅" : "⚠️";
        
        _logger.Log(level,
            "[{ProcessingId}] {Icon} AI配置检查 | " +
            "EntityType: {EntityType} | " +
            "EntityID: '{EntityId}' | " +
            "IsConfigured: {IsConfigured} | " +
            "Reason: {Reason}",
            processingId, icon, entityType, entityId, isConfigured, reason);
    }

    public void LogAiProcessingStart(string processingId, string aiAgentId, string content)
    {
        var truncatedContent = content?.Substring(0, Math.Min(200, content?.Length ?? 0));
        
        _logger.LogInformation(
            "[{ProcessingId}] 🤖 AI处理开始 | " +
            "AiAgentId: {AiAgentId} | " +
            "ContentLength: {ContentLength} | " +
            "Content: {Content}",
            processingId, aiAgentId, content?.Length ?? 0, truncatedContent);
    }

    public void LogAiProcessingComplete(string processingId, bool success, int responseLength, long durationMs)
    {
        var level = success ? LogLevel.Information : LogLevel.Warning;
        var icon = success ? "✅" : "❌";
        
        _logger.Log(level,
            "[{ProcessingId}] {Icon} AI处理完成 | " +
            "Success: {Success} | " +
            "ResponseLength: {ResponseLength} | " +
            "Duration: {Duration}ms",
            processingId, icon, success, responseLength, durationMs);
    }

    public void LogMessageSending(string processingId, string wId, string wcId, string content, bool isGroup, string? atUser = null)
    {
        var truncatedContent = content?.Substring(0, Math.Min(100, content?.Length ?? 0));
        var atInfo = !string.IsNullOrEmpty(atUser) ? $" | At: {atUser}" : "";
        
        _logger.LogInformation(
            "[{ProcessingId}] 🚀 消息发送 | " +
            "WId: {WId} | " +
            "WcId: {WcId} | " +
            "IsGroup: {IsGroup} | " +
            "ContentLength: {ContentLength} | " +
            "Content: {Content}{AtInfo}",
            processingId, wId, wcId, isGroup, content?.Length ?? 0, truncatedContent, atInfo);
    }

    public void LogProcessingSummary(string processingId, string messageType, bool success, long totalDurationMs, string summary)
    {
        var level = success ? LogLevel.Information : LogLevel.Error;
        var icon = success ? "🎉" : "💥";
        
        _logger.Log(level,
            "[{ProcessingId}] {Icon} 处理完成摘要 | " +
            "MessageType: {MessageType} | " +
            "Success: {Success} | " +
            "TotalDuration: {TotalDuration}ms | " +
            "Summary: {Summary}",
            processingId, icon, messageType, success, totalDurationMs, summary);
    }
}