using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// SessionId获取策略抽象基类
/// 定义统一的获取接口和优先级机制
/// </summary>
public abstract class SessionIdRetrievalStrategy
{
    protected readonly ILogger Logger;

    protected SessionIdRetrievalStrategy(ILogger logger)
    {
        Logger = logger;
    }

    /// <summary>
    /// 策略类型
    /// </summary>
    public abstract SessionIdRetrievalStrategyType StrategyType { get; }

    /// <summary>
    /// 策略优先级（数字越小优先级越高）
    /// </summary>
    public abstract int Priority { get; }

    /// <summary>
    /// 策略名称
    /// </summary>
    public abstract string StrategyName { get; }

    /// <summary>
    /// 是否可用
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <returns>是否可用</returns>
    public abstract Task<bool> IsAvailableAsync(HttpContext? httpContext = null);

    /// <summary>
    /// 获取SessionId
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <returns>SessionId或null</returns>
    public abstract Task<string?> GetSessionIdAsync(HttpContext? httpContext = null);

    /// <summary>
    /// 验证获取到的SessionId
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否有效</returns>
    public virtual Task<bool> ValidateSessionIdAsync(string sessionId)
    {
        // 基本验证：非空且长度合理
        return Task.FromResult(!string.IsNullOrEmpty(sessionId) && sessionId.Length >= 10);
    }

    /// <summary>
    /// 获取策略描述信息
    /// </summary>
    /// <returns>策略描述</returns>
    public virtual string GetDescription()
    {
        return $"{StrategyName} (Priority: {Priority}, Type: {StrategyType})";
    }

    /// <summary>
    /// 记录成功获取日志
    /// </summary>
    /// <param name="sessionId">获取到的SessionId</param>
    protected virtual void LogSuccess(string sessionId)
    {
        Logger.LogDebug("✅ {StrategyName} 获取SessionId成功: {SessionId}", 
            StrategyName, sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
    }

    /// <summary>
    /// 记录失败获取日志
    /// </summary>
    /// <param name="reason">失败原因</param>
    /// <param name="exception">异常信息（可选）</param>
    protected virtual void LogFailure(string reason, Exception? exception = null)
    {
        if (exception != null)
        {
            Logger.LogDebug(exception, "❌ {StrategyName} 获取SessionId失败: {Reason}", StrategyName, reason);
        }
        else
        {
            Logger.LogDebug("❌ {StrategyName} 获取SessionId失败: {Reason}", StrategyName, reason);
        }
    }

    /// <summary>
    /// 记录不可用日志
    /// </summary>
    /// <param name="reason">不可用原因</param>
    protected virtual void LogUnavailable(string reason)
    {
        Logger.LogDebug("⚠️ {StrategyName} 不可用: {Reason}", StrategyName, reason);
    }

    /// <summary>
    /// 安全执行操作，包含异常处理
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="defaultValue">默认返回值</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作结果</returns>
    protected async Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, T defaultValue, string operationName)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex)
        {
            LogFailure($"{operationName}执行异常", ex);
            return defaultValue;
        }
    }

    /// <summary>
    /// 安全执行同步操作
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="defaultValue">默认返回值</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作结果</returns>
    protected T SafeExecute<T>(Func<T> operation, T defaultValue, string operationName)
    {
        try
        {
            return operation();
        }
        catch (Exception ex)
        {
            LogFailure($"{operationName}执行异常", ex);
            return defaultValue;
        }
    }
}

/// <summary>
/// 策略执行结果
/// </summary>
public class StrategyExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// SessionId
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 策略类型
    /// </summary>
    public SessionIdRetrievalStrategyType StrategyType { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="sessionId">SessionId</param>
    /// <param name="strategyType">策略类型</param>
    /// <param name="executionTimeMs">执行时间</param>
    /// <returns>成功结果</returns>
    public static StrategyExecutionResult Success(string sessionId, SessionIdRetrievalStrategyType strategyType, long executionTimeMs = 0)
    {
        return new StrategyExecutionResult
        {
            IsSuccess = true,
            SessionId = sessionId,
            StrategyType = strategyType,
            ExecutionTimeMs = executionTimeMs
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="strategyType">策略类型</param>
    /// <param name="executionTimeMs">执行时间</param>
    /// <returns>失败结果</returns>
    public static StrategyExecutionResult Failure(string errorMessage, SessionIdRetrievalStrategyType strategyType, long executionTimeMs = 0)
    {
        return new StrategyExecutionResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            StrategyType = strategyType,
            ExecutionTimeMs = executionTimeMs
        };
    }
}
