namespace HappyWechat.Infrastructure.MessageProcessing.Models;

/// <summary>
/// 消息分类结果
/// </summary>
public class MessageClassificationResult
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 分类描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否为私聊消息
    /// </summary>
    public bool IsPrivateMessage { get; set; }

    /// <summary>
    /// 是否为群聊消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// 是否为好友请求
    /// </summary>
    public bool IsFriendRequest { get; set; }

    /// <summary>
    /// 是否为群邀请
    /// </summary>
    public bool IsGroupInvite { get; set; }

    /// <summary>
    /// 是否为离线通知
    /// </summary>
    public bool IsOfflineNotification { get; set; }

    /// <summary>
    /// 是否为文本消息
    /// </summary>
    public bool IsTextMessage { get; set; }

    /// <summary>
    /// 是否为媒体消息（图片、语音、视频、文件）
    /// </summary>
    public bool IsMediaMessage { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public MessageType Type { get; set; }

    /// <summary>
    /// 是否需要立即处理
    /// </summary>
    public bool RequiresImmediateProcessing { get; set; }

    /// <summary>
    /// 是否需要AI处理
    /// </summary>
    public bool RequiresAiProcessing { get; set; }

    /// <summary>
    /// 是否需要文件下载
    /// </summary>
    public bool RequiresFileDownload { get; set; }

    /// <summary>
    /// 是否需要语音转换
    /// </summary>
    public bool RequiresVoiceConversion { get; set; }

    /// <summary>
    /// 是否需要@检查
    /// </summary>
    public bool RequiresMentionCheck { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();

    /// <summary>
    /// 分类时间
    /// </summary>
    public DateTime ClassifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建成功的分类结果
    /// </summary>
    public static MessageClassificationResult CreateSuccess(string messageType, string description)
    {
        return new MessageClassificationResult
        {
            MessageType = messageType,
            Description = description
        };
    }

    /// <summary>
    /// 创建私聊消息分类结果
    /// </summary>
    public static MessageClassificationResult CreatePrivateMessage(string messageType, string description)
    {
        return new MessageClassificationResult
        {
            MessageType = messageType,
            Description = description,
            IsPrivateMessage = true,
            IsTextMessage = messageType == "60001",
            IsMediaMessage = messageType is "60002" or "60004" or "60009"
        };
    }

    /// <summary>
    /// 创建群聊消息分类结果
    /// </summary>
    public static MessageClassificationResult CreateGroupMessage(string messageType, string description)
    {
        return new MessageClassificationResult
        {
            MessageType = messageType,
            Description = description,
            IsGroupMessage = true,
            IsTextMessage = messageType == "80001",
            IsMediaMessage = messageType is "80002" or "80004" or "80009"
        };
    }

    /// <summary>
    /// 创建好友请求分类结果
    /// </summary>
    public static MessageClassificationResult CreateFriendRequest()
    {
        return new MessageClassificationResult
        {
            MessageType = "37",
            Description = "好友请求",
            IsFriendRequest = true,
            Priority = MessagePriority.High
        };
    }

    /// <summary>
    /// 创建离线通知分类结果
    /// </summary>
    public static MessageClassificationResult CreateOfflineNotification()
    {
        return new MessageClassificationResult
        {
            MessageType = "30000",
            Description = "离线通知",
            IsOfflineNotification = true,
            Priority = MessagePriority.High
        };
    }
}

/// <summary>
/// 消息优先级
/// </summary>
public enum MessagePriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// 消息类型枚举
/// </summary>
public enum MessageType
{
    Unknown = 0,
    FriendRequest = 1,
    OfflineNotification = 2,
    OnlineNotification = 3,
    PrivateText = 10,
    PrivateImage = 11,
    PrivateVoice = 12,
    PrivateFile = 13,
    PrivateVideo = 14,
    GroupText = 20,
    GroupImage = 21,
    GroupVoice = 22,
    GroupFile = 23,
    GroupVideo = 24
}
