namespace HappyWechat.Application.DTOs.Configurations;

/// <summary>
/// 弹性配置选项
/// </summary>
public class ResilienceOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Resilience";

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 基础延迟时间（毫秒）
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;

    /// <summary>
    /// 最大延迟时间（毫秒）
    /// </summary>
    public int MaxDelayMs { get; set; } = 30000;

    /// <summary>
    /// 断路器失败阈值
    /// </summary>
    public int CircuitBreakerFailureThreshold { get; set; } = 5;

    /// <summary>
    /// 断路器超时时间（毫秒）
    /// </summary>
    public int CircuitBreakerTimeoutMs { get; set; } = 60000;

    /// <summary>
    /// EYun API 重试次数
    /// </summary>
    public int EYunMaxRetries { get; set; } = 2;

    /// <summary>
    /// 联系人同步重试次数
    /// </summary>
    public int ContactSyncMaxRetries { get; set; } = 1;

    /// <summary>
    /// 消息发送重试次数
    /// </summary>
    public int MessageSendMaxRetries { get; set; } = 2;
}