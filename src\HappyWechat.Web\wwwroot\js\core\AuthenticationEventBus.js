/**
 * 认证状态变更事件总线
 * 提供解耦的事件发布订阅机制，用于认证状态变更通知
 * 
 * 支持的事件类型：
 * - 'authStateChanged': 认证状态变更
 * - 'sessionUpdated': 会话更新
 * - 'userLoggedIn': 用户登录
 * - 'userLoggedOut': 用户登出
 * - 'sessionExpired': 会话过期
 */
class AuthenticationEventBus {
    constructor() {
        this.events = new Map();
        this.isDebugMode = false;
        
        // 初始化日志
        this.log('🚌 认证事件总线已初始化');
    }

    /**
     * 订阅事件
     * @param {string} eventType 事件类型
     * @param {Function} callback 回调函数
     * @param {Object} options 选项 { once: boolean, priority: number }
     * @returns {Function} 取消订阅函数
     */
    subscribe(eventType, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是function类型');
        }

        if (!this.events.has(eventType)) {
            this.events.set(eventType, []);
        }

        const subscription = {
            callback,
            once: options.once || false,
            priority: options.priority || 0,
            id: this.generateSubscriptionId()
        };

        const subscribers = this.events.get(eventType);
        subscribers.push(subscription);
        
        // 按优先级排序（高优先级先执行）
        subscribers.sort((a, b) => b.priority - a.priority);

        this.log(`📝 订阅事件: ${eventType}, ID: ${subscription.id}`);

        // 返回取消订阅函数
        return () => this.unsubscribe(eventType, subscription.id);
    }

    /**
     * 取消订阅
     * @param {string} eventType 事件类型
     * @param {string} subscriptionId 订阅ID
     */
    unsubscribe(eventType, subscriptionId) {
        if (!this.events.has(eventType)) {
            return;
        }

        const subscribers = this.events.get(eventType);
        const index = subscribers.findIndex(sub => sub.id === subscriptionId);
        
        if (index !== -1) {
            subscribers.splice(index, 1);
            this.log(`🗑️ 取消订阅: ${eventType}, ID: ${subscriptionId}`);
        }
    }

    /**
     * 发布事件
     * @param {string} eventType 事件类型
     * @param {*} data 事件数据
     * @param {Object} options 选项 { async: boolean }
     */
    publish(eventType, data, options = {}) {
        if (!this.events.has(eventType)) {
            this.log(`⚠️ 没有订阅者的事件: ${eventType}`);
            return;
        }

        const subscribers = this.events.get(eventType);
        const isAsync = options.async !== false; // 默认异步

        this.log(`📢 发布事件: ${eventType}, 订阅者数量: ${subscribers.length}`);

        // 创建事件对象
        const eventObj = {
            type: eventType,
            data,
            timestamp: Date.now(),
            source: 'AuthenticationEventBus'
        };

        if (isAsync) {
            // 异步执行所有回调
            this.executeCallbacksAsync(subscribers, eventObj);
        } else {
            // 同步执行所有回调
            this.executeCallbacksSync(subscribers, eventObj);
        }
    }

    /**
     * 异步执行回调
     * @param {Array} subscribers 订阅者列表
     * @param {Object} eventObj 事件对象
     */
    async executeCallbacksAsync(subscribers, eventObj) {
        const toRemove = [];

        for (const subscription of subscribers) {
            try {
                if (typeof subscription.callback === 'function') {
                    await Promise.resolve(subscription.callback(eventObj));
                }

                // 如果是一次性订阅，标记为删除
                if (subscription.once) {
                    toRemove.push(subscription.id);
                }
            } catch (error) {
                console.error(`❌ 事件回调执行失败 - 事件: ${eventObj.type}, ID: ${subscription.id}`, error);
            }
        }

        // 移除一次性订阅
        this.removeSubscriptions(eventObj.type, toRemove);
    }

    /**
     * 同步执行回调
     * @param {Array} subscribers 订阅者列表
     * @param {Object} eventObj 事件对象
     */
    executeCallbacksSync(subscribers, eventObj) {
        const toRemove = [];

        for (const subscription of subscribers) {
            try {
                if (typeof subscription.callback === 'function') {
                    subscription.callback(eventObj);
                }

                // 如果是一次性订阅，标记为删除
                if (subscription.once) {
                    toRemove.push(subscription.id);
                }
            } catch (error) {
                console.error(`❌ 事件回调执行失败 - 事件: ${eventObj.type}, ID: ${subscription.id}`, error);
            }
        }

        // 移除一次性订阅
        this.removeSubscriptions(eventObj.type, toRemove);
    }

    /**
     * 移除指定的订阅
     * @param {string} eventType 事件类型
     * @param {Array} subscriptionIds 要移除的订阅ID列表
     */
    removeSubscriptions(eventType, subscriptionIds) {
        if (subscriptionIds.length === 0) return;

        const subscribers = this.events.get(eventType);
        if (!subscribers) return;

        for (const id of subscriptionIds) {
            const index = subscribers.findIndex(sub => sub.id === id);
            if (index !== -1) {
                subscribers.splice(index, 1);
                this.log(`🗑️ 移除一次性订阅: ${eventType}, ID: ${id}`);
            }
        }
    }

    /**
     * 清除所有事件订阅
     */
    clear() {
        this.events.clear();
        this.log('🧹 清除所有事件订阅');
    }

    /**
     * 获取事件统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            totalEventTypes: this.events.size,
            totalSubscriptions: 0,
            eventTypes: {}
        };

        for (const [eventType, subscribers] of this.events) {
            stats.eventTypes[eventType] = subscribers.length;
            stats.totalSubscriptions += subscribers.length;
        }

        return stats;
    }

    /**
     * 生成订阅ID
     * @returns {string} 唯一订阅ID
     */
    generateSubscriptionId() {
        return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled 是否启用
     */
    setDebugMode(enabled) {
        this.isDebugMode = enabled;
        this.log(`🔧 调试模式: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 日志输出
     * @param {string} message 日志消息
     */
    log(message) {
        if (this.isDebugMode) {
            console.log(`[AuthEventBus] ${message}`);
        }
    }

    /**
     * 销毁事件总线
     */
    destroy() {
        this.clear();
        this.log('💥 认证事件总线已销毁');
    }
}

// 创建全局单例实例
if (!window.authEventBus) {
    window.authEventBus = new AuthenticationEventBus();
    
    // 在开发环境启用调试模式
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        window.authEventBus.setDebugMode(true);
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthenticationEventBus;
}

console.log('✅ AuthenticationEventBus已加载并注册到window.authEventBus');
