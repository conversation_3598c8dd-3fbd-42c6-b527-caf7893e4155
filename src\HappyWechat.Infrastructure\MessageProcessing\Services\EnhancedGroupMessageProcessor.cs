using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.Identity.Repositories;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.Interfaces;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 增强的群消息处理器 - 按消息类型区分@判断规则
/// </summary>
public interface IEnhancedGroupMessageProcessor
{
    /// <summary>
    /// 检查群消息是否应该触发AI回复
    /// </summary>
    Task<GroupMessageProcessResult> ShouldTriggerAiReplyAsync(WxCallbackMessageDto callbackMessage);

    /// <summary>
    /// 检查群消息AI回复触发条件（新架构兼容方法）
    /// </summary>
    Task<GroupMessageProcessResult> CheckGroupMessageAiReplyTriggerAsync(WxCallbackMessageDto callbackMessage);
}

/// <summary>
/// 群消息处理结果
/// </summary>
public class GroupMessageProcessResult
{
    public bool ShouldReply { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool IsConfigured { get; set; }
    public bool IsMentioned { get; set; }
    public string MessageType { get; set; } = string.Empty;
    public string GroupName { get; set; } = string.Empty;
    public bool ShouldWaitForCombination { get; set; } = false;
    public WxCallbackMessageDto? CombinedMessage { get; set; }

    // 🚀 新架构：增加群聊模式相关属性
    public bool OnlyReplyWhenMentioned { get; set; } = false;
    public bool ReplyToAllMessages { get; set; } = false;
    public string GroupId { get; set; } = string.Empty;
    public string FromUser { get; set; } = string.Empty;
}

/// <summary>
/// 增强的群消息处理器实现
/// </summary>
public class EnhancedGroupMessageProcessor : IEnhancedGroupMessageProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<EnhancedGroupMessageProcessor> _logger;
    private readonly IMessageCombinationService _messageCombinationService;

    public EnhancedGroupMessageProcessor(
        ApplicationDbContext dbContext,
        ILogger<EnhancedGroupMessageProcessor> logger,
        IMessageCombinationService messageCombinationService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _messageCombinationService = messageCombinationService;
    }

    public async Task<GroupMessageProcessResult> ShouldTriggerAiReplyAsync(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            var messageType = callbackMessage.MessageType;
            var fromGroup = callbackMessage.Data?.FromGroup;
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);

            _logger.LogInformation("🔍 检查群消息AI回复触发条件 - MessageType: {MessageType}, Group: {Group}, WxManagerId: {WxManagerId}", 
                messageType, fromGroup, wxManagerId);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromGroup))
            {
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组信息缺失",
                    MessageType = messageType!
                };
            }

            // 2. 查询群组配置
            var groupConfig = await _dbContext.WxGroupEntities
                .Where(g => g.WxManagerId == wxManagerId && g.ChatRoomId == fromGroup)
                .Select(g => new
                {
                    g.NickName,
                    g.IsAiEnabled,
                    g.OnlyReplyWhenMentioned,
                    g.AiAgentId
                })
                .FirstOrDefaultAsync();

            if (groupConfig == null)
            {
                _logger.LogWarning("❌ 群组配置不存在 - Group: {Group}, WxManagerId: {WxManagerId}", fromGroup, wxManagerId);
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组不存在",
                    MessageType = messageType!,
                    IsConfigured = false
                };
            }

            // 3. 检查AI配置
            _logger.LogInformation("📋 群组配置检查 - GroupName: {GroupName}, IsAiEnabled: {IsAiEnabled}, AiAgentId: {AiAgentId}",
                groupConfig.NickName ?? fromGroup, groupConfig.IsAiEnabled, groupConfig.AiAgentId);
                
            if (!groupConfig.IsAiEnabled || !groupConfig.AiAgentId.HasValue)
            {
                _logger.LogWarning("❌ 群组AI配置无效 - IsAiEnabled: {IsAiEnabled}, AiAgentId: {AiAgentId}", 
                    groupConfig.IsAiEnabled, groupConfig.AiAgentId);
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组AI未启用或未配置代理",
                    MessageType = messageType!,
                    IsConfigured = false,
                    GroupName = groupConfig.NickName ?? fromGroup
                };
            }

            // 4. 按消息类型应用不同的@判断规则，并处理消息合并 (增强版)
            var (shouldReply, combinedMessage) = await ApplyMessageTypeRulesWithCombinationAsync(messageType!, groupConfig.OnlyReplyWhenMentioned, callbackMessage);
            var isMentioned = IsMessageMentioned(callbackMessage);
            
            // 🔧 增强：记录合并消息的详细信息
            if (combinedMessage != null)
            {
                _logger.LogInformation("🔗 群组消息合并成功 - OriginalType: {OriginalType}, CombinedContentLength: {CombinedLength}, Group: {Group}",
                    messageType, combinedMessage.Data?.Content?.Length ?? 0, fromGroup);
            }

            _logger.LogInformation("🎯 群消息处理结果 - MessageType: {MessageType}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}, ShouldReply: {ShouldReply}, HasCombinedMessage: {HasCombinedMessage}",
                messageType, groupConfig.OnlyReplyWhenMentioned, isMentioned, shouldReply, combinedMessage != null);

            return new GroupMessageProcessResult
            {
                ShouldReply = shouldReply,
                Reason = shouldReply ? "满足回复条件" : GetRejectReason(messageType!, groupConfig.OnlyReplyWhenMentioned, isMentioned),
                IsConfigured = true,
                IsMentioned = isMentioned,
                MessageType = messageType!,
                GroupName = groupConfig.NickName ?? fromGroup,
                CombinedMessage = combinedMessage, // 🔧 传递合并后的消息
                ShouldWaitForCombination = !shouldReply && combinedMessage == null // 🔧 新增：标记是否等待合并
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群消息AI回复触发条件异常");
            return new GroupMessageProcessResult
            {
                ShouldReply = false,
                Reason = $"处理异常: {ex.Message}",
                MessageType = callbackMessage.MessageType!
            };
        }
    }

    /// <summary>
    /// 🔧 新版：按消息类型应用不同的@判断规则并处理消息合并
    /// </summary>
    private async Task<(bool shouldReply, WxCallbackMessageDto? combinedMessage)> ApplyMessageTypeRulesWithCombinationAsync(string messageType, bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        // 根据需求文档的规则：
        // - 80001(群聊文本)和80004(群聊语音)：需要判断OnlyReplyWhenMentioned
        // - 80009(群聊文件)和80002(群聊图片)：全部放行，只需要判断AI是否模式是自动回复，且机器人id不为空

        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE => 
                await ApplyMentionRuleWithCombinationAsync(onlyReplyWhenMentioned, callbackMessage),
            
            MessageTypeConstants.GROUP_FILE or MessageTypeConstants.GROUP_IMAGE => 
                await ApplyAutoReplyRuleWithCombinationAsync(callbackMessage),
            
            // 其他群消息类型默认应用@规则
            _ when MessageTypeConstants.IsGroupMessage(messageType) => 
                await ApplyMentionRuleWithCombinationAsync(onlyReplyWhenMentioned, callbackMessage),
            
            _ => (false, null)
        };
    }

    /// <summary>
    /// 按消息类型应用不同的@判断规则（旧版，保留兼容性）
    /// </summary>
    private async Task<bool> ApplyMessageTypeRulesAsync(string messageType, bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        // 根据需求文档的规则：
        // - 80001(群聊文本)和80004(群聊语音)：需要判断OnlyReplyWhenMentioned
        // - 80009(群聊文件)和80002(群聊图片)：全部放行，只需要判断AI是否模式是自动回复，且机器人id不为空

        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE => 
                await ApplyMentionRuleAsync(onlyReplyWhenMentioned, callbackMessage),
            
            MessageTypeConstants.GROUP_FILE or MessageTypeConstants.GROUP_IMAGE => 
                await ApplyAutoReplyRuleAsync(callbackMessage),
            
            // 其他群消息类型默认应用@规则
            _ when MessageTypeConstants.IsGroupMessage(messageType) => 
                await ApplyMentionRuleAsync(onlyReplyWhenMentioned, callbackMessage),
            
            _ => false
        };
    }

    /// <summary>
    /// 🔧 新版：应用@提及规则并处理消息合并
    /// </summary>
    private async Task<(bool shouldReply, WxCallbackMessageDto? combinedMessage)> ApplyMentionRuleWithCombinationAsync(bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        if (!onlyReplyWhenMentioned)
        {
            // 配置为所有消息回复
            _logger.LogInformation("📝 @提及规则 - 配置为回复所有消息，直接通过");
            return (true, null);
        }

        // 配置为仅@后回复，检查是否被@
        var isMentioned = IsMessageMentioned(callbackMessage);
        
        _logger.LogInformation("📝 应用@提及规则 - OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}",
            onlyReplyWhenMentioned, isMentioned);

        // 🔧 修复：在群组@模式下，对于@消息启用消息合并，并正确处理合并结果（支持文本和语音消息）
        if (isMentioned && (callbackMessage.MessageType == MessageTypeConstants.GROUP_TEXT || callbackMessage.MessageType == MessageTypeConstants.GROUP_VOICE))
        {
            try
            {
                var processingId = Guid.NewGuid().ToString("N")[..8];
                var combinationResult = await _messageCombinationService.ProcessMessageAsync(callbackMessage, processingId);
                
                if (combinationResult.Action == MessageCombinationAction.WaitForCombination)
                {
                    _logger.LogInformation("🔄 群组@模式 - 文本消息合并处理：等待可能的媒体消息组合 - ProcessingId: {ProcessingId}", 
                        processingId);
                    return (false, null); // 暂不触发AI回复，等待合并
                }
                
                if (combinationResult.Action == MessageCombinationAction.CombinedMessage && combinationResult.Message != null)
                {
                    _logger.LogInformation("✅ 群组@模式 - 消息合并成功：{Reason} - ProcessingId: {ProcessingId}, CombinedContentLength: {ContentLength}", 
                        combinationResult.Reason, processingId, combinationResult.Message.Data?.Content?.Length ?? 0);
                    
                    // 🔧 增强：返回合并后的消息供AI处理使用，并验证数据完整性
                    if (ValidateCombinedMessage(combinationResult.Message, processingId))
                    {
                        return (true, combinationResult.Message);
                    }
                    else
                    {
                        _logger.LogWarning("⚠️ 群组@模式 - 合并消息验证失败，使用原始消息 - ProcessingId: {ProcessingId}", processingId);
                        return (true, null);
                    }
                }
                
                // 🔧 新增：处理其他合并结果（如ProcessImmediately或Skip）
                if (combinationResult.Action == MessageCombinationAction.ProcessImmediately)
                {
                    _logger.LogInformation("⚡ 群组@模式 - 消息立即处理：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return (true, null); // 使用原始消息直接处理
                }
                
                if (combinationResult.Action == MessageCombinationAction.Skip)
                {
                    _logger.LogInformation("⏭️ 群组@模式 - 消息跳过处理：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return (false, null); // 跳过处理
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 群组@模式 - 消息合并处理失败，使用降级策略继续正常处理流程 - Error: {Error}", ex.Message);
                // 🔧 增强的降级策略：记录更详细的错误信息，确保不丢失用户请求
                return (true, null);
            }
        }

        return (isMentioned, null);
    }

    /// <summary>
    /// 应用@提及规则（80001和80004）- 集成消息合并服务（旧版，保留兼容性）
    /// </summary>
    private async Task<bool> ApplyMentionRuleAsync(bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        if (!onlyReplyWhenMentioned)
        {
            // 配置为所有消息回复
            _logger.LogInformation("📝 @提及规则 - 配置为回复所有消息，直接通过");
            return true;
        }

        // 配置为仅@后回复，检查是否被@
        var isMentioned = IsMessageMentioned(callbackMessage);
        
        _logger.LogInformation("📝 应用@提及规则 - OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}",
            onlyReplyWhenMentioned, isMentioned);

        // 🔧 修复：在群组@模式下，对于@消息启用消息合并，并正确处理合并结果（支持文本和语音消息）
        if (isMentioned && (callbackMessage.MessageType == MessageTypeConstants.GROUP_TEXT || callbackMessage.MessageType == MessageTypeConstants.GROUP_VOICE))
        {
            try
            {
                var processingId = Guid.NewGuid().ToString("N")[..8];
                var combinationResult = await _messageCombinationService.ProcessMessageAsync(callbackMessage, processingId);
                
                if (combinationResult.Action == MessageCombinationAction.WaitForCombination)
                {
                    _logger.LogInformation("🔄 群组@模式 - 文本消息合并处理：等待可能的媒体消息组合 - ProcessingId: {ProcessingId}", 
                        processingId);
                    return false; // 暂不触发AI回复，等待合并
                }
                
                if (combinationResult.Action == MessageCombinationAction.CombinedMessage && combinationResult.Message != null)
                {
                    _logger.LogInformation("✅ 群组@模式 - 消息合并成功：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    
                    // 🔧 新增：将合并后的消息放入群组处理结果中，供后续AI处理使用
                    // 通过在GroupMessageProcessResult中添加CombinedMessage字段来传递
                    // 这样AI处理器就能获取到合并后的消息内容
                    return true; // 触发AI处理，但使用合并后的消息
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 群组@模式 - 消息合并处理失败，继续正常处理流程");
            }
        }

        return isMentioned;
    }

    /// <summary>
    /// 🔧 新版：应用自动回复规则并处理消息合并
    /// </summary>
    private async Task<(bool shouldReply, WxCallbackMessageDto? combinedMessage)> ApplyAutoReplyRuleWithCombinationAsync(WxCallbackMessageDto callbackMessage)
    {
        // 对于文件和图片消息，只要AI模式是自动回复且机器人ID不为空就放行
        // 这里的逻辑是：如果群组配置了AI且有代理ID，就认为满足条件
        // （在调用此方法前已经验证过IsAiEnabled和AiAgentId）
        
        _logger.LogInformation("📁 应用自动回复规则 - MessageType: {MessageType}", 
            callbackMessage.MessageType);

        // 🔧 修复：对于媒体消息，启用消息合并并正确处理合并流程
        if (callbackMessage.MessageType == MessageTypeConstants.GROUP_FILE || 
            callbackMessage.MessageType == MessageTypeConstants.GROUP_IMAGE)
        {
            try
            {
                var processingId = Guid.NewGuid().ToString("N")[..8];
                var combinationResult = await _messageCombinationService.ProcessMessageAsync(callbackMessage, processingId);
                
                if (combinationResult.Action == MessageCombinationAction.WaitForCombination)
                {
                    _logger.LogInformation("🔄 自动回复模式 - 媒体消息已缓存，等待后续文本消息合并 - ProcessingId: {ProcessingId}", 
                        processingId);
                    return (false, null); // 暂不触发AI回复，等待合并
                }
                
                // 🔧 新增：处理立即处理的情况（没有等待合并）
                if (combinationResult.Action == MessageCombinationAction.ProcessImmediately)
                {
                    _logger.LogInformation("📁 自动回复模式 - 媒体消息立即处理：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return (true, null); // 直接触发AI处理
                }
                
                if (combinationResult.Action == MessageCombinationAction.CombinedMessage && combinationResult.Message != null)
                {
                    _logger.LogInformation("✅ 自动回复模式 - 消息合并成功：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return (true, combinationResult.Message); // 触发AI处理，使用合并后的消息
                }
                
                // 🔧 新增：处理跳过情况
                if (combinationResult.Action == MessageCombinationAction.Skip)
                {
                    _logger.LogInformation("⏭️ 自动回复模式 - 媒体消息跳过处理：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return (false, null); // 跳过处理
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 自动回复模式 - 消息合并处理失败，使用降级策略直接处理媒体消息");
                // 降级策略：合并失败时，直接处理媒体消息，确保用户体验
                return (true, null);
            }
        }

        return (true, null);
    }

    /// <summary>
    /// 🔧 重构：应用自动回复规则（80009和80002）- 集成消息合并服务（旧版，保留兼容性）
    /// </summary>
    private async Task<bool> ApplyAutoReplyRuleAsync(WxCallbackMessageDto callbackMessage)
    {
        // 对于文件和图片消息，只要AI模式是自动回复且机器人ID不为空就放行
        // 这里的逻辑是：如果群组配置了AI且有代理ID，就认为满足条件
        // （在调用此方法前已经验证过IsAiEnabled和AiAgentId）
        
        _logger.LogInformation("📁 应用自动回复规则 - MessageType: {MessageType}", 
            callbackMessage.MessageType);

        // 🔧 修复：对于媒体消息，启用消息合并并正确处理合并流程
        if (callbackMessage.MessageType == MessageTypeConstants.GROUP_FILE || 
            callbackMessage.MessageType == MessageTypeConstants.GROUP_IMAGE)
        {
            try
            {
                var processingId = Guid.NewGuid().ToString("N")[..8];
                var combinationResult = await _messageCombinationService.ProcessMessageAsync(callbackMessage, processingId);
                
                if (combinationResult.Action == MessageCombinationAction.WaitForCombination)
                {
                    _logger.LogInformation("🔄 自动回复模式 - 媒体消息已缓存，等待后续文本消息合并 - ProcessingId: {ProcessingId}", 
                        processingId);
                    return false; // 暂不触发AI回复，等待合并
                }
                
                // 🔧 新增：处理立即处理的情况（没有等待合并）
                if (combinationResult.Action == MessageCombinationAction.ProcessImmediately)
                {
                    _logger.LogInformation("📁 自动回复模式 - 媒体消息立即处理：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return true; // 直接触发AI处理
                }
                
                if (combinationResult.Action == MessageCombinationAction.CombinedMessage)
                {
                    _logger.LogInformation("✅ 自动回复模式 - 消息合并成功：{Reason} - ProcessingId: {ProcessingId}", 
                        combinationResult.Reason, processingId);
                    return true; // 触发AI处理，使用合并后的消息
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 自动回复模式 - 消息合并处理失败，继续正常处理流程");
            }
        }

        return true;
    }

    /// <summary>
    /// 检查消息是否@了机器人（增强版）
    /// </summary>
    private bool IsMessageMentioned(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            // 🔧 修复：获取正确的机器人WcId
            var botWcId = GetBotWcIdFromDatabase(callbackMessage.WxManagerId);
            var atList = callbackMessage.Data?.Atlist ?? new List<string>();
            var content = callbackMessage.Data?.Content ?? "";
            var messageType = callbackMessage.MessageType ?? "";

            _logger.LogInformation("🔍 开始@检测 - MessageType: {MessageType}, BotWcId: {BotWcId}, AtList: [{AtList}], Content: '{Content}'",
                messageType, botWcId, string.Join(",", atList), content.Length > 50 ? content.Substring(0, 50) + "..." : content);

            if (string.IsNullOrEmpty(botWcId))
            {
                _logger.LogWarning("⚠️ 机器人wcId为空，无法判断@状态");
                return false;
            }

            // 🔧 增强@检测逻辑：支持多种@检测方式
            var isMentioned = false;
            var detectionMethod = "";

            // 方式1：检查@列表中是否包含机器人WcId（精确匹配）
            if (atList.Contains(botWcId, StringComparer.OrdinalIgnoreCase))
            {
                isMentioned = true;
                detectionMethod = "方式1：@列表精确匹配";
            }

            // 方式2：检查@列表中是否包含部分匹配（容错处理）
            if (!isMentioned)
            {
                foreach (var atItem in atList)
                {
                    if (!string.IsNullOrEmpty(atItem) && 
                        (botWcId.Contains(atItem, StringComparison.OrdinalIgnoreCase) || 
                         atItem.Contains(botWcId, StringComparison.OrdinalIgnoreCase)))
                    {
                        isMentioned = true;
                        detectionMethod = $"方式2：@列表部分匹配({atItem})";
                        break;
                    }
                }
            }

            // 方式3：检查消息内容中的@所有人和相关关键词（语音消息跳过此检查）
            if (!isMentioned && messageType != MessageTypeConstants.GROUP_VOICE)
            {
                var mentionKeywords = new[] { "@所有人", "@everyone", "@all" };
                foreach (var keyword in mentionKeywords)
                {
                    if (content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                    {
                        isMentioned = true;
                        detectionMethod = $"方式3：内容关键词匹配({keyword})";
                        break;
                    }
                }
            }

            // 方式4：检查消息内容中是否直接包含机器人昵称或ID（容错处理，语音消息跳过此检查）
            if (!isMentioned && !string.IsNullOrEmpty(botWcId) && messageType != MessageTypeConstants.GROUP_VOICE)
            {
                // 提取机器人的简短昵称（去掉前缀和后缀）
                var shortBotId = ExtractShortBotId(botWcId);
                if (!string.IsNullOrEmpty(shortBotId) && 
                    content.Contains($"@{shortBotId}", StringComparison.OrdinalIgnoreCase))
                {
                    isMentioned = true;
                    detectionMethod = $"方式4：内容昵称匹配(@{shortBotId})";
                }
            }

            // 🚀 新增：方式5：语音消息特殊处理 - 主要依赖AtList
            if (!isMentioned && messageType == MessageTypeConstants.GROUP_VOICE)
            {
                // 对于语音消息，如果AtList不为空，说明用户在发语音时@了某人
                if (atList.Any())
                {
                    _logger.LogInformation("🎤 语音消息@检测 - 发现AtList但未匹配到机器人，AtList: [{AtList}]", 
                        string.Join(",", atList));
                }
                else
                {
                    _logger.LogInformation("🎤 语音消息@检测 - AtList为空，按OnlyReplyWhenMentioned配置处理");
                }
            }

            if (isMentioned)
            {
                _logger.LogInformation("✅ @检测成功 - {Method}", detectionMethod);
            }
            else
            {
                _logger.LogDebug("🚫 未检测到@信号 - 已尝试所有检测方式");
            }

            _logger.LogInformation("🎯 @状态检查完成 - BotWcId: {BotWcId}, AtList: [{AtList}], IsMentioned: {IsMentioned}, Method: {Method}",
                botWcId, string.Join(",", atList), isMentioned, detectionMethod);

            return isMentioned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查@状态异常");
            return false;
        }
    }
    
    /// <summary>
    /// 🔧 新增：提取机器人的简短昵称
    /// </summary>
    private string? ExtractShortBotId(string botWcId)
    {
        if (string.IsNullOrEmpty(botWcId))
            return null;
            
        try
        {
            // 移除常见的微信ID前缀
            var cleanedId = botWcId;
            var prefixes = new[] { "wxid_", "wx_", "gh_" };
            
            foreach (var prefix in prefixes)
            {
                if (cleanedId.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    cleanedId = cleanedId.Substring(prefix.Length);
                    break;
                }
            }
            
            // 取前8个字符作为简短昵称
            if (cleanedId.Length > 8)
            {
                cleanedId = cleanedId.Substring(0, 8);
            }
            
            return cleanedId.Length >= 3 ? cleanedId : null; // 至少保留3个字符
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "提取简短机器人 ID 失败 - BotWcId: {BotWcId}", botWcId);
            return null;
        }
    }

    /// <summary>
    /// 从数据库获取机器人WcId
    /// </summary>
    private string? GetBotWcIdFromDatabase(string wxManagerId)
    {
        try
        {
            if (string.IsNullOrEmpty(wxManagerId) || !Guid.TryParse(wxManagerId, out var managerId))
            {
                _logger.LogWarning("⚠️ WxManagerId无效: {WxManagerId}", wxManagerId);
                return null;
            }

            var wxManager = _dbContext.WxManagerEntities
                .AsNoTracking()
                .Where(w => w.Id == managerId)
                .Select(w => w.WcId)
                .FirstOrDefault();

            if (string.IsNullOrEmpty(wxManager))
            {
                _logger.LogWarning("⚠️ 未找到对应的WxManager - WxManagerId: {WxManagerId}", wxManagerId);
                return null;
            }

            _logger.LogDebug("✅ 获取机器人WcId成功 - WxManagerId: {WxManagerId}, BotWcId: {BotWcId}", 
                wxManagerId, wxManager);

            return wxManager;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取机器人WcId异常 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    /// <summary>
    /// 获取拒绝回复的原因
    /// </summary>
    private string GetRejectReason(string messageType, bool onlyReplyWhenMentioned, bool isMentioned)
    {
        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE when onlyReplyWhenMentioned && !isMentioned =>
                "群聊文本/语音消息需要@机器人才能触发回复",
            
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE when !onlyReplyWhenMentioned =>
                "群聊文本/语音消息配置为所有消息回复，应该触发",
            
            MessageTypeConstants.GROUP_FILE or MessageTypeConstants.GROUP_IMAGE =>
                "群聊文件/图片消息应该自动回复，检查AI配置",
            
            _ => "未知的拒绝原因"
        };
    }
    
    /// <summary>
    /// 🔧 新增：验证合并消息的完整性
    /// </summary>
    private bool ValidateCombinedMessage(WxCallbackMessageDto combinedMessage, string processingId)
    {
        try
        {
            if (combinedMessage?.Data == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 合并消息验证失败 - 消息或Data为空", processingId);
                return false;
            }
            
            if (string.IsNullOrEmpty(combinedMessage.Data.FromUser))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 合并消息验证失败 - FromUser为空", processingId);
                return false;
            }
            
            if (string.IsNullOrEmpty(combinedMessage.Data.FromGroup))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 合并消息验证失败 - FromGroup为空", processingId);
                return false;
            }
            
            if (string.IsNullOrEmpty(combinedMessage.Data.Content))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 合并消息验证失败 - Content为空", processingId);
                return false;
            }
            
            if (combinedMessage.Data.Content.Length > 10000) // 合理的最大长度限制
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 合并消息验证失败 - Content长度过长: {Length}", 
                    processingId, combinedMessage.Data.Content.Length);
                return false;
            }
            
            _logger.LogDebug("[{ProcessingId}] ✅ 合并消息验证成功 - ContentLength: {Length}", 
                processingId, combinedMessage.Data.Content.Length);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❤️ 合并消息验证异常", processingId);
            return false;
        }
    }

    /// <summary>
    /// 检查群消息AI回复触发条件（新架构兼容方法）
    /// 为新的流水线架构提供统一的接口
    /// </summary>
    public async Task<GroupMessageProcessResult> CheckGroupMessageAiReplyTriggerAsync(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            // 🔧 新架构：直接调用现有的方法，保持业务逻辑一致
            var result = await ShouldTriggerAiReplyAsync(callbackMessage);

            // 🔧 新架构：增强结果信息，支持新的处理逻辑
            var messageType = callbackMessage.MessageType;

            // 根据消息类型和群聊模式，设置额外的处理标志
            if (messageType == MessageTypeConstants.GROUP_IMAGE || messageType == MessageTypeConstants.GROUP_FILE)
            {
                // 图片和文件消息在"仅@后回复"模式下可能需要等待文本消息
                if (!result.IsMentioned && result.IsConfigured)
                {
                    result.ShouldWaitForCombination = true;
                    result.Reason = $"媒体消息等待文本组合: {messageType}";
                }
            }
            else if (messageType == MessageTypeConstants.GROUP_VOICE)
            {
                // 语音消息的特殊处理逻辑
                if (!result.IsMentioned && result.IsConfigured)
                {
                    // 在"仅@后回复"模式下，语音消息应该跳过
                    result.ShouldReply = false;
                    result.Reason = "语音消息在仅@后回复模式下跳过";
                }
            }

            _logger.LogDebug("🔗 群消息AI回复检查完成 - ShouldReply: {ShouldReply}, ShouldWait: {ShouldWait}, Reason: {Reason}",
                result.ShouldReply, result.ShouldWaitForCombination, result.Reason);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群消息AI回复触发检查异常");
            return new GroupMessageProcessResult
            {
                ShouldReply = false,
                Reason = $"检查异常: {ex.Message}",
                MessageType = callbackMessage.MessageType ?? "unknown"
            };
        }
    }
}
