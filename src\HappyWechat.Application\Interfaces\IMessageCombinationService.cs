using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 消息组合服务接口 - 仅用于群组"仅@后回复"模式的30秒消息组合
/// </summary>
public interface IMessageCombinationService
{
    /// <summary>
    /// 处理可能需要组合的消息
    /// </summary>
    /// <param name="callbackMessage">微信回调消息</param>
    /// <param name="processingId">处理ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>
    /// ProcessImmediately: 立即处理（文本消息或单独媒体消息）
    /// WaitForCombination: 等待组合（媒体消息已缓存）
    /// CombinedMessage: 组合后的消息（媒体+文本）
    /// Skip: 跳过处理（语音消息在仅@后回复模式）
    /// </returns>
    Task<MessageCombinationResult> ProcessMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的缓存消息
    /// </summary>
    Task CleanupExpiredMessagesAsync();
}

/// <summary>
/// 消息组合结果
/// </summary>
public class MessageCombinationResult
{
    public MessageCombinationAction Action { get; set; }
    public WxCallbackMessageDto? Message { get; set; }
    public WxCallbackMessageDto? PreviousMediaMessage { get; set; }
    public string Reason { get; set; } = string.Empty;

    public static MessageCombinationResult ProcessImmediately(WxCallbackMessageDto message, string reason = "")
    {
        return new MessageCombinationResult
        {
            Action = MessageCombinationAction.ProcessImmediately,
            Message = message,
            Reason = reason
        };
    }

    public static MessageCombinationResult WaitForCombination(string reason = "")
    {
        return new MessageCombinationResult
        {
            Action = MessageCombinationAction.WaitForCombination,
            Reason = reason
        };
    }

    public static MessageCombinationResult CombinedMessage(WxCallbackMessageDto combinedMessage, WxCallbackMessageDto previousMedia, string reason = "")
    {
        return new MessageCombinationResult
        {
            Action = MessageCombinationAction.CombinedMessage,
            Message = combinedMessage,
            PreviousMediaMessage = previousMedia,
            Reason = reason
        };
    }

    public static MessageCombinationResult Skip(string reason = "")
    {
        return new MessageCombinationResult
        {
            Action = MessageCombinationAction.Skip,
            Reason = reason
        };
    }
}

/// <summary>
/// 消息组合动作
/// </summary>
public enum MessageCombinationAction
{
    /// <summary>
    /// 立即处理
    /// </summary>
    ProcessImmediately,
    
    /// <summary>
    /// 等待组合
    /// </summary>
    WaitForCombination,
    
    /// <summary>
    /// 组合后的消息
    /// </summary>
    CombinedMessage,
    
    /// <summary>
    /// 跳过处理
    /// </summary>
    Skip
}