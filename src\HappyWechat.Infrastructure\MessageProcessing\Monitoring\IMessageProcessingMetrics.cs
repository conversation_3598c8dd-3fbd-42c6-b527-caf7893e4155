using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Monitoring;

/// <summary>
/// 消息处理指标接口
/// </summary>
public interface IMessageProcessingMetrics
{
    /// <summary>
    /// 开始消息处理计时
    /// </summary>
    IDisposable StartMessageProcessing(string messageType, string wxManagerId);

    /// <summary>
    /// 记录消息处理成功
    /// </summary>
    void RecordMessageProcessingSuccess(string messageType, string wxManagerId, TimeSpan duration);

    /// <summary>
    /// 记录消息处理失败
    /// </summary>
    void RecordMessageProcessingFailure(string messageType, string wxManagerId, string errorType, TimeSpan duration);

    /// <summary>
    /// 记录队列消息数量
    /// </summary>
    void RecordQueueMessageCount(string queueName, int count);

    /// <summary>
    /// 记录AI处理时间
    /// </summary>
    void RecordAiProcessingTime(string aiAgentId, TimeSpan duration);

    /// <summary>
    /// 记录媒体处理时间
    /// </summary>
    void RecordMediaProcessingTime(string mediaType, TimeSpan duration);

    /// <summary>
    /// 增加消息计数器
    /// </summary>
    void IncrementMessageCounter(string messageType, string status);

    /// <summary>
    /// 获取处理统计信息
    /// </summary>
    Task<Dictionary<string, object>> GetProcessingStatisticsAsync();
}

/// <summary>
/// 消息处理监控服务接口
/// </summary>
public interface IMessageProcessingMonitorService
{
    /// <summary>
    /// 开始监控消息处理
    /// </summary>
    Task StartMonitoringAsync(string processingId, string messageType, string wxManagerId);

    /// <summary>
    /// 更新处理状态
    /// </summary>
    Task UpdateProcessingStatusAsync(string processingId, string status, Dictionary<string, object>? metadata = null);

    /// <summary>
    /// 完成监控
    /// </summary>
    Task CompleteMonitoringAsync(string processingId, bool success, string? errorMessage = null);

    /// <summary>
    /// 获取活跃的处理任务
    /// </summary>
    Task<List<ProcessingTask>> GetActiveProcessingTasksAsync();

    /// <summary>
    /// 获取处理历史
    /// </summary>
    Task<List<ProcessingTask>> GetProcessingHistoryAsync(DateTime from, DateTime to, int limit = 100);

    /// <summary>
    /// 记录消息丢弃
    /// </summary>
    Task RecordMessageDiscardAsync(string messageId, string? messageType, string wcId, string reason, string details);

    /// <summary>
    /// 记录配置变更
    /// </summary>
    Task RecordConfigurationChangeAsync(string configType, string changeDetails);
}

/// <summary>
/// 消息处理指标实现
/// </summary>
public class MessageProcessingMetrics : IMessageProcessingMetrics
{
    private readonly ILogger<MessageProcessingMetrics> _logger;
    private readonly Dictionary<string, long> _counters = new();
    private readonly Dictionary<string, List<TimeSpan>> _durations = new();
    private readonly object _lock = new();

    public MessageProcessingMetrics(ILogger<MessageProcessingMetrics> logger)
    {
        _logger = logger;
    }

    public IDisposable StartMessageProcessing(string messageType, string wxManagerId)
    {
        return new MessageProcessingTimer(this, messageType, wxManagerId);
    }

    public void RecordMessageProcessingSuccess(string messageType, string wxManagerId, TimeSpan duration)
    {
        lock (_lock)
        {
            var key = $"{messageType}_{wxManagerId}_success";
            _counters[key] = _counters.GetValueOrDefault(key, 0) + 1;
            
            var durationKey = $"{messageType}_{wxManagerId}_duration";
            if (!_durations.ContainsKey(durationKey))
                _durations[durationKey] = new List<TimeSpan>();
            _durations[durationKey].Add(duration);
        }

        _logger.LogDebug("消息处理成功 - MessageType: {MessageType}, WxManagerId: {WxManagerId}, Duration: {Duration}ms", 
            messageType, wxManagerId, duration.TotalMilliseconds);
    }

    public void RecordMessageProcessingFailure(string messageType, string wxManagerId, string errorType, TimeSpan duration)
    {
        lock (_lock)
        {
            var key = $"{messageType}_{wxManagerId}_failure_{errorType}";
            _counters[key] = _counters.GetValueOrDefault(key, 0) + 1;
        }

        _logger.LogWarning("消息处理失败 - MessageType: {MessageType}, WxManagerId: {WxManagerId}, ErrorType: {ErrorType}, Duration: {Duration}ms", 
            messageType, wxManagerId, errorType, duration.TotalMilliseconds);
    }

    public void RecordQueueMessageCount(string queueName, int count)
    {
        lock (_lock)
        {
            _counters[$"queue_{queueName}_count"] = count;
        }

        _logger.LogDebug("队列消息数量 - QueueName: {QueueName}, Count: {Count}", queueName, count);
    }

    public void RecordAiProcessingTime(string aiAgentId, TimeSpan duration)
    {
        lock (_lock)
        {
            var key = $"ai_{aiAgentId}_duration";
            if (!_durations.ContainsKey(key))
                _durations[key] = new List<TimeSpan>();
            _durations[key].Add(duration);
        }

        _logger.LogDebug("AI处理时间 - AiAgentId: {AiAgentId}, Duration: {Duration}ms", 
            aiAgentId, duration.TotalMilliseconds);
    }

    public void RecordMediaProcessingTime(string mediaType, TimeSpan duration)
    {
        lock (_lock)
        {
            var key = $"media_{mediaType}_duration";
            if (!_durations.ContainsKey(key))
                _durations[key] = new List<TimeSpan>();
            _durations[key].Add(duration);
        }

        _logger.LogDebug("媒体处理时间 - MediaType: {MediaType}, Duration: {Duration}ms", 
            mediaType, duration.TotalMilliseconds);
    }

    public void IncrementMessageCounter(string messageType, string status)
    {
        lock (_lock)
        {
            var key = $"{messageType}_{status}";
            _counters[key] = _counters.GetValueOrDefault(key, 0) + 1;
        }
    }

    public async Task<Dictionary<string, object>> GetProcessingStatisticsAsync()
    {
        lock (_lock)
        {
            var statistics = new Dictionary<string, object>();
            
            // 复制计数器
            foreach (var counter in _counters)
            {
                statistics[counter.Key] = counter.Value;
            }

            // 计算平均处理时间
            foreach (var duration in _durations)
            {
                if (duration.Value.Count > 0)
                {
                    var avgMs = duration.Value.Average(d => d.TotalMilliseconds);
                    statistics[$"{duration.Key}_avg"] = avgMs;
                }
            }

            statistics["LastUpdated"] = DateTime.UtcNow;
            return statistics;
        }
    }

    private class MessageProcessingTimer : IDisposable
    {
        private readonly MessageProcessingMetrics _metrics;
        private readonly string _messageType;
        private readonly string _wxManagerId;
        private readonly DateTime _startTime;
        private bool _disposed;

        public MessageProcessingTimer(MessageProcessingMetrics metrics, string messageType, string wxManagerId)
        {
            _metrics = metrics;
            _messageType = messageType;
            _wxManagerId = wxManagerId;
            _startTime = DateTime.UtcNow;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                var duration = DateTime.UtcNow - _startTime;
                _metrics.RecordMessageProcessingSuccess(_messageType, _wxManagerId, duration);
                _disposed = true;
            }
        }
    }
}

/// <summary>
/// 处理任务信息
/// </summary>
public class ProcessingTask
{
    public string ProcessingId { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public string WxManagerId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration => EndTime?.Subtract(StartTime);
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 消息处理监控服务实现
/// </summary>
public class MessageProcessingMonitorService : IMessageProcessingMonitorService
{
    private readonly ILogger<MessageProcessingMonitorService> _logger;
    private readonly Dictionary<string, ProcessingTask> _activeTasks = new();
    private readonly List<ProcessingTask> _completedTasks = new();
    private readonly object _lock = new();

    public MessageProcessingMonitorService(ILogger<MessageProcessingMonitorService> logger)
    {
        _logger = logger;
    }

    public async Task StartMonitoringAsync(string processingId, string messageType, string wxManagerId)
    {
        lock (_lock)
        {
            _activeTasks[processingId] = new ProcessingTask
            {
                ProcessingId = processingId,
                MessageType = messageType,
                WxManagerId = wxManagerId,
                Status = "Started",
                StartTime = DateTime.UtcNow
            };
        }
        await Task.CompletedTask;
    }

    public async Task UpdateProcessingStatusAsync(string processingId, string status, Dictionary<string, object>? metadata = null)
    {
        lock (_lock)
        {
            if (_activeTasks.TryGetValue(processingId, out var task))
            {
                task.Status = status;
                if (metadata != null)
                {
                    foreach (var kvp in metadata)
                    {
                        task.Metadata[kvp.Key] = kvp.Value;
                    }
                }
            }
        }
        await Task.CompletedTask;
    }

    public async Task CompleteMonitoringAsync(string processingId, bool success, string? errorMessage = null)
    {
        lock (_lock)
        {
            if (_activeTasks.TryGetValue(processingId, out var task))
            {
                task.EndTime = DateTime.UtcNow;
                task.Success = success;
                task.ErrorMessage = errorMessage;
                task.Status = success ? "Completed" : "Failed";

                _activeTasks.Remove(processingId);
                _completedTasks.Add(task);

                // 保持最近1000个完成的任务
                if (_completedTasks.Count > 1000)
                {
                    _completedTasks.RemoveAt(0);
                }
            }
        }
        await Task.CompletedTask;
    }

    public async Task<List<ProcessingTask>> GetActiveProcessingTasksAsync()
    {
        lock (_lock)
        {
            return new List<ProcessingTask>(_activeTasks.Values);
        }
    }

    public async Task<List<ProcessingTask>> GetProcessingHistoryAsync(DateTime from, DateTime to, int limit = 100)
    {
        lock (_lock)
        {
            return _completedTasks
                .Where(t => t.StartTime >= from && t.StartTime <= to)
                .OrderByDescending(t => t.StartTime)
                .Take(limit)
                .ToList();
        }
    }

    public async Task RecordMessageDiscardAsync(string messageId, string? messageType, string wcId, string reason, string details)
    {
        _logger.LogWarning("消息被丢弃 - MessageId: {MessageId}, MessageType: {MessageType}, WcId: {WcId}, Reason: {Reason}, Details: {Details}",
            messageId, messageType, wcId, reason, details);
        await Task.CompletedTask;
    }

    public async Task RecordConfigurationChangeAsync(string configType, string changeDetails)
    {
        _logger.LogInformation("配置变更记录 - ConfigType: {ConfigType}, Details: {Details}",
            configType, changeDetails);
        await Task.CompletedTask;
    }
}
