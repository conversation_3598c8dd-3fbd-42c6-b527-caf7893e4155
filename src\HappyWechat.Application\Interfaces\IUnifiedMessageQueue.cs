using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 统一消息队列接口
/// </summary>
public interface IUnifiedMessageQueue
{
    /// <summary>
    /// 入队AI请求
    /// </summary>
    Task<string> EnqueueAiRequestAsync(
        Guid wxManagerId, 
        string aiTemplate, 
        WxCallbackMessageDto originalMessage, 
        string processingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 入队媒体处理请求
    /// </summary>
    Task<string> EnqueueMediaProcessingAsync(
        Guid wxManagerId, 
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 出队媒体请求
    /// </summary>
    Task<List<UnifiedMediaRequest>> DequeueMediaRequestsAsync(
        Guid wxManagerId, 
        int maxCount, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 出队AI请求
    /// </summary>
    Task<List<UnifiedAiRequest>> DequeueAiRequestsAsync(
        Guid wxManagerId, 
        int maxCount, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 标记任务完成
    /// </summary>
    Task MarkTaskCompletedAsync(
        Guid wxManagerId, 
        string taskId, 
        bool success, 
        string? errorMessage, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 统一媒体请求
/// </summary>
public class UnifiedMediaRequest
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public Guid WxManagerId { get; set; }
    public WxCallbackMessageDto? CallbackMessage { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public int Priority { get; set; } = 5;
}

/// <summary>
/// 统一AI请求
/// </summary>
public class UnifiedAiRequest
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public Guid WxManagerId { get; set; }
    public string AiTemplate { get; set; } = string.Empty;
    public WxCallbackMessageDto? OriginalMessage { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public int Priority { get; set; } = 5;
}