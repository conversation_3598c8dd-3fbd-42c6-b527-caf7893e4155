# 用户上下文注入使用指南

## 概述

本项目现在支持基于控制器的用户上下文注入，提供了更优雅和类型安全的用户身份管理方式。

## 架构组件

### 1. 核心接口和实现

- **`ICurrentUserContext`**: 用户上下文接口，提供统一的用户身份信息访问
- **`CurrentUserContext`**: 基于ASP.NET Core的用户上下文实现
- **双重保障**: 同时支持HTTP请求头和用户上下文注入两种方式

### 2. 基础控制器类

#### `BaseApiController`
```csharp
public abstract class BaseApiController : ControllerBase
{
    protected readonly ICurrentUserContext UserContext;
    protected Guid CurrentUserId => UserContext.UserId;
    protected string? CurrentUsername => UserContext.Username;
    // ... 更多便捷属性和方法
}
```

#### `BaseAuthenticatedController`
```csharp
public abstract class BaseAuthenticatedController : ControllerBase
{
    // 自动验证用户认证状态
    // 提供 ExecuteAuthenticatedOperation 和 ExecuteRoleBasedOperation 方法
}
```

### 3. 中间件支持

- **`UserIdHeaderMiddleware`**: 将用户身份从Claims转换为HTTP请求头（向后兼容）
- **`UserContextValidationMiddleware`**: 验证用户上下文的完整性

## 使用方式

### 方式一：继承BaseAuthenticatedController（推荐）

```csharp
[Route("api/my-feature")]
public class MyFeatureController : BaseAuthenticatedController
{
    private readonly IMyService _myService;

    public MyFeatureController(
        IMyService myService,
        ICurrentUserContext userContext,
        ILogger<MyFeatureController> logger) : base(userContext, logger)
    {
        _myService = myService;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<MyData>>> GetData()
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetMyData");
            
            // 直接使用 CurrentUserId，已确保用户认证
            var data = await _myService.GetDataAsync(CurrentUserId);
            
            return data;
        });
    }
}
```

### 方式二：继承BaseApiController（更灵活）

```csharp
[Route("api/my-feature")]
public class MyFeatureController : BaseApiController
{
    public MyFeatureController(
        ICurrentUserContext userContext,
        ILogger<MyFeatureController> logger) : base(userContext, logger)
    {
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<MyData>>> GetData()
    {
        try
        {
            // 手动验证认证状态
            var authResult = EnsureAuthenticated<MyData>();
            if (authResult != null) return authResult;

            // 执行业务逻辑
            var data = await GetMyDataAsync(CurrentUserId);
            
            return Success(data, "获取数据成功");
        }
        catch (Exception ex)
        {
            return HandleException<MyData>(ex, "获取数据失败");
        }
    }
}
```

### 方式三：直接注入ICurrentUserContext

```csharp
[ApiController]
[Route("api/my-feature")]
public class MyFeatureController : ControllerBase
{
    private readonly ICurrentUserContext _userContext;
    private readonly IMyService _myService;

    public MyFeatureController(
        ICurrentUserContext userContext,
        IMyService myService)
    {
        _userContext = userContext;
        _myService = myService;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<MyData>>> GetData()
    {
        // 确保用户已认证
        _userContext.EnsureAuthenticated();
        
        var data = await _myService.GetDataAsync(_userContext.UserId);
        
        return Ok(ApiResponse<MyData>.Success(data));
    }
}
```

## 用户上下文功能

### 基本属性
```csharp
Guid UserId                    // 用户ID
string? Username               // 用户名
string? DisplayName            // 显示名称
string? Email                  // 邮箱
IReadOnlyList<string> Roles    // 角色列表
bool IsAuthenticated           // 是否已认证
```

### 角色验证方法
```csharp
bool HasRole(string role)                     // 是否有指定角色
bool HasAnyRole(params string[] roles)       // 是否有任一角色
bool HasAllRoles(params string[] roles)      // 是否有所有角色
```

### 安全验证方法
```csharp
void EnsureAuthenticated()      // 确保已认证，否则抛异常
void EnsureRole(string role)    // 确保有指定角色，否则抛异常
```

## 兼容性

### 向后兼容
现有的基于HTTP请求头的代码仍然可以正常工作：
```csharp
// 这种方式仍然有效
Guid userId = Guid.TryParse(Request.Headers[MyHttpHeaders.UserId], out var guid) ? guid : Guid.Empty;
```

### HTTP请求头
中间件会自动设置以下请求头：
- `Happy-User-Id`: 用户ID
- `Happy-Username`: 用户名
- `Happy-Display-Name`: 显示名称
- `Happy-Roles`: 角色列表（逗号分隔）
- `Happy-Email`: 邮箱

## 最佳实践

### 1. 选择合适的基类
- **新的API控制器**: 使用 `BaseAuthenticatedController`
- **需要灵活控制**: 使用 `BaseApiController`
- **特殊需求**: 直接注入 `ICurrentUserContext`

### 2. 错误处理
```csharp
public async Task<ActionResult<ApiResponse<T>>> MyAction()
{
    return await ExecuteAuthenticatedOperation(async () =>
    {
        // 业务逻辑
        // 异常会被自动捕获和处理
        return result;
    });
}
```

### 3. 日志记录
```csharp
LogOperation("MyAction", new { SomeParameter = value });
```

### 4. 角色验证
```csharp
// 需要管理员权限的操作
return await ExecuteRoleBasedOperation("Admin", async () =>
{
    // 只有管理员可以执行的操作
    return result;
});
```

## 调试和诊断

### 会话信息端点
```
GET /api/user-profile/session
```
返回当前用户的详细会话信息，用于调试认证问题。

### 日志输出
中间件会记录详细的用户上下文信息，方便问题排查：
```
✅ 用户上下文验证通过 - UserId: {UserId}, Username: {Username}, Path: {Path}
```

## 迁移指南

### 从旧方式迁移

**旧代码**:
```csharp
public async Task<ActionResult> MyAction()
{
    Guid userId = Guid.TryParse(Request.Headers[MyHttpHeaders.UserId], out var guid) ? guid : Guid.Empty;
    if (userId == Guid.Empty)
    {
        return BadRequest("用户未认证");
    }
    
    // 业务逻辑
    var result = await _service.GetDataAsync(userId);
    return Ok(ApiResponse<T>.Success(result));
}
```

**新代码**:
```csharp
public async Task<ActionResult<ApiResponse<T>>> MyAction()
{
    return await ExecuteAuthenticatedOperation(async () =>
    {
        // 直接使用 CurrentUserId，无需手动验证
        return await _service.GetDataAsync(CurrentUserId);
    });
}
```

## 注意事项

1. **服务注册**: `ICurrentUserContext` 已注册为 Scoped 服务
2. **中间件顺序**: 用户上下文中间件在认证之后，授权之前执行
3. **性能**: 用户信息会被缓存，避免重复解析Claims
4. **安全**: 所有用户身份验证都经过标准化处理
5. **兼容性**: 与现有的请求头方式完全兼容

## 示例项目

参考以下示例控制器：
- `AiAgentControllerV2`: 使用 `BaseAuthenticatedController` 的完整示例
- `UserProfileController`: 使用 `BaseApiController` 的灵活示例

这些示例展示了不同场景下的最佳实践。