/**
 * Blazor认证助手 - 确保Blazor Server SignalR连接获得正确的认证
 */
window.blazorAuthHelper = {
    /**
     * 🔧 初始化Blazor认证 - 设置SignalR连接的access_token
     */
    init: function() {
        console.log('🔑 初始化Blazor认证助手...');
        
        try {
            // 获取SessionId
            const sessionId = this.getSessionId();
            
            if (sessionId) {
                // 设置Blazor SignalR连接的access_token
                this.setSignalRAccessToken(sessionId);
                console.log('✅ 已为Blazor SignalR设置access_token');
            } else {
                console.warn('⚠️ 未找到SessionId，Blazor SignalR连接可能无法认证');
            }
        } catch (error) {
            console.error('❌ 初始化Blazor认证助手失败:', error);
        }
    },

    /**
     * 🔧 获取SessionId
     */
    getSessionId: function() {
        // 1. 从Redis认证管理器获取
        if (window.redisAuthManager && typeof window.redisAuthManager.getSessionId === 'function') {
            const sessionId = window.redisAuthManager.getSessionId();
            if (sessionId) return sessionId;
        }

        // 2. 从统一会话管理器获取
        if (window.unifiedSessionManager && typeof window.unifiedSessionManager.getSessionId === 'function') {
            const sessionId = window.unifiedSessionManager.getSessionId();
            if (sessionId) return sessionId;
        }

        // 3. 从localStorage获取
        try {
            const stored = localStorage.getItem('hw_session_id');
            if (stored) return stored;
        } catch (error) {
            console.warn('⚠️ 无法从localStorage获取sessionId:', error);
        }

        // 4. 从Cookie获取
        const value = `; ${document.cookie}`;
        const parts = value.split(`; HW_SessionId=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }

        return null;
    },

    /**
     * 🔧 设置SignalR连接的access_token
     */
    setSignalRAccessToken: function(sessionId) {
        try {
            // 如果Blazor还没有启动，等待启动
            if (typeof Blazor === 'undefined') {
                // 延迟执行
                setTimeout(() => this.setSignalRAccessToken(sessionId), 100);
                return;
            }

            // 重写Blazor的连接配置
            if (Blazor && Blazor.defaultReconnectionHandler) {
                const originalStart = Blazor.start;
                Blazor.start = function(options) {
                    options = options || {};
                    options.configureSignalR = function(builder) {
                        // 设置access_token查询参数
                        builder.withUrl("/_blazor", {
                            accessTokenFactory: () => sessionId
                        });
                    };
                    return originalStart.call(this, options);
                };
            }

            // 如果已经连接，尝试重新连接
            if (typeof window.blazorReconnect === 'function') {
                window.blazorReconnect();
            }

        } catch (error) {
            console.error('❌ 设置SignalR access_token失败:', error);
        }
    },

    /**
     * 🔧 更新认证状态
     */
    updateAuthState: function(sessionId) {
        console.log('🔄 更新Blazor认证状态...');
        
        if (sessionId) {
            this.setSignalRAccessToken(sessionId);
        }
        
        // 通知Blazor组件刷新认证状态
        if (typeof DotNet !== 'undefined') {
            try {
                DotNet.invokeMethodAsync('HappyWechat.Web', 'RefreshAuthenticationState');
            } catch (error) {
                console.warn('⚠️ 无法调用Blazor认证状态刷新方法:', error);
            }
        }
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    window.blazorAuthHelper.init();
});

// 注册到全局
console.log('🔑 AuthHelper已注册到window.blazorAuthHelper');