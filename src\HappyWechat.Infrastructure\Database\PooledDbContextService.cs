using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Identity.Repositories;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 池化DbContext服务实现
/// 提供高性能、线程安全的数据库访问
/// </summary>
public class PooledDbContextService : IPooledDbContextService, IDisposable
{
    private readonly IDbContextFactory<ApplicationDbContext> _pooledDbContextFactory;
    private readonly ILogger<PooledDbContextService> _logger;
    
    // 性能监控
    private readonly ConcurrentDictionary<string, OperationMetrics> _operationMetrics = new();
    private long _totalOperations;
    private long _successfulOperations;
    private long _failedOperations;
    private readonly object _statisticsLock = new();
    
    public PooledDbContextService(
        IDbContextFactory<ApplicationDbContext> pooledDbContextFactory,
        ILogger<PooledDbContextService> logger)
    {
        _pooledDbContextFactory = pooledDbContextFactory ?? throw new ArgumentNullException(nameof(pooledDbContextFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 获取池化的DbContext实例
    /// 注意：调用者负责释放返回的DbContext
    /// </summary>
    public async Task<DbContext> GetDbContextAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var context = await _pooledDbContextFactory.CreateDbContextAsync();
            
            RecordOperation("GetDbContext", stopwatch.Elapsed, true);
            
            _logger.LogTrace("从池中获取DbContext成功，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            
            return context;
        }
        catch (Exception ex)
        {
            RecordOperation("GetDbContext", stopwatch.Elapsed, false);
            _logger.LogError(ex, "从池中获取DbContext失败，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 执行查询操作（自动管理DbContext生命周期）
    /// </summary>
    public async Task<TResult> ExecuteQueryAsync<TResult>(Func<DbContext, Task<TResult>> query)
    {
        if (query == null) throw new ArgumentNullException(nameof(query));
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _pooledDbContextFactory.CreateDbContextAsync();
            
            // 查询优化配置
            context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
            context.ChangeTracker.AutoDetectChangesEnabled = false;
            
            var result = await query(context);
            
            RecordOperation("ExecuteQuery", stopwatch.Elapsed, true);
            
            _logger.LogTrace("查询操作执行成功，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            RecordOperation("ExecuteQuery", stopwatch.Elapsed, false);
            _logger.LogError(ex, "查询操作执行失败，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 执行命令操作（自动管理DbContext生命周期和事务）
    /// </summary>
    public async Task<TResult> ExecuteCommandAsync<TResult>(Func<DbContext, Task<TResult>> command)
    {
        if (command == null) throw new ArgumentNullException(nameof(command));
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _pooledDbContextFactory.CreateDbContextAsync();
            
            // 使用事务确保数据一致性
            await using var transaction = await context.Database.BeginTransactionAsync();
            
            try
            {
                var result = await command(context);
                await transaction.CommitAsync();
                
                RecordOperation("ExecuteCommand", stopwatch.Elapsed, true);
                
                _logger.LogTrace("命令操作执行成功，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
                
                return result;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            RecordOperation("ExecuteCommand", stopwatch.Elapsed, false);
            _logger.LogError(ex, "命令操作执行失败，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 批量操作（高性能批处理）
    /// </summary>
    public async Task ExecuteBatchAsync(Func<DbContext, Task> batchOperation)
    {
        if (batchOperation == null) throw new ArgumentNullException(nameof(batchOperation));
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await using var context = await _pooledDbContextFactory.CreateDbContextAsync();
            
            // 批量操作优化
            context.ChangeTracker.AutoDetectChangesEnabled = false;
            
            await using var transaction = await context.Database.BeginTransactionAsync();
            
            try
            {
                await batchOperation(context);
                
                // 检测变更并保存
                context.ChangeTracker.DetectChanges();
                await context.SaveChangesAsync();
                
                await transaction.CommitAsync();
                
                RecordOperation("ExecuteBatch", stopwatch.Elapsed, true);
                
                _logger.LogTrace("批量操作执行成功，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            RecordOperation("ExecuteBatch", stopwatch.Elapsed, false);
            _logger.LogError(ex, "批量操作执行失败，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 获取池化统计信息
    /// </summary>
    public PooledDbContextStatistics GetStatistics()
    {
        lock (_statisticsLock)
        {
            var totalOperations = _totalOperations;
            var successfulOperations = _successfulOperations;
            var failedOperations = _failedOperations;
            var averageTime = 0.0;
            
            if (_operationMetrics.Any())
            {
                var allExecutionTimes = _operationMetrics.Values
                    .SelectMany(m => m.ExecutionTimes)
                    .ToList();
                    
                if (allExecutionTimes.Any())
                {
                    averageTime = allExecutionTimes.Average(t => t.TotalMilliseconds);
                }
            }
            
            return new PooledDbContextStatistics
            {
                PoolSize = 128, // 从配置获取，这里暂时硬编码
                ActiveConnections = 0, // 需要从连接池获取实际值
                AvailableConnections = 0, // 需要从连接池获取实际值
                TotalOperations = totalOperations,
                SuccessfulOperations = successfulOperations,
                FailedOperations = failedOperations,
                AverageOperationTime = averageTime,
                LastStatisticsUpdate = DateTime.UtcNow
            };
        }
    }
    
    /// <summary>
    /// 记录操作性能指标
    /// </summary>
    private void RecordOperation(string operationType, TimeSpan duration, bool success)
    {
        Interlocked.Increment(ref _totalOperations);
        
        if (success)
        {
            Interlocked.Increment(ref _successfulOperations);
        }
        else
        {
            Interlocked.Increment(ref _failedOperations);
        }
        
        _operationMetrics.AddOrUpdate(operationType, 
            new OperationMetrics(), 
            (key, existing) =>
            {
                existing.AddOperation(duration, success);
                return existing;
            });
    }

    public void Dispose()
    {
        // 清理性能监控数据
        _operationMetrics.Clear();
        
        _logger.LogInformation("PooledDbContextService已释放，总操作数: {TotalOperations}, 成功: {Success}, 失败: {Failed}", 
            _totalOperations, _successfulOperations, _failedOperations);
    }
}

/// <summary>
/// 操作性能指标
/// </summary>
internal class OperationMetrics
{
    private readonly Queue<TimeSpan> _executionTimes = new();
    private readonly object _lock = new();
    private long _successCount;
    private long _failureCount;
    
    public IEnumerable<TimeSpan> ExecutionTimes 
    { 
        get 
        { 
            lock (_lock) 
            { 
                return _executionTimes.ToArray(); 
            } 
        } 
    }
    
    public long SuccessCount => _successCount;
    public long FailureCount => _failureCount;
    
    public void AddOperation(TimeSpan duration, bool success)
    {
        lock (_lock)
        {
            _executionTimes.Enqueue(duration);
            
            // 只保留最近100次操作的记录
            while (_executionTimes.Count > 100)
            {
                _executionTimes.Dequeue();
            }
            
            if (success)
                Interlocked.Increment(ref _successCount);
            else
                Interlocked.Increment(ref _failureCount);
        }
    }
}