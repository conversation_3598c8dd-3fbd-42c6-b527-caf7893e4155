using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.DTOs.Responses;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis统一认证服务接口
/// </summary>
public interface IRedisAuthenticationService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="loginQuery">登录请求</param>
    /// <returns>登录结果</returns>
    Task<RedisAuthenticationResult> LoginAsync(LoginQuery loginQuery);

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否成功登出</returns>
    Task<bool> LogoutAsync(string sessionId);

    /// <summary>
    /// 验证会话并获取认证状态
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>认证状态</returns>
    Task<AuthenticationState> GetAuthenticationStateAsync(string sessionId);

    /// <summary>
    /// 检查用户是否已认证
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否已认证</returns>
    Task<bool> IsAuthenticatedAsync(string sessionId);

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>用户信息</returns>
    Task<ClaimsPrincipal?> GetCurrentUserAsync(string sessionId);

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="role">角色名称</param>
    /// <returns>是否具有该角色</returns>
    Task<bool> IsInRoleAsync(string sessionId, string role);

    /// <summary>
    /// 检查用户是否具有指定权限
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="permission">权限名称</param>
    /// <returns>是否具有该权限</returns>
    Task<bool> HasPermissionAsync(string sessionId, string permission);

    /// <summary>
    /// 续期会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <returns>是否成功续期</returns>
    Task<bool> RenewSessionAsync(string sessionId);

    /// <summary>
    /// 强制用户登出（管理员功能）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>登出的会话数量</returns>
    Task<int> ForceLogoutUserAsync(string userId);

    /// <summary>
    /// 获取用户的活跃会话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>会话信息列表</returns>
    Task<IEnumerable<RedisSessionInfo>> GetUserActiveSessionsAsync(string userId);

    /// <summary>
    /// 从HTTP上下文获取会话ID
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <returns>会话ID</returns>
    string? GetSessionIdFromContext(HttpContext httpContext);

    /// <summary>
    /// 设置会话ID到HTTP上下文
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <param name="sessionId">会话ID</param>
    void SetSessionIdToContext(HttpContext httpContext, string sessionId);

    /// <summary>
    /// 清除HTTP上下文中的会话ID
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    void ClearSessionIdFromContext(HttpContext httpContext);
}

/// <summary>
/// Redis认证结果
/// </summary>
public class RedisAuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? SessionId { get; set; }
    public string? UserId { get; set; }
    public string? Username { get; set; }
    public IEnumerable<string> Roles { get; set; } = new List<string>();
    public IEnumerable<string> Permissions { get; set; } = new List<string>();
    public DateTime ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }

    public static RedisAuthenticationResult Success(string sessionId, string userId, string username, 
        IEnumerable<string> roles, IEnumerable<string> permissions, DateTime expiresAt)
    {
        return new RedisAuthenticationResult
        {
            IsSuccess = true,
            SessionId = sessionId,
            UserId = userId,
            Username = username,
            Roles = roles,
            Permissions = permissions,
            ExpiresAt = expiresAt
        };
    }

    public static RedisAuthenticationResult Failure(string errorMessage)
    {
        return new RedisAuthenticationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}
