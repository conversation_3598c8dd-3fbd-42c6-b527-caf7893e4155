namespace HappyWechat.Application.DTOs.Monitoring;

/// <summary>
/// RabbitMQ指标DTO
/// </summary>
public class RabbitMQMetricsDto
{
    /// <summary>
    /// 收集时间
    /// </summary>
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 连接状态
    /// </summary>
    public RabbitMQConnectionStatusDto ConnectionStatus { get; set; } = new();
    
    /// <summary>
    /// 队列指标
    /// </summary>
    public List<RabbitMQQueueMetricsDto> QueueMetrics { get; set; } = new();
    
    /// <summary>
    /// 性能统计
    /// </summary>
    public RabbitMQPerformanceDto Performance { get; set; } = new();
    
    /// <summary>
    /// 健康状态
    /// </summary>
    public RabbitMQHealthStatus HealthStatus { get; set; } = RabbitMQHealthStatus.Unknown;
    
    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// RabbitMQ连接状态DTO
/// </summary>
public class RabbitMQConnectionStatusDto
{
    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }
    
    /// <summary>
    /// 主机名
    /// </summary>
    public string HostName { get; set; } = string.Empty;
    
    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// 虚拟主机
    /// </summary>
    public string VirtualHost { get; set; } = string.Empty;
    
    /// <summary>
    /// 连接开始时间
    /// </summary>
    public DateTime? ConnectedSince { get; set; }
    
    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan? ConnectionUptime { get; set; }
    
    /// <summary>
    /// 最后连接时间
    /// </summary>
    public DateTime? LastConnectedTime { get; set; }
    
    /// <summary>
    /// 最后断开时间
    /// </summary>
    public DateTime? LastDisconnectedTime { get; set; }
    
    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectionCount { get; set; }
    
    /// <summary>
    /// 连接错误信息
    /// </summary>
    public string? ConnectionError { get; set; }
}

/// <summary>
/// RabbitMQ队列指标DTO
/// </summary>
public class RabbitMQQueueMetricsDto
{
    /// <summary>
    /// 队列名称
    /// </summary>
    public string QueueName { get; set; } = string.Empty;
    
    /// <summary>
    /// 当前消息数量
    /// </summary>
    public uint MessageCount { get; set; }
    
    /// <summary>
    /// 消费者数量
    /// </summary>
    public uint ConsumerCount { get; set; }
    
    /// <summary>
    /// 总发布消息数
    /// </summary>
    public long TotalPublished { get; set; }
    
    /// <summary>
    /// 总消费消息数
    /// </summary>
    public long TotalConsumed { get; set; }
    
    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }
    
    /// <summary>
    /// 总发布字节数
    /// </summary>
    public long TotalBytesPublished { get; set; }
    
    /// <summary>
    /// 平均处理时间
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }
    
    /// <summary>
    /// 每分钟消息数
    /// </summary>
    public long MessagesPerMinute { get; set; }
    
    /// <summary>
    /// 错误率（百分比）
    /// </summary>
    public double ErrorRate { get; set; }
    
    /// <summary>
    /// 队列状态
    /// </summary>
    public QueueHealthStatus Status { get; set; } = QueueHealthStatus.Unknown;
    
    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }
    
    /// <summary>
    /// 队列特定警告
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// RabbitMQ性能DTO
/// </summary>
public class RabbitMQPerformanceDto
{
    /// <summary>
    /// 总发布消息数
    /// </summary>
    public long TotalMessagesPublished { get; set; }
    
    /// <summary>
    /// 总消费消息数
    /// </summary>
    public long TotalMessagesConsumed { get; set; }
    
    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }
    
    /// <summary>
    /// 平均处理时间
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }
    
    /// <summary>
    /// 吞吐量（每分钟消息数）
    /// </summary>
    public long ThroughputPerMinute { get; set; }
    
    /// <summary>
    /// 总体错误率（百分比）
    /// </summary>
    public double OverallErrorRate { get; set; }
    
    /// <summary>
    /// 峰值吞吐量
    /// </summary>
    public long PeakThroughput { get; set; }
    
    /// <summary>
    /// 峰值吞吐量时间
    /// </summary>
    public DateTime? PeakThroughputTime { get; set; }
    
    /// <summary>
    /// 最长处理时间
    /// </summary>
    public TimeSpan MaxProcessingTime { get; set; }
    
    /// <summary>
    /// 最短处理时间
    /// </summary>
    public TimeSpan MinProcessingTime { get; set; }
    
    /// <summary>
    /// 过去24小时统计
    /// </summary>
    public RabbitMQDailyStatsDto DailyStats { get; set; } = new();
}

/// <summary>
/// RabbitMQ每日统计DTO
/// </summary>
public class RabbitMQDailyStatsDto
{
    /// <summary>
    /// 统计日期
    /// </summary>
    public DateTime Date { get; set; } = DateTime.Today;
    
    /// <summary>
    /// 当日发布消息数
    /// </summary>
    public long MessagesPublishedToday { get; set; }
    
    /// <summary>
    /// 当日消费消息数
    /// </summary>
    public long MessagesConsumedToday { get; set; }
    
    /// <summary>
    /// 当日错误数
    /// </summary>
    public long ErrorsToday { get; set; }
    
    /// <summary>
    /// 当日平均处理时间
    /// </summary>
    public TimeSpan AverageProcessingTimeToday { get; set; }
    
    /// <summary>
    /// 当日峰值吞吐量
    /// </summary>
    public long PeakThroughputToday { get; set; }
    
    /// <summary>
    /// 当日峰值时间
    /// </summary>
    public DateTime? PeakTimeToday { get; set; }
    
    /// <summary>
    /// 小时统计（24小时）
    /// </summary>
    public List<RabbitMQHourlyStatsDto> HourlyStats { get; set; } = new();
}

/// <summary>
/// RabbitMQ小时统计DTO
/// </summary>
public class RabbitMQHourlyStatsDto
{
    /// <summary>
    /// 小时（0-23）
    /// </summary>
    public int Hour { get; set; }
    
    /// <summary>
    /// 该小时发布消息数
    /// </summary>
    public long MessagesPublished { get; set; }
    
    /// <summary>
    /// 该小时消费消息数
    /// </summary>
    public long MessagesConsumed { get; set; }
    
    /// <summary>
    /// 该小时错误数
    /// </summary>
    public long Errors { get; set; }
    
    /// <summary>
    /// 该小时平均处理时间
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }
}

/// <summary>
/// RabbitMQ健康状态
/// </summary>
public enum RabbitMQHealthStatus
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 健康
    /// </summary>
    Healthy = 1,
    
    /// <summary>
    /// 警告
    /// </summary>
    Warning = 2,
    
    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy = 3,
    
    /// <summary>
    /// 严重错误
    /// </summary>
    Critical = 4
}

/// <summary>
/// 队列健康状态
/// </summary>
public enum QueueHealthStatus
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 健康
    /// </summary>
    Healthy = 1,
    
    /// <summary>
    /// 警告（消息积压）
    /// </summary>
    Warning = 2,
    
    /// <summary>
    /// 不健康（严重积压或错误）
    /// </summary>
    Unhealthy = 3,
    
    /// <summary>
    /// 队列不可用
    /// </summary>
    Unavailable = 4
}
