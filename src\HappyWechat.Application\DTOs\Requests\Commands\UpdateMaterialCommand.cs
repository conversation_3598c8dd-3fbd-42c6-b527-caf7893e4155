using System.ComponentModel.DataAnnotations;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 更新素材命令
/// </summary>
public class UpdateMaterialCommand
{
    /// <summary>
    /// 素材ID
    /// </summary>
    [Required]
    public Guid Id { get; set; }
    
    /// <summary>
    /// 分类ID
    /// </summary>
    public Guid? CategoryId { get; set; }
    
    /// <summary>
    /// 素材名称/标题
    /// </summary>
    [Required]
    [StringLength(100, ErrorMessage = "素材名称不能超过100个字符")]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 素材描述
    /// </summary>
    [StringLength(500, ErrorMessage = "素材描述不能超过500个字符")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 文字内容（用于文字素材和链接素材）
    /// </summary>
    public string? Content { get; set; }
    
    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}
