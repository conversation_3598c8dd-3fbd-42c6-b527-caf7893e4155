using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using System.Diagnostics;
using System.Text.Json;

namespace HappyWechat.Infrastructure.IdManagement;

/// <summary>
/// 统一ID管理器实现 - 高性能、多级缓存、批量操作优化
/// </summary>
public class UnifiedIdManager : IUnifiedIdManager
{
    private readonly IMemoryCache _localCache;
    private readonly IDistributedCache _distributedCache;
    private readonly IWxManagerRepository _repository;
    private readonly ILogger<UnifiedIdManager> _logger;
    private readonly SemaphoreSlim _batchLock = new(1, 1);
    private readonly SemaphoreSlim _warmupLock = new(1, 1);

    // 缓存配置
    private readonly TimeSpan _localCacheExpiry = TimeSpan.FromMinutes(5);
    private readonly TimeSpan _distributedCacheExpiry = TimeSpan.FromHours(1);
    private readonly int _maxBatchSize = 100;

    // 性能监控
    private long _totalRequests = 0;
    private long _cacheHits = 0;
    private readonly List<double> _responseTimes = new();

    public UnifiedIdManager(
        IMemoryCache localCache,
        IDistributedCache distributedCache,
        IWxManagerRepository repository,
        ILogger<UnifiedIdManager> logger)
    {
        _localCache = localCache;
        _distributedCache = distributedCache;
        _repository = repository;
        _logger = logger;
    }

    public async Task<Guid?> ResolveWxManagerIdAsync(string wcId)
    {
        if (string.IsNullOrEmpty(wcId))
        {
            return null;
        }

        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalRequests);

        try
        {
            // L1: 内存缓存
            var cacheKey = GetWcIdCacheKey(wcId);
            if (_localCache.TryGetValue(cacheKey, out Guid cachedId))
            {
                Interlocked.Increment(ref _cacheHits);
                _logger.LogTrace("L1缓存命中 - WcId: {WcId} -> WxManagerId: {WxManagerId}", wcId, cachedId);
                return cachedId;
            }

            // L2: Redis缓存
            var redisValue = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(redisValue) && Guid.TryParse(redisValue, out var redisId))
            {
                // 回写到L1缓存
                _localCache.Set(cacheKey, redisId, _localCacheExpiry);
                Interlocked.Increment(ref _cacheHits);
                _logger.LogTrace("L2缓存命中 - WcId: {WcId} -> WxManagerId: {WxManagerId}", wcId, redisId);
                return redisId;
            }

            // L3: 数据库查询
            var manager = await _repository.GetByWcIdAsync(wcId);
            if (manager != null)
            {
                // 双写缓存
                await SetCacheAsync(cacheKey, manager.Id);
                _logger.LogDebug("数据库查询成功 - WcId: {WcId} -> WxManagerId: {WxManagerId}", wcId, manager.Id);
                return manager.Id;
            }

            _logger.LogWarning("ID映射未找到 - WcId: {WcId}", wcId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ID解析失败 - WcId: {WcId}", wcId);
            return null;
        }
        finally
        {
            stopwatch.Stop();
            RecordResponseTime(stopwatch.Elapsed.TotalMilliseconds);
        }
    }

    public async Task<string?> ResolveWcIdAsync(Guid wxManagerId)
    {
        if (wxManagerId == Guid.Empty)
        {
            return null;
        }

        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalRequests);

        try
        {
            // L1: 内存缓存
            var cacheKey = GetWxManagerIdCacheKey(wxManagerId);
            if (_localCache.TryGetValue(cacheKey, out string? cachedWcId))
            {
                Interlocked.Increment(ref _cacheHits);
                _logger.LogTrace("L1缓存命中 - WxManagerId: {WxManagerId} -> WcId: {WcId}", wxManagerId, cachedWcId);
                return cachedWcId;
            }

            // L2: Redis缓存
            var redisValue = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(redisValue))
            {
                // 回写到L1缓存
                _localCache.Set(cacheKey, redisValue, _localCacheExpiry);
                Interlocked.Increment(ref _cacheHits);
                _logger.LogTrace("L2缓存命中 - WxManagerId: {WxManagerId} -> WcId: {WcId}", wxManagerId, redisValue);
                return redisValue;
            }

            // L3: 数据库查询
            var manager = await _repository.GetByIdAsync(wxManagerId);
            if (manager?.WcId != null)
            {
                // 双写缓存
                await SetCacheAsync(cacheKey, manager.WcId);
                _logger.LogDebug("数据库查询成功 - WxManagerId: {WxManagerId} -> WcId: {WcId}", wxManagerId, manager.WcId);
                return manager.WcId;
            }

            _logger.LogWarning("ID映射未找到 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ID解析失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
        finally
        {
            stopwatch.Stop();
            RecordResponseTime(stopwatch.Elapsed.TotalMilliseconds);
        }
    }

    public async Task<Dictionary<string, Guid>> BatchResolveWxManagerIdAsync(IEnumerable<string> wcIds)
    {
        var validWcIds = wcIds.Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
        var result = new Dictionary<string, Guid>();

        if (!validWcIds.Any())
        {
            return result;
        }

        await _batchLock.WaitAsync();
        try
        {
            var stopwatch = Stopwatch.StartNew();
            var uncachedIds = new List<string>();

            // 批量检查缓存
            foreach (var wcId in validWcIds)
            {
                var cached = await TryGetFromCacheAsync<Guid>(GetWcIdCacheKey(wcId));
                if (cached != null && !cached.Equals(default(Guid)))
                {
                    result[wcId] = cached;
                    Interlocked.Increment(ref _cacheHits);
                }
                else
                {
                    uncachedIds.Add(wcId);
                }
            }

            // 批量数据库查询
            if (uncachedIds.Any())
            {
                var managers = await _repository.GetByWcIdsAsync(uncachedIds);
                var cacheOperations = new List<Task>();

                foreach (var manager in managers)
                {
                    if (!string.IsNullOrEmpty(manager.WcId))
                    {
                        result[manager.WcId] = manager.Id;
                        
                        // 异步缓存写入
                        cacheOperations.Add(SetCacheAsync(GetWcIdCacheKey(manager.WcId), manager.Id));
                        cacheOperations.Add(SetCacheAsync(GetWxManagerIdCacheKey(manager.Id), manager.WcId));
                    }
                }

                // 等待缓存写入完成
                await Task.WhenAll(cacheOperations);
            }

            stopwatch.Stop();
            Interlocked.Add(ref _totalRequests, validWcIds.Count);
            
            _logger.LogDebug("批量ID解析完成 - 请求: {RequestCount}, 缓存命中: {CacheHits}, 耗时: {ElapsedMs}ms", 
                validWcIds.Count, validWcIds.Count - uncachedIds.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        finally
        {
            _batchLock.Release();
        }
    }

    public async Task<Dictionary<Guid, string>> BatchResolveWcIdAsync(IEnumerable<Guid> wxManagerIds)
    {
        var validIds = wxManagerIds.Where(id => id != Guid.Empty).Distinct().ToList();
        var result = new Dictionary<Guid, string>();

        if (!validIds.Any())
        {
            return result;
        }

        await _batchLock.WaitAsync();
        try
        {
            var stopwatch = Stopwatch.StartNew();
            var uncachedIds = new List<Guid>();

            // 批量检查缓存
            foreach (var id in validIds)
            {
                var cached = await TryGetFromCacheAsync<string>(GetWxManagerIdCacheKey(id));
                if (!string.IsNullOrEmpty(cached))
                {
                    result[id] = cached;
                    Interlocked.Increment(ref _cacheHits);
                }
                else
                {
                    uncachedIds.Add(id);
                }
            }

            // 批量数据库查询
            if (uncachedIds.Any())
            {
                var managers = await _repository.GetByIdsAsync(uncachedIds);
                var cacheOperations = new List<Task>();

                foreach (var manager in managers)
                {
                    if (!string.IsNullOrEmpty(manager.WcId))
                    {
                        result[manager.Id] = manager.WcId;
                        
                        // 异步缓存写入
                        cacheOperations.Add(SetCacheAsync(GetWxManagerIdCacheKey(manager.Id), manager.WcId));
                        cacheOperations.Add(SetCacheAsync(GetWcIdCacheKey(manager.WcId), manager.Id));
                    }
                }

                // 等待缓存写入完成
                await Task.WhenAll(cacheOperations);
            }

            stopwatch.Stop();
            Interlocked.Add(ref _totalRequests, validIds.Count);
            
            _logger.LogDebug("批量WcId解析完成 - 请求: {RequestCount}, 缓存命中: {CacheHits}, 耗时: {ElapsedMs}ms", 
                validIds.Count, validIds.Count - uncachedIds.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        finally
        {
            _batchLock.Release();
        }
    }

    public async Task<bool> EnsureMappingAsync(string wcId)
    {
        var wxManagerId = await ResolveWxManagerIdAsync(wcId);
        
        if (wxManagerId.HasValue)
        {
            return true;
        }

        // 尝试刷新缓存并重新查询
        await InvalidateMappingAsync(wcId);
        wxManagerId = await ResolveWxManagerIdAsync(wcId);
        
        if (wxManagerId.HasValue)
        {
            _logger.LogInformation("映射刷新成功 - WcId: {WcId} -> WxManagerId: {WxManagerId}", wcId, wxManagerId);
            return true;
        }

        _logger.LogWarning("无法建立映射 - WcId: {WcId}", wcId);
        return false;
    }

    public async Task<int> WarmupActiveAccountsAsync()
    {
        await _warmupLock.WaitAsync();
        try
        {
            _logger.LogInformation("开始预热活跃账号缓存...");
            var stopwatch = Stopwatch.StartNew();

            var activeManagers = await _repository.GetActiveManagersAsync();
            var warmupTasks = new List<Task>();

            foreach (var manager in activeManagers)
            {
                if (!string.IsNullOrEmpty(manager.WcId))
                {
                    warmupTasks.Add(SetCacheAsync(GetWcIdCacheKey(manager.WcId), manager.Id));
                    warmupTasks.Add(SetCacheAsync(GetWxManagerIdCacheKey(manager.Id), manager.WcId));
                }
            }

            await Task.WhenAll(warmupTasks);
            
            stopwatch.Stop();
            _logger.LogInformation("缓存预热完成 - 账号数: {AccountCount}, 耗时: {ElapsedMs}ms", 
                activeManagers.Count, stopwatch.ElapsedMilliseconds);

            return activeManagers.Count;
        }
        finally
        {
            _warmupLock.Release();
        }
    }

    public async Task InvalidateMappingAsync(string wcId)
    {
        if (string.IsNullOrEmpty(wcId))
        {
            return;
        }

        var wcIdKey = GetWcIdCacheKey(wcId);
        
        // 首先尝试获取对应的WxManagerId以便同时清理反向缓存
        if (_localCache.TryGetValue(wcIdKey, out Guid wxManagerId))
        {
            var wxManagerIdKey = GetWxManagerIdCacheKey(wxManagerId);
            _localCache.Remove(wxManagerIdKey);
            await _distributedCache.RemoveAsync(wxManagerIdKey);
        }

        // 清理WcId缓存
        _localCache.Remove(wcIdKey);
        await _distributedCache.RemoveAsync(wcIdKey);

        _logger.LogDebug("映射缓存已失效 - WcId: {WcId}", wcId);
    }

    public async Task RefreshMappingAsync(Guid wxManagerId)
    {
        if (wxManagerId == Guid.Empty)
        {
            return;
        }

        var wxManagerIdKey = GetWxManagerIdCacheKey(wxManagerId);
        
        // 首先尝试获取对应的WcId以便同时清理正向缓存
        if (_localCache.TryGetValue(wxManagerIdKey, out string? wcId) && !string.IsNullOrEmpty(wcId))
        {
            var wcIdKey = GetWcIdCacheKey(wcId);
            _localCache.Remove(wcIdKey);
            await _distributedCache.RemoveAsync(wcIdKey);
        }

        // 清理WxManagerId缓存
        _localCache.Remove(wxManagerIdKey);
        await _distributedCache.RemoveAsync(wxManagerIdKey);

        // 强制重新加载
        await ResolveWcIdAsync(wxManagerId);

        _logger.LogDebug("映射缓存已刷新 - WxManagerId: {WxManagerId}", wxManagerId);
    }

    public async Task<bool> IsValidMappingAsync(string wcId, Guid wxManagerId)
    {
        if (string.IsNullOrEmpty(wcId) || wxManagerId == Guid.Empty)
        {
            return false;
        }

        var resolvedWxManagerId = await ResolveWxManagerIdAsync(wcId);
        return resolvedWxManagerId == wxManagerId;
    }

    public async Task<IdMappingHealth> CheckHealthAsync()
    {
        var health = new IdMappingHealth();

        try
        {
            // 计算缓存命中率
            var totalRequests = Interlocked.Read(ref _totalRequests);
            var cacheHits = Interlocked.Read(ref _cacheHits);
            health.TotalRequests = totalRequests;
            health.CacheHitRate = totalRequests > 0 ? (double)cacheHits / totalRequests : 0;

            // 计算平均响应时间
            lock (_responseTimes)
            {
                health.AverageResponseTime = _responseTimes.Any() ? _responseTimes.Average() : 0;
            }

            // 估算缓存条目数
            health.CachedEntryCount = EstimateCacheSize();

            // 检查映射覆盖率
            health.MappingCoverage = await CalculateMappingCoverageAsync();

            // 只有在有足够请求量时才检查缓存命中率
            if (totalRequests > 10)
            {
                if (health.CacheHitRate < 0.8)
                {
                    health.HealthMessages.Add($"缓存命中率偏低: {health.CacheHitRate:P2}");
                }
            }

            if (health.AverageResponseTime > 50)
            {
                health.HealthMessages.Add($"平均响应时间偏高: {health.AverageResponseTime:F2}ms");
            }

            if (health.MappingCoverage < 0.95)
            {
                health.HealthMessages.Add($"映射覆盖率偏低: {health.MappingCoverage:P2}");
            }

            if (health.IsHealthy)
            {
                health.HealthMessages.Add("ID管理器运行正常");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查失败");
            health.HealthMessages.Add($"健康检查异常: {ex.Message}");
        }

        return health;
    }

    #region 私有方法

    private string GetWcIdCacheKey(string wcId) => $"id_mapping:wcid_to_mgrid:{wcId}";
    private string GetWxManagerIdCacheKey(Guid wxManagerId) => $"id_mapping:mgrid_to_wcid:{wxManagerId}";

    private async Task SetCacheAsync<T>(string key, T value)
    {
        try
        {
            // L1缓存
            _localCache.Set(key, value, _localCacheExpiry);
            
            // L2缓存
            var serializedValue = typeof(T) == typeof(string) ? value?.ToString() : JsonSerializer.Serialize(value);
            await _distributedCache.SetStringAsync(key, serializedValue!, 
                new DistributedCacheEntryOptions { SlidingExpiration = _distributedCacheExpiry });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存写入失败 - Key: {Key}", key);
        }
    }

    private async Task<T?> TryGetFromCacheAsync<T>(string key)
    {
        // L1缓存
        if (_localCache.TryGetValue(key, out T? localValue))
        {
            return localValue;
        }

        // L2缓存
        try
        {
            var redisValue = await _distributedCache.GetStringAsync(key);
            if (!string.IsNullOrEmpty(redisValue))
            {
                T? deserializedValue;
                if (typeof(T) == typeof(string))
                {
                    deserializedValue = (T?)(object)redisValue;
                }
                else
                {
                    deserializedValue = JsonSerializer.Deserialize<T>(redisValue);
                }

                if (deserializedValue != null)
                {
                    _localCache.Set(key, deserializedValue, _localCacheExpiry);
                    return deserializedValue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存读取失败 - Key: {Key}", key);
        }

        return default;
    }

    private void RecordResponseTime(double milliseconds)
    {
        lock (_responseTimes)
        {
            _responseTimes.Add(milliseconds);
            
            // 保持最近1000次记录
            if (_responseTimes.Count > 1000)
            {
                _responseTimes.RemoveAt(0);
            }
        }
    }

    private int EstimateCacheSize()
    {
        // 这是一个简化的估算，实际实现可能需要更精确的方法
        if (_localCache is MemoryCache memoryCache)
        {
            var field = typeof(MemoryCache).GetField("_cache", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field?.GetValue(memoryCache) is System.Collections.IDictionary cache)
            {
                return cache.Count;
            }
        }
        
        return 0;
    }

    private async Task<double> CalculateMappingCoverageAsync()
    {
        try
        {
            var activeCount = await _repository.GetActiveManagerCountAsync();
            var mappedCount = await _repository.GetMappedManagerCountAsync();
            
            return activeCount > 0 ? (double)mappedCount / activeCount : 1.0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "映射覆盖率计算失败");
            return 0.0;
        }
    }

    #endregion
}