using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.Interfaces;

namespace HappyWechat.Infrastructure.IdManagement;

/// <summary>
/// ID映射健康监控服务 - 定期检查ID管理器的健康状态
/// </summary>
public class IdMappingHealthService : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<IdMappingHealthService> _logger;
    private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5);
    private readonly TimeSpan _warmupInterval = TimeSpan.FromHours(1);
    
    private DateTime _lastWarmup = DateTime.MinValue;

    public IdMappingHealthService(
        IServiceScopeFactory serviceScopeFactory,
        ILogger<IdMappingHealthService> logger)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 启动时预热缓存
        await PerformWarmupAsync();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthCheckAsync();
                
                // 检查是否需要预热
                if (DateTime.UtcNow - _lastWarmup > _warmupInterval)
                {
                    await PerformWarmupAsync();
                }
                
                await Task.Delay(_checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ID映射健康检查服务异常");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }

    private async Task PerformHealthCheckAsync()
    {
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var idManager = scope.ServiceProvider.GetRequiredService<IUnifiedIdManager>();

            var health = await idManager.CheckHealthAsync();

            if (health.IsHealthy)
            {
                _logger.LogTrace("ID映射健康检查正常 - 缓存命中率: {CacheHitRate:P2}, 响应时间: {ResponseTime:F2}ms",
                    health.CacheHitRate, health.AverageResponseTime);
            }
            else
            {
                // 只有在有足够请求量时才报告缓存命中率问题
                if (health.TotalRequests > 10)
                {
                    _logger.LogWarning("ID映射健康检查异常 - 缓存命中率: {CacheHitRate:P2}, 响应时间: {ResponseTime:F2}ms, 覆盖率: {Coverage:P2}, 总请求数: {TotalRequests}",
                        health.CacheHitRate, health.AverageResponseTime, health.MappingCoverage, health.TotalRequests);

                    foreach (var message in health.HealthMessages)
                    {
                        _logger.LogWarning("健康检查问题: {Message}", message);
                    }

                    // 如果健康状态不佳且有足够请求量，触发预热
                    if (health.CacheHitRate < 0.5)
                    {
                        _logger.LogInformation("检测到缓存命中率过低，触发预热操作");
                        await PerformWarmupAsync();
                    }
                }
                else
                {
                    _logger.LogDebug("ID映射健康检查 - 请求量较少({TotalRequests})，跳过缓存命中率检查", health.TotalRequests);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行ID映射健康检查失败");
        }
    }

    private async Task PerformWarmupAsync()
    {
        try
        {
            _logger.LogInformation("开始执行ID映射缓存预热...");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            using var scope = _serviceScopeFactory.CreateScope();
            var idManager = scope.ServiceProvider.GetRequiredService<IUnifiedIdManager>();
            
            var warmedCount = await idManager.WarmupActiveAccountsAsync();
            
            stopwatch.Stop();
            _lastWarmup = DateTime.UtcNow;
            
            _logger.LogInformation("ID映射缓存预热完成 - 账号数: {AccountCount}, 耗时: {ElapsedMs}ms", 
                warmedCount, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ID映射缓存预热失败");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ID映射健康监控服务正在停止...");
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("ID映射健康监控服务已停止");
    }
}