namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 独立的池化数据库配置选项
/// 避免与现有DatabaseOptions产生冲突
/// </summary>
public class PooledDbOptions
{
    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;
    
    /// <summary>
    /// 连接池大小
    /// </summary>
    public int PoolSize { get; set; } = 128;
    
    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 60;
    
    /// <summary>
    /// 查询超时时间（秒）
    /// </summary>
    public int QueryTimeout { get; set; } = 30;
    
    /// <summary>
    /// 是否启用敏感数据日志
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;
    
    /// <summary>
    /// 是否启用详细错误信息
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = false;
    
    /// <summary>
    /// 重试策略配置
    /// </summary>
    public PooledRetryPolicy RetryPolicy { get; set; } = new();
}

/// <summary>
/// 池化重试策略配置
/// </summary>
public class PooledRetryPolicy
{
    /// <summary>
    /// 是否启用重试
    /// </summary>
    public bool EnableRetry { get; set; } = true;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 基础延迟时间（毫秒）
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;
    
    /// <summary>
    /// 最大延迟时间（毫秒）
    /// </summary>
    public int MaxDelayMs { get; set; } = 30000;
    
    /// <summary>
    /// 延迟倍数
    /// </summary>
    public double DelayMultiplier { get; set; } = 2.0;
}