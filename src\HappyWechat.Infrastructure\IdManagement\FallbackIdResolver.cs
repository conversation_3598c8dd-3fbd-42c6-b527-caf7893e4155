using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Caching;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.IdManagement;

/// <summary>
/// 降级ID解析器 - 在主要ID管理器失败时提供备用解析方案
/// </summary>
public class FallbackIdResolver : IFallbackIdResolver
{
    private readonly IUnifiedIdManager _primaryIdManager;
    private readonly IWxManagerRepository _repository;
    private readonly IWxManagerIdCacheService _legacyCacheService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<FallbackIdResolver> _logger;

    private readonly bool _allowAutoCreateMapping;
    private readonly int _maxRetryAttempts;

    public FallbackIdResolver(
        IUnifiedIdManager primaryIdManager,
        IWxManagerRepository repository,
        IWxManagerIdCacheService legacyCacheService,
        IConfiguration configuration,
        ILogger<FallbackIdResolver> logger)
    {
        _primaryIdManager = primaryIdManager;
        _repository = repository;
        _legacyCacheService = legacyCacheService;
        _configuration = configuration;
        _logger = logger;

        _allowAutoCreateMapping = _configuration.GetValue<bool>("IdMapping:AllowAutoCreate", false);
        _maxRetryAttempts = _configuration.GetValue<int>("IdMapping:MaxRetryAttempts", 3);
    }

    public async Task<Guid?> ResolveWithFallbackAsync(string wcId)
    {
        if (string.IsNullOrEmpty(wcId))
        {
            return null;
        }

        var stopwatch = Stopwatch.StartNew();
        var attemptCount = 0;

        while (attemptCount < _maxRetryAttempts)
        {
            attemptCount++;

            try
            {
                // 方案1: 主要统一ID管理器
                var primaryResult = await TryPrimaryResolverAsync(wcId);
                if (primaryResult.HasValue)
                {
                    _logger.LogTrace("主要解析器成功 - WcId: {WcId}, 尝试次数: {AttemptCount}", wcId, attemptCount);
                    return primaryResult;
                }

                // 方案2: 遗留缓存服务
                var legacyResult = await TryLegacyCacheAsync(wcId);
                if (legacyResult.HasValue)
                {
                    _logger.LogInformation("遗留缓存解析成功 - WcId: {WcId}, 尝试次数: {AttemptCount}", wcId, attemptCount);
                    
                    // 将结果同步到主要管理器
                    await SyncToPrimaryAsync(wcId, legacyResult.Value);
                    return legacyResult;
                }

                // 方案3: 直接数据库查询
                var dbResult = await TryDirectDatabaseAsync(wcId);
                if (dbResult.HasValue)
                {
                    _logger.LogInformation("直接数据库查询成功 - WcId: {WcId}, 尝试次数: {AttemptCount}", wcId, attemptCount);
                    
                    // 将结果同步到主要管理器
                    await SyncToPrimaryAsync(wcId, dbResult.Value);
                    return dbResult;
                }

                // 方案4: 创建新映射（如果配置允许）
                if (_allowAutoCreateMapping && attemptCount == _maxRetryAttempts)
                {
                    var createdResult = await TryCreateMappingAsync(wcId);
                    if (createdResult.HasValue)
                    {
                        _logger.LogWarning("自动创建映射成功 - WcId: {WcId}", wcId);
                        return createdResult;
                    }
                }

                // 等待后重试
                if (attemptCount < _maxRetryAttempts)
                {
                    var delay = TimeSpan.FromMilliseconds(100 * Math.Pow(2, attemptCount - 1)); // 指数退避
                    await Task.Delay(delay);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "降级解析异常 - WcId: {WcId}, 尝试次数: {AttemptCount}", wcId, attemptCount);
                
                if (attemptCount < _maxRetryAttempts)
                {
                    await Task.Delay(TimeSpan.FromSeconds(1));
                }
            }
        }

        stopwatch.Stop();
        _logger.LogError("所有降级方案失败 - WcId: {WcId}, 总尝试次数: {TotalAttempts}, 总耗时: {ElapsedMs}ms", 
            wcId, attemptCount, stopwatch.ElapsedMilliseconds);

        return null;
    }

    public async Task<string?> ResolveWcIdWithFallbackAsync(Guid wxManagerId)
    {
        if (wxManagerId == Guid.Empty)
        {
            return null;
        }

        var stopwatch = Stopwatch.StartNew();
        var attemptCount = 0;

        while (attemptCount < _maxRetryAttempts)
        {
            attemptCount++;

            try
            {
                // 方案1: 主要统一ID管理器
                var primaryResult = await _primaryIdManager.ResolveWcIdAsync(wxManagerId);
                if (!string.IsNullOrEmpty(primaryResult))
                {
                    _logger.LogTrace("主要解析器成功 - WxManagerId: {WxManagerId}, 尝试次数: {AttemptCount}", wxManagerId, attemptCount);
                    return primaryResult;
                }

                // 方案2: 直接数据库查询
                var manager = await _repository.GetByIdAsync(wxManagerId);
                if (manager?.WcId != null)
                {
                    _logger.LogInformation("直接数据库查询成功 - WxManagerId: {WxManagerId}, 尝试次数: {AttemptCount}", wxManagerId, attemptCount);
                    
                    // 将结果同步到主要管理器
                    await SyncToPrimaryAsync(manager.WcId, wxManagerId);
                    return manager.WcId;
                }

                // 等待后重试
                if (attemptCount < _maxRetryAttempts)
                {
                    var delay = TimeSpan.FromMilliseconds(100 * Math.Pow(2, attemptCount - 1));
                    await Task.Delay(delay);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "降级WcId解析异常 - WxManagerId: {WxManagerId}, 尝试次数: {AttemptCount}", wxManagerId, attemptCount);
                
                if (attemptCount < _maxRetryAttempts)
                {
                    await Task.Delay(TimeSpan.FromSeconds(1));
                }
            }
        }

        stopwatch.Stop();
        _logger.LogError("所有WcId降级方案失败 - WxManagerId: {WxManagerId}, 总尝试次数: {TotalAttempts}, 总耗时: {ElapsedMs}ms", 
            wxManagerId, attemptCount, stopwatch.ElapsedMilliseconds);

        return null;
    }

    private async Task<Guid?> TryPrimaryResolverAsync(string wcId)
    {
        try
        {
            return await _primaryIdManager.ResolveWxManagerIdAsync(wcId);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "主要解析器失败 - WcId: {WcId}", wcId);
            return null;
        }
    }

    private async Task<Guid?> TryLegacyCacheAsync(string wcId)
    {
        try
        {
            return await _legacyCacheService.GetWxManagerIdAsync(wcId);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "遗留缓存解析失败 - WcId: {WcId}", wcId);
            return null;
        }
    }

    private async Task<Guid?> TryDirectDatabaseAsync(string wcId)
    {
        try
        {
            var manager = await _repository.GetByWcIdAsync(wcId);
            return manager?.Id;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "直接数据库查询失败 - WcId: {WcId}", wcId);
            return null;
        }
    }

    private async Task<Guid?> TryCreateMappingAsync(string wcId)
    {
        try
        {
            // 这里实现自动创建映射的逻辑
            // 注意：这个功能需要谨慎使用，通常应该是禁用的
            _logger.LogWarning("自动创建映射功能已被调用但未实现 - WcId: {WcId}", wcId);
            
            // 暂时返回null，具体实现取决于业务需求
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动创建映射失败 - WcId: {WcId}", wcId);
            return null;
        }
    }

    private async Task SyncToPrimaryAsync(string wcId, Guid wxManagerId)
    {
        try
        {
            // 刷新主要管理器的缓存，让它重新加载这个映射
            await _primaryIdManager.RefreshMappingAsync(wxManagerId);
            _logger.LogDebug("映射已同步到主要管理器 - WcId: {WcId}, WxManagerId: {WxManagerId}", wcId, wxManagerId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "同步映射到主要管理器失败 - WcId: {WcId}, WxManagerId: {WxManagerId}", wcId, wxManagerId);
        }
    }
}

/// <summary>
/// 降级ID解析器接口
/// </summary>
public interface IFallbackIdResolver
{
    /// <summary>
    /// 使用降级策略解析WxManagerId
    /// </summary>
    Task<Guid?> ResolveWithFallbackAsync(string wcId);

    /// <summary>
    /// 使用降级策略解析WcId
    /// </summary>
    Task<string?> ResolveWcIdWithFallbackAsync(Guid wxManagerId);
}