using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Infrastructure.Notifications.Models;

/// <summary>
/// 统一同步通知基类
/// </summary>
public abstract class BaseSyncNotification
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 通知ID
    /// </summary>
    public string NotificationId { get; set; } = Guid.NewGuid().ToString("N")[..8];

    /// <summary>
    /// 同步状态
    /// </summary>
    public SyncStatus Status { get; set; }

    /// <summary>
    /// 通知时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 同步进度通知
/// </summary>
public class SyncProgressNotification : BaseSyncNotification
{
    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public int ProgressPercentage { get; set; }

    /// <summary>
    /// 已处理数量
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 当前阶段描述
    /// </summary>
    public string CurrentPhase { get; set; } = string.Empty;

    /// <summary>
    /// 预计剩余时间（毫秒）
    /// </summary>
    public long? EstimatedRemainingMs { get; set; }
}

/// <summary>
/// 同步完成通知
/// </summary>
public class SyncCompletionNotification : BaseSyncNotification
{
    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 同步会话ID
    /// </summary>
    public string? SessionId { get; set; }
}

/// <summary>
/// 联系人同步完成通知
/// </summary>
public class ContactSyncCompletionNotification : SyncCompletionNotification
{
    /// <summary>
    /// 个人联系人成功数量
    /// </summary>
    public int FriendsSuccessCount { get; set; }

    /// <summary>
    /// 企业联系人成功数量
    /// </summary>
    public int EnterpriseSuccessCount { get; set; }

    /// <summary>
    /// 个人联系人失败数量
    /// </summary>
    public int FriendsFailedCount { get; set; }

    /// <summary>
    /// 企业联系人失败数量
    /// </summary>
    public int EnterpriseFailedCount { get; set; }
}

/// <summary>
/// 群组同步完成通知
/// </summary>
public class GroupSyncCompletionNotification : SyncCompletionNotification
{
    /// <summary>
    /// 同步的群组数量
    /// </summary>
    public int GroupCount { get; set; }

    /// <summary>
    /// 新增群组数量
    /// </summary>
    public int NewGroupCount { get; set; }

    /// <summary>
    /// 更新群组数量
    /// </summary>
    public int UpdatedGroupCount { get; set; }
}

/// <summary>
/// 通知发送选项
/// </summary>
public class NotificationSendOptions
{
    /// <summary>
    /// 是否启用重试
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用降级机制
    /// </summary>
    public bool EnableFallback { get; set; } = true;

    /// <summary>
    /// 通知优先级
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
}

/// <summary>
/// 通知优先级
/// </summary>
public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// 通知发送结果
/// </summary>
public class NotificationSendResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 发送耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }

    /// <summary>
    /// 是否使用了降级机制
    /// </summary>
    public bool UsedFallback { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static NotificationSendResult CreateSuccess(long elapsedMs = 0, int retryCount = 0, bool usedFallback = false)
    {
        return new NotificationSendResult
        {
            Success = true,
            ElapsedMs = elapsedMs,
            RetryCount = retryCount,
            UsedFallback = usedFallback
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static NotificationSendResult CreateFailure(string errorMessage, long elapsedMs = 0, int retryCount = 0)
    {
        return new NotificationSendResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            ElapsedMs = elapsedMs,
            RetryCount = retryCount
        };
    }
}
