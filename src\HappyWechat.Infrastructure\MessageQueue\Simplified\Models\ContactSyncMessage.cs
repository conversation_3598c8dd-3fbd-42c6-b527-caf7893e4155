using HappyWechat.Domain.ValueObjects.Enums;
using System.Text.Json.Serialization;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

/// <summary>
/// 联系人同步消息数据结构
/// </summary>
public class ContactSyncMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    [JsonPropertyName("wxManagerId")]
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 同步会话ID（用于跟踪整个同步过程）
    /// </summary>
    [JsonPropertyName("syncSessionId")]
    public string SyncSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 联系人类型
    /// </summary>
    [JsonPropertyName("listType")]
    public WxContactListType ListType { get; set; }

    /// <summary>
    /// 联系人ID列表
    /// </summary>
    [JsonPropertyName("contactIds")]
    public List<string> ContactIds { get; set; } = new();

    /// <summary>
    /// 批次索引（从0开始）
    /// </summary>
    [JsonPropertyName("batchIndex")]
    public int BatchIndex { get; set; }

    /// <summary>
    /// 总批次数
    /// </summary>
    [JsonPropertyName("totalBatches")]
    public int TotalBatches { get; set; }

    /// <summary>
    /// 当前批次大小
    /// </summary>
    [JsonPropertyName("batchSize")]
    public int BatchSize { get; set; }

    /// <summary>
    /// 延时配置
    /// </summary>
    [JsonPropertyName("delayConfig")]
    public ContactSyncDelayConfig DelayConfig { get; set; } = new();

    /// <summary>
    /// 重试配置
    /// </summary>
    [JsonPropertyName("retryConfig")]
    public ContactSyncRetryConfig RetryConfig { get; set; } = new();

    /// <summary>
    /// 同步类型
    /// </summary>
    [JsonPropertyName("syncType")]
    public string SyncType { get; set; } = "normal";

    /// <summary>
    /// 请求时间
    /// </summary>
    [JsonPropertyName("requestedAt")]
    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否为最后一个批次
    /// </summary>
    [JsonIgnore]
    public bool IsLastBatch => BatchIndex == TotalBatches - 1;

    /// <summary>
    /// 获取批次描述
    /// </summary>
    [JsonIgnore]
    public string BatchDescription
    {
        get
        {
            var contactTypeName = ListType == WxContactListType.Friends ? "个人" : "企业";
            return $"{contactTypeName}联系人批次 {BatchIndex + 1}/{TotalBatches} (本批 {BatchSize} 个)";
        }
    }

    /// <summary>
    /// 获取联系人ID的逗号分隔字符串（用于API调用）
    /// </summary>
    [JsonIgnore]
    public string ContactIdsString => string.Join(",", ContactIds);

    /// <summary>
    /// 验证消息数据是否有效
    /// </summary>
    /// <returns></returns>
    public bool IsValid()
    {
        if (WxManagerId == Guid.Empty) return false;
        if (string.IsNullOrEmpty(SyncSessionId)) return false;
        if (ContactIds == null || !ContactIds.Any()) return false;
        if (BatchIndex < 0 || TotalBatches <= 0) return false;
        if (BatchIndex >= TotalBatches) return false;
        if (BatchSize != ContactIds.Count) return false;

        // 验证联系人类型特定的规则
        if (ListType == WxContactListType.Friends && ContactIds.Count > 20)
        {
            return false; // 个人联系人批次不能超过20个
        }

        if (ListType == WxContactListType.Enterprise && ContactIds.Count > 1)
        {
            return false; // 企业联系人必须单个处理
        }

        return true;
    }
}

/// <summary>
/// 联系人同步延时配置
/// </summary>
public class ContactSyncDelayConfig
{
    /// <summary>
    /// 最小延时（毫秒）
    /// </summary>
    [JsonPropertyName("minDelayMs")]
    public int MinDelayMs { get; set; } = 300;

    /// <summary>
    /// 最大延时（毫秒）
    /// </summary>
    [JsonPropertyName("maxDelayMs")]
    public int MaxDelayMs { get; set; } = 1500;

    /// <summary>
    /// 是否启用随机延时
    /// </summary>
    [JsonPropertyName("enableRandomDelay")]
    public bool EnableRandomDelay { get; set; } = true;

    /// <summary>
    /// 固定延时（毫秒，当不使用随机延时时）
    /// </summary>
    [JsonPropertyName("fixedDelayMs")]
    public int FixedDelayMs { get; set; } = 1000;

    /// <summary>
    /// 获取实际延时时间
    /// </summary>
    /// <returns></returns>
    public int GetActualDelay()
    {
        if (!EnableRandomDelay)
        {
            return FixedDelayMs;
        }

        var random = new Random();
        return random.Next(MinDelayMs, MaxDelayMs + 1);
    }
}

/// <summary>
/// 联系人同步重试配置
/// </summary>
public class ContactSyncRetryConfig
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    [JsonPropertyName("maxRetryCount")]
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    [JsonPropertyName("currentRetryCount")]
    public int CurrentRetryCount { get; set; } = 0;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    [JsonPropertyName("retryIntervalMs")]
    public int RetryIntervalMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用指数退避
    /// </summary>
    [JsonPropertyName("enableExponentialBackoff")]
    public bool EnableExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 退避倍数
    /// </summary>
    [JsonPropertyName("backoffMultiplier")]
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// 最大重试间隔（毫秒）
    /// </summary>
    [JsonPropertyName("maxRetryIntervalMs")]
    public int MaxRetryIntervalMs { get; set; } = 30000;

    /// <summary>
    /// 是否可以重试
    /// </summary>
    [JsonIgnore]
    public bool CanRetry => CurrentRetryCount < MaxRetryCount;

    /// <summary>
    /// 增加重试次数
    /// </summary>
    public void IncrementRetry()
    {
        CurrentRetryCount++;
    }

    /// <summary>
    /// 获取下次重试的延时时间
    /// </summary>
    /// <returns></returns>
    public int GetNextRetryDelay()
    {
        if (!EnableExponentialBackoff)
        {
            return RetryIntervalMs;
        }

        var delay = (int)(RetryIntervalMs * Math.Pow(BackoffMultiplier, CurrentRetryCount));
        return Math.Min(delay, MaxRetryIntervalMs);
    }

    /// <summary>
    /// 重置重试计数
    /// </summary>
    public void Reset()
    {
        CurrentRetryCount = 0;
    }
}
