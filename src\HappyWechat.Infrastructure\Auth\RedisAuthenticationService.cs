using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Infrastructure.Identity.Entities;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis统一认证服务实现
/// </summary>
public class RedisAuthenticationService : IRedisAuthenticationService
{
    private readonly IRedisSessionManager _sessionManager;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ILogger<RedisAuthenticationService> _logger;
    private readonly RedisAuthenticationOptions _options;
    private const string SessionCookieName = "HW_SessionId";
    private const string SessionHeaderName = "X-Session-Id";

    public RedisAuthenticationService(
        IRedisSessionManager sessionManager,
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ILogger<RedisAuthenticationService> logger,
        IOptions<RedisAuthenticationOptions> options)
    {
        _sessionManager = sessionManager;
        _userManager = userManager;
        _signInManager = signInManager;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<RedisAuthenticationResult> LoginAsync(LoginQuery loginQuery)
    {
        try
        {
            if (string.IsNullOrEmpty(loginQuery.Username) || string.IsNullOrEmpty(loginQuery.Password))
            {
                return RedisAuthenticationResult.Failure("用户名和密码不能为空");
            }

            // 使用Identity验证用户凭据
            var result = await _signInManager.PasswordSignInAsync(
                loginQuery.Username, 
                loginQuery.Password, 
                isPersistent: false, 
                lockoutOnFailure: true);

            if (!result.Succeeded)
            {
                var errorMessage = result.IsLockedOut ? "账户已被锁定" :
                                 result.IsNotAllowed ? "账户未激活" :
                                 result.RequiresTwoFactor ? "需要双因素认证" :
                                 "用户名或密码错误";

                _logger.LogWarning("🔐 登录失败 - Username: {Username}, Reason: {Reason}", 
                    loginQuery.Username, errorMessage);

                return RedisAuthenticationResult.Failure(errorMessage);
            }

            // 获取用户信息
            var user = await _userManager.FindByNameAsync(loginQuery.Username);
            if (user == null)
            {
                return RedisAuthenticationResult.Failure("用户不存在");
            }

            // 获取用户角色
            var roles = await _userManager.GetRolesAsync(user);
            
            // 获取用户权限（这里可以根据实际需求扩展）
            var permissions = await GetUserPermissionsAsync(user);

            // 创建Redis会话
            var sessionId = await _sessionManager.CreateSessionAsync(
                user.Id, 
                user.UserName ?? user.Email ?? user.Id, 
                roles, 
                permissions);

            var expiresAt = DateTime.UtcNow.AddMinutes(_options.SessionExpirationMinutes);

            _logger.LogInformation("✅ 用户登录成功 - UserId: {UserId}, SessionId: {SessionId}", 
                user.Id, sessionId);

            return RedisAuthenticationResult.Success(
                sessionId, 
                user.Id, 
                user.UserName ?? user.Email ?? user.Id, 
                roles, 
                permissions, 
                expiresAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 登录处理异常 - Username: {Username}", loginQuery.Username);
            return RedisAuthenticationResult.Failure("登录处理异常，请稍后重试");
        }
    }

    public async Task<bool> LogoutAsync(string sessionId)
    {
        try
        {
            if (string.IsNullOrEmpty(sessionId))
                return false;

            var result = await _sessionManager.DestroySessionAsync(sessionId);
            
            if (result)
            {
                _logger.LogInformation("✅ 用户登出成功 - SessionId: {SessionId}", sessionId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 登出处理异常 - SessionId: {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<AuthenticationState> GetAuthenticationStateAsync(string sessionId)
    {
        try
        {
            if (string.IsNullOrEmpty(sessionId))
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }

            var sessionInfo = await _sessionManager.ValidateSessionAsync(sessionId);
            if (sessionInfo == null)
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }

            var claimsPrincipal = sessionInfo.ToClaimsPrincipal();
            return new AuthenticationState(claimsPrincipal);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取认证状态异常 - SessionId: {SessionId}", sessionId);
            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
        }
    }

    public async Task<bool> IsAuthenticatedAsync(string sessionId)
    {
        return await _sessionManager.IsAuthenticatedAsync(sessionId);
    }

    public async Task<ClaimsPrincipal?> GetCurrentUserAsync(string sessionId)
    {
        try
        {
            var sessionInfo = await _sessionManager.ValidateSessionAsync(sessionId);
            return sessionInfo?.ToClaimsPrincipal();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取当前用户异常 - SessionId: {SessionId}", sessionId);
            return null;
        }
    }

    public async Task<bool> IsInRoleAsync(string sessionId, string role)
    {
        try
        {
            var sessionInfo = await _sessionManager.ValidateSessionAsync(sessionId);
            return sessionInfo?.Roles.Contains(role) == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查角色异常 - SessionId: {SessionId}, Role: {Role}", sessionId, role);
            return false;
        }
    }

    public async Task<bool> HasPermissionAsync(string sessionId, string permission)
    {
        try
        {
            var sessionInfo = await _sessionManager.ValidateSessionAsync(sessionId);
            return sessionInfo?.Permissions.Contains(permission) == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查权限异常 - SessionId: {SessionId}, Permission: {Permission}", sessionId, permission);
            return false;
        }
    }

    public async Task<bool> RenewSessionAsync(string sessionId)
    {
        return await _sessionManager.RenewSessionAsync(sessionId);
    }

    public async Task<int> ForceLogoutUserAsync(string userId)
    {
        try
        {
            var count = await _sessionManager.DestroyUserSessionsAsync(userId);
            _logger.LogInformation("🔒 强制用户登出 - UserId: {UserId}, SessionCount: {Count}", userId, count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 强制登出异常 - UserId: {UserId}", userId);
            return 0;
        }
    }

    public async Task<IEnumerable<RedisSessionInfo>> GetUserActiveSessionsAsync(string userId)
    {
        return await _sessionManager.GetUserSessionsAsync(userId);
    }

    public string? GetSessionIdFromContext(HttpContext httpContext)
    {
        // 优先从专用Header获取
        if (httpContext.Request.Headers.TryGetValue(SessionHeaderName, out var headerValue))
        {
            var sessionId = headerValue.FirstOrDefault();
            if (!string.IsNullOrEmpty(sessionId))
                return sessionId;
        }

        // 从Authorization头获取（兼容前端JWT格式）
        if (httpContext.Request.Headers.TryGetValue("Authorization", out var authHeader))
        {
            var authValue = authHeader.FirstOrDefault();
            if (!string.IsNullOrEmpty(authValue))
            {
                // 支持 "Bearer sessionId" 格式
                if (authValue.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                {
                    var sessionId = authValue.Substring(7).Trim();
                    if (!string.IsNullOrEmpty(sessionId))
                        return sessionId;
                }
                // 直接使用Authorization头的值
                else
                {
                    return authValue;
                }
            }
        }

        // 从Cookie获取
        if (httpContext.Request.Cookies.TryGetValue(SessionCookieName, out var cookieValue))
        {
            if (!string.IsNullOrEmpty(cookieValue))
                return cookieValue;
        }

        // 从查询字符串获取（用于SignalR等场景）
        if (httpContext.Request.Query.TryGetValue("session_id", out var queryValue))
        {
            var sessionId = queryValue.FirstOrDefault();
            if (!string.IsNullOrEmpty(sessionId))
                return sessionId;
        }

        return null;
    }

    public void SetSessionIdToContext(HttpContext httpContext, string sessionId)
    {
        try
        {
            // 检查响应是否已开始
            if (httpContext.Response.HasStarted)
            {
                _logger.LogDebug("响应已开始，无法设置Cookie和Header - SessionId: {SessionId}", sessionId.Substring(0, 8) + "...");
                return;
            }

            // 设置Cookie
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = httpContext.Request.IsHttps,
                SameSite = SameSiteMode.Lax,
                Expires = DateTime.UtcNow.AddMinutes(_options.SessionExpirationMinutes)
            };

            httpContext.Response.Cookies.Append(SessionCookieName, sessionId, cookieOptions);

            // 设置响应头
            httpContext.Response.Headers[SessionHeaderName] = sessionId;
            
            _logger.LogDebug("✅ SessionId已设置到HTTP上下文 - SessionId: {SessionId}", sessionId.Substring(0, 8) + "...");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Headers are read-only"))
        {
            _logger.LogDebug("无法设置Cookie/Header，响应已开始 - SessionId: {SessionId}, Error: {Error}",
                sessionId.Substring(0, 8) + "...", ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 设置SessionId到HTTP上下文异常 - SessionId: {SessionId}", sessionId.Substring(0, 8) + "...");
        }
    }

    public void ClearSessionIdFromContext(HttpContext httpContext)
    {
        // 清除Cookie
        httpContext.Response.Cookies.Delete(SessionCookieName);

        // 清除响应头
        httpContext.Response.Headers.Remove(SessionHeaderName);
    }

    private async Task<IEnumerable<string>> GetUserPermissionsAsync(ApplicationUser user)
    {
        // 这里可以根据实际需求实现权限获取逻辑
        // 例如从数据库获取用户的具体权限
        var permissions = new List<string>();

        try
        {
            var roles = await _userManager.GetRolesAsync(user);
            
            // 根据角色映射基本权限
            foreach (var role in roles)
            {
                switch (role.ToLower())
                {
                    case "admin":
                        permissions.AddRange(new[] { "admin.all", "user.manage", "system.config" });
                        break;
                    case "user":
                        permissions.AddRange(new[] { "user.basic", "data.read" });
                        break;
                }
            }

            // 去重
            return permissions.Distinct();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取用户权限异常 - UserId: {UserId}", user.Id);
            return new List<string>();
        }
    }
}
