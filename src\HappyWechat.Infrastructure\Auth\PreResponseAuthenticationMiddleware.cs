using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// 预响应认证中间件 - 确保在响应开始前处理认证信息
/// </summary>
public class PreResponseAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PreResponseAuthenticationMiddleware> _logger;

    public PreResponseAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<PreResponseAuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 🔧 在处理请求前拦截需要设置认证信息的响应
        var originalBodyStream = context.Response.Body;
        
        try
        {
            using var memoryStream = new MemoryStream();
            context.Response.Body = memoryStream;

            // 处理请求
            await _next(context);

            // 🔧 在响应写入前检查是否需要设置认证信息
            await HandleAuthenticationHeaders(context);

            // 将缓存的响应写入原始流
            memoryStream.Position = 0;
            await memoryStream.CopyToAsync(originalBodyStream);
        }
        finally
        {
            context.Response.Body = originalBodyStream;
        }
    }

    /// <summary>
    /// 处理认证头信息
    /// </summary>
    private async Task HandleAuthenticationHeaders(HttpContext context)
    {
        try
        {
            // 只处理登录成功的响应
            if (!ShouldProcessAuthentication(context))
                return;

            var authService = context.RequestServices.GetService<IRedisAuthenticationService>();
            if (authService == null)
                return;

            // 尝试从响应中提取SessionId
            var sessionId = await ExtractSessionIdFromResponse(context);
            if (string.IsNullOrEmpty(sessionId))
                return;

            // 🔧 现在安全地设置Cookie和Header，因为响应还没有开始
            try
            {
                authService.SetSessionIdToContext(context, sessionId);
                _logger.LogDebug("✅ 预响应中间件成功设置认证头 - SessionId: {SessionId}", 
                    sessionId.Substring(0, 8) + "...");
            }
            catch (Exception ex)
            {
                _logger.LogWarning("⚠️ 预响应中间件设置认证头失败: {Error}", ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 预响应认证中间件处理异常");
        }
    }

    /// <summary>
    /// 判断是否应该处理认证
    /// </summary>
    private static bool ShouldProcessAuthentication(HttpContext context)
    {
        // 只处理登录API的成功响应
        if (!context.Request.Path.StartsWithSegments("/api/auth/login"))
            return false;

        if (context.Request.Method != HttpMethods.Post)
            return false;

        if (context.Response.StatusCode != StatusCodes.Status200OK)
            return false;

        return true;
    }

    /// <summary>
    /// 从响应中提取SessionId
    /// </summary>
    private async Task<string?> ExtractSessionIdFromResponse(HttpContext context)
    {
        try
        {
            // 检查响应体是否包含SessionId
            if (context.Response.Body.CanSeek)
            {
                var position = context.Response.Body.Position;
                context.Response.Body.Position = 0;

                using var reader = new StreamReader(context.Response.Body, leaveOpen: true);
                var content = await reader.ReadToEndAsync();
                context.Response.Body.Position = position;

                // 简单的JSON解析来提取sessionId
                if (content.Contains("\"sessionId\""))
                {
                    var sessionIdMatch = System.Text.RegularExpressions.Regex.Match(
                        content, @"""sessionId""\s*:\s*""([^""]+)""");
                    
                    if (sessionIdMatch.Success)
                    {
                        return sessionIdMatch.Groups[1].Value;
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning("⚠️ 提取SessionId失败: {Error}", ex.Message);
            return null;
        }
    }
}