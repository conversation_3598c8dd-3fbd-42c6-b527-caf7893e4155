using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// 群组同步队列消息
/// </summary>
public class GroupSyncQueueMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 是否包含群成员信息
    /// </summary>
    public bool IncludeMembers { get; set; } = true;
    
    /// <summary>
    /// 单次处理的最大群组数量
    /// </summary>
    public int MaxGroupsPerBatch { get; set; } = 50;
    
    /// <summary>
    /// 最小延时（毫秒）
    /// </summary>
    public int MinDelayMs { get; set; } = 300;
    
    /// <summary>
    /// 最大延时（毫秒）
    /// </summary>
    public int MaxDelayMs { get; set; } = 1500;
    
    /// <summary>
    /// 是否强制刷新
    /// </summary>
    public bool ForceRefresh { get; set; } = false;
    
    /// <summary>
    /// 消息创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;
    
    /// <summary>
    /// 优先级（数字越小优先级越高）
    /// </summary>
    public int Priority { get; set; } = 5;
    
    /// <summary>
    /// 同步类型
    /// </summary>
    public SyncType SyncType { get; set; } = SyncType.Full;
    
    /// <summary>
    /// 上次同步时间
    /// </summary>
    public DateTime? LastSyncTime { get; set; }
}

/// <summary>
/// 群组同步进度消息
/// </summary>
public class GroupSyncProgressMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 同步状态
    /// </summary>
    public SyncStatus Status { get; set; }
    
    /// <summary>
    /// 总群组数量
    /// </summary>
    public int TotalGroups { get; set; }
    
    /// <summary>
    /// 已处理群组数量
    /// </summary>
    public int ProcessedGroups { get; set; }
    
    /// <summary>
    /// 成功处理的群组数量
    /// </summary>
    public int SuccessGroups { get; set; }
    
    /// <summary>
    /// 失败的群组数量
    /// </summary>
    public int FailedGroups { get; set; }
    
    /// <summary>
    /// 当前处理的群组
    /// </summary>
    public string? CurrentGroup { get; set; }
    
    /// <summary>
    /// 当前处理阶段
    /// </summary>
    public string? CurrentStage { get; set; }
    
    /// <summary>
    /// 群成员处理进度
    /// </summary>
    public GroupMemberSyncProgress? MemberProgress { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 进度百分比
    /// </summary>
    public int ProgressPercentage => TotalGroups > 0 ? (ProcessedGroups * 100) / TotalGroups : 0;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 队列状态信息
/// </summary>
public class QueueStatusInfo
{
    /// <summary>
    /// 是否暂停
    /// </summary>
    public bool IsPaused { get; set; }
    
    /// <summary>
    /// 队列中待处理消息数量
    /// </summary>
    public int PendingCount { get; set; }
    
    /// <summary>
    /// 正在处理的消息数量
    /// </summary>
    public int ProcessingCount { get; set; }
    
    /// <summary>
    /// 今日处理的消息数量
    /// </summary>
    public int TodayProcessedCount { get; set; }
    
    /// <summary>
    /// 队列健康状态
    /// </summary>
    public string HealthStatus { get; set; } = "Healthy";
}