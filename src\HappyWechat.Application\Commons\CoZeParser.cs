﻿using Newtonsoft.Json;

namespace HappyWechat.Application.Commons;

public class CoZeParser
{
    public class ParseResult
    {
        public string Text { get; set; } = string.Empty;
        public List<string> ImageUrls { get; set; } = [];
        public List<string> VideoUrls { get; set; } = [];
        public List<string> FileUrls { get; set; } = [];
        public List<string> OtherUrls { get; set; } = [];
        public string MessageType { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public static ParseResult Parse(string content)
    {
        var result = new ParseResult();
        var UrlIdentifiers = new Dictionary<string, Action<string>>()
        {
            { "图片链接", url => result.ImageUrls.Add(url) },
            { "视频链接", url => result.VideoUrls.Add(url) },
            { "文件链接", url => result.FileUrls.Add(url) },
        };
        var array = content.Split(new[] { "\\n", "\n" }, StringSplitOptions.RemoveEmptyEntries)
            .Select(line => line.Trim())
            .Where(line => !string.IsNullOrEmpty(line))
            .ToArray();
        for (int i = 0; i < array.Length; i++)
        {
            string? matchedKey = UrlIdentifiers.Keys.FirstOrDefault(array[i].Contains);
            if (matchedKey is not null)
            {
                var addUri = UrlIdentifiers[matchedKey];
                if (i + 1 < array.Length && IsValidUrl(array[i + 1]))
                {
                    addUri(array[i + 1]);
                    i++;
                }
            }
            else if (IsValidUrl(array[i]))
            {
                CategorizeUrl(array[i], result);
            }
            else
            {
                result.Text += array[i] + "\n";
            }
        }

        return result;
    }

    private static bool IsValidUrl(string str)
    {
        return Uri.TryCreate(str, UriKind.Absolute, out var uri) &&
               (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps);
    }

    private static void CategorizeUrl(string url, ParseResult result)
    {
        try
        {
            var uri = new Uri(url);
            var extension = Path.GetExtension(uri.AbsolutePath).ToLower();

            switch (extension)
            {
                case ".png":
                case ".jpg":
                case ".jpeg":
                case ".gif":
                    result.ImageUrls.Add(url);
                    break;
                case ".mp4":
                case ".mov":
                case ".avi":
                    result.VideoUrls.Add(url);
                    break;
                case ".pdf":
                case ".doc":
                case ".docx":
                case ".xlsx":
                    result.FileUrls.Add(url);
                    break;
                default:
                    result.OtherUrls.Add(url);
                    break;
            }
        }
        catch
        {
            result.OtherUrls.Add(url);
        }
    }
}