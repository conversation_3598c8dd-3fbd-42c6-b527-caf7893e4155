using HappyWechat.Infrastructure.Notifications.Models;

namespace HappyWechat.Infrastructure.Notifications.Interfaces;

/// <summary>
/// 统一同步通知服务接口
/// 提供统一、可靠的同步通知机制
/// </summary>
public interface IUnifiedSyncNotificationService
{
    /// <summary>
    /// 发送联系人同步进度通知
    /// </summary>
    /// <param name="notification">进度通知</param>
    /// <param name="options">发送选项</param>
    /// <returns>发送结果</returns>
    Task<NotificationSendResult> SendContactSyncProgressAsync(
        SyncProgressNotification notification, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 发送联系人同步完成通知
    /// </summary>
    /// <param name="notification">完成通知</param>
    /// <param name="options">发送选项</param>
    /// <returns>发送结果</returns>
    Task<NotificationSendResult> SendContactSyncCompletedAsync(
        ContactSyncCompletionNotification notification, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 发送群组同步进度通知
    /// </summary>
    /// <param name="notification">进度通知</param>
    /// <param name="options">发送选项</param>
    /// <returns>发送结果</returns>
    Task<NotificationSendResult> SendGroupSyncProgressAsync(
        SyncProgressNotification notification, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 发送群组同步完成通知
    /// </summary>
    /// <param name="notification">完成通知</param>
    /// <param name="options">发送选项</param>
    /// <returns>发送结果</returns>
    Task<NotificationSendResult> SendGroupSyncCompletedAsync(
        GroupSyncCompletionNotification notification, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 发送同步失败通知
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="syncType">同步类型（Contact/Group）</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="options">发送选项</param>
    /// <returns>发送结果</returns>
    Task<NotificationSendResult> SendSyncFailedAsync(
        Guid wxManagerId, 
        string syncType, 
        string errorMessage, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 批量发送通知
    /// </summary>
    /// <param name="notifications">通知列表</param>
    /// <param name="options">发送选项</param>
    /// <returns>批量发送结果</returns>
    Task<List<NotificationSendResult>> SendBatchNotificationsAsync(
        List<BaseSyncNotification> notifications, 
        NotificationSendOptions? options = null);

    /// <summary>
    /// 检查通知服务健康状态
    /// </summary>
    /// <returns>健康状态</returns>
    Task<bool> CheckHealthAsync();

    /// <summary>
    /// 获取通知统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<NotificationStatistics> GetStatisticsAsync();
}

/// <summary>
/// 通知统计信息
/// </summary>
public class NotificationStatistics
{
    /// <summary>
    /// 总发送次数
    /// </summary>
    public long TotalSent { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public long RetryCount { get; set; }

    /// <summary>
    /// 降级次数
    /// </summary>
    public long FallbackCount { get; set; }

    /// <summary>
    /// 平均发送耗时（毫秒）
    /// </summary>
    public double AverageElapsedMs { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalSent > 0 ? (double)SuccessCount / TotalSent * 100 : 0;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
