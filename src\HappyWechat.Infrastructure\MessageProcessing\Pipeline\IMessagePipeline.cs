using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.MessageQueue;

namespace HappyWechat.Infrastructure.MessageProcessing.Pipeline;

/// <summary>
/// 消息处理流水线接口
/// 定义统一的消息处理流程，支持快速和慢速处理模式
/// </summary>
public interface IMessagePipeline
{
    /// <summary>
    /// 流水线名称
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 流水线类型
    /// </summary>
    PipelineType Type { get; }
    
    /// <summary>
    /// 是否可以处理指定类型的消息
    /// </summary>
    bool CanProcess(string messageType);
    
    /// <summary>
    /// 处理消息
    /// </summary>
    Task<PipelineResult> ProcessAsync(WxCallbackMessageDto message, PipelineContext context, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取流水线状态
    /// </summary>
    PipelineStatus GetStatus();
}

/// <summary>
/// 流水线类型
/// </summary>
public enum PipelineType
{
    /// <summary>
    /// 快速流水线 - 处理文本消息
    /// </summary>
    Fast,
    
    /// <summary>
    /// 慢速流水线 - 处理媒体文件
    /// </summary>
    Slow,
    
    /// <summary>
    /// 协调流水线 - 处理需要组合的消息
    /// </summary>
    Coordination
}

/// <summary>
/// 流水线处理结果
/// </summary>
public class PipelineResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ProcessingId { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    
    public static PipelineResult CreateSuccess(string processingId, TimeSpan processingTime)
    {
        return new PipelineResult
        {
            Success = true,
            ProcessingId = processingId,
            ProcessingTime = processingTime
        };
    }
    
    public static PipelineResult CreateFailure(string errorMessage, string? processingId = null)
    {
        return new PipelineResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            ProcessingId = processingId
        };
    }
}

/// <summary>
/// 流水线处理上下文
/// </summary>
public class PipelineContext
{
    public Guid WxManagerId { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public CancellationToken CancellationToken { get; set; }
}

/// <summary>
/// 流水线状态
/// </summary>
public class PipelineStatus
{
    public string Name { get; set; } = string.Empty;
    public PipelineType Type { get; set; }
    public bool IsHealthy { get; set; }
    public int ActiveTasks { get; set; }
    public int QueuedMessages { get; set; }
    public double ThroughputPerSecond { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
    public DateTime LastProcessedAt { get; set; }
}
