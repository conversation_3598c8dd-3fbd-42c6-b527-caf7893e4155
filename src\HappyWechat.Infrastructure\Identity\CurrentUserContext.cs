using System.Security.Claims;
using HappyWechat.Application.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Identity;

/// <summary>
/// 当前用户上下文实现
/// 基于ASP.NET Core的HttpContext提供用户身份信息
/// </summary>
public class CurrentUserContext : ICurrentUserContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<CurrentUserContext> _logger;
    
    private ClaimsPrincipal? _user;
    private Guid? _userId;
    private string? _username;
    private string? _displayName;
    private string? _email;
    private IReadOnlyList<string>? _roles;

    public CurrentUserContext(IHttpContextAccessor httpContextAccessor, ILogger<CurrentUserContext> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户的ClaimsPrincipal
    /// </summary>
    private ClaimsPrincipal? User => _user ??= _httpContextAccessor.HttpContext?.User;

    /// <summary>
    /// 当前用户ID
    /// </summary>
    public Guid UserId
    {
        get
        {
            if (_userId.HasValue)
                return _userId.Value;

            if (User?.Identity?.IsAuthenticated != true)
            {
                _logger.LogWarning("尝试获取用户ID，但用户未认证");
                return Guid.Empty;
            }

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                _logger.LogWarning("用户已认证但无法解析用户ID - Claim: {UserIdClaim}", userIdClaim);
                _userId = Guid.Empty;
                return Guid.Empty;
            }

            _userId = userId;
            return userId;
        }
    }

    /// <summary>
    /// 当前用户名
    /// </summary>
    public string? Username
    {
        get
        {
            if (_username != null)
                return _username;

            _username = User?.FindFirst(ClaimTypes.Name)?.Value ?? 
                       User?.FindFirst("preferred_username")?.Value ??
                       User?.Identity?.Name;
            
            return _username;
        }
    }

    /// <summary>
    /// 当前用户显示名称
    /// </summary>
    public string? DisplayName
    {
        get
        {
            if (_displayName != null)
                return _displayName;

            _displayName = User?.FindFirst("DisplayName")?.Value ?? 
                          User?.FindFirst(ClaimTypes.GivenName)?.Value ??
                          User?.FindFirst("name")?.Value ??
                          Username;
            
            return _displayName;
        }
    }

    /// <summary>
    /// 当前用户邮箱
    /// </summary>
    public string? Email
    {
        get
        {
            if (_email != null)
                return _email;

            _email = User?.FindFirst(ClaimTypes.Email)?.Value ??
                    User?.FindFirst("email")?.Value;
            
            return _email;
        }
    }

    /// <summary>
    /// 当前用户角色列表
    /// </summary>
    public IReadOnlyList<string> Roles
    {
        get
        {
            if (_roles != null)
                return _roles;

            if (User?.Identity?.IsAuthenticated != true)
            {
                _roles = Array.Empty<string>();
                return _roles;
            }

            _roles = User.FindAll(ClaimTypes.Role)
                        .Select(c => c.Value)
                        .Where(role => !string.IsNullOrWhiteSpace(role))
                        .ToList()
                        .AsReadOnly();
            
            return _roles;
        }
    }

    /// <summary>
    /// 用户是否已认证
    /// </summary>
    public bool IsAuthenticated => User?.Identity?.IsAuthenticated == true;

    /// <summary>
    /// 用户是否有指定角色
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <returns>是否有该角色</returns>
    public bool HasRole(string role)
    {
        if (string.IsNullOrWhiteSpace(role))
            return false;

        return Roles.Contains(role, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 用户是否有任一指定角色
    /// </summary>
    /// <param name="roles">角色名称列表</param>
    /// <returns>是否有任一角色</returns>
    public bool HasAnyRole(params string[] roles)
    {
        if (roles == null || roles.Length == 0)
            return false;

        return roles.Any(role => HasRole(role));
    }

    /// <summary>
    /// 用户是否有所有指定角色
    /// </summary>
    /// <param name="roles">角色名称列表</param>
    /// <returns>是否有所有角色</returns>
    public bool HasAllRoles(params string[] roles)
    {
        if (roles == null || roles.Length == 0)
            return true;

        return roles.All(role => HasRole(role));
    }

    /// <summary>
    /// 确保用户已认证，否则抛出异常
    /// </summary>
    /// <exception cref="UnauthorizedAccessException">用户未认证时抛出</exception>
    public void EnsureAuthenticated()
    {
        if (!IsAuthenticated)
        {
            _logger.LogWarning("用户未认证，访问被拒绝");
            throw new UnauthorizedAccessException("用户未认证，请先登录");
        }
    }

    /// <summary>
    /// 确保用户有指定角色，否则抛出异常
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <exception cref="UnauthorizedAccessException">用户没有该角色时抛出</exception>
    public void EnsureRole(string role)
    {
        EnsureAuthenticated();
        
        if (!HasRole(role))
        {
            _logger.LogWarning("用户 {UserId} 没有角色 {Role}，访问被拒绝", UserId, role);
            throw new UnauthorizedAccessException($"用户没有 '{role}' 角色权限");
        }
    }
}