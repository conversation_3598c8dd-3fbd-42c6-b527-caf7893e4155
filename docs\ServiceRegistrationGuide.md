# 服务注册开发指南

## 概述

本指南定义了HappyWechat项目中服务注册的标准流程和最佳实践，确保所有开发者能够正确地注册和管理依赖注入服务。

## 服务注册架构

### 分层注册结构

项目采用分层服务注册架构，避免单一文件过大：

```
ServiceRegistration/
├── UnifiedInfrastructureServices.cs    # 主入口，统一调用其他注册方法
├── CoreServicesExtensions.cs           # 核心业务服务（用户、AI、微信）
├── EYunServiceRegistrationExtensions.cs # EYun API服务群组
├── ServiceRegistrationValidator.cs      # 服务注册完整性验证
└── [其他专门的服务注册扩展]
```

### 服务生命周期策略

- **Singleton**: 配置管理器、HTTP客户端工厂、缓存服务
- **Scoped**: 业务服务、数据访问服务、API服务（推荐）
- **Transient**: 轻量级辅助服务、工具类

## 新服务开发清单

### 1. 接口定义

在 `HappyWechat.Application/Interfaces/` 中定义服务接口：

```csharp
namespace HappyWechat.Application.Interfaces;

public interface IMyNewService
{
    Task<string> DoSomethingAsync();
}
```

### 2. 实现类

在 `HappyWechat.Infrastructure/` 相应目录中创建实现：

```csharp
namespace HappyWechat.Infrastructure.MyModule;

public class MyNewService : IMyNewService
{
    private readonly ILogger<MyNewService> _logger;
    
    public MyNewService(ILogger<MyNewService> logger)
    {
        _logger = logger;
    }
    
    public async Task<string> DoSomethingAsync()
    {
        // 实现逻辑
        return "Done";
    }
}
```

### 3. 服务注册

在适当的服务注册扩展中注册：

```csharp
// 在CoreServicesExtensions.cs或其他相应的扩展中
services.AddScoped<IMyNewService, MyNewService>();
```

### 4. 验证

启动应用时会自动验证服务注册完整性。

## 依赖注入最佳实践

### ✅ 正确做法

1. **依赖接口而不是具体实现**：
   ```csharp
   public class MyService
   {
       private readonly ILogger<MyService> _logger;     // ✅ 正确
       private readonly IHttpClientFactory _factory;   // ✅ 正确
   ```

2. **使用构造函数注入**：
   ```csharp
   public MyService(ILogger<MyService> logger, IHttpClientFactory factory)
   {
       _logger = logger;
       _factory = factory;
   }
   ```

3. **遵循单一职责原则**：
   ```csharp
   // ✅ 职责清晰的服务
   public class UserService : IUserService
   {
       // 只处理用户相关逻辑
   }
   ```

### ❌ 错误做法

1. **直接依赖具体类型**：
   ```csharp
   public class MyService
   {
       private readonly ConcreteService _service; // ❌ 错误
   ```

2. **在业务代码中手动创建实例**：
   ```csharp
   // ❌ 错误
   var service = new MyDependentService();
   ```

3. **循环依赖**：
   ```csharp
   // ❌ 错误 - A依赖B，B又依赖A
   public class ServiceA { public ServiceA(IServiceB b) }
   public class ServiceB { public ServiceB(IServiceA a) }
   ```

## 占位符服务机制

对于尚未实现但需要注册的服务，使用占位符：

```csharp
services.AddScoped<IMyFutureService>(provider => 
    new PlaceholderService<IMyFutureService>("IMyFutureService"));
```

占位符服务的优势：
- 避免依赖注入失败
- 清楚标识未实现的服务
- 便于后续完整实现

## 服务注册验证

系统提供自动化的服务注册验证：

### 关键服务验证
启动时验证关键服务是否可正常解析

### 完整性验证（开发环境）
检查所有Application层接口是否有对应的注册

### 手动验证命令
```csharp
// 在开发时手动验证
ServiceRegistrationValidator.ValidateServiceRegistrations(serviceProvider, logger);
```

## 常见问题排查

### 1. 依赖注入失败
```
Unable to resolve service for type 'IMyService'
```
**解决方法**: 检查服务是否已注册，确认生命周期配置正确

### 2. 循环依赖
```
A circular dependency was detected
```
**解决方法**: 重新设计服务依赖关系，考虑使用事件或中介者模式

### 3. 生命周期不匹配
```
Cannot consume scoped service from singleton
```
**解决方法**: 调整服务生命周期，或使用 `IServiceScopeFactory`

## 性能考虑

1. **避免过度注册**: 只注册实际使用的服务
2. **合理选择生命周期**: Scoped通常是最佳选择
3. **延迟初始化**: 对于重型服务考虑延迟加载
4. **监控启动时间**: 过多的服务注册会影响启动性能

## 开发流程

1. **需求分析** → 确定需要哪些服务接口
2. **接口设计** → 在Application层定义接口
3. **实现开发** → 在Infrastructure层实现接口
4. **服务注册** → 在相应的注册扩展中注册
5. **验证测试** → 启动应用验证注册正确性
6. **集成测试** → 确保服务在实际场景中正常工作

## 技术债务管理

定期检查和清理：
- 未使用的服务注册
- 过时的占位符服务
- 不必要的服务依赖
- 重复的服务实现

---

遵循本指南可以确保服务注册的一致性、可维护性和系统的整体健康。