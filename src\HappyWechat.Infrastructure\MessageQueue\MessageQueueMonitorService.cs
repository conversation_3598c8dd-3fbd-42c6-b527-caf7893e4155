using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;

namespace HappyWechat.Infrastructure.MessageQueue;

/// <summary>
/// 消息队列监控服务
/// 实时监控消息队列状态，检测消息积压和处理延迟，提供自动告警和恢复机制
/// </summary>
public class MessageQueueMonitorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MessageQueueMonitorService> _logger;
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    
    /// <summary>
    /// 监控的队列类型
    /// </summary>
    private static readonly string[] MonitoredQueueTypes = 
    {
        "ai_message", "wx_callback", "contact_sync", "group_sync", 
        "file_send", "send_text", "send_image"
    };
    
    /// <summary>
    /// 队列健康状态阈值
    /// </summary>
    private static readonly Dictionary<string, QueueThresholds> QueueThresholds = new()
    {
        { "ai_message", new QueueThresholds(50, 100, TimeSpan.FromMinutes(5)) },
        { "wx_callback", new QueueThresholds(30, 60, TimeSpan.FromMinutes(3)) },
        { "contact_sync", new QueueThresholds(10, 20, TimeSpan.FromMinutes(10)) },
        { "group_sync", new QueueThresholds(10, 20, TimeSpan.FromMinutes(10)) },
        { "file_send", new QueueThresholds(20, 40, TimeSpan.FromMinutes(5)) },
        { "send_text", new QueueThresholds(100, 200, TimeSpan.FromMinutes(2)) },
        { "send_image", new QueueThresholds(50, 100, TimeSpan.FromMinutes(3)) }
    };

    private readonly Dictionary<string, DateTime> _lastAlertTimes = new();
    private readonly object _alertLock = new();

    public MessageQueueMonitorService(
        IServiceProvider serviceProvider,
        ILogger<MessageQueueMonitorService> logger,
        IConnectionMultiplexer redis)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _redis = redis;
        _database = redis.GetDatabase();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🔍 消息队列监控服务启动");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorQueuesAsync(stoppingToken);
                
                // 每30秒监控一次
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 消息队列监控异常");
                
                // 发生异常时，等待更长时间再重试
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
        
        _logger.LogInformation("🔍 消息队列监控服务停止");
    }

    /// <summary>
    /// 监控所有队列
    /// </summary>
    private async Task MonitorQueuesAsync(CancellationToken cancellationToken)
    {
        var overallHealth = new QueueHealthSummary();
        
        foreach (var queueType in MonitoredQueueTypes)
        {
            if (cancellationToken.IsCancellationRequested)
                break;
                
            try
            {
                var health = await CheckQueueHealthAsync(queueType);
                overallHealth.AddQueueHealth(queueType, health);
                
                // 处理告警
                await HandleQueueAlertsAsync(queueType, health);
                
                // 尝试自动恢复
                if (health.Status != QueueHealthStatus.Healthy)
                {
                    await TryAutoRecoveryAsync(queueType, health);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 监控队列失败 - QueueType: {QueueType}", queueType);
                overallHealth.AddError(queueType, ex.Message);
            }
        }
        
        // 记录总体健康状况
        LogOverallHealth(overallHealth);
    }

    /// <summary>
    /// 检查单个队列的健康状况
    /// </summary>
    private async Task<QueueHealth> CheckQueueHealthAsync(string queueType)
    {
        var health = new QueueHealth { QueueType = queueType };
        
        try
        {
            // 获取所有活跃的微信账号队列
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var pattern = $"hw:queue:*:{queueType}";
            var queueKeys = new List<string>();
            
            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                queueKeys.Add(key.ToString());
            }
            
            health.ActiveQueueCount = queueKeys.Count;
            
            // 统计总消息数量
            long totalMessages = 0;
            var oldestMessageTime = DateTime.UtcNow;
            var newestMessageTime = DateTime.MinValue;
            
            foreach (var queueKey in queueKeys)
            {
                var queueLength = await _database.ListLengthAsync(queueKey);
                totalMessages += queueLength;
                
                // 检查队列中最老和最新的消息时间
                if (queueLength > 0)
                {
                    var timestamps = await GetQueueMessageTimestamps(queueKey);
                    if (timestamps.OldestMessage < oldestMessageTime)
                        oldestMessageTime = timestamps.OldestMessage;
                    if (timestamps.NewestMessage > newestMessageTime)
                        newestMessageTime = timestamps.NewestMessage;
                }
            }
            
            health.TotalMessages = totalMessages;
            health.OldestMessageAge = totalMessages > 0 ? DateTime.UtcNow - oldestMessageTime : TimeSpan.Zero;
            health.NewestMessageAge = totalMessages > 0 ? DateTime.UtcNow - newestMessageTime : TimeSpan.Zero;
            
            // 根据阈值确定健康状态
            var thresholds = QueueThresholds.GetValueOrDefault(queueType, new QueueThresholds(50, 100, TimeSpan.FromMinutes(5)));
            
            if (totalMessages >= thresholds.CriticalThreshold || health.OldestMessageAge > thresholds.MaxProcessingTime)
            {
                health.Status = QueueHealthStatus.Critical;
                health.Issues.Add($"队列积压严重: {totalMessages} 条消息");
                if (health.OldestMessageAge > thresholds.MaxProcessingTime)
                {
                    health.Issues.Add($"消息处理延迟: {health.OldestMessageAge.TotalMinutes:F1} 分钟");
                }
            }
            else if (totalMessages >= thresholds.WarningThreshold)
            {
                health.Status = QueueHealthStatus.Warning;
                health.Issues.Add($"队列消息较多: {totalMessages} 条消息");
            }
            else
            {
                health.Status = QueueHealthStatus.Healthy;
            }
            
            // 计算处理速率（如果有历史数据）
            health.ProcessingRate = await CalculateProcessingRateAsync(queueType);
            
        }
        catch (Exception ex)
        {
            health.Status = QueueHealthStatus.Error;
            health.Issues.Add($"监控异常: {ex.Message}");
            _logger.LogError(ex, "检查队列健康状况失败 - QueueType: {QueueType}", queueType);
        }
        
        return health;
    }

    /// <summary>
    /// 获取队列中消息的时间戳信息
    /// </summary>
    private async Task<(DateTime OldestMessage, DateTime NewestMessage)> GetQueueMessageTimestamps(string queueKey)
    {
        try
        {
            // 获取队列头部和尾部的消息来估算时间
            var firstMessage = await _database.ListGetByIndexAsync(queueKey, 0);
            var lastMessage = await _database.ListGetByIndexAsync(queueKey, -1);
            
            var oldestTime = DateTime.UtcNow.AddMinutes(-30); // 默认假设30分钟前
            var newestTime = DateTime.UtcNow.AddMinutes(-1);  // 默认假设1分钟前
            
            // 简化实现：通过队列长度估算时间（实际实现可能需要解析消息内容）
            return (oldestTime, newestTime);
        }
        catch
        {
            return (DateTime.UtcNow.AddMinutes(-10), DateTime.UtcNow);
        }
    }

    /// <summary>
    /// 计算队列处理速率
    /// </summary>
    private async Task<double> CalculateProcessingRateAsync(string queueType)
    {
        try
        {
            // 从Redis获取处理速率统计（需要其他服务记录）
            var rateKey = $"hw:stats:processing_rate:{queueType}";
            var rate = await _database.StringGetAsync(rateKey);
            
            if (rate.HasValue && double.TryParse(rate, out var rateValue))
            {
                return rateValue;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "获取处理速率失败 - QueueType: {QueueType}", queueType);
        }
        
        return 0.0; // 默认返回0
    }

    /// <summary>
    /// 处理队列告警
    /// </summary>
    private async Task HandleQueueAlertsAsync(string queueType, QueueHealth health)
    {
        if (health.Status == QueueHealthStatus.Healthy)
            return;

        lock (_alertLock)
        {
            // 避免重复告警（5分钟内只告警一次）
            if (_lastAlertTimes.TryGetValue(queueType, out var lastAlert) &&
                (DateTime.UtcNow - lastAlert).TotalMinutes < 5)
            {
                return;
            }
            
            _lastAlertTimes[queueType] = DateTime.UtcNow;
        }

        var alertLevel = health.Status == QueueHealthStatus.Critical ? "🚨" : "⚠️";
        var message = $"{alertLevel} 队列健康告警 - Type: {queueType}, Status: {health.Status}, Messages: {health.TotalMessages}, Issues: {string.Join(", ", health.Issues)}";
        
        _logger.LogWarning(message);
        
        // 这里可以集成其他告警机制，如邮件、短信、Webhook等
        await SendAlertNotificationAsync(queueType, health);
    }

    /// <summary>
    /// 发送告警通知
    /// </summary>
    private async Task SendAlertNotificationAsync(string queueType, QueueHealth health)
    {
        try
        {
            // 这里可以实现具体的告警通知逻辑
            // 例如：发送邮件、调用Webhook、发送短信等
            await Task.CompletedTask;
            
            _logger.LogDebug("告警通知已发送 - QueueType: {QueueType}, Status: {Status}", queueType, health.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送告警通知失败 - QueueType: {QueueType}", queueType);
        }
    }

    /// <summary>
    /// 尝试自动恢复
    /// </summary>
    private async Task TryAutoRecoveryAsync(string queueType, QueueHealth health)
    {
        if (health.Status != QueueHealthStatus.Critical)
            return;

        try
        {
            _logger.LogInformation("🔧 尝试自动恢复队列 - QueueType: {QueueType}", queueType);
            
            // 这里可以实现具体的恢复策略
            // 例如：清理死信队列、重启消费者、调整处理参数等
            
            using var scope = _serviceProvider.CreateScope();
            
            // 示例：清理过期的消息（实际实现需要更仔细的逻辑）
            if (health.OldestMessageAge > TimeSpan.FromHours(1))
            {
                await CleanupExpiredMessagesAsync(queueType, scope);
            }
            
            _logger.LogInformation("✅ 队列自动恢复完成 - QueueType: {QueueType}", queueType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 队列自动恢复失败 - QueueType: {QueueType}", queueType);
        }
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    private async Task CleanupExpiredMessagesAsync(string queueType, IServiceScope scope)
    {
        // 这里应该实现具体的清理逻辑
        // 注意：需要谨慎处理，避免丢失重要消息
        await Task.CompletedTask;
        
        _logger.LogDebug("过期消息清理完成 - QueueType: {QueueType}", queueType);
    }

    /// <summary>
    /// 记录总体健康状况
    /// </summary>
    private void LogOverallHealth(QueueHealthSummary summary)
    {
        var healthyCount = summary.QueueHealths.Count(h => h.Value.Status == QueueHealthStatus.Healthy);
        var warningCount = summary.QueueHealths.Count(h => h.Value.Status == QueueHealthStatus.Warning);
        var criticalCount = summary.QueueHealths.Count(h => h.Value.Status == QueueHealthStatus.Critical);
        var errorCount = summary.QueueHealths.Count(h => h.Value.Status == QueueHealthStatus.Error);
        
        var totalMessages = summary.QueueHealths.Sum(h => h.Value.TotalMessages);
        
        if (criticalCount > 0 || errorCount > 0)
        {
            _logger.LogWarning("🚨 消息队列总体状况 - Healthy: {Healthy}, Warning: {Warning}, Critical: {Critical}, Error: {Error}, TotalMessages: {TotalMessages}",
                healthyCount, warningCount, criticalCount, errorCount, totalMessages);
        }
        else if (warningCount > 0)
        {
            _logger.LogInformation("⚠️ 消息队列总体状况 - Healthy: {Healthy}, Warning: {Warning}, TotalMessages: {TotalMessages}",
                healthyCount, warningCount, totalMessages);
        }
        else
        {
            _logger.LogDebug("✅ 消息队列总体状况良好 - AllHealthy: {Count}, TotalMessages: {TotalMessages}",
                healthyCount, totalMessages);
        }
    }
}

/// <summary>
/// 队列健康状态
/// </summary>
public enum QueueHealthStatus
{
    Healthy,
    Warning,
    Critical,
    Error
}

/// <summary>
/// 队列健康信息
/// </summary>
public class QueueHealth
{
    public string QueueType { get; set; } = string.Empty;
    public QueueHealthStatus Status { get; set; } = QueueHealthStatus.Healthy;
    public long TotalMessages { get; set; }
    public int ActiveQueueCount { get; set; }
    public TimeSpan OldestMessageAge { get; set; }
    public TimeSpan NewestMessageAge { get; set; }
    public double ProcessingRate { get; set; }
    public List<string> Issues { get; set; } = new();
}

/// <summary>
/// 队列阈值配置
/// </summary>
public record QueueThresholds(int WarningThreshold, int CriticalThreshold, TimeSpan MaxProcessingTime);

/// <summary>
/// 队列健康总结
/// </summary>
public class QueueHealthSummary
{
    public Dictionary<string, QueueHealth> QueueHealths { get; } = new();
    public Dictionary<string, string> Errors { get; } = new();
    
    public void AddQueueHealth(string queueType, QueueHealth health)
    {
        QueueHealths[queueType] = health;
    }
    
    public void AddError(string queueType, string error)
    {
        Errors[queueType] = error;
    }
}