using HappyWechat.Domain.ValueObjects.Enums;
namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 联系人AI配置DTO
/// </summary>
public class ContactAiConfigDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 联系人ID（外键关联WxContactEntity）
    /// </summary>
    public Guid ContactId { get; set; }
    
    /// <summary>
    /// 联系人信息（通过导航属性获取）
    /// </summary>
    public ContactInfo? Contact { get; set; }
    
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool IsEnabled { get; set; } = false;
    

    
    /// <summary>
    /// 关联的AI智能体ID
    /// </summary>
    public Guid? AiAgentId { get; set; }
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string? AiAgentName { get; set; }
    
    /// <summary>
    /// 自定义提示词（联系人不使用，设置为null）
    /// </summary>
    public string? CustomPrompt { get; set; }
    
    /// <summary>
    /// 回复延迟时间（秒）（从AI智能体获取）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 3;
    
    /// <summary>
    /// 是否启用敏感词过滤（从AI智能体获取）
    /// </summary>
    public bool EnableSensitiveWordFilter { get; set; } = true;

    /// <summary>
    /// 是否为手动回复模式
    /// </summary>
    public bool IsManualReplyMode { get; set; } = false;
}


/// <summary>
/// 联系人基本信息
/// </summary>
public class ContactInfo
{
    /// <summary>
    /// 联系人微信ID
    /// </summary>
    public string WcId { get; set; } = string.Empty;
    
    /// <summary>
    /// 联系人昵称
    /// </summary>
    public string? NickName { get; set; } = string.Empty;
    
    /// <summary>
    /// 联系人头像
    /// </summary>
    public string? BigHead { get; set; } = string.Empty;
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
}

