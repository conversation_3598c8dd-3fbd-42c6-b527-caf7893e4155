namespace HappyWechat.Application.DTOs.MessageProcess;

/// <summary>
/// 文件下载任务DTO
/// </summary>
public class FileDownloadTaskDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 优先级（1-10，数值越大优先级越高）
    /// </summary>
    public int Priority { get; set; } = 5;
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 关联的非文本消息
    /// </summary>
    public NonTextMessageDto Message { get; set; } = new();
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 任务创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryAt { get; set; }
    
    /// <summary>
    /// 下次重试时间
    /// </summary>
    public DateTime? NextRetryAt { get; set; }
    
    /// <summary>
    /// 处理配置
    /// </summary>
    public MessageProcessOptions? ProcessOptions { get; set; }
    
    /// <summary>
    /// 任务状态
    /// </summary>
    public FileDownloadTaskStatus Status { get; set; } = FileDownloadTaskStatus.Pending;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object>? ExtendedProperties { get; set; }
    
    /// <summary>
    /// 文件哈希值（用于去重）
    /// </summary>
    public string? FileHash { get; set; }
    
    /// <summary>
    /// 是否需要重试
    /// </summary>
    public bool ShouldRetry => Status == FileDownloadTaskStatus.Failed && RetryCount < MaxRetryCount;
    
    /// <summary>
    /// 计算下次重试时间
    /// </summary>
    /// <returns></returns>
    public DateTime CalculateNextRetryTime()
    {
        // 指数退避算法：2^重试次数 * 基础延迟（秒）
        var baseDelaySeconds = 30;
        var delaySeconds = Math.Pow(2, RetryCount) * baseDelaySeconds;
        return DateTime.UtcNow.AddSeconds(delaySeconds);
    }
}

/// <summary>
/// 文件下载任务状态
/// </summary>
public enum FileDownloadTaskStatus
{
    /// <summary>
    /// 等待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 正在处理
    /// </summary>
    Processing = 1,
    
    /// <summary>
    /// 处理完成
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// 处理失败
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4,
    
    /// <summary>
    /// 重复文件（已跳过）
    /// </summary>
    Duplicate = 5
}

/// <summary>
/// 批量文件下载结果
/// </summary>
public class BatchFileDownloadResult
{
    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }
    
    /// <summary>
    /// 成功下载数
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 失败数
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 跳过数（重复文件）
    /// </summary>
    public int SkippedCount { get; set; }
    
    /// <summary>
    /// 总下载字节数
    /// </summary>
    public long TotalBytesDownloaded { get; set; }
    
    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalDurationMs { get; set; }
    
    /// <summary>
    /// 错误详情
    /// </summary>
    public List<FileDownloadError> Errors { get; set; } = new();
    
    /// <summary>
    /// 平均下载速度（字节/秒）
    /// </summary>
    public double AverageSpeedBps => TotalDurationMs > 0 ? TotalBytesDownloaded * 1000.0 / TotalDurationMs : 0;
}

/// <summary>
/// 文件下载错误信息
/// </summary>
public class FileDownloadError
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误类型
    /// </summary>
    public FileDownloadErrorType ErrorType { get; set; }
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// 发生时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 文件下载错误类型
/// </summary>
public enum FileDownloadErrorType
{
    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError = 1,
    
    /// <summary>
    /// 文件不存在
    /// </summary>
    FileNotFound = 2,
    
    /// <summary>
    /// 文件过大
    /// </summary>
    FileTooLarge = 3,
    
    /// <summary>
    /// 格式不支持
    /// </summary>
    UnsupportedFormat = 4,
    
    /// <summary>
    /// 存储空间不足
    /// </summary>
    InsufficientStorage = 5,
    
    /// <summary>
    /// 超时
    /// </summary>
    Timeout = 6,
    
    /// <summary>
    /// 访问被拒绝
    /// </summary>
    AccessDenied = 7,
    
    /// <summary>
    /// 其他错误
    /// </summary>
    Other = 99
}