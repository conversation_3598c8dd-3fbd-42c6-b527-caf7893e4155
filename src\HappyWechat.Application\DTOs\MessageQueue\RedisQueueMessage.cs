using System.Text.Json.Serialization;

namespace HappyWechat.Application.DTOs.MessageQueue;

/// <summary>
/// Redis队列消息基类
/// </summary>
public class RedisQueueMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("type")]
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 微信管理器ID
    /// </summary>
    [JsonPropertyName("wxManagerId")]
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 延时执行时间（Unix时间戳）
    /// </summary>
    [JsonPropertyName("delayUntil")]
    public long? DelayUntil { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    [JsonPropertyName("retryCount")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [JsonPropertyName("maxRetryCount")]
    public int MaxRetryCount { get; set; } = 1;

    /// <summary>
    /// 优先级（数字越大优先级越高）
    /// </summary>
    [JsonPropertyName("priority")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 消息数据
    /// </summary>
    [JsonPropertyName("data")]
    public object? Data { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    [JsonPropertyName("properties")]
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 泛型Redis队列消息
/// </summary>
/// <typeparam name="T">消息数据类型</typeparam>
public class RedisQueueMessage<T> : RedisQueueMessage where T : class
{
    /// <summary>
    /// 强类型消息数据
    /// </summary>
    [JsonPropertyName("data")]
    public new T? Data { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public RedisQueueMessage()
    {
        MessageType = typeof(T).Name;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="data">消息数据</param>
    /// <param name="wxManagerId">微信管理器ID</param>
    public RedisQueueMessage(T data, Guid wxManagerId) : this()
    {
        Data = data;
        WxManagerId = wxManagerId;
    }
}

/// <summary>
/// 微信消息队列消息
/// </summary>
public class WxMessageQueueData
{
    /// <summary>
    /// 回调消息
    /// </summary>
    public HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto CallbackMessage { get; set; } = null!;

    /// <summary>
    /// 处理ID
    /// </summary>
    public string ProcessingId { get; set; } = string.Empty;
}

/// <summary>
/// AI消息队列消息
/// </summary>
public class AiMessageQueueData
{
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 发送者
    /// </summary>
    public string FromUser { get; set; } = string.Empty;

    /// <summary>
    /// 接收者
    /// </summary>
    public string ToUser { get; set; } = string.Empty;

    /// <summary>
    /// AI代理名称
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// 消息上下文
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 联系人同步队列消息
/// </summary>
public class ContactSyncQueueData
{
    /// <summary>
    /// 同步类型
    /// </summary>
    public ContactSyncType SyncType { get; set; }

    /// <summary>
    /// 联系人ID列表
    /// </summary>
    public List<string> ContactIds { get; set; } = new();

    /// <summary>
    /// 列表类型
    /// </summary>
    public int ListType { get; set; }

    /// <summary>
    /// 同步会话ID
    /// </summary>
    public string SyncSessionId { get; set; } = string.Empty;
}

/// <summary>
/// 群组同步队列消息
/// </summary>
public class GroupSyncQueueData
{
    /// <summary>
    /// 同步类型
    /// </summary>
    public GroupSyncType SyncType { get; set; }

    /// <summary>
    /// 群组ID列表
    /// </summary>
    public List<string> GroupIds { get; set; } = new();

    /// <summary>
    /// 同步会话ID
    /// </summary>
    public string SyncSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 是否同步成员
    /// </summary>
    public bool SyncMembers { get; set; } = true;
}

/// <summary>
/// 文件发送队列消息
/// </summary>
public class FileSendQueueData
{
    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 接收者
    /// </summary>
    public string ToUser { get; set; } = string.Empty;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 消息序号
    /// </summary>
    public int MessageOrder { get; set; }
}

/// <summary>
/// 联系人同步类型
/// </summary>
public enum ContactSyncType
{
    /// <summary>
    /// 获取联系人详情
    /// </summary>
    GetContactDetail = 1,

    /// <summary>
    /// 获取企业联系人详情
    /// </summary>
    GetOpenImContactDetail = 2
}

/// <summary>
/// 群组同步类型
/// </summary>
public enum GroupSyncType
{
    /// <summary>
    /// 获取群组信息
    /// </summary>
    GetGroupInfo = 1,

    /// <summary>
    /// 获取群组成员
    /// </summary>
    GetGroupMembers = 2
}
