using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Unified;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息发送目标决策服务接口
/// 统一处理群组/私聊消息的发送目标逻辑
/// </summary>
public interface IMessageTargetResolver
{
    /// <summary>
    /// 解析消息发送目标
    /// </summary>
    /// <param name="callbackMessage">回调消息</param>
    /// <param name="aiConfig">AI配置信息</param>
    /// <param name="processingId">处理ID</param>
    /// <returns>消息发送目标决策结果</returns>
    Task<MessageTargetDecision> ResolveTargetAsync(
        WxCallbackMessageDto callbackMessage, 
        AiConfigurationInfo aiConfig,
        string processingId);
    
    /// <summary>
    /// 检查文件+@组合功能
    /// </summary>
    /// <param name="callbackMessage">回调消息</param>
    /// <param name="processingId">处理ID</param>
    /// <returns>文件+@组合检测结果</returns>
    Task<FileAtCombinationResult> CheckFileAtCombinationAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId);
    
    /// <summary>
    /// 检查消息是否@了机器人或特定目标
    /// </summary>
    /// <param name="callbackMessage">回调消息</param>
    /// <returns>是否被@</returns>
    bool IsAtMentioned(WxCallbackMessageDto callbackMessage);
}
