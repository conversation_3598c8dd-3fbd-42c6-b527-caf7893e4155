using Newtonsoft.Json;

namespace HappyWechat.Application.DTOs.Wrappers.EYun;

public class WxCallbackMessageDto
{
    [JsonProperty("wcId")]
    public string WcId { get; set; }

    [JsonProperty("account")]
    public string Account { get; set; }

    [JsonProperty("messageType")]
    public string MessageType { get; set; }

    [JsonProperty("data")]
    public MessageData Data { get; set; }

    /// <summary>
    /// 微信管理器ID（内部使用，不参与JSON序列化）
    /// </summary>
    [JsonIgnore]
    public string WxManagerId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID（内部使用，不参与JSON序列化）
    /// </summary>
    [JsonIgnore]
    public string MessageId { get; set; } = string.Empty;

    public override string ToString()
    {
        return JsonConvert.SerializeObject(this);
    }
}
// 消息数据实体
public class MessageData
{
    [JsonProperty("fromUser")]
    public string FromUser { get; set; }

    [JsonProperty("fromGroup")]
    public string? FromGroup { get; set; }

    [JsonProperty("toUser")]
    public string ToUser { get; set; }

    [JsonProperty("msgId")]
    public long MsgId { get; set; }

    [JsonProperty("atlist")]
    public List<string>? Atlist { get; set; }

    [JsonProperty("newMsgId")]
    public long NewMsgId { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("content")]
    public string Content { get; set; } // 文本或XML

    [JsonProperty("self")]
    public bool Self { get; set; }

    // 特殊消息字段
    [JsonProperty("wId")]
    public string? WId { get; set; }

    /// <summary>
    /// 消息类型 - 从EYun API获取或根据内容推断
    /// </summary>
    [JsonProperty("msgType")]
    public int? MsgType { get; set; }

    // 🔧 新增：语音消息相关字段
    /// <summary>
    /// 语音消息长度（字节）
    /// </summary>
    [JsonProperty("length")]
    public int? Length { get; set; }

    /// <summary>
    /// 语音时长（毫秒）
    /// </summary>
    [JsonProperty("voiceLength")]
    public int? VoiceLength { get; set; }

    /// <summary>
    /// 语音消息缓冲区ID
    /// </summary>
    [JsonProperty("bufId")]
    public string? BufId { get; set; }

    /// <summary>
    /// 推送内容
    /// </summary>
    [JsonProperty("pushContent")]
    public string? PushContent { get; set; }
}

