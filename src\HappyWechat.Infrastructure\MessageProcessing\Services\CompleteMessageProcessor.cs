using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.MessageQueue;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.MessageQueue.Models;
using HappyWechat.Infrastructure.MessageProcessing.Configuration;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using HappyWechat.Infrastructure.Services;
using SensitiveWordService = HappyWechat.Infrastructure.RiskControl.Services.ISensitiveWordDetectionService;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 完整消息处理器 - 实现流程图中的完整处理链路
/// </summary>
public interface ICompleteMessageProcessor
{
    /// <summary>
    /// 处理微信回调消息
    /// </summary>
    Task<MessageProcessResult> ProcessWxCallbackMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息处理结果
/// </summary>
public class MessageProcessResult
{
    public bool Success { get; set; }
    public bool ShouldContinue { get; set; } = true;
    public string? ErrorMessage { get; set; }
    public string? ProcessingDetails { get; set; }
    public MessageProcessStage CompletedStage { get; set; }
    public Dictionary<string, object> ExtendedData { get; set; } = new();

    public static MessageProcessResult CreateSuccess(string details, MessageProcessStage stage)
    {
        return new MessageProcessResult
        {
            Success = true,
            ShouldContinue = true,
            ProcessingDetails = details,
            CompletedStage = stage
        };
    }

    public static MessageProcessResult CreateFailure(string errorMessage, MessageProcessStage stage, Dictionary<string, object>? extendedData = null)
    {
        return new MessageProcessResult
        {
            Success = false,
            ShouldContinue = false,
            ErrorMessage = errorMessage,
            CompletedStage = stage,
            ExtendedData = extendedData ?? new Dictionary<string, object>()
        };
    }
}

/// <summary>
/// 消息处理阶段
/// </summary>
public enum MessageProcessStage
{
    Validation,
    AccountConfigCheck,
    GlobalConfigLoad,
    EntityValidation,
    MessageTypeClassification,
    FriendRequestProcessing,
    IncomingSensitiveWordCheck,
    MessageProcessing,
    AiProcessing,
    AiSensitiveWordCheck,
    MessageLengthCheck,
    OfflineNotificationProcessing,
    QueueRouting,
    Completed
}

/// <summary>
/// 完整消息处理器实现
/// </summary>
public class CompleteMessageProcessor : ICompleteMessageProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IUnifiedMessageRouter _messageRouter;
    private readonly ISystemConfigManager _configManager;
    private readonly SensitiveWordService _sensitiveWordService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CompleteMessageProcessor> _logger;

    public CompleteMessageProcessor(
        ApplicationDbContext dbContext,
        IUnifiedMessageRouter messageRouter,
        ISystemConfigManager configManager,
        SensitiveWordService sensitiveWordService,
        IServiceProvider serviceProvider,
        ILogger<CompleteMessageProcessor> logger)
    {
        _dbContext = dbContext;
        _messageRouter = messageRouter;
        _configManager = configManager;
        _sensitiveWordService = sensitiveWordService;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<MessageProcessResult> ProcessWxCallbackMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 🚀 开始完整消息处理流程", processingId);

            // 阶段1: 基础验证
            var validationResult = await ValidateMessageAsync(callbackMessage, processingId);
            if (!validationResult.Success)
                return validationResult;

            // 阶段2: 账号配置检查
            var accountConfigResult = await CheckAccountConfigAsync(callbackMessage, processingId);
            if (!accountConfigResult.Success)
                return accountConfigResult;

            // 阶段3: 加载全局机器人配置
            var globalConfig = await LoadGlobalConfigAsync(processingId);

            // 阶段4: 实体存在性验证
            var entityValidationResult = await ValidateEntityExistenceAsync(callbackMessage, processingId);
            if (!entityValidationResult.Success)
                return entityValidationResult;

            // 阶段5: 消息类型分类处理
            var classificationResult = await ClassifyAndProcessMessageAsync(callbackMessage, globalConfig, processingId, cancellationToken);
            
            return classificationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 完整消息处理异常", processingId);
            return MessageProcessResult.CreateFailure($"消息处理异常: {ex.Message}", MessageProcessStage.Validation);
        }
    }

    /// <summary>
    /// 验证消息基础信息
    /// </summary>
    private async Task<MessageProcessResult> ValidateMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 🔍 开始消息基础验证", processingId);

        if (callbackMessage?.Data == null)
        {
            return MessageProcessResult.CreateFailure("消息数据为空", MessageProcessStage.Validation);
        }

        if (string.IsNullOrEmpty(callbackMessage.WxManagerId) || !Guid.TryParse(callbackMessage.WxManagerId, out _))
        {
            return MessageProcessResult.CreateFailure("WxManagerId无效", MessageProcessStage.Validation);
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 消息基础验证通过", processingId);
        return MessageProcessResult.CreateSuccess("消息基础验证通过", MessageProcessStage.Validation);
    }

    /// <summary>
    /// 检查账号配置
    /// </summary>
    private async Task<MessageProcessResult> CheckAccountConfigAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 🔍 开始账号配置检查", processingId);

        var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
        
        var wxManager = await _dbContext.WxManagerEntities
            .Where(w => w.Id == wxManagerId && w.IsEnabled)
            .FirstOrDefaultAsync();

        if (wxManager == null)
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ 微信账号不存在或未启用 - WxManagerId: {WxManagerId}", 
                processingId, wxManagerId);
            return MessageProcessResult.CreateFailure("微信账号不存在或未启用", MessageProcessStage.AccountConfigCheck);
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 账号配置检查通过 - Account: {Account}",
            processingId, wxManager.WAccount);
        
        return MessageProcessResult.CreateSuccess("账号配置检查通过", MessageProcessStage.AccountConfigCheck);
    }

    /// <summary>
    /// 加载全局机器人配置
    /// </summary>
    private async Task<GlobalRobotConfig> LoadGlobalConfigAsync(string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 🔍 开始加载全局机器人配置", processingId);

        var config = new GlobalRobotConfig
        {
            // 自动通过好友配置 - 使用默认值，因为ISystemConfigManager没有通用的GetConfigValueAsync方法
            EnableAutoAcceptFriend = false, // 默认关闭自动通过好友
            FriendAcceptKeywords = new List<string>(),
            DailyFriendAcceptLimit = 50,
            FriendWelcomeMessage = "",

            // 消息长度限制配置
            EnableMessageLengthCheck = true,
            MaxMessageLength = 2000,
            EnableAutoSplit = true // 默认启用自动分割
        };

        _logger.LogDebug("[{ProcessingId}] ✅ 全局机器人配置加载完成", processingId);
        return config;
    }

    /// <summary>
    /// 验证实体存在性
    /// </summary>
    private async Task<MessageProcessResult> ValidateEntityExistenceAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 🔍 开始实体存在性验证", processingId);

        var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);

        // 检查群组消息
        if (!string.IsNullOrEmpty(callbackMessage.Data?.FromGroup))
        {
            var fromGroup = callbackMessage.Data.FromGroup;
            var groupExists = await _dbContext.WxGroupEntities
                .AnyAsync(g => g.ChatRoomId == fromGroup && g.WxManagerId == wxManagerId);

            if (!groupExists)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 群组不存在，消息被丢弃 - FromGroup: {FromGroup}", 
                    processingId, fromGroup);
                return MessageProcessResult.CreateFailure($"群组 {fromGroup} 不存在", MessageProcessStage.EntityValidation);
            }
        }
        // 检查联系人消息
        else if (!string.IsNullOrEmpty(callbackMessage.Data?.FromUser))
        {
            var fromUser = callbackMessage.Data.FromUser;
            var contactExists = await _dbContext.WxContactEntities
                .AnyAsync(c => c.WcId == fromUser && c.WxManagerId == wxManagerId);

            if (!contactExists)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 联系人不存在，消息被丢弃 - FromUser: {FromUser}", 
                    processingId, fromUser);
                return MessageProcessResult.CreateFailure($"联系人 {fromUser} 不存在", MessageProcessStage.EntityValidation);
            }
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 实体存在性验证通过", processingId);
        return MessageProcessResult.CreateSuccess("实体存在性验证通过", MessageProcessStage.EntityValidation);
    }

    /// <summary>
    /// 消息类型分类处理
    /// </summary>
    private async Task<MessageProcessResult> ClassifyAndProcessMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        GlobalRobotConfig globalConfig, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        var messageType = callbackMessage.MessageType;
        
        _logger.LogDebug("[{ProcessingId}] 🔍 开始消息类型分类处理 - MessageType: {MessageType}", 
            processingId, messageType);

        return messageType switch
        {
            MessageTypeConstants.FRIEND_REQUEST => await ProcessFriendRequestAsync(callbackMessage, globalConfig, processingId, cancellationToken),
            MessageTypeConstants.OFFLINE_NOTIFICATION => await ProcessOfflineNotificationAsync(callbackMessage, processingId, cancellationToken),
            _ => await ProcessRegularMessageAsync(callbackMessage, globalConfig, processingId, cancellationToken)
        };
    }

    /// <summary>
    /// 处理好友请求
    /// </summary>
    private async Task<MessageProcessResult> ProcessFriendRequestAsync(
        WxCallbackMessageDto callbackMessage, 
        GlobalRobotConfig globalConfig, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 👥 开始处理好友请求", processingId);

        if (!globalConfig.EnableAutoAcceptFriend)
        {
            _logger.LogDebug("[{ProcessingId}] ⏭️ 自动通过好友功能未启用，跳过处理", processingId);
            return MessageProcessResult.CreateSuccess("自动通过好友功能未启用", MessageProcessStage.FriendRequestProcessing);
        }

        // 检查关键词匹配
        var content = callbackMessage.Data?.Content ?? "";
        var hasKeyword = globalConfig.FriendAcceptKeywords.Any(keyword => 
            content.Contains(keyword, StringComparison.OrdinalIgnoreCase));

        if (!hasKeyword)
        {
            _logger.LogDebug("[{ProcessingId}] ⏭️ 好友请求不包含通过关键词，跳过处理", processingId);
            return MessageProcessResult.CreateSuccess("不包含通过关键词", MessageProcessStage.FriendRequestProcessing);
        }

        // 检查每日限制
        var today = DateTime.Today;
        var todayAcceptCount = await GetTodayFriendAcceptCountAsync(Guid.Parse(callbackMessage.WxManagerId), today);
        
        if (todayAcceptCount >= globalConfig.DailyFriendAcceptLimit)
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ 今日通过好友数量已达限制 - Count: {Count}, Limit: {Limit}", 
                processingId, todayAcceptCount, globalConfig.DailyFriendAcceptLimit);
            return MessageProcessResult.CreateSuccess("今日通过好友数量已达限制", MessageProcessStage.FriendRequestProcessing);
        }

        // 自动通过好友请求
        await AcceptFriendRequestAsync(callbackMessage, globalConfig, processingId, cancellationToken);

        return MessageProcessResult.CreateSuccess("好友请求处理完成", MessageProcessStage.FriendRequestProcessing);
    }

    /// <summary>
    /// 处理离线通知
    /// </summary>
    private async Task<MessageProcessResult> ProcessOfflineNotificationAsync(
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 📴 开始处理离线通知", processingId);

        var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
        
        // 修改微信状态为离线
        await UpdateWxManagerStatusAsync(wxManagerId, false);
        
        // 发送邮件通知（如果配置了）
        await SendOfflineNotificationEmailAsync(wxManagerId, processingId);

        _logger.LogInformation("[{ProcessingId}] ✅ 离线通知处理完成 - WxManagerId: {WxManagerId}", 
            processingId, wxManagerId);

        return MessageProcessResult.CreateSuccess("离线通知处理完成", MessageProcessStage.OfflineNotificationProcessing);
    }

    /// <summary>
    /// 处理常规消息
    /// </summary>
    private async Task<MessageProcessResult> ProcessRegularMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        GlobalRobotConfig globalConfig, 
        string processingId, 
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 💬 开始处理常规消息", processingId);

        // 接收消息敏感词检测
        var sensitiveWordResult = await CheckIncomingSensitiveWordsAsync(callbackMessage, processingId);
        if (!sensitiveWordResult.ShouldContinue)
            return sensitiveWordResult;

        // 路由到AI处理队列
        var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
        var aiMessage = new AiMessageQueueData
        {
            Content = callbackMessage.Data?.Content ?? "",
            FromUser = callbackMessage.Data?.FromUser ?? "",
            ToUser = callbackMessage.Data?.ToUser ?? "",
            AgentName = "DefaultAgent",
            Context = new Dictionary<string, object>
            {
                ["CallbackMessage"] = callbackMessage,
                ["ProcessingId"] = processingId,
                ["WxManagerId"] = callbackMessage.WxManagerId
            }
        };

        await _messageRouter.RouteAiMessageAsync(
            wxManagerId.ToString(),
            aiMessage.AgentName,
            aiMessage.Content,
            aiMessage.FromUser,
            callbackMessage.Data?.FromGroup != null, // 判断是否为群消息
            aiMessage.Context,
            cancellationToken);

        _logger.LogDebug("[{ProcessingId}] ✅ 常规消息已路由到AI处理队列", processingId);
        return MessageProcessResult.CreateSuccess("消息已路由到AI处理", MessageProcessStage.QueueRouting);
    }

    /// <summary>
    /// 获取今日好友通过数量
    /// </summary>
    private async Task<int> GetTodayFriendAcceptCountAsync(Guid wxManagerId, DateTime today)
    {
        try
        {
            // 这里应该查询好友通过记录表，暂时返回0
            // 实际实现需要根据数据库设计来查询
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取今日好友通过数量失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return 0;
        }
    }

    /// <summary>
    /// 自动通过好友请求
    /// </summary>
    private async Task AcceptFriendRequestAsync(WxCallbackMessageDto callbackMessage, GlobalRobotConfig globalConfig, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤝 开始自动通过好友请求", processingId);

            // 这里需要调用EYun API来通过好友请求
            // 由于API接口需要具体的参数，这里提供框架

            // 发送欢迎语（如果配置了）
            if (!string.IsNullOrEmpty(globalConfig.FriendWelcomeMessage))
            {
                var welcomeRequest = SendMessageRequest.CreateTextMessage(
                    callbackMessage.WxManagerId ?? string.Empty,
                    callbackMessage.Data?.FromUser ?? "",
                    globalConfig.FriendWelcomeMessage,
                    false);

                welcomeRequest.Priority = HappyWechat.Infrastructure.MessageQueue.Models.MessagePriority.Normal;
                welcomeRequest.DelayMs = 2000;

                await _messageRouter.RouteMessageSendAsync(
                    callbackMessage.WxManagerId ?? string.Empty,
                    "text",
                    callbackMessage.Data?.FromUser ?? "",
                    globalConfig.FriendWelcomeMessage,
                    welcomeRequest.Metadata,
                    cancellationToken);

                _logger.LogDebug("[{ProcessingId}] ✅ 好友欢迎语已发送", processingId);
            }

            _logger.LogInformation("[{ProcessingId}] ✅ 好友请求自动通过完成", processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 自动通过好友请求失败", processingId);
        }
    }

    /// <summary>
    /// 更新微信管理器状态
    /// </summary>
    private async Task UpdateWxManagerStatusAsync(Guid wxManagerId, bool isOnline)
    {
        try
        {
            var wxManager = await _dbContext.WxManagerEntities
                .FirstOrDefaultAsync(w => w.Id == wxManagerId);

            if (wxManager != null)
            {
                // WxMangerEntity没有IsOnline和LastUpdateTime属性，使用现有属性
                // 可以使用WxStatus来表示在线状态，UpdatedTime来表示更新时间
                wxManager.WxStatus = isOnline ? Domain.ValueObjects.Enums.WxStatus.AlreadyLogIn : Domain.ValueObjects.Enums.WxStatus.NotLogIn;
                wxManager.UpdatedTime = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("微信管理器状态已更新 - WxManagerId: {WxManagerId}, IsOnline: {IsOnline}",
                    wxManagerId, isOnline);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新微信管理器状态失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 发送离线通知邮件
    /// </summary>
    private async Task SendOfflineNotificationEmailAsync(Guid wxManagerId, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📧 开始发送离线通知邮件", processingId);

            // 获取邮件配置 - 暂时默认启用
            var enableEmailNotification = true; // 默认启用离线邮件通知
            if (!enableEmailNotification)
            {
                _logger.LogDebug("[{ProcessingId}] ⏭️ 离线邮件通知功能未启用", processingId);
                return;
            }

            var wxManager = await _dbContext.WxManagerEntities
                .FirstOrDefaultAsync(w => w.Id == wxManagerId);

            if (wxManager != null)
            {
                var notification = new NotificationMessage
                {
                    NotificationType = "OfflineNotification",
                    Content = $"微信账号 {wxManager.WAccount} 已离线，请及时处理。",
                    Data = new Dictionary<string, object>
                    {
                        ["WxManagerId"] = wxManagerId,
                        ["Account"] = wxManager.WAccount,
                        ["OfflineTime"] = DateTime.UtcNow,
                        ["Title"] = "微信账号离线通知",
                        ["Recipients"] = new List<string>() // 需要配置收件人列表
                    }
                };

                await _messageRouter.RouteNotificationAsync(
                    notification.NotificationType,
                    wxManagerId.ToString(),
                    notification.Content,
                    notification.Data);

                _logger.LogDebug("[{ProcessingId}] ✅ 离线通知邮件已发送", processingId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 发送离线通知邮件失败", processingId);
        }
    }

    /// <summary>
    /// 检查接收消息敏感词 - 使用现有的敏感词检测服务
    /// </summary>
    private async Task<MessageProcessResult> CheckIncomingSensitiveWordsAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔍 开始接收消息敏感词检测", processingId);

            var content = callbackMessage.Data?.Content ?? "";
            if (string.IsNullOrEmpty(content))
            {
                return MessageProcessResult.CreateSuccess("消息内容为空，跳过敏感词检测", MessageProcessStage.IncomingSensitiveWordCheck);
            }

            // 🔧 使用现有的敏感词检测服务
            var sensitiveWordResult = await _sensitiveWordService.ProcessIncomingMessageAsync(content, processingId);

            if (sensitiveWordResult.ContainsSensitiveWords)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 检测到敏感词 - Words: {Words}, Action: {Action}",
                    processingId, string.Join(", ", sensitiveWordResult.DetectedWords), sensitiveWordResult.Action);

                // 根据处理结果决定是否继续
                if (sensitiveWordResult.ShouldBlock)
                {
                    _logger.LogWarning("[{ProcessingId}] 🚫 消息被拦截 - 包含敏感词", processingId);
                    return MessageProcessResult.CreateFailure("消息包含敏感词，已拦截", MessageProcessStage.IncomingSensitiveWordCheck);
                }

                // 如果是替换模式，更新消息内容
                if (sensitiveWordResult.ProcessedText != content)
                {
                    callbackMessage.Data.Content = sensitiveWordResult.ProcessedText;
                    _logger.LogDebug("[{ProcessingId}] 🔄 敏感词已替换", processingId);
                }
            }

            _logger.LogDebug("[{ProcessingId}] ✅ 接收消息敏感词检测完成", processingId);
            return MessageProcessResult.CreateSuccess("敏感词检测完成", MessageProcessStage.IncomingSensitiveWordCheck);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 接收消息敏感词检测异常", processingId);
            return MessageProcessResult.CreateSuccess("敏感词检测异常，继续处理", MessageProcessStage.IncomingSensitiveWordCheck);
        }
    }
}

/// <summary>
/// 全局机器人配置 - 简化版，敏感词检测由专门的服务处理
/// </summary>
public class GlobalRobotConfig
{
    // 自动通过好友配置
    public bool EnableAutoAcceptFriend { get; set; }
    public List<string> FriendAcceptKeywords { get; set; } = new();
    public int DailyFriendAcceptLimit { get; set; } = 50;
    public string FriendWelcomeMessage { get; set; } = "";

    // 消息长度限制配置
    public bool EnableMessageLengthCheck { get; set; }
    public int MaxMessageLength { get; set; } = 2000;
    public bool EnableAutoSplit { get; set; } = true;
}
