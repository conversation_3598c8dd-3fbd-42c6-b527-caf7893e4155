using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Domain.Entities;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Application.Commons;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 联系人AI配置检查结果
/// </summary>
public class ContactAiConfigResult
{
    public bool IsAiConfigured { get; set; }
    public bool IsEnabled { get; set; }
    public Guid? AiAgentId { get; set; }
    public string? ContactName { get; set; }
    public string? ErrorMessage { get; set; }
    public bool ContactExists { get; set; } = true;
    public MessageDiscardReason DiscardReason { get; set; } = MessageDiscardReason.None;
}

/// <summary>
/// 联系人AI配置检查服务接口
/// </summary>
public interface IContactAiConfigChecker
{
    /// <summary>
    /// 检查指定联系人的AI配置
    /// </summary>
    Task<ContactAiConfigResult> CheckContactAiConfigAsync(Guid wxManagerId, string fromUser);

    /// <summary>
    /// 检查联系人是否启用AI且非人工回复模式
    /// </summary>
    Task<bool> ShouldTriggerAiReplyAsync(Guid wxManagerId, string fromUser);

    /// <summary>
    /// 验证联系人消息是否应该处理（包含实体存在性检查）
    /// </summary>
    Task<MessageValidationResult> ValidateContactMessageAsync(Guid wxManagerId, string fromUser, string processingId);
}

/// <summary>
/// 联系人AI配置检查服务实现
/// </summary>
public class ContactAiConfigChecker : IContactAiConfigChecker
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<ContactAiConfigChecker> _logger;

    public ContactAiConfigChecker(
        ApplicationDbContext dbContext,
        ILogger<ContactAiConfigChecker> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ContactAiConfigResult> CheckContactAiConfigAsync(Guid wxManagerId, string fromUser)
    {
        try
        {
            _logger.LogDebug("检查联系人AI配置 - WxManagerId: {WxManagerId}, FromUser: {FromUser}", 
                wxManagerId, fromUser);

            // 查找联系人实体 - 修复字段匹配错误，使用WcId而不是UserName
            var contact = await _dbContext.WxContactEntities
                .Where(c => c.WxManagerId == wxManagerId && c.WcId == fromUser)
                .Select(c => new
                {
                    c.Id,
                    c.NickName,
                    c.IsAiEnabled,
                    c.AiAgentId
                })
                .FirstOrDefaultAsync();

            if (contact == null)
            {
                _logger.LogDebug("联系人不存在于WxContactEntities表 - FromUser: '{FromUser}'",
                    fromUser);

                return new ContactAiConfigResult
                {
                    IsAiConfigured = false,
                    IsEnabled = false,
                    ContactName = fromUser,
                    ErrorMessage = $"FromUser '{fromUser}' 不存在",
                    ContactExists = false,
                    DiscardReason = MessageDiscardReason.ContactNotExists
                };
            }

            // 简化AI配置判断逻辑：需要同时满足启用和有代理
            var isFullyConfigured = contact.IsAiEnabled && contact.AiAgentId.HasValue;

            var result = new ContactAiConfigResult
            {
                IsAiConfigured = isFullyConfigured,
                IsEnabled = contact.IsAiEnabled, // 使用IsAiEnabled字段
                AiAgentId = contact.AiAgentId,
                ContactName = contact.NickName ?? fromUser
            };

            _logger.LogDebug("联系人AI配置检查完成 - Contact: {ContactName}, IsAiEnabled: {IsAiEnabled}, AiAgentId: {AiAgentId}, IsFullyConfigured: {IsFullyConfigured}",
                result.ContactName, contact.IsAiEnabled, result.AiAgentId, isFullyConfigured);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查联系人AI配置失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}", 
                wxManagerId, fromUser);
            
            return new ContactAiConfigResult
            {
                IsAiConfigured = false,
                IsEnabled = false,
                ContactName = fromUser,
                ErrorMessage = $"检查失败: {ex.Message}"
            };
        }
    }

    public async Task<bool> ShouldTriggerAiReplyAsync(Guid wxManagerId, string fromUser)
    {
        try
        {
            var config = await CheckContactAiConfigAsync(wxManagerId, fromUser);

            // 简化判断逻辑：只要启用AI且有AI代理就触发AI回复
            var shouldTrigger = config.IsAiConfigured && config.IsEnabled;

            _logger.LogDebug("联系人AI回复触发检查 - Contact: {ContactName}, ShouldTrigger: {ShouldTrigger}, IsEnabled: {IsEnabled}",
                config.ContactName, shouldTrigger, config.IsEnabled);

            return shouldTrigger;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查联系人AI回复触发条件失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}",
                wxManagerId, fromUser);
            return false;
        }
    }

    public async Task<MessageValidationResult> ValidateContactMessageAsync(Guid wxManagerId, string fromUser, string processingId)
    {
        try
        {
            _logger.LogDebug("验证联系人消息 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, ProcessingId: {ProcessingId}",
                wxManagerId, fromUser, processingId);

            var config = await CheckContactAiConfigAsync(wxManagerId, fromUser);

            // 如果联系人不存在，直接丢弃消息
            if (!config.ContactExists)
            {
                _logger.LogWarning("⚠️ 联系人不存在，消息将被丢弃 - FromUser: '{FromUser}'",
                    fromUser);

                return MessageValidationResult.CreateDiscard(processingId, MessageDiscardReason.ContactNotExists,
                    $"联系人 {fromUser} 在数据库中不存在");
            }

            // 联系人存在，返回成功结果
            var result = MessageValidationResult.CreateSuccess(processingId);
            result.ProcessingDetails = $"联系人验证通过 - Contact: {config.ContactName}, AI配置: {(config.IsAiConfigured ? "已启用" : "未启用")}";

            _logger.LogDebug("✅ 联系人消息验证通过 - Contact: {ContactName}, ProcessingId: {ProcessingId}",
                config.ContactName, processingId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证联系人消息失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, ProcessingId: {ProcessingId}",
                wxManagerId, fromUser, processingId);

            return MessageValidationResult.CreateFailure(processingId, $"验证联系人消息异常: {ex.Message}");
        }
    }
}
