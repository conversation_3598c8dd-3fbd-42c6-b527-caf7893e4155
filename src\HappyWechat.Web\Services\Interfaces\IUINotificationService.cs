using MudBlazor;

namespace HappyWechat.Web.Services.Interfaces;

/// <summary>
/// UI通知服务接口 - 分离UI通知职责
/// 提供统一的UI通知功能，支持不同的UI框架
/// </summary>
public interface IUINotificationService
{
    /// <summary>
    /// 显示信息通知
    /// </summary>
    /// <param name="message">通知消息</param>
    void ShowInfo(string message);

    /// <summary>
    /// 显示成功通知
    /// </summary>
    /// <param name="message">通知消息</param>
    void ShowSuccess(string message);

    /// <summary>
    /// 显示警告通知
    /// </summary>
    /// <param name="message">通知消息</param>
    void ShowWarning(string message);

    /// <summary>
    /// 显示错误通知
    /// </summary>
    /// <param name="message">通知消息</param>
    void ShowError(string message);

    /// <summary>
    /// 显示加载状态通知
    /// </summary>
    /// <param name="message">加载消息</param>
    void ShowLoading(string message);

    /// <summary>
    /// 检查通知服务是否可用
    /// </summary>
    /// <returns>如果通知服务可用返回true，否则返回false</returns>
    bool IsAvailable { get; }
}
