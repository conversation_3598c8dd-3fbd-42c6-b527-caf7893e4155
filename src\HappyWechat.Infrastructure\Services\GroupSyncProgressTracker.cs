using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Caching.Interfaces;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Notifications.Interfaces;
using HappyWechat.Infrastructure.Notifications.Models;
using System.Text.Json;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 群组同步进度跟踪服务
/// 参照ContactSyncProgressTracker的设计模式
/// </summary>
public class GroupSyncProgressTracker
{
    private readonly ILogger<GroupSyncProgressTracker> _logger;
    private readonly IUnifiedCacheManager _cacheManager;
    private readonly IUnifiedSyncNotificationService _notificationService;
    private const string PROGRESS_KEY_PREFIX = "group_sync_progress";
    private const int PROGRESS_CACHE_DURATION_MINUTES = 30;

    public GroupSyncProgressTracker(
        ILogger<GroupSyncProgressTracker> logger,
        IUnifiedCacheManager cacheManager,
        IUnifiedSyncNotificationService notificationService)
    {
        _logger = logger;
        _cacheManager = cacheManager;
        _notificationService = notificationService;
    }

    /// <summary>
    /// 初始化群组同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="syncSessionId">同步会话ID</param>
    /// <param name="totalGroups">总群组数量</param>
    /// <returns></returns>
    public async Task InitializeProgressAsync(
        Guid wxManagerId, 
        string syncSessionId,
        int totalGroups)
    {
        var progress = new GroupSyncProgressDto
        {
            WxManagerId = wxManagerId,
            SyncSessionId = syncSessionId,
            Status = SyncStatus.InProgress,
            StartTime = DateTime.UtcNow,
            LastUpdateTime = DateTime.UtcNow,
            TotalCount = totalGroups,
            ProcessedCount = 0,
            SuccessCount = 0,
            FailedCount = 0,
            NewGroupCount = 0,
            UpdatedGroupCount = 0,
            CurrentPhase = "开始群组同步"
        };

        await SaveProgressAsync(wxManagerId, progress);

        // 🔧 注释冗余的初始化进度日志 - 减少日志噪音
        // _logger.LogInformation("🚀 初始化群组同步进度 - WxManagerId: {WxManagerId}, SessionId: {SessionId}, TotalGroups: {TotalGroups}",
        //     wxManagerId, syncSessionId, totalGroups);
    }

    /// <summary>
    /// 更新单个群组同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="groupName">群组名称</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="isNewGroup">是否为新群组</param>
    /// <param name="errorMessage">错误消息（如果失败）</param>
    /// <returns></returns>
    public async Task UpdateGroupProgressAsync(
        Guid wxManagerId,
        string groupId,
        string? groupName = null,
        bool isSuccess = true,
        bool isNewGroup = false,
        string? errorMessage = null)
    {
        var progress = await GetProgressAsync(wxManagerId);
        if (progress == null)
        {
            _logger.LogWarning("未找到群组同步进度信息 - WxManagerId: {WxManagerId}", wxManagerId);
            return;
        }

        // 更新进度计数
        progress.ProcessedCount++;
        if (isSuccess)
        {
            progress.SuccessCount++;
            if (isNewGroup)
            {
                progress.NewGroupCount++;
            }
            else
            {
                progress.UpdatedGroupCount++;
            }
        }
        else
        {
            progress.FailedCount++;
            if (!string.IsNullOrEmpty(errorMessage))
            {
                progress.ErrorMessage = errorMessage;
            }
        }

        // 更新当前处理信息
        progress.CurrentGroup = groupId;
        progress.CurrentPhase = $"处理群组: {groupName ?? groupId}";
        progress.LastUpdateTime = DateTime.UtcNow;

        // 检查是否完成
        if (progress.ProcessedCount >= progress.TotalCount)
        {
            progress.Status = SyncStatus.Completed;
            progress.CompletedTime = DateTime.UtcNow;
            progress.CurrentPhase = "群组同步完成";

            // 发送同步完成通知
            await SendSyncCompletedNotificationAsync(wxManagerId, progress);
        }
        else
        {
            // 发送进度更新通知
            await SendProgressUpdateNotificationAsync(wxManagerId, progress);
        }

        await SaveProgressAsync(wxManagerId, progress);

        // 🔧 注释冗余的进度更新日志 - 减少日志噪音
        // _logger.LogDebug("更新群组同步进度 - WxManagerId: {WxManagerId}, GroupId: {GroupId}, Success: {Success}, Progress: {Progress}%",
        //     wxManagerId, groupId, isSuccess, progress.ProgressPercentage);
    }

    /// <summary>
    /// 标记同步失败
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns></returns>
    public async Task MarkSyncFailedAsync(Guid wxManagerId, string errorMessage)
    {
        var progress = await GetProgressAsync(wxManagerId);
        if (progress == null)
        {
            _logger.LogWarning("未找到群组同步进度信息 - WxManagerId: {WxManagerId}", wxManagerId);
            return;
        }

        progress.Status = SyncStatus.Failed;
        progress.CompletedTime = DateTime.UtcNow;
        progress.ErrorMessage = errorMessage;
        progress.CurrentPhase = "群组同步失败";
        progress.LastUpdateTime = DateTime.UtcNow;

        await SaveProgressAsync(wxManagerId, progress);

        // 发送同步失败通知
        await _notificationService.SendSyncFailedAsync(wxManagerId, "Group", errorMessage);

        _logger.LogError("标记群组同步失败 - WxManagerId: {WxManagerId}, 错误: {Error}", wxManagerId, errorMessage);
    }

    /// <summary>
    /// 获取同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task<GroupSyncProgressDto?> GetProgressAsync(Guid wxManagerId)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            var cachedProgress = await _cacheManager.GetAsync<string>(cacheKey);
            
            if (string.IsNullOrEmpty(cachedProgress))
            {
                return null;
            }

            var progress = JsonSerializer.Deserialize<GroupSyncProgressDto>(cachedProgress);
            return progress;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    /// <summary>
    /// 清除同步进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task ClearProgressAsync(Guid wxManagerId)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            await _cacheManager.RemoveAsync(cacheKey);
            
            // 🔧 注释冗余的清除进度日志 - 减少日志噪音
        // _logger.LogDebug("清除群组同步进度 - WxManagerId: {WxManagerId}", wxManagerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除群组同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 保存进度到缓存
    /// </summary>
    private async Task SaveProgressAsync(Guid wxManagerId, GroupSyncProgressDto progress)
    {
        try
        {
            var cacheKey = GetProgressCacheKey(wxManagerId);
            var json = JsonSerializer.Serialize(progress);
            await _cacheManager.SetAsync(cacheKey, json, TimeSpan.FromMinutes(PROGRESS_CACHE_DURATION_MINUTES));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存群组同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 生成进度缓存键
    /// </summary>
    private static string GetProgressCacheKey(Guid wxManagerId)
    {
        return $"{PROGRESS_KEY_PREFIX}:{wxManagerId}";
    }

    /// <summary>
    /// 发送同步完成通知
    /// </summary>
    private async Task SendSyncCompletedNotificationAsync(Guid wxManagerId, GroupSyncProgressDto progress)
    {
        try
        {
            var notification = new GroupSyncCompletionNotification
            {
                WxManagerId = wxManagerId,
                Status = progress.Status,
                SuccessCount = progress.SuccessCount,
                FailedCount = progress.FailedCount,
                GroupCount = progress.TotalCount,
                NewGroupCount = progress.NewGroupCount,
                UpdatedGroupCount = progress.UpdatedGroupCount,
                ElapsedMilliseconds = progress.CompletedTime.HasValue
                    ? (long)(progress.CompletedTime.Value - progress.StartTime).TotalMilliseconds
                    : 0,
                CompletedTime = progress.CompletedTime ?? DateTime.UtcNow,
                SessionId = progress.SyncSessionId
            };

            var result = await _notificationService.SendGroupSyncCompletedAsync(notification);

            if (result.Success)
            {
                // 🔧 提升为Warning级别 - 重要的同步完成通知
            _logger.LogWarning("✅ 群组同步完成通知发送成功 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
                    wxManagerId, notification.SuccessCount, notification.FailedCount);
            }
            else
            {
                _logger.LogWarning("⚠️ 群组同步完成通知发送失败 - WxManagerId: {WxManagerId}, Error: {Error}",
                    wxManagerId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送群组同步完成通知异常 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 发送进度更新通知
    /// </summary>
    private async Task SendProgressUpdateNotificationAsync(Guid wxManagerId, GroupSyncProgressDto progress)
    {
        try
        {
            var notification = new SyncProgressNotification
            {
                WxManagerId = wxManagerId,
                Status = progress.Status,
                ProgressPercentage = (int)progress.ProgressPercentage,
                ProcessedCount = progress.ProcessedCount,
                TotalCount = progress.TotalCount,
                CurrentPhase = progress.CurrentPhase ?? "群组同步中",
                EstimatedRemainingMs = CalculateEstimatedRemainingTime(progress)
            };

            // 进度通知使用低优先级，避免过于频繁
            var options = new NotificationSendOptions
            {
                Priority = NotificationPriority.Low,
                EnableRetry = false // 进度通知失败不重试
            };

            await _notificationService.SendGroupSyncProgressAsync(notification, options);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "发送群组同步进度通知失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 计算预估剩余时间
    /// </summary>
    private static long CalculateEstimatedRemainingTime(GroupSyncProgressDto progress)
    {
        if (progress.ProcessedCount <= 0 || progress.TotalCount <= progress.ProcessedCount)
        {
            return 0;
        }

        var elapsed = DateTime.UtcNow - progress.StartTime;
        var averageTimePerGroup = elapsed.TotalMilliseconds / progress.ProcessedCount;
        var remainingGroups = progress.TotalCount - progress.ProcessedCount;
        
        return (long)(averageTimePerGroup * remainingGroups);
    }
}
