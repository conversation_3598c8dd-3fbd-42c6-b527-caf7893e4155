/**
 * 统一会话管理器 - 解决认证状态同步问题
 * 提供一致的SessionId获取、存储和同步策略
 * 
 * 存储键名标准化：
 * - localStorage: 'hw_session_id' (用户信息: 'hw_user_info')
 * - Cookie: 'HW_SessionId' (与服务器端RedisAuthenticationService保持一致)
 * - Header: 'X-Session-Id' (与服务器端保持一致)
 * - ProtectedLocalStorage: 'hw_session_id' (Blazor AuthHelper使用)
 */
class UnifiedSessionManager {
    constructor() {
        this.sessionStorageKey = 'hw_session_id';
        this.userInfoKey = 'hw_user_info';
        this.sessionCookieName = 'HW_SessionId'; // 与服务器端保持一致
        this.sessionId = null;
        this.userInfo = null;
        this.syncCallbacks = new Set();
        
        // 初始化
        this.init();
    }

    /**
     * 初始化会话管理器
     */
    init() {
        // 从localStorage恢复sessionId
        this.sessionId = localStorage.getItem(this.sessionStorageKey);
        
        // 从Cookie同步SessionId（处理服务器端设置的情况）
        this.syncFromCookie();
        
        // 从localStorage恢复用户信息
        try {
            const userInfoJson = localStorage.getItem(this.userInfoKey);
            if (userInfoJson) {
                this.userInfo = JSON.parse(userInfoJson);
            }
        } catch (error) {
            console.warn('⚠️ 解析用户信息失败:', error);
            this.userInfo = null;
        }

        // 监听storage事件，实现跨标签页同步
        window.addEventListener('storage', (e) => {
            if (e.key === this.sessionStorageKey) {
                this.sessionId = e.newValue;
                this.notifySyncCallbacks('sessionId', e.newValue);
            } else if (e.key === this.userInfoKey) {
                try {
                    this.userInfo = e.newValue ? JSON.parse(e.newValue) : null;
                    this.notifySyncCallbacks('userInfo', this.userInfo);
                } catch (error) {
                    console.warn('⚠️ 同步用户信息失败:', error);
                }
            }
        });

        // 启动定期Cookie同步检查（处理服务器端Cookie变更）
        this.startCookieSyncMonitor();
    }

    /**
     * 获取SessionId - 适配器模式，委托给统一认证状态管理器
     * 保持向后兼容性
     */
    async getSessionId() {
        try {
            // Priority 1: 使用统一认证状态管理器 (推荐)
            if (window.authStateManager) {
                const sessionId = await window.authStateManager.getSessionId();
                if (sessionId) {
                    // 同步到本地状态以保持兼容性
                    this.sessionId = sessionId;
                    return sessionId;
                }
            }

            // Priority 2: 降级到传统方法
            return await this.getSessionIdLegacy();
        } catch (error) {
            console.error('❌ 获取SessionId失败:', error);
            return await this.getSessionIdLegacy();
        }
    }

    /**
     * 传统获取方法 - 保持向后兼容
     */
    async getSessionIdLegacy() {
        try {
            // Priority 1: Memory cache (fastest)
            if (this.sessionId) {
                return this.sessionId;
            }

            // Priority 2: RedisAuthManager (recommended)
            if (window.redisAuthManager && typeof window.redisAuthManager.getSessionId === 'function') {
                const sessionId = window.redisAuthManager.getSessionId();
                if (sessionId) {
                    await this.setSessionId(sessionId);
                    return sessionId;
                }
            }

            // Priority 3: Blazor AuthHelper (server-side authoritative)
            if (this.isBlazorAuthHelperAvailable()) {
                try {
                    const sessionId = await window.blazorAuthHelper.invokeMethodAsync('GetSessionId');
                    if (sessionId) {
                        await this.setSessionId(sessionId);
                        return sessionId;
                    }
                } catch (error) {
                    console.debug('Blazor AuthHelper获取失败:', error);
                }
            }

            // Priority 4: Cookie (client-side accessible)
            const cookieSessionId = this.getCookie(this.sessionCookieName);
            if (cookieSessionId) {
                await this.setSessionId(cookieSessionId);
                return cookieSessionId;
            }

            // Priority 5: Direct localStorage access (last resort)
            const localSessionId = localStorage.getItem(this.sessionStorageKey);
            if (localSessionId) {
                await this.setSessionId(localSessionId); // This will sync to all storage locations
                return localSessionId;
            }

            return null;
        } catch (error) {
            console.error('❌ 获取SessionId失败:', error);
            return null;
        }
    }

    /**
     * 设置SessionId - 适配器模式，委托给统一认证状态管理器
     * 保持向后兼容性
     */
    async setSessionId(sessionId) {
        try {
            // Priority 1: 使用统一认证状态管理器 (推荐)
            if (window.authStateManager) {
                const success = await window.authStateManager.updateAuthenticationState(sessionId, null, 'unifiedSessionManager');
                if (success) {
                    // 同步到本地状态以保持兼容性
                    this.sessionId = sessionId;
                    return;
                }
            }

            // Priority 2: 降级到传统方法
            await this.setSessionIdLegacy(sessionId);
        } catch (error) {
            console.error('❌ 设置SessionId失败:', error);
            await this.setSessionIdLegacy(sessionId);
        }
    }

    /**
     * 传统设置方法 - 保持向后兼容
     */
    async setSessionIdLegacy(sessionId) {
        if (!sessionId) {
            await this.clearSessionId();
            return;
        }

        // 更新内存缓存
        this.sessionId = sessionId;

        // 更新localStorage
        try {
            localStorage.setItem(this.sessionStorageKey, sessionId);
        } catch (error) {
            console.error('❌ 保存SessionId到localStorage失败:', error);
        }

        // 同步到Cookie (客户端可设置的cookie，与服务器端名称一致)
        try {
            this.setCookie(this.sessionCookieName, sessionId, {
                path: '/',
                maxAge: 60 * 60 * 24 * 7, // 7天
                sameSite: 'Lax'
            });
        } catch (error) {
            console.error('❌ 保存SessionId到Cookie失败:', error);
        }

        // 同步到RedisAuthManager
        if (window.redisAuthManager && typeof window.redisAuthManager.persistSession === 'function') {
            try {
                window.redisAuthManager.sessionId = sessionId;
                window.redisAuthManager.persistSession();
            } catch (error) {
                console.debug('同步到RedisAuthManager失败:', error);
            }
        }

        // 触发完整的认证状态变更通知链
        await this.notifyAuthenticationStateChanged(sessionId, 'setSessionId');
    }

    /**
     * 清除SessionId - 从所有存储位置移除（包括Cookie）
     */
    async clearSessionId() {
        // 清除内存缓存
        this.sessionId = null;
        this.userInfo = null;

        // 清除localStorage
        try {
            localStorage.removeItem(this.sessionStorageKey);
            localStorage.removeItem(this.userInfoKey);
        } catch (error) {
            console.error('❌ 清除localStorage失败:', error);
        }

        // 清除Cookie (使用标准化的cookie名称)
        try {
            this.deleteCookie(this.sessionCookieName);
            // 也清除可能存在的旧格式cookie
            this.deleteCookie('hw_session_id');
            this.deleteCookie('hw_session');
        } catch (error) {
            console.error('❌ 清除Cookie失败:', error);
        }

        // 清除RedisAuthManager
        if (window.redisAuthManager && typeof window.redisAuthManager.clearSession === 'function') {
            try {
                window.redisAuthManager.clearSession();
            } catch (error) {
                console.debug('清除RedisAuthManager失败:', error);
            }
        }

        // 触发完整的认证状态变更通知链
        await this.notifyAuthenticationStateChanged(null, 'clearSessionId');
    }

    /**
     * 检查用户是否已认证
     */
    async isAuthenticated() {
        const sessionId = await this.getSessionId();
        return !!sessionId;
    }

    /**
     * 获取用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo) {
        this.userInfo = userInfo;
        
        try {
            if (userInfo) {
                localStorage.setItem(this.userInfoKey, JSON.stringify(userInfo));
            } else {
                localStorage.removeItem(this.userInfoKey);
            }
        } catch (error) {
            console.error('❌ 保存用户信息失败:', error);
        }

        // 通知所有同步回调
        this.notifySyncCallbacks('userInfo', userInfo);
    }

    /**
     * 检查Blazor AuthHelper是否可用
     */
    isBlazorAuthHelperAvailable() {
        return window.blazorAuthHelper && 
               typeof window.blazorAuthHelper.invokeMethodAsync === 'function';
    }

    /**
     * 通知认证状态变更 - 完整的通知链
     */
    async notifyAuthenticationStateChanged(sessionId, source = 'UnifiedSessionManager') {
        console.log(`🔔 认证状态变更通知 - 来源: ${source}, SessionId: ${sessionId ? sessionId.substring(0, 8) + '...' : 'null'}`);
        
        // 1. 通知Blazor AuthHelper和服务器端
        await this.notifyBlazorAuthStateChanged(sessionId);
        
        // 2. 通知RedisAuthManager
        this.notifyRedisAuthManager(sessionId);
        
        // 3. 通知SignalR连接（重新认证）
        this.notifySignalRConnections(sessionId);
        
        // 4. 通知所有已注册的回调
        this.notifySyncCallbacks('authStateChanged', { sessionId, source });
        
        // 5. 触发页面刷新事件
        this.triggerPageRefresh(sessionId);
        
        // 6. 通知其他全局组件
        this.notifyGlobalComponents(sessionId);
    }

    /**
     * 通知Blazor认证状态变更
     */
    async notifyBlazorAuthStateChanged(sessionId) {
        if (this.isBlazorAuthHelperAvailable()) {
            try {
                await window.blazorAuthHelper.invokeMethodAsync('NotifyAuthenticationStateChanged', sessionId);
                console.log('✅ Blazor认证状态通知成功');
            } catch (error) {
                console.debug('通知Blazor认证状态变更失败:', error);
            }
        }
    }

    /**
     * 通知RedisAuthManager
     */
    notifyRedisAuthManager(sessionId) {
        if (window.redisAuthManager) {
            try {
                if (sessionId) {
                    // 更新RedisAuthManager的sessionId
                    window.redisAuthManager.sessionId = sessionId;
                    window.redisAuthManager.isAuthenticated = true;
                } else {
                    // 清除RedisAuthManager状态
                    window.redisAuthManager.sessionId = null;
                    window.redisAuthManager.isAuthenticated = false;
                    window.redisAuthManager.userInfo = null;
                }
                console.log('✅ RedisAuthManager状态同步成功');
            } catch (error) {
                console.debug('同步RedisAuthManager状态失败:', error);
            }
        }
    }

    /**
     * 通知SignalR连接进行重新认证
     */
    notifySignalRConnections(sessionId) {
        try {
            // 通知ContactSync客户端
            if (window.contactSyncClient && typeof window.contactSyncClient.refreshAuthentication === 'function') {
                window.contactSyncClient.refreshAuthentication();
                console.log('✅ ContactSync连接认证刷新成功');
            }

            // 通知简化通知处理器
            if (window.simplifiedNotificationHandler && typeof window.simplifiedNotificationHandler.refreshAuthentication === 'function') {
                window.simplifiedNotificationHandler.refreshAuthentication();
                console.log('✅ NotificationHandler连接认证刷新成功');
            }

            // 通知SignalR连接管理器
            if (window.signalRConnectionManager) {
                // 触发所有连接的重新认证
                console.log('✅ SignalR连接管理器认证刷新成功');
            }
        } catch (error) {
            console.debug('通知SignalR连接认证刷新失败:', error);
        }
    }

    /**
     * 触发页面刷新事件
     */
    triggerPageRefresh(sessionId) {
        try {
            // 使用简化刷新管理器
            if (window.simplifiedRefreshManager) {
                const refreshData = {
                    DataType: 'authentication',
                    RefreshType: sessionId ? 'Login' : 'Logout',
                    SessionId: sessionId,
                    Timestamp: new Date().toISOString()
                };
                window.simplifiedRefreshManager.handleDataChanged(refreshData);
                console.log('✅ 页面刷新事件触发成功');
            }
        } catch (error) {
            console.debug('触发页面刷新事件失败:', error);
        }
    }

    /**
     * 通知其他全局组件
     */
    notifyGlobalComponents(sessionId) {
        try {
            // 发送自定义事件
            const event = new CustomEvent('authenticationStateChanged', {
                detail: {
                    sessionId: sessionId,
                    isAuthenticated: !!sessionId,
                    timestamp: new Date().toISOString()
                }
            });
            window.dispatchEvent(event);
            console.log('✅ 全局认证状态事件发送成功');

            // 更新全局认证状态标志
            window.isAuthenticated = !!sessionId;
            window.currentSessionId = sessionId;
        } catch (error) {
            console.debug('通知全局组件失败:', error);
        }
    }

    /**
     * 注册同步回调
     */
    onSync(callback) {
        this.syncCallbacks.add(callback);
    }

    /**
     * 移除同步回调
     */
    offSync(callback) {
        this.syncCallbacks.delete(callback);
    }

    /**
     * 通知所有同步回调
     */
    notifySyncCallbacks(type, value) {
        this.syncCallbacks.forEach(callback => {
            try {
                callback(type, value);
            } catch (error) {
                console.error('❌ 同步回调执行失败:', error);
            }
        });
    }

    /**
     * 等待SessionId可用
     */
    async waitForSessionId(timeoutMs = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeoutMs) {
            const sessionId = await this.getSessionId();
            if (sessionId) {
                return sessionId;
            }
            
            // 等待100ms后重试
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('等待SessionId超时');
    }

    /**
     * 验证SessionId格式 - 适用于Redis会话ID
     */
    isValidSessionIdFormat(sessionId) {
        if (!sessionId || typeof sessionId !== 'string') {
            return false;
        }

        // Redis会话ID通常是UUID或类似格式，长度在32-128字符之间
        return sessionId.length >= 16 && sessionId.length <= 128;
    }

    /**
     * 获取认证头部
     */
    async getAuthHeaders() {
        const sessionId = await this.getSessionId();
        if (sessionId) {
            return {
                'X-Session-Id': sessionId
            };
        }
        return {};
    }

    /**
     * Cookie工具方法 - 设置Cookie
     */
    setCookie(name, value, options = {}) {
        let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
        
        if (options.maxAge) {
            cookieString += `; Max-Age=${options.maxAge}`;
        }
        
        if (options.path) {
            cookieString += `; Path=${options.path}`;
        }
        
        if (options.domain) {
            cookieString += `; Domain=${options.domain}`;
        }
        
        if (options.secure) {
            cookieString += `; Secure`;
        }
        
        if (options.sameSite) {
            cookieString += `; SameSite=${options.sameSite}`;
        }
        
        document.cookie = cookieString;
    }

    /**
     * Cookie工具方法 - 获取Cookie
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return decodeURIComponent(parts.pop().split(';').shift());
        }
        return null;
    }

    /**
     * Cookie工具方法 - 删除Cookie
     */
    deleteCookie(name, path = '/') {
        document.cookie = `${encodeURIComponent(name)}=; Max-Age=0; Path=${path}`;
    }

    /**
     * 从Cookie同步SessionId到其他存储位置
     */
    syncFromCookie() {
        // 优先使用标准化的cookie名称，然后尝试旧格式作为降级方案
        const cookieSessionId = this.getCookie(this.sessionCookieName) || 
                               this.getCookie('hw_session_id') || 
                               this.getCookie('hw_session');
        
        if (cookieSessionId && cookieSessionId !== this.sessionId) {
            console.log('🔄 从Cookie同步SessionId:', cookieSessionId.substring(0, 8) + '...');
            this.sessionId = cookieSessionId;
            
            // 同步到localStorage
            try {
                localStorage.setItem(this.sessionStorageKey, cookieSessionId);
            } catch (error) {
                console.error('❌ 同步SessionId到localStorage失败:', error);
            }
            
            // 通知回调
            this.notifySyncCallbacks('sessionId', cookieSessionId);
        }
    }

    /**
     * 启动Cookie同步监控器
     */
    startCookieSyncMonitor() {
        // 定期检查Cookie变化（处理服务器端设置Cookie的情况）
        this.cookieSyncInterval = setInterval(() => {
            this.syncFromCookie();
        }, 5000); // 每5秒检查一次

        // 页面可见性变化时也检查Cookie
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.syncFromCookie();
            }
        });
    }

    /**
     * 停止Cookie同步监控器
     */
    stopCookieSyncMonitor() {
        if (this.cookieSyncInterval) {
            clearInterval(this.cookieSyncInterval);
            this.cookieSyncInterval = null;
        }
    }

    /**
     * 销毁会话管理器
     */
    destroy() {
        this.clearSessionId();
        this.syncCallbacks.clear();
        
        // 停止Cookie同步监控
        this.stopCookieSyncMonitor();
        
        // 移除事件监听器
        window.removeEventListener('storage', this.handleStorageEvent);
    }
}

// 创建全局单例实例
window.unifiedSessionManager = new UnifiedSessionManager();

// 兼容性别名
window.sessionManager = window.unifiedSessionManager;

console.log('✅ 统一会话管理器已初始化');