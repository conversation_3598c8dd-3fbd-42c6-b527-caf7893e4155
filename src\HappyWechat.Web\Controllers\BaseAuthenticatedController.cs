using HappyWechat.Application.Commons;
using HappyWechat.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace HappyWechat.Web.Controllers;

/// <summary>
/// 需要认证的控制器基类
/// 自动验证用户认证状态，提供统一的用户上下文访问
/// </summary>
[ApiController]
[Route("api/[controller]")]
public abstract class BaseAuthenticatedController : ControllerBase
{
    protected readonly ICurrentUserContext UserContext;
    protected readonly ILogger Logger;

    protected BaseAuthenticatedController(ICurrentUserContext userContext, ILogger logger)
    {
        UserContext = userContext;
        Logger = logger;
        
        // 在构造函数中记录用户信息（用于调试）
        if (userContext.IsAuthenticated)
        {
            Logger.LogDebug("已认证用户访问控制器 - Controller: {Controller}, UserId: {UserId}, Username: {Username}", 
                GetType().Name, userContext.UserId, userContext.Username);
        }
    }

    /// <summary>
    /// 当前用户ID
    /// 如果用户未认证或ID无效，返回Guid.Empty
    /// </summary>
    protected Guid CurrentUserId => UserContext.UserId;

    /// <summary>
    /// 当前用户名
    /// </summary>
    protected string? CurrentUsername => UserContext.Username;

    /// <summary>
    /// 当前用户显示名称
    /// </summary>
    protected string? CurrentUserDisplayName => UserContext.DisplayName;

    /// <summary>
    /// 当前用户角色列表
    /// </summary>
    protected IReadOnlyList<string> CurrentUserRoles => UserContext.Roles;

    /// <summary>
    /// 验证用户是否已认证且用户ID有效
    /// </summary>
    /// <returns>验证结果</returns>
    protected (bool IsValid, ActionResult? ErrorResponse) ValidateUserContext()
    {
        if (!UserContext.IsAuthenticated)
        {
            Logger.LogWarning("未认证用户尝试访问受保护资源 - Controller: {Controller}", GetType().Name);
            var response = Unauthorized(ApiResponse<object>.Failure("用户未认证，请先登录", 401));
            return (false, response);
        }

        if (UserContext.UserId == Guid.Empty)
        {
            Logger.LogWarning("用户已认证但用户ID无效 - Controller: {Controller}, Username: {Username}", 
                GetType().Name, UserContext.Username);
            var response = BadRequest(ApiResponse<object>.Failure("用户身份信息异常，请重新登录", 400));
            return (false, response);
        }

        return (true, null);
    }

    /// <summary>
    /// 验证用户角色权限
    /// </summary>
    /// <param name="requiredRole">所需角色</param>
    /// <returns>验证结果</returns>
    protected (bool IsValid, ActionResult? ErrorResponse) ValidateUserRole(string requiredRole)
    {
        var (isValid, errorResponse) = ValidateUserContext();
        if (!isValid)
            return (false, errorResponse);

        if (!UserContext.HasRole(requiredRole))
        {
            Logger.LogWarning("用户 {UserId} 缺少角色权限 {Role} - Controller: {Controller}", 
                UserContext.UserId, requiredRole, GetType().Name);
            var response = StatusCode(403, ApiResponse<object>.Failure($"用户没有 '{requiredRole}' 角色权限", 403));
            return (false, response);
        }

        return (true, null);
    }

    /// <summary>
    /// 执行需要认证的操作
    /// </summary>
    /// <param name="operation">操作函数</param>
    /// <typeparam name="T">返回类型</typeparam>
    /// <returns>操作结果</returns>
    protected async Task<ActionResult<ApiResponse<T>>> ExecuteAuthenticatedOperation<T>(
        Func<Task<T>> operation)
    {
        try
        {
            var (isValid, errorResponse) = ValidateUserContext();
            if (!isValid)
                return (ActionResult<ApiResponse<T>>)errorResponse!;

            var result = await operation();
            return Ok(ApiResponse<T>.Success(result));
        }
        catch (Exception ex)
        {
            return HandleException<T>(ex);
        }
    }

    /// <summary>
    /// 执行需要特定角色权限的操作
    /// </summary>
    /// <param name="requiredRole">所需角色</param>
    /// <param name="operation">操作函数</param>
    /// <typeparam name="T">返回类型</typeparam>
    /// <returns>操作结果</returns>
    protected async Task<ActionResult<ApiResponse<T>>> ExecuteRoleBasedOperation<T>(
        string requiredRole, 
        Func<Task<T>> operation)
    {
        try
        {
            var (isValid, errorResponse) = ValidateUserRole(requiredRole);
            if (!isValid)
                return (ActionResult<ApiResponse<T>>)errorResponse!;

            var result = await operation();
            return Ok(ApiResponse<T>.Success(result));
        }
        catch (Exception ex)
        {
            return HandleException<T>(ex);
        }
    }

    /// <summary>
    /// 处理异常并返回标准化响应
    /// </summary>
    /// <param name="ex">异常</param>
    /// <typeparam name="T">数据类型</typeparam>
    /// <returns>错误响应</returns>
    protected ActionResult<ApiResponse<T>> HandleException<T>(Exception ex)
    {
        Logger.LogError(ex, "API操作异常 - Controller: {Controller}, UserId: {UserId}, Username: {Username}", 
            GetType().Name, UserContext.UserId, UserContext.Username);

        return ex switch
        {
            UnauthorizedAccessException => Unauthorized(ApiResponse<T>.Failure("访问被拒绝", 401)),
            ArgumentException argEx => BadRequest(ApiResponse<T>.Failure(argEx.Message, 400)),
            InvalidOperationException opEx => BadRequest(ApiResponse<T>.Failure(opEx.Message, 400)),
            _ => StatusCode(500, ApiResponse<T>.Failure("服务器内部错误", 500))
        };
    }

    /// <summary>
    /// 记录操作日志
    /// </summary>
    /// <param name="operation">操作名称</param>
    /// <param name="details">操作详情</param>
    protected void LogOperation(string operation, object? details = null)
    {
        Logger.LogInformation("用户操作 - Controller: {Controller}, Operation: {Operation}, UserId: {UserId}, Username: {Username}, Details: {Details}", 
            GetType().Name, operation, UserContext.UserId, UserContext.Username, details);
    }
}