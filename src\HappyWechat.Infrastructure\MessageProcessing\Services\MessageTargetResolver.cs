using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Unified;
using HappyWechat.Infrastructure.MessageProcessing.Strategies;
using HappyWechat.Infrastructure.MessageProcessing.Constants;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using static HappyWechat.Infrastructure.MessageProcessing.Constants.MessageProcessingConstants;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息发送目标决策服务实现
/// 统一处理群组/私聊消息的发送目标逻辑，彻底解决发送目标混乱问题
/// </summary>
public class MessageTargetResolver : IMessageTargetResolver
{
    private readonly ILogger<MessageTargetResolver> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMessageRoutingStrategy _messageRoutingStrategy;

    public MessageTargetResolver(
        ILogger<MessageTargetResolver> logger,
        IServiceProvider serviceProvider,
        IMessageRoutingStrategy messageRoutingStrategy)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _messageRoutingStrategy = messageRoutingStrategy;
    }

    /// <summary>
    /// 解析消息发送目标 - 核心决策逻辑
    /// </summary>
    public async Task<MessageTargetDecision> ResolveTargetAsync(
        WxCallbackMessageDto callbackMessage, 
        AiConfigurationInfo aiConfig,
        string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🎯 开始消息发送目标决策 - MessageType: {MessageType}, IsGroupMessage: {IsGroupMessage}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}",
                processingId, callbackMessage.MessageType, aiConfig.IsGroupMessage, aiConfig.OnlyReplyWhenMentioned);

            // 🎯 新架构：文件+@组合检查已移至MessageRoutingStrategy，此处只做目标决策
            return aiConfig.IsGroupMessage
                ? ResolveGroupTarget(callbackMessage, aiConfig, processingId)
                : ResolvePrivateTarget(callbackMessage, aiConfig, processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息发送目标决策异常", processingId);
            
            // 降级处理：发送给原发送者
            return new MessageTargetDecision
            {
                TargetUser = callbackMessage.Data?.FromUser ?? "",
                SendMode = MessageSendMode.Private,
                Reason = $"决策异常，降级为私聊模式: {ex.Message}",
                IsGroupMessage = false
            };
        }
    }

    /// <summary>
    /// 解析群组消息发送目标
    /// </summary>
    private MessageTargetDecision ResolveGroupTarget(
        WxCallbackMessageDto callbackMessage, 
        AiConfigurationInfo aiConfig, 
        string processingId)
    {
        var groupId = callbackMessage.Data?.FromGroup;
        var senderId = callbackMessage.Data?.FromUser;

        if (string.IsNullOrEmpty(groupId))
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ 群组消息缺少群组ID，降级为私聊模式", processingId);
            return new MessageTargetDecision
            {
                TargetUser = senderId ?? "",
                SendMode = MessageSendMode.Private,
                Reason = "群组ID缺失，降级为私聊模式",
                IsGroupMessage = false
            };
        }

        // 🔧 核心修复：根据OnlyReplyWhenMentioned配置决定发送模式
        if (aiConfig.OnlyReplyWhenMentioned)
        {
            // "仅@后回复"模式：发送到群组，@原发送者
            _logger.LogDebug("[{ProcessingId}] 🎯 群组仅@后回复模式 - 发送到群组: {GroupId}, @用户: {SenderId}",
                processingId, groupId, senderId);

            return new MessageTargetDecision
            {
                TargetUser = groupId,
                AtUsers = new List<string> { senderId },
                SendMode = MessageSendMode.GroupReplyMentioned,
                Reason = "群组仅@后回复模式",
                IsGroupMessage = true,
                RequiresGroupAtProcessing = true,
                OriginalSenderId = senderId,
                GroupId = groupId
            };
        }
        else
        {
            // 🔧 修复2："回复所有消息"模式：发送到群组，@原发送者
            _logger.LogDebug("[{ProcessingId}] 🎯 群组回复所有消息模式 - 发送到群组: {GroupId}, @原发送者: {SenderId}",
                processingId, groupId, senderId);

            return new MessageTargetDecision
            {
                TargetUser = groupId,
                AtUsers = new List<string> { senderId },
                SendMode = MessageSendMode.GroupReplyAll,
                Reason = "群组回复所有消息模式（@原发送者）",
                IsGroupMessage = true,
                RequiresGroupAtProcessing = true,
                OriginalSenderId = senderId,
                GroupId = groupId
            };
        }
    }

    /// <summary>
    /// 解析私聊消息发送目标
    /// </summary>
    private MessageTargetDecision ResolvePrivateTarget(
        WxCallbackMessageDto callbackMessage, 
        AiConfigurationInfo aiConfig, 
        string processingId)
    {
        var senderId = callbackMessage.Data?.FromUser;

        _logger.LogDebug("[{ProcessingId}] 🎯 私聊模式 - 发送给发送者: {SenderId}", processingId, senderId);

        return new MessageTargetDecision
        {
            TargetUser = senderId ?? "",
            AtUsers = null,
            SendMode = MessageSendMode.Private,
            Reason = "私聊消息模式",
            IsGroupMessage = false,
            RequiresGroupAtProcessing = false,
            OriginalSenderId = senderId
        };
    }

    /// <summary>
    /// 创建文件+@组合目标决策
    /// 🎯 已废弃：新架构使用FileAtCombinationOrchestrator处理文件+@组合
    /// </summary>
    [Obsolete("已废弃：新架构使用FileAtCombinationOrchestrator处理文件+@组合")]
    private MessageTargetDecision CreateFileAtCombinationTarget(
        WxCallbackMessageDto callbackMessage,
        FileAtCombinationResult fileAtResult,
        string processingId)
    {
        _logger.LogWarning("[{ProcessingId}] ⚠️ 使用了已废弃的CreateFileAtCombinationTarget方法", processingId);
        return new MessageTargetDecision
        {
            TargetUser = callbackMessage.Data?.FromGroup ?? "",
            AtUsers = callbackMessage.Data?.FromUser != null ? new List<string> { callbackMessage.Data.FromUser } : new List<string>(),
            SendMode = MessageSendMode.GroupReplyMentioned,
            Reason = "已废弃方法的降级处理",
            IsGroupMessage = true
        };
    }

    /// <summary>
    /// 检查文件+@组合功能
    /// 🎯 已废弃：新架构使用FileAtCombinationOrchestrator处理文件+@组合
    /// </summary>
    [Obsolete("已废弃：新架构使用FileAtCombinationOrchestrator处理文件+@组合")]
    public async Task<FileAtCombinationResult> CheckFileAtCombinationAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId)
    {
        _logger.LogWarning("[{ProcessingId}] ⚠️ 使用了已废弃的CheckFileAtCombinationAsync方法，请使用新架构", processingId);
        return new FileAtCombinationResult { HasCombination = false };
    }

    // 🔧 删除：IsFileMessage方法已不需要，现在所有消息都检查文件+@组合

    /// <summary>
    /// 检查消息是否@了机器人或特定目标
    /// 🔧 使用精确的@检测：只检测AtList中的WcId和@所有人，避免昵称重名混乱
    /// </summary>
    public bool IsAtMentioned(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            var messageData = callbackMessage.Data;
            var atList = messageData?.Atlist ?? new List<string>();
            var content = messageData?.Content ?? "";
            var botWcId = callbackMessage.WcId;

            // 🔧 精确@检测：只使用AtList中的WcId和@所有人，避免昵称重名问题
            var isMentioned = atList.Contains(botWcId) || content.Contains("@所有人");

            _logger.LogDebug("🔍 @检测结果 - BotWcId: {BotWcId}, AtList: [{AtList}], Content: '{ContentPreview}', IsMentioned: {IsMentioned}",
                botWcId, string.Join(",", atList),
                content.Length > 20 ? content.Substring(0, 20) + "..." : content,
                isMentioned);

            return isMentioned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ @检测异常");
            return false;
        }
    }

    // 🎯 已移除：IsGroupMessage和IsPrivateMessage方法
    // 新架构使用MessageProcessingConstants中的静态方法
}
