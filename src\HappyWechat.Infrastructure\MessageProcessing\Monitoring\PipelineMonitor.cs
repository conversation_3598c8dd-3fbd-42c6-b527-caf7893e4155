using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageProcessing.Pipeline;
using HappyWechat.Infrastructure.MessageProcessing.Isolation;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.MessageProcessing.Monitoring;

/// <summary>
/// 流水线监控服务
/// 实时监控消息处理流水线的性能和健康状态
/// </summary>
public interface IPipelineMonitor
{
    /// <summary>
    /// 获取流水线性能指标
    /// </summary>
    Task<PipelineMetrics> GetPipelineMetricsAsync(string pipelineName);
    
    /// <summary>
    /// 获取所有流水线状态
    /// </summary>
    Task<List<PipelineStatus>> GetAllPipelineStatusAsync();
    
    /// <summary>
    /// 获取账号资源使用情况
    /// </summary>
    Task<List<AccountResourceUsage>> GetAccountResourceUsageAsync();
    
    /// <summary>
    /// 记录处理事件
    /// </summary>
    void RecordProcessingEvent(string pipelineName, ProcessingEvent processingEvent);
}

/// <summary>
/// 流水线性能指标
/// </summary>
public class PipelineMetrics
{
    public string PipelineName { get; set; } = string.Empty;
    public long TotalProcessed { get; set; }
    public long TotalFailed { get; set; }
    public double SuccessRate { get; set; }
    public double AverageProcessingTimeMs { get; set; }
    public double ThroughputPerSecond { get; set; }
    public int CurrentActiveJobs { get; set; }
    public int QueuedJobs { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    public List<ProcessingEvent> RecentEvents { get; set; } = new();
}

/// <summary>
/// 处理事件
/// </summary>
public class ProcessingEvent
{
    public string EventId { get; set; } = Guid.NewGuid().ToString();
    public string PipelineName { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public Guid WxManagerId { get; set; }
    public bool Success { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PipelineMonitor : BackgroundService, IPipelineMonitor
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PipelineMonitor> _logger;
    
    // 性能指标缓存
    private readonly ConcurrentDictionary<string, PipelineMetrics> _metricsCache = new();
    
    // 事件存储（内存中保留最近的事件）
    private readonly ConcurrentDictionary<string, Queue<ProcessingEvent>> _eventQueues = new();
    private readonly object _eventLock = new();
    
    private const int MAX_EVENTS_PER_PIPELINE = 1000;
    private const int MONITORING_INTERVAL_SECONDS = 30;
    
    public PipelineMonitor(
        IServiceProvider serviceProvider,
        ILogger<PipelineMonitor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🔍 流水线监控服务启动");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CollectMetricsAsync(stoppingToken);
                await Task.Delay(TimeSpan.FromSeconds(MONITORING_INTERVAL_SECONDS), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 流水线监控异常");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }
        
        _logger.LogInformation("流水线监控服务已停止");
    }
    
    /// <summary>
    /// 收集性能指标
    /// </summary>
    private async Task CollectMetricsAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pipelines = scope.ServiceProvider.GetServices<IMessagePipeline>();
            
            foreach (var pipeline in pipelines)
            {
                await CollectPipelineMetricsAsync(pipeline);
            }
            
            // 清理过期事件
            CleanupOldEvents();
            
            _logger.LogDebug("📊 流水线指标收集完成 - 流水线数量: {Count}", pipelines.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 收集流水线指标异常");
        }
    }
    
    /// <summary>
    /// 收集单个流水线的指标
    /// </summary>
    private async Task CollectPipelineMetricsAsync(IMessagePipeline pipeline)
    {
        try
        {
            var status = pipeline.GetStatus();
            var events = GetRecentEvents(pipeline.Name);
            
            var metrics = new PipelineMetrics
            {
                PipelineName = pipeline.Name,
                TotalProcessed = events.Count(e => e.Success),
                TotalFailed = events.Count(e => !e.Success),
                CurrentActiveJobs = status.ActiveTasks,
                QueuedJobs = status.QueuedMessages,
                LastUpdated = DateTime.UtcNow,
                RecentEvents = events.TakeLast(10).ToList()
            };
            
            // 计算成功率
            var totalEvents = events.Count;
            if (totalEvents > 0)
            {
                metrics.SuccessRate = (double)metrics.TotalProcessed / totalEvents * 100;
                metrics.AverageProcessingTimeMs = events.Average(e => e.ProcessingTime.TotalMilliseconds);
                
                // 计算吞吐量（最近5分钟）
                var recentEvents = events.Where(e => e.Timestamp > DateTime.UtcNow.AddMinutes(-5)).ToList();
                if (recentEvents.Any())
                {
                    var timeSpan = DateTime.UtcNow - recentEvents.Min(e => e.Timestamp);
                    metrics.ThroughputPerSecond = recentEvents.Count / Math.Max(1, timeSpan.TotalSeconds);
                }
            }
            
            _metricsCache.AddOrUpdate(pipeline.Name, metrics, (key, existing) => metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 收集流水线 {PipelineName} 指标异常", pipeline.Name);
        }
    }
    
    /// <summary>
    /// 获取流水线性能指标
    /// </summary>
    public async Task<PipelineMetrics> GetPipelineMetricsAsync(string pipelineName)
    {
        if (_metricsCache.TryGetValue(pipelineName, out var metrics))
        {
            return metrics;
        }
        
        return new PipelineMetrics
        {
            PipelineName = pipelineName,
            LastUpdated = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 获取所有流水线状态
    /// </summary>
    public async Task<List<PipelineStatus>> GetAllPipelineStatusAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pipelines = scope.ServiceProvider.GetServices<IMessagePipeline>();
            
            var statusList = new List<PipelineStatus>();
            foreach (var pipeline in pipelines)
            {
                var status = pipeline.GetStatus();
                statusList.Add(status);
            }
            
            return statusList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取流水线状态异常");
            return new List<PipelineStatus>();
        }
    }
    
    /// <summary>
    /// 获取账号资源使用情况
    /// </summary>
    public async Task<List<AccountResourceUsage>> GetAccountResourceUsageAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var resourceManager = scope.ServiceProvider.GetService<IAccountResourceManager>();
            
            if (resourceManager == null)
            {
                return new List<AccountResourceUsage>();
            }
            
            // TODO: 获取所有活跃账号的资源使用情况
            // 这里需要从数据库或缓存中获取活跃账号列表
            var activeAccounts = new List<Guid>(); // 暂时为空
            
            var usageList = new List<AccountResourceUsage>();
            foreach (var accountId in activeAccounts)
            {
                var usage = await resourceManager.GetResourceUsageAsync(accountId);
                usageList.Add(usage);
            }
            
            return usageList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取账号资源使用情况异常");
            return new List<AccountResourceUsage>();
        }
    }
    
    /// <summary>
    /// 记录处理事件
    /// </summary>
    public void RecordProcessingEvent(string pipelineName, ProcessingEvent processingEvent)
    {
        try
        {
            lock (_eventLock)
            {
                var queue = _eventQueues.GetOrAdd(pipelineName, _ => new Queue<ProcessingEvent>());
                
                queue.Enqueue(processingEvent);
                
                // 限制队列大小
                while (queue.Count > MAX_EVENTS_PER_PIPELINE)
                {
                    queue.Dequeue();
                }
            }
            
            _logger.LogDebug("📝 记录处理事件 - Pipeline: {PipelineName}, Success: {Success}, Duration: {Duration}ms",
                pipelineName, processingEvent.Success, processingEvent.ProcessingTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 记录处理事件异常 - Pipeline: {PipelineName}", pipelineName);
        }
    }
    
    /// <summary>
    /// 获取最近的事件
    /// </summary>
    private List<ProcessingEvent> GetRecentEvents(string pipelineName)
    {
        lock (_eventLock)
        {
            if (_eventQueues.TryGetValue(pipelineName, out var queue))
            {
                return queue.ToList();
            }
            return new List<ProcessingEvent>();
        }
    }
    
    /// <summary>
    /// 清理过期事件
    /// </summary>
    private void CleanupOldEvents()
    {
        lock (_eventLock)
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-24); // 保留24小时内的事件
            
            foreach (var kvp in _eventQueues)
            {
                var queue = kvp.Value;
                var tempList = queue.ToList();
                queue.Clear();
                
                foreach (var evt in tempList.Where(e => e.Timestamp > cutoffTime))
                {
                    queue.Enqueue(evt);
                }
            }
        }
    }
}
