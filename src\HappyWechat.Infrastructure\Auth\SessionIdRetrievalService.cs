using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// SessionId获取服务实现
/// 协调多个获取策略，实现优先级和降级逻辑
/// </summary>
public class SessionIdRetrievalService : ISessionIdRetrievalService
{
    private readonly ILogger<SessionIdRetrievalService> _logger;
    private readonly List<SessionIdRetrievalStrategy> _strategies;
    private readonly SessionIdRetrievalStats _stats;
    private readonly object _statsLock = new object();

    public SessionIdRetrievalService(
        IEnumerable<SessionIdRetrievalStrategy> strategies,
        ILogger<SessionIdRetrievalService> logger)
    {
        _logger = logger;
        _strategies = strategies.OrderBy(s => s.Priority).ToList();
        _stats = new SessionIdRetrievalStats();

        _logger.LogInformation("🔧 SessionId获取服务已初始化，策略数量: {Count}", _strategies.Count);
        foreach (var strategy in _strategies)
        {
            _logger.LogDebug("📋 注册策略: {Description}", strategy.GetDescription());
        }
    }

    public async Task<string?> GetSessionIdAsync(HttpContext? httpContext = null)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("🔍 开始获取SessionId，策略数量: {Count}", _strategies.Count);

            foreach (var strategy in _strategies)
            {
                var strategyStopwatch = Stopwatch.StartNew();
                
                try
                {
                    // 检查策略是否可用
                    if (!await strategy.IsAvailableAsync(httpContext))
                    {
                        _logger.LogDebug("⚠️ 策略不可用: {StrategyName}", strategy.StrategyName);
                        continue;
                    }

                    // 尝试获取SessionId
                    var sessionId = await strategy.GetSessionIdAsync(httpContext);
                    strategyStopwatch.Stop();

                    // 更新统计信息
                    UpdateStrategyStats(strategy.StrategyType, true, strategyStopwatch.ElapsedMilliseconds);

                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        // 验证SessionId
                        if (await strategy.ValidateSessionIdAsync(sessionId))
                        {
                            _logger.LogInformation("✅ SessionId获取成功 - 策略: {StrategyName}, SessionId: {SessionId}, 耗时: {ElapsedMs}ms", 
                                strategy.StrategyName, 
                                sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId,
                                strategyStopwatch.ElapsedMilliseconds);

                            // 更新缓存（如果有内存缓存策略）
                            await UpdateCacheIfAvailable(sessionId);

                            // 更新全局统计
                            UpdateGlobalStats(true, strategy.StrategyType);

                            return sessionId;
                        }
                        else
                        {
                            _logger.LogWarning("⚠️ SessionId验证失败 - 策略: {StrategyName}, SessionId: {SessionId}", 
                                strategy.StrategyName, 
                                sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    strategyStopwatch.Stop();
                    _logger.LogError(ex, "❌ 策略执行异常: {StrategyName}", strategy.StrategyName);
                    
                    // 更新统计信息
                    UpdateStrategyStats(strategy.StrategyType, false, strategyStopwatch.ElapsedMilliseconds);
                }
            }

            _logger.LogWarning("⚠️ 所有策略都无法获取有效的SessionId");
            UpdateGlobalStats(false, null);
            return null;
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("🔍 SessionId获取完成，总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
    }

    public async Task<string?> GetSessionIdFromStrategyAsync(SessionIdRetrievalStrategyType strategyType, HttpContext? httpContext = null)
    {
        var strategy = _strategies.FirstOrDefault(s => s.StrategyType == strategyType);
        if (strategy == null)
        {
            _logger.LogWarning("⚠️ 未找到指定类型的策略: {StrategyType}", strategyType);
            return null;
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!await strategy.IsAvailableAsync(httpContext))
            {
                _logger.LogDebug("⚠️ 指定策略不可用: {StrategyName}", strategy.StrategyName);
                return null;
            }

            var sessionId = await strategy.GetSessionIdAsync(httpContext);
            stopwatch.Stop();

            // 更新统计信息
            UpdateStrategyStats(strategy.StrategyType, !string.IsNullOrEmpty(sessionId), stopwatch.ElapsedMilliseconds);

            if (!string.IsNullOrEmpty(sessionId))
            {
                _logger.LogDebug("✅ 指定策略获取SessionId成功: {StrategyName}, SessionId: {SessionId}", 
                    strategy.StrategyName, 
                    sessionId.Length > 8 ? sessionId.Substring(0, 8) + "..." : sessionId);
            }

            return sessionId;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "❌ 指定策略执行异常: {StrategyName}", strategy.StrategyName);
            
            // 更新统计信息
            UpdateStrategyStats(strategy.StrategyType, false, stopwatch.ElapsedMilliseconds);
            return null;
        }
    }

    public async Task<bool> ValidateSessionIdAsync(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            return false;
        }

        // 使用HTTP上下文策略进行验证（它有完整的验证逻辑）
        var httpContextStrategy = _strategies.FirstOrDefault(s => s.StrategyType == SessionIdRetrievalStrategyType.HttpContext);
        if (httpContextStrategy != null)
        {
            return await httpContextStrategy.ValidateSessionIdAsync(sessionId);
        }

        // 降级到基本验证
        return sessionId.Length >= 10;
    }

    public Task<SessionIdRetrievalStats> GetRetrievalStatsAsync()
    {
        lock (_statsLock)
        {
            // 创建统计信息的副本
            var statsCopy = new SessionIdRetrievalStats
            {
                TotalRetrievals = _stats.TotalRetrievals,
                SuccessfulRetrievals = _stats.SuccessfulRetrievals,
                FailedRetrievals = _stats.FailedRetrievals,
                LastSuccessfulStrategy = _stats.LastSuccessfulStrategy,
                LastRetrievalTime = _stats.LastRetrievalTime,
                StrategyStats = new Dictionary<SessionIdRetrievalStrategyType, StrategyUsageStats>(_stats.StrategyStats)
            };

            return Task.FromResult(statsCopy);
        }
    }

    /// <summary>
    /// 更新缓存（如果有内存缓存策略）
    /// </summary>
    /// <param name="sessionId">SessionId</param>
    private async Task UpdateCacheIfAvailable(string sessionId)
    {
        var cacheStrategy = _strategies.FirstOrDefault(s => s.StrategyType == SessionIdRetrievalStrategyType.MemoryCache);
        if (cacheStrategy is MemoryCacheSessionIdStrategy memoryCacheStrategy)
        {
            memoryCacheStrategy.SetCache(sessionId);
        }
    }

    /// <summary>
    /// 更新策略统计信息
    /// </summary>
    /// <param name="strategyType">策略类型</param>
    /// <param name="success">是否成功</param>
    /// <param name="responseTimeMs">响应时间</param>
    private void UpdateStrategyStats(SessionIdRetrievalStrategyType strategyType, bool success, long responseTimeMs)
    {
        lock (_statsLock)
        {
            if (!_stats.StrategyStats.ContainsKey(strategyType))
            {
                _stats.StrategyStats[strategyType] = new StrategyUsageStats();
            }

            var strategyStats = _stats.StrategyStats[strategyType];
            strategyStats.UsageCount++;
            strategyStats.LastUsedAt = DateTime.UtcNow;

            if (success)
            {
                strategyStats.SuccessCount++;
            }
            else
            {
                strategyStats.FailureCount++;
            }

            // 更新平均响应时间
            strategyStats.AverageResponseTimeMs = 
                (strategyStats.AverageResponseTimeMs * (strategyStats.UsageCount - 1) + responseTimeMs) / strategyStats.UsageCount;
        }
    }

    /// <summary>
    /// 更新全局统计信息
    /// </summary>
    /// <param name="success">是否成功</param>
    /// <param name="successfulStrategy">成功的策略类型</param>
    private void UpdateGlobalStats(bool success, SessionIdRetrievalStrategyType? successfulStrategy)
    {
        lock (_statsLock)
        {
            _stats.TotalRetrievals++;
            _stats.LastRetrievalTime = DateTime.UtcNow;

            if (success)
            {
                _stats.SuccessfulRetrievals++;
                _stats.LastSuccessfulStrategy = successfulStrategy;
            }
            else
            {
                _stats.FailedRetrievals++;
            }
        }
    }
}
