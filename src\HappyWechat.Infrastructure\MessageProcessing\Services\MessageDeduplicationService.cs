using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 消息去重服务
/// </summary>
public interface IMessageDeduplicationService
{
    /// <summary>
    /// 检查消息是否重复
    /// </summary>
    Task<bool> IsDuplicateMessageAsync(WxCallbackMessageDto message);

    /// <summary>
    /// 标记消息已处理
    /// </summary>
    Task MarkMessageProcessedAsync(WxCallbackMessageDto message);
}

/// <summary>
/// 消息去重配置选项
/// </summary>
public class MessageDeduplicationOptions
{
    public const string SectionName = "MessageDeduplication";
    
    /// <summary>
    /// 去重时间窗口（分钟）
    /// </summary>
    public int TimeWindowMinutes { get; set; } = 5;
    
    /// <summary>
    /// 是否启用去重
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// 缓存键前缀
    /// </summary>
    public string CacheKeyPrefix { get; set; } = "msg_dedup";
}

/// <summary>
/// 基于内存缓存的消息去重服务实现
/// </summary>
public class MessageDeduplicationService : IMessageDeduplicationService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<MessageDeduplicationService> _logger;
    private readonly MessageDeduplicationOptions _options;

    public MessageDeduplicationService(
        IMemoryCache cache,
        ILogger<MessageDeduplicationService> logger,
        IOptions<MessageDeduplicationOptions> options)
    {
        _cache = cache;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<bool> IsDuplicateMessageAsync(WxCallbackMessageDto message)
    {
        if (!_options.Enabled)
        {
            return false;
        }

        try
        {
            var cacheKey = GenerateCacheKey(message);

            // 🔧 修复：使用原子操作检查和标记，避免竞态条件
            var lockKey = $"lock:{cacheKey}";
            var lockValue = Guid.NewGuid().ToString();
            var lockExpiry = TimeSpan.FromSeconds(10);

            // 简单的内存锁机制（对于单实例应用）
            lock (_cache)
            {
                var isDuplicate = _cache.TryGetValue(cacheKey, out _);

                if (isDuplicate)
                {
                    // 🔧 精简日志 - 注释掉重复消息检测日志，减少日志噪音
                    // _logger.LogWarning("检测到重复消息 - MsgId: {MsgId}, MessageType: {MessageType}, FromUser: {FromUser}",
                    //     message.Data?.MsgId, message.MessageType, message.Data?.FromUser);
                    return true;
                }

                // 立即标记消息为处理中，防止重复处理
                var expiration = TimeSpan.FromMinutes(_options.TimeWindowMinutes);
                _cache.Set(cacheKey, DateTime.UtcNow, expiration);

                _logger.LogDebug("消息已标记为处理中 - MsgId: {MsgId}, CacheKey: {CacheKey}",
                    message.Data?.MsgId, cacheKey);

                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查消息重复性失败 - MsgId: {MsgId}", message.Data?.MsgId);
            // 出错时不阻止消息处理
            return false;
        }
    }

    public async Task MarkMessageProcessedAsync(WxCallbackMessageDto message)
    {
        if (!_options.Enabled)
        {
            return;
        }

        try
        {
            var cacheKey = GenerateCacheKey(message);
            var expiration = TimeSpan.FromMinutes(_options.TimeWindowMinutes);

            // 🔧 修复：消息已在IsDuplicateMessageAsync中标记，这里只需要更新状态
            lock (_cache)
            {
                _cache.Set(cacheKey, $"processed:{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}", expiration);
            }

            _logger.LogDebug("更新消息处理状态 - MsgId: {MsgId}, 过期时间: {Expiration}分钟",
                message.Data?.MsgId, _options.TimeWindowMinutes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新消息处理状态失败 - MsgId: {MsgId}", message.Data?.MsgId);
        }
    }

    /// <summary>
    /// 🔧 优化：生成更精确的缓存键，避免冲突
    /// </summary>
    private string GenerateCacheKey(WxCallbackMessageDto message)
    {
        // 使用MsgId + MessageType + FromUser + WcId作为唯一标识
        // 添加WcId确保不同微信账号的消息不会冲突
        // 添加时间戳的小时部分，避免长时间缓存导致的问题
        var hourKey = DateTime.UtcNow.ToString("yyyyMMddHH");
        return $"{_options.CacheKeyPrefix}:{message.Data?.MsgId}:{message.MessageType}:{message.Data?.FromUser}:{message.WcId}:{hourKey}";
    }
}
