using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Interfaces;
using System.Text.Json;

namespace HappyWechat.Infrastructure.MessageQueue.Unified;

/// <summary>
/// 统一EYun发送队列消费者
/// 按账号隔离，严格控制发送间隔，先进先出
/// </summary>
public class UnifiedEYunSendQueueConsumer : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<UnifiedEYunSendQueueConsumer> _logger;
    private readonly SemaphoreSlim _processingLock = new(1, 1);
    private readonly JsonSerializerOptions _jsonOptions;
    
    public UnifiedEYunSendQueueConsumer(
        IServiceProvider serviceProvider,
        ILogger<UnifiedEYunSendQueueConsumer> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🚀 UnifiedEYunSendQueueConsumer启动 - 开始转发消息到发送队列");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessSendMessagesAsync(stoppingToken);
                
                // 短暂延迟，避免过度消耗CPU
                await Task.Delay(200, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("统一EYun发送队列消费者正在停止");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 统一EYun发送队列处理异常");
                await Task.Delay(5000, stoppingToken); // 异常时等待5秒
            }
        }
        
        _logger.LogInformation("统一EYun发送队列消费者已停止");
    }
    
    /// <summary>
    /// 处理发送消息
    /// </summary>
    private async Task ProcessSendMessagesAsync(CancellationToken cancellationToken)
    {
        if (!await _processingLock.WaitAsync(100, cancellationToken))
        {
            return; // 如果无法获取锁，跳过本次处理
        }
        
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var unifiedSendQueue = scope.ServiceProvider.GetRequiredService<IUnifiedEYunSendQueue>();
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();
            
            // 获取所有活跃的微信账号
            var activeWxManagerIds = await GetActiveWxManagerIdsAsync(scope.ServiceProvider, cancellationToken);
            
            foreach (var wxManagerId in activeWxManagerIds)
            {
                await ProcessWxManagerSendMessagesAsync(unifiedSendQueue, queueService, wxManagerId, cancellationToken);
            }
        }
        finally
        {
            _processingLock.Release();
        }
    }
    
    /// <summary>
    /// 处理特定微信账号的发送消息 - 增强错误处理，避免单个消息失败阻塞整个队列
    /// </summary>
    private async Task ProcessWxManagerSendMessagesAsync(
        IUnifiedEYunSendQueue unifiedSendQueue,
        ISimplifiedQueueService queueService,
        Guid wxManagerId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取待发送的消息（严格按时间顺序）
            var readyMessages = await unifiedSendQueue.DequeueReadyMessagesAsync(wxManagerId, 3, cancellationToken);

            if (!readyMessages.Any())
            {
                return;
            }

            _logger.LogInformation("[FORWARD] 📦 开始转发消息到发送队列 - WxManagerId: {WxManagerId}, Count: {Count}",
                wxManagerId, readyMessages.Count);

            var successCount = 0;
            var failureCount = 0;

            // 严格按顺序逐个发送，确保间隔，单个消息失败不影响其他消息
            foreach (var message in readyMessages.OrderBy(m => m.Order))
            {
                try
                {
                    await SendSingleMessageAsync(queueService, unifiedSendQueue, message, cancellationToken);

                    // 发送完成后立即标记，避免重复发送
                    await unifiedSendQueue.MarkAsSentAsync(wxManagerId, message.Id, true, null, cancellationToken);

                    successCount++;
                    _logger.LogInformation("[FORWARD] ✅ 消息转发完成 - MessageId: {MessageId}, CommandType: {CommandType}, WxManagerId: {WxManagerId}",
                        message.Id, message.CommandType, wxManagerId);
                }
                catch (Exception messageEx)
                {
                    failureCount++;
                    _logger.LogError(messageEx, "❌ 单个消息发送失败 - MessageId: {MessageId}, CommandType: {CommandType}",
                        message.Id, message.CommandType);

                    // 标记消息为失败，但继续处理其他消息
                    try
                    {
                        await unifiedSendQueue.MarkAsSentAsync(wxManagerId, message.Id, false, messageEx.Message, cancellationToken);
                    }
                    catch (Exception markEx)
                    {
                        _logger.LogError(markEx, "❌ 标记消息失败状态异常 - MessageId: {MessageId}", message.Id);
                    }
                }
            }

            if (failureCount > 0)
            {
                _logger.LogWarning("[FORWARD] ⚠️ 批量转发完成，部分失败 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
                    wxManagerId, successCount, failureCount);
            }
            else if (successCount > 0)
            {
                _logger.LogInformation("[FORWARD] ✅ 批量转发全部成功 - WxManagerId: {WxManagerId}, Count: {Count}",
                    wxManagerId, successCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理微信账号发送消息异常 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }
    
    /// <summary>
    /// 发送单个消息
    /// </summary>
    private async Task SendSingleMessageAsync(
        ISimplifiedQueueService queueService,
        IUnifiedEYunSendQueue unifiedSendQueue,
        UnifiedSendMessage message,
        CancellationToken cancellationToken)
    {
        try
        {
            // 根据命令类型确定队列类型
            var queueType = GetQueueTypeFromCommand(message.CommandType);
            if (string.IsNullOrEmpty(queueType))
            {
                _logger.LogWarning("⚠️ 未知的命令类型 - CommandType: {CommandType}", message.CommandType);
                await unifiedSendQueue.MarkAsSentAsync(message.WxManagerId, message.Id, false, "未知的命令类型", cancellationToken);
                return;
            }
            
            // 反序列化命令对象
            var command = DeserializeCommand(message.CommandJson, message.CommandType);
            if (command == null)
            {
                _logger.LogWarning("⚠️ 命令反序列化失败 - CommandType: {CommandType}, CommandJson: {CommandJson}", 
                    message.CommandType, message.CommandJson);
                await unifiedSendQueue.MarkAsSentAsync(message.WxManagerId, message.Id, false, "命令反序列化失败", cancellationToken);
                return;
            }

            // 记录命令内容用于调试
            LogCommandContent(command, message.CommandType, message.Id);
            
            // 发送到对应的队列
            var messageId = await queueService.EnqueueAsync(message.WxManagerId, queueType, command, cancellationToken);
            
            _logger.LogInformation("[FORWARD] ✅ 消息转发成功 - MessageId: {OriginalMessageId}, QueueMessageId: {QueueMessageId}, QueueType: {QueueType}, WxManagerId: {WxManagerId}",
                message.Id, messageId, queueType, message.WxManagerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送单个消息异常 - MessageId: {MessageId}", message.Id);
            await unifiedSendQueue.MarkAsSentAsync(message.WxManagerId, message.Id, false, ex.Message, cancellationToken);
        }
    }
    
    /// <summary>
    /// 根据命令类型获取队列类型
    /// </summary>
    private string GetQueueTypeFromCommand(string commandType)
    {
        return commandType switch
        {
            "WxSendTextMessageCommand" => "send_text",
            "WxSendImageMessageCommand" => "send_image",
            "WxSendFileMessageCommand" => "file_send",
            "WxSendVoiceMessageCommand" => "send_voice",
            "WxSendVideoMessageCommand" => "send_video",
            "WxSendEmojiMessageCommand" => "send_emoji",
            "WxSendLinkMessageCommand" => "send_link",
            _ => string.Empty
        };
    }
    
    /// <summary>
    /// 反序列化命令对象
    /// </summary>
    private object? DeserializeCommand(string commandJson, string commandType)
    {
        try
        {
            return commandType switch
            {
                "WxSendTextMessageCommand" => JsonSerializer.Deserialize<WxSendTextMessageCommand>(commandJson, _jsonOptions),
                "WxSendImageMessageCommand" => JsonSerializer.Deserialize<WxSendImageMessageCommand>(commandJson, _jsonOptions),
                "WxSendFileMessageCommand" => JsonSerializer.Deserialize<WxSendFileMessageCommand>(commandJson, _jsonOptions),
                "WxSendVoiceMessageCommand" => JsonSerializer.Deserialize<WxSendVoiceMessageCommand>(commandJson, _jsonOptions),
                "WxSendVideoMessageCommand" => JsonSerializer.Deserialize<WxSendVideoMessageCommand>(commandJson, _jsonOptions),
                "WxSendFileBase64MessageCommand" => JsonSerializer.Deserialize<WxSendFileBase64MessageCommand>(commandJson, _jsonOptions),
                _ => null
            };
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "命令反序列化失败 - CommandType: {CommandType}", commandType);
            return null;
        }
    }
    
    /// <summary>
    /// 记录命令内容用于调试
    /// </summary>
    private void LogCommandContent(object command, string commandType, string messageId)
    {
        try
        {
            switch (command)
            {
                case WxSendTextMessageCommand textCommand:
                    _logger.LogInformation("[FORWARD] 📝 转发文本消息命令 - MessageId: {MessageId}, WId: {WId}, WcId: {WcId}, ContentLength: {ContentLength}",
                        messageId, textCommand.WId, textCommand.WcId, textCommand.Content?.Length ?? 0);
                    _logger.LogDebug("[FORWARD] 📝 文本内容: {Content}", 
                        textCommand.Content?.Length > 100 ? textCommand.Content[..100] + "..." : textCommand.Content);
                    break;
                case WxSendFileMessageCommand fileCommand:
                    _logger.LogInformation("[FORWARD] 📁 转发文件消息命令 - MessageId: {MessageId}, WId: {WId}, WcId: {WcId}, Path: {Path}",
                        messageId, fileCommand.WId, fileCommand.WcId, fileCommand.Path);
                    break;
                case WxSendImageMessageCommand imageCommand:
                    _logger.LogInformation("[FORWARD] 🖼️ 转发图片消息命令 - MessageId: {MessageId}, WId: {WId}, WcId: {WcId}, ImagePath: {ImagePath}",
                        messageId, imageCommand.WId, imageCommand.WcId, imageCommand.ImagePath);
                    break;
                case WxSendVoiceMessageCommand voiceCommand:
                    _logger.LogInformation("[FORWARD] 🎵 转发语音消息命令 - MessageId: {MessageId}, WId: {WId}, WcId: {WcId}, VoicePath: {VoicePath}",
                        messageId, voiceCommand.WId, voiceCommand.WcId, voiceCommand.VoiceFilePath);
                    break;
                case WxSendVideoMessageCommand videoCommand:
                    _logger.LogInformation("[FORWARD] 🎬 转发视频消息命令 - MessageId: {MessageId}, WId: {WId}, WcId: {WcId}, Path: {Path}",
                        messageId, videoCommand.WId, videoCommand.WcId, videoCommand.Path);
                    break;
                default:
                    _logger.LogInformation("[FORWARD] 📦 转发其他类型消息命令 - MessageId: {MessageId}, CommandType: {CommandType}",
                        messageId, commandType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录命令内容失败 - MessageId: {MessageId}, CommandType: {CommandType}", messageId, commandType);
        }
    }

    /// <summary>
    /// 获取活跃的微信账号ID列表
    /// </summary>
    private async Task<List<Guid>> GetActiveWxManagerIdsAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var wxManagerRepository = serviceProvider.GetRequiredService<IWxManagerRepository>();
            var allManagers = await wxManagerRepository.GetActiveManagersAsync();

            // 只处理活跃状态的账号（已经通过GetActiveManagersAsync过滤）
            return allManagers
                .Select(m => m.Id)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取活跃微信账号失败，使用空列表");
            return new List<Guid>();
        }
    }
}
