#!/bin/bash

# 配置验证脚本 - 验证Docker配置修复效果
# 使用方法: ./scripts/validate-configuration.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 记录检查结果
record_check() {
    local result=$1
    local message=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$result" = "PASS" ]; then
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        log_success "$message"
    else
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        log_error "$message"
    fi
}

# 检查文件是否存在
check_file_exists() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        record_check "PASS" "$description: $file 存在"
        return 0
    else
        record_check "FAIL" "$description: $file 不存在"
        return 1
    fi
}

# 检查配置文件语法
check_json_syntax() {
    local file=$1
    
    if command -v jq >/dev/null 2>&1; then
        if jq empty "$file" >/dev/null 2>&1; then
            record_check "PASS" "JSON语法检查: $file 语法正确"
        else
            record_check "FAIL" "JSON语法检查: $file 语法错误"
        fi
    else
        log_warning "jq未安装，跳过JSON语法检查"
    fi
}

# 检查Docker Compose语法
check_docker_compose_syntax() {
    local file=$1
    
    if command -v docker-compose >/dev/null 2>&1; then
        if docker-compose -f "$file" config >/dev/null 2>&1; then
            record_check "PASS" "Docker Compose语法检查: $file 语法正确"
        else
            record_check "FAIL" "Docker Compose语法检查: $file 语法错误"
        fi
    else
        log_warning "docker-compose未安装，跳过语法检查"
    fi
}

# 检查环境变量映射
check_env_var_mapping() {
    local compose_file="docker-compose.yml"
    
    log_info "检查环境变量映射..."
    
    # 检查必要的环境变量是否在docker-compose.yml中定义
    local required_vars=(
        "MYSQL_HOST"
        "MYSQL_PORT" 
        "MYSQL_DATABASE"
        "MYSQL_USER"
        "MYSQL_PASSWORD"
        "REDIS_HOST"
        "REDIS_PORT"
        "REDIS_PASSWORD"
        "REDIS_DATABASE_CACHE"
        "REDIS_DATABASE_MESSAGEQUEUE"
        "REDIS_DATABASE_SESSION"
        "REDIS_DATABASE_LOCK"
        "EYUN_ACCOUNT"
        "EYUN_PASSWORD"
        "EYUN_BASE_URL"
        "EYUN_CALLBACK_URL"
        "EYUN_TOKEN"
        "MINIO_ENDPOINT"
        "MINIO_ACCESSKEY"
        "MINIO_SECRETKEY"
        "APPLICATION_DOMAIN"
    )
    
    for var in "${required_vars[@]}"; do
        if grep -q "- ${var}=" "$compose_file"; then
            record_check "PASS" "环境变量映射: $var 已定义"
        else
            record_check "FAIL" "环境变量映射: $var 未定义"
        fi
    done
}

# 检查废弃配置是否已移除
check_deprecated_configs() {
    local config_file="src/HappyWechat.Web/appsettings.json"
    
    log_info "检查废弃配置是否已移除..."
    
    # 检查JWT配置是否已注释（应该只有注释形式存在）
    if grep -q '^[[:space:]]*"Jwt":' "$config_file"; then
        record_check "FAIL" "废弃配置检查: JWT配置仍然存在（未注释）"
    elif grep -q '// "Jwt":' "$config_file"; then
        record_check "PASS" "废弃配置检查: JWT配置已正确注释"
    else
        record_check "PASS" "废弃配置检查: JWT配置已移除"
    fi

    # 检查UnifiedCache配置是否已注释
    if grep -q '^[[:space:]]*"UnifiedCache":' "$config_file"; then
        record_check "FAIL" "废弃配置检查: UnifiedCache配置仍然存在（未注释）"
    elif grep -q '// "UnifiedCache":' "$config_file"; then
        record_check "PASS" "废弃配置检查: UnifiedCache配置已正确注释"
    else
        record_check "PASS" "废弃配置检查: UnifiedCache配置已移除"
    fi

    # 检查UnifiedArchitecture配置是否已注释
    if grep -q '^[[:space:]]*"UnifiedArchitecture":' "$config_file"; then
        record_check "FAIL" "废弃配置检查: UnifiedArchitecture配置仍然存在（未注释）"
    elif grep -q '// "UnifiedArchitecture":' "$config_file"; then
        record_check "PASS" "废弃配置检查: UnifiedArchitecture配置已正确注释"
    else
        record_check "PASS" "废弃配置检查: UnifiedArchitecture配置已移除"
    fi

    # 检查QueryOptimizer配置是否已注释
    if grep -q '^[[:space:]]*"QueryOptimizer":' "$config_file"; then
        record_check "FAIL" "废弃配置检查: QueryOptimizer配置仍然存在（未注释）"
    elif grep -q '// "QueryOptimizer":' "$config_file"; then
        record_check "PASS" "废弃配置检查: QueryOptimizer配置已正确注释"
    else
        record_check "PASS" "废弃配置检查: QueryOptimizer配置已移除"
    fi
}

# 检查重要配置是否保留
check_important_configs() {
    local config_file="src/HappyWechat.Web/appsettings.json"
    
    log_info "检查重要配置是否保留..."
    
    local important_configs=(
        "RedisQueue"
        "Notification"
        "Monitoring"
        "CircuitBreaker"
        "Resilience"
        "Database"
        "Redis"
        "EYun"
        "FileStorage"
        "SystemInfo"
        "MediaProcessing"
    )
    
    for config in "${important_configs[@]}"; do
        if grep -q "\"$config\":" "$config_file"; then
            record_check "PASS" "重要配置检查: $config 配置已保留"
        else
            record_check "FAIL" "重要配置检查: $config 配置缺失"
        fi
    done
}

# 检查EnvironmentVariableConfigurationProvider
check_env_provider() {
    local provider_file="src/HappyWechat.Infrastructure/Configuration/EnvironmentVariableConfigurationProvider.cs"
    
    log_info "检查EnvironmentVariableConfigurationProvider..."
    
    # 检查新增的方法是否存在
    if grep -q "GetRedisDbConfig" "$provider_file"; then
        record_check "PASS" "环境变量提供者: GetRedisDbConfig方法已添加"
    else
        record_check "FAIL" "环境变量提供者: GetRedisDbConfig方法缺失"
    fi
    
    if grep -q "GetMinIOConfig" "$provider_file"; then
        record_check "PASS" "环境变量提供者: GetMinIOConfig方法已添加"
    else
        record_check "FAIL" "环境变量提供者: GetMinIOConfig方法缺失"
    fi
    
    # 检查配置模型类是否存在
    if grep -q "class RedisDbConfig" "$provider_file"; then
        record_check "PASS" "环境变量提供者: RedisDbConfig类已添加"
    else
        record_check "FAIL" "环境变量提供者: RedisDbConfig类缺失"
    fi
    
    if grep -q "class MinIOConfig" "$provider_file"; then
        record_check "PASS" "环境变量提供者: MinIOConfig类已添加"
    else
        record_check "FAIL" "环境变量提供者: MinIOConfig类缺失"
    fi
}

# 主函数
main() {
    log_info "开始配置验证..."
    echo "========================================"
    
    # 1. 检查文件存在性
    log_info "1. 检查关键文件..."
    check_file_exists "src/HappyWechat.Web/appsettings.json" "配置文件"
    check_file_exists "docker-compose.yml" "Docker Compose文件"
    check_file_exists "Dockerfile" "Dockerfile"
    check_file_exists "entrypoint.sh" "启动脚本"
    check_file_exists "src/HappyWechat.Infrastructure/Configuration/EnvironmentVariableConfigurationProvider.cs" "环境变量配置提供者"
    
    echo ""
    
    # 2. 检查文件语法
    log_info "2. 检查文件语法..."
    check_json_syntax "src/HappyWechat.Web/appsettings.json"
    check_docker_compose_syntax "docker-compose.yml"
    
    echo ""
    
    # 3. 检查环境变量映射
    check_env_var_mapping
    
    echo ""
    
    # 4. 检查废弃配置
    check_deprecated_configs
    
    echo ""
    
    # 5. 检查重要配置
    check_important_configs
    
    echo ""
    
    # 6. 检查环境变量提供者
    check_env_provider
    
    echo ""
    echo "========================================"
    log_info "验证完成"
    
    # 显示统计结果
    echo ""
    log_info "验证统计:"
    echo "  总检查项: $TOTAL_CHECKS"
    echo "  通过: $PASSED_CHECKS"
    echo "  失败: $FAILED_CHECKS"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "所有检查都通过！配置优化成功。"
        exit 0
    else
        log_error "有 $FAILED_CHECKS 项检查失败，请检查上述错误。"
        exit 1
    fi
}

# 执行主函数
main "$@"
