using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 高性能数据库服务接口
/// 提供批量操作、缓存和优化查询功能
/// 替代池化DbContext方案，避免生命周期冲突
/// </summary>
public interface IHighPerformanceDbService
{
    /// <summary>
    /// 执行批量查询操作
    /// </summary>
    Task<TResult> ExecuteBatchQueryAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> query);

    /// <summary>
    /// 执行批量命令操作
    /// </summary>
    Task<TResult> ExecuteBatchCommandAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> command);

    /// <summary>
    /// 执行事务操作
    /// </summary>
    Task<TResult> ExecuteTransactionAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> operation);

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    HighPerformanceDbStatistics GetStatistics();
}

/// <summary>
/// 高性能数据库统计信息
/// </summary>
public class HighPerformanceDbStatistics
{
    public long TotalOperations { get; set; }
    public long QueryOperations { get; set; }
    public long CommandOperations { get; set; }
    public long TransactionOperations { get; set; }
    public double AverageExecutionTimeMs { get; set; }
    public DateTime LastStatisticsUpdate { get; set; }
}