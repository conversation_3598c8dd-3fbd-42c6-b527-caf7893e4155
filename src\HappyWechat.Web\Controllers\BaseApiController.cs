using HappyWechat.Application.Commons;
using HappyWechat.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace HappyWechat.Web.Controllers;

/// <summary>
/// API控制器基类
/// 提供统一的用户上下文访问和通用API响应处理
/// </summary>
[ApiController]
[Route("api/[controller]")]
public abstract class BaseApiController : ControllerBase
{
    protected readonly ICurrentUserContext UserContext;
    protected readonly ILogger Logger;

    protected BaseApiController(ICurrentUserContext userContext, ILogger logger)
    {
        UserContext = userContext;
        Logger = logger;
    }

    /// <summary>
    /// 当前用户ID（快捷属性）
    /// </summary>
    protected Guid CurrentUserId => UserContext.UserId;

    /// <summary>
    /// 当前用户名（快捷属性）
    /// </summary>
    protected string? CurrentUsername => UserContext.Username;

    /// <summary>
    /// 当前用户显示名称（快捷属性）
    /// </summary>
    protected string? CurrentUserDisplayName => UserContext.DisplayName;

    /// <summary>
    /// 当前用户是否已认证（快捷属性）
    /// </summary>
    protected bool IsUserAuthenticated => UserContext.IsAuthenticated;

    /// <summary>
    /// 确保用户已认证，返回标准化的未认证响应
    /// </summary>
    /// <returns>如果未认证返回401响应，否则返回null</returns>
    protected ActionResult<ApiResponse<T>>? EnsureAuthenticated<T>()
    {
        if (!UserContext.IsAuthenticated)
        {
            Logger.LogWarning("用户未认证访问受保护资源 - Controller: {Controller}, Action: {Action}", 
                ControllerContext.ActionDescriptor.ControllerName, 
                ControllerContext.ActionDescriptor.ActionName);
            
            return Unauthorized(ApiResponse<T>.Failure("用户未认证，请先登录", 401));
        }

        if (UserContext.UserId == Guid.Empty)
        {
            Logger.LogWarning("用户已认证但用户ID无效 - Controller: {Controller}, Action: {Action}, Username: {Username}", 
                ControllerContext.ActionDescriptor.ControllerName, 
                ControllerContext.ActionDescriptor.ActionName,
                UserContext.Username);
            
            return BadRequest(ApiResponse<T>.Failure("用户身份信息异常，请重新登录", 400));
        }

        return null;
    }

    /// <summary>
    /// 确保用户有指定角色，返回标准化的权限不足响应
    /// </summary>
    /// <param name="role">所需角色</param>
    /// <typeparam name="T">响应数据类型</typeparam>
    /// <returns>如果权限不足返回403响应，否则返回null</returns>
    protected ActionResult<ApiResponse<T>>? EnsureRole<T>(string role)
    {
        var authResult = EnsureAuthenticated<T>();
        if (authResult != null)
            return authResult;

        if (!UserContext.HasRole(role))
        {
            Logger.LogWarning("用户 {UserId} 没有角色 {Role} - Controller: {Controller}, Action: {Action}", 
                UserContext.UserId, role,
                ControllerContext.ActionDescriptor.ControllerName, 
                ControllerContext.ActionDescriptor.ActionName);
            
            return Forbid(ApiResponse<T>.Failure($"用户没有 '{role}' 角色权限", 403).ToString() ?? "权限不足");
        }

        return null;
    }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="message">响应消息</param>
    /// <typeparam name="T">数据类型</typeparam>
    /// <returns>成功响应</returns>
    protected ActionResult<ApiResponse<T>> Success<T>(T data, string? message = null)
    {
        return Ok(ApiResponse<T>.Success(data, message));
    }

    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="code">错误代码</param>
    /// <typeparam name="T">数据类型</typeparam>
    /// <returns>失败响应</returns>
    protected ActionResult<ApiResponse<T>> Failure<T>(string message, int code = 400)
    {
        return BadRequest(ApiResponse<T>.Failure(message, code));
    }

    /// <summary>
    /// 处理异常并返回标准化响应
    /// </summary>
    /// <param name="ex">异常</param>
    /// <param name="defaultMessage">默认错误消息</param>
    /// <typeparam name="T">数据类型</typeparam>
    /// <returns>错误响应</returns>
    protected ActionResult<ApiResponse<T>> HandleException<T>(Exception ex, string defaultMessage = "操作失败")
    {
        Logger.LogError(ex, "API操作异常 - Controller: {Controller}, Action: {Action}, UserId: {UserId}", 
            ControllerContext.ActionDescriptor.ControllerName, 
            ControllerContext.ActionDescriptor.ActionName,
            UserContext.UserId);

        // 根据异常类型返回不同的响应
        return ex switch
        {
            UnauthorizedAccessException => Unauthorized(ApiResponse<T>.Failure("访问被拒绝", 401)),
            ArgumentException argEx => BadRequest(ApiResponse<T>.Failure(argEx.Message, 400)),
            InvalidOperationException opEx => BadRequest(ApiResponse<T>.Failure(opEx.Message, 400)),
            _ => StatusCode(500, ApiResponse<T>.Failure(defaultMessage, 500))
        };
    }

    /// <summary>
    /// 记录API操作日志
    /// </summary>
    /// <param name="action">操作名称</param>
    /// <param name="details">操作详情</param>
    protected void LogApiOperation(string action, object? details = null)
    {
        Logger.LogInformation("API操作 - Controller: {Controller}, Action: {ApiAction}, UserId: {UserId}, Username: {Username}, Details: {Details}", 
            ControllerContext.ActionDescriptor.ControllerName,
            action,
            UserContext.UserId, 
            UserContext.Username,
            details?.ToString());
    }
}