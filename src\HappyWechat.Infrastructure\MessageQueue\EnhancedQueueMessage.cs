namespace HappyWechat.Infrastructure.MessageQueue;

/// <summary>
/// 增强队列消息结构 - 标准化消息格式，支持路由、重试、监控
/// </summary>
/// <typeparam name="T">业务数据类型</typeparam>
public class EnhancedQueueMessage<T>
{
    /// <summary>
    /// 主要路由ID - 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 冗余ID - 微信ID（便于调试和降级）
    /// </summary>
    public string? WcId { get; set; }

    /// <summary>
    /// 消息唯一标识
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString("N");

    /// <summary>
    /// 消息创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息处理优先级 (0-10, 数字越大优先级越高)
    /// </summary>
    public int Priority { get; set; } = 5;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息来源
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string? MessageType { get; set; }

    /// <summary>
    /// 业务数据
    /// </summary>
    public T Data { get; set; } = default!;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 处理历史记录
    /// </summary>
    public List<ProcessingHistory> ProcessingHistory { get; set; } = new();

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;

    /// <summary>
    /// 是否可以重试
    /// </summary>
    public bool CanRetry => RetryCount < MaxRetryCount && !IsExpired;

    /// <summary>
    /// 记录处理历史
    /// </summary>
    /// <param name="processor">处理器名称</param>
    /// <param name="status">处理状态</param>
    /// <param name="message">处理消息</param>
    /// <param name="duration">处理耗时</param>
    public void RecordProcessing(string processor, ProcessingStatus status, string? message = null, TimeSpan? duration = null)
    {
        ProcessingHistory.Add(new ProcessingHistory
        {
            Processor = processor,
            Status = status,
            Message = message,
            Duration = duration,
            ProcessedAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 增加重试次数
    /// </summary>
    public void IncrementRetry()
    {
        RetryCount++;
    }

    /// <summary>
    /// 克隆消息（用于重试）
    /// </summary>
    public EnhancedQueueMessage<T> Clone()
    {
        return new EnhancedQueueMessage<T>
        {
            WxManagerId = WxManagerId,
            WcId = WcId,
            MessageId = MessageId,
            CreatedAt = CreatedAt,
            Priority = Priority,
            RetryCount = RetryCount,
            MaxRetryCount = MaxRetryCount,
            ExpiresAt = ExpiresAt,
            Source = Source,
            MessageType = MessageType,
            Data = Data,
            Properties = new Dictionary<string, object>(Properties),
            ProcessingHistory = new List<ProcessingHistory>(ProcessingHistory)
        };
    }
}

/// <summary>
/// 处理历史记录
/// </summary>
public class ProcessingHistory
{
    /// <summary>
    /// 处理器名称
    /// </summary>
    public string Processor { get; set; } = "";

    /// <summary>
    /// 处理状态
    /// </summary>
    public ProcessingStatus Status { get; set; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 处理耗时
    /// </summary>
    public TimeSpan? Duration { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 处理状态枚举
/// </summary>
public enum ProcessingStatus
{
    /// <summary>
    /// 处理开始
    /// </summary>
    Started,

    /// <summary>
    /// 处理成功
    /// </summary>
    Succeeded,

    /// <summary>
    /// 处理失败
    /// </summary>
    Failed,

    /// <summary>
    /// 处理跳过
    /// </summary>
    Skipped,

    /// <summary>
    /// 等待重试
    /// </summary>
    WaitingRetry,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired
}

/// <summary>
/// 队列消息构建器
/// </summary>
public static class EnhancedQueueMessageBuilder
{
    /// <summary>
    /// 创建消息构建器
    /// </summary>
    public static EnhancedQueueMessageBuilder<T> Create<T>(T data)
    {
        return new EnhancedQueueMessageBuilder<T>(data);
    }
}

/// <summary>
/// 队列消息构建器
/// </summary>
public class EnhancedQueueMessageBuilder<T>
{
    private readonly EnhancedQueueMessage<T> _message;

    internal EnhancedQueueMessageBuilder(T data)
    {
        _message = new EnhancedQueueMessage<T>
        {
            Data = data
        };
    }

    public EnhancedQueueMessageBuilder<T> WithWxManagerId(Guid wxManagerId)
    {
        _message.WxManagerId = wxManagerId;
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithWcId(string? wcId)
    {
        _message.WcId = wcId;
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithPriority(int priority)
    {
        _message.Priority = Math.Max(0, Math.Min(10, priority));
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithMaxRetryCount(int maxRetryCount)
    {
        _message.MaxRetryCount = Math.Max(0, maxRetryCount);
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithExpiration(TimeSpan expiration)
    {
        _message.ExpiresAt = DateTime.UtcNow.Add(expiration);
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithSource(string source)
    {
        _message.Source = source;
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithMessageType(string messageType)
    {
        _message.MessageType = messageType;
        return this;
    }

    public EnhancedQueueMessageBuilder<T> WithProperty(string key, object value)
    {
        _message.Properties[key] = value;
        return this;
    }

    public EnhancedQueueMessage<T> Build()
    {
        if (_message.WxManagerId == Guid.Empty)
        {
            throw new InvalidOperationException("WxManagerId不能为空");
        }

        return _message;
    }
}