using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests;
using HappyWechat.Infrastructure.AiProvider;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.DTOs.MessageQueue;
using HappyWechat.Infrastructure.Caching;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 微信消息服务实现 - 负责所有微信消息发送功能
/// </summary>
public class WxMessageService : IWxMessageService
{
    private readonly IEYunMessageWrapper _eYunMessageWrapper;
    private readonly IEYunConfigurationManager _eYunConfigurationManager;
    private readonly ILogger<WxMessageService> _logger;

    public WxMessageService(
        IEYunMessageWrapper eYunMessageWrapper,
        IEYunConfigurationManager eYunConfigurationManager,
        ILogger<WxMessageService> logger)
    {
        _eYunMessageWrapper = eYunMessageWrapper ?? throw new ArgumentNullException(nameof(eYunMessageWrapper));
        _eYunConfigurationManager = eYunConfigurationManager ?? throw new ArgumentNullException(nameof(eYunConfigurationManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }



    public async Task<ApiResponse<string>> SendTextMessageAsync(Guid userId, WxSendTextMessageCommand message)
    {
        try
        {
            _logger.LogInformation("发送文本消息 - WId: {WId}, WcId: {WcId}, Content: {Content}",
                message.WId, message.WcId,
                message.Content?.Length > 50 ? message.Content[..50] + "..." : message.Content);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(message.Content))
            {
                return ApiResponse<string>.Failure("消息内容不能为空");
            }

            // 🔧 修复：验证WId并提供详细错误信息
            if (string.IsNullOrEmpty(message.WId))
            {
                _logger.LogError("发送文本消息失败 - WId为空 - WcId: {WcId}, Content: {Content}",
                    message.WcId, message.Content?.Substring(0, Math.Min(50, message.Content?.Length ?? 0)));
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录。如果问题持续，请检查账号状态。");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendTextMessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Content = message.Content
            };

            // 调用EYun API发送消息
            var result = await _eYunMessageWrapper.SendTextMessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("文本消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "消息发送成功");
            }
            else
            {
                _logger.LogWarning("文本消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送文本消息异常 - WId: {WId}, WcId: {WcId}", 
                message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendImageMessageAsync(Guid userId, WxSendImageMessageCommand message)
    {
        try
        {
            _logger.LogInformation("发送图片消息 - UserId: {UserId}, WId: {WId}, WcId: {WcId}, ImagePath: {ImagePath}",
                userId, message.WId, message.WcId, message.ImagePath);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(message.ImagePath))
            {
                return ApiResponse<string>.Failure("图片路径不能为空");
            }

            // 🔧 简化：直接验证WId
            if (string.IsNullOrEmpty(message.WId))
            {
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送图片消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendImageMessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Path = message.ImagePath
            };

            // 调用EYun API发送图片消息
            var result = await _eYunMessageWrapper.SendImageMessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("图片消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "图片消息发送成功");
            }
            else
            {
                _logger.LogWarning("图片消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("图片消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送图片消息异常 - UserId: {UserId}, WId: {WId}, WcId: {WcId}", 
                userId, message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送图片消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendFileMessageAsync(Guid userId, WxSendFileMessageCommand message)
    {
        try
        {
            _logger.LogInformation("发送文件消息 - UserId: {UserId}, WId: {WId}, WcId: {WcId}, FilePath: {FilePath}",
                userId, message.WId, message.WcId, message.FilePath);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(message.FilePath))
            {
                return ApiResponse<string>.Failure("文件路径不能为空");
            }

            // 🔧 简化：直接验证WId
            if (string.IsNullOrEmpty(message.WId))
            {
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送文件消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendFileMessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Path = message.FilePath,
                FileName = System.IO.Path.GetFileName(message.FilePath) ?? "file"
            };

            // 调用EYun API发送文件消息
            var result = await _eYunMessageWrapper.SendFileMessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("文件消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "文件消息发送成功");
            }
            else
            {
                _logger.LogWarning("文件消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("文件消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送文件消息异常 - UserId: {UserId}, WId: {WId}, WcId: {WcId}",
                userId, message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送文件消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendVideoMessageAsync(Guid userId, WxSendVideoMessageCommand message)
    {
        try
        {
            _logger.LogInformation("发送视频消息 - UserId: {UserId}, WId: {WId}, WcId: {WcId}, Path: {Path}",
                userId, message.WId, message.WcId, message.Path);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(message.Path))
            {
                return ApiResponse<string>.Failure("视频路径不能为空");
            }

            // 🔧 简化：直接验证WId
            if (string.IsNullOrEmpty(message.WId))
            {
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送视频消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendVideoMessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Path = message.Path,
                ThumbPath = message.ThumbPath ?? "" // 视频缩略图路径
            };

            // 调用EYun API发送视频消息
            var result = await _eYunMessageWrapper.SendVideoMessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("视频消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "视频消息发送成功");
            }
            else
            {
                _logger.LogWarning("视频消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("视频消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送视频消息异常 - UserId: {UserId}, WId: {WId}, WcId: {WcId}",
                userId, message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送视频消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendVoiceMessageAsync(Guid userId, WxSendVoiceMessageCommand message)
    {
        try
        {
            var voicePath = message.VoiceFilePath ?? message.Content;
            _logger.LogInformation("发送语音消息 - UserId: {UserId}, WId: {WId}, WcId: {WcId}, VoicePath: {VoicePath}",
                userId, message.WId, message.WcId, voicePath);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(voicePath))
            {
                return ApiResponse<string>.Failure("语音路径不能为空");
            }

            // 🔧 简化：直接验证WId
            if (string.IsNullOrEmpty(message.WId))
            {
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送语音消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendVoiceMessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Path = voicePath,
                Length = message.Length
            };

            // 调用EYun API发送语音消息
            var result = await _eYunMessageWrapper.SendVoiceMessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("语音消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "语音消息发送成功");
            }
            else
            {
                _logger.LogWarning("语音消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("语音消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送语音消息异常 - UserId: {UserId}, WId: {WId}, WcId: {WcId}",
                userId, message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送语音消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendFileBase64MessageAsync(Guid userId, WxSendFileBase64MessageCommand message)
    {
        try
        {
            _logger.LogInformation("发送Base64文件消息 - UserId: {UserId}, WId: {WId}, WcId: {WcId}, FileName: {FileName}",
                userId, message.WId, message.WcId, message.FileName);

            // 验证基本参数
            if (string.IsNullOrEmpty(message.WcId))
            {
                return ApiResponse<string>.Failure("WcId不能为空");
            }

            if (string.IsNullOrEmpty(message.Base64))
            {
                return ApiResponse<string>.Failure("文件Base64内容不能为空");
            }

            if (string.IsNullOrEmpty(message.FileName))
            {
                return ApiResponse<string>.Failure("文件名不能为空");
            }

            // 🔧 简化：直接验证WId
            if (string.IsNullOrEmpty(message.WId))
            {
                return ApiResponse<string>.Failure("WId不能为空，请确保微信账号已登录");
            }

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送Base64文件消息");
                return ApiResponse<string>.Failure("EYun Token未配置，请先在系统配置中获取Token");
            }

            // 构建EYun请求
            var eYunRequest = new EYunSendFileBase64MessageRequest
            {
                WId = message.WId,
                WcId = message.WcId,
                Base64 = message.Base64,
                FileName = message.FileName
            };

            // 调用EYun API发送Base64文件消息
            var result = await _eYunMessageWrapper.SendFileBase64MessageAsync(eYunRequest);

            if (result != null && result.MsgId > 0)
            {
                _logger.LogInformation("Base64文件消息发送成功 - MsgId: {MsgId}", result.MsgId);
                return ApiResponse<string>.Success(result.MsgId.ToString(), "Base64文件消息发送成功");
            }
            else
            {
                _logger.LogWarning("Base64文件消息发送失败 - 未返回MessageId");
                return ApiResponse<string>.Failure("Base64文件消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送Base64文件消息异常 - UserId: {UserId}, WId: {WId}, WcId: {WcId}",
                userId, message.WId, message.WcId);
            return ApiResponse<string>.Failure($"发送Base64文件消息异常: {ex.Message}");
        }
    }

    public async Task SendDifferentWxMessageByCoZePlainResult(string wId, string wcId, CoZeParser.ParseResult coZeResult)
    {
        try
        {
            _logger.LogInformation("根据CoZe解析结果发送消息 - WId: {WId}, WcId: {WcId}, MessageType: {MessageType}",
                wId, wcId, coZeResult.MessageType);

            // 获取EYun配置
            var config = await _eYunConfigurationManager.GetCurrentConfigAsync();
            if (string.IsNullOrEmpty(config.Token))
            {
                _logger.LogWarning("EYun Token未配置，无法发送CoZe解析消息");
                return;
            }

            // 发送文本消息
            if (!string.IsNullOrEmpty(coZeResult.Text))
            {
                var textRequest = new EYunSendTextMessageRequest
                {
                    WId = wId,
                    WcId = wcId,
                    Content = coZeResult.Text
                };
                await _eYunMessageWrapper.SendTextMessageAsync(textRequest);
                _logger.LogInformation("CoZe文本消息发送成功");
            }

            // 发送图片消息
            foreach (var imageUrl in coZeResult.ImageUrls)
            {
                var imageRequest = new EYunSendImageMessageRequest
                {
                    WId = wId,
                    WcId = wcId,
                    Path = imageUrl
                };
                await _eYunMessageWrapper.SendImageMessageAsync(imageRequest);
                _logger.LogInformation("CoZe图片消息发送成功 - ImageUrl: {ImageUrl}", imageUrl);
            }

            // 发送文件消息
            foreach (var fileUrl in coZeResult.FileUrls)
            {
                var fileRequest = new EYunSendFileMessageRequest
                {
                    WId = wId,
                    WcId = wcId,
                    Path = fileUrl,
                    FileName = System.IO.Path.GetFileName(fileUrl) ?? "file"
                };
                await _eYunMessageWrapper.SendFileMessageAsync(fileRequest);
                _logger.LogInformation("CoZe文件消息发送成功 - FileUrl: {FileUrl}", fileUrl);
            }

            // 发送视频消息
            foreach (var videoUrl in coZeResult.VideoUrls)
            {
                var videoRequest = new EYunSendVideoMessageRequest
                {
                    WId = wId,
                    WcId = wcId,
                    Path = videoUrl,
                    ThumbPath = "" // 视频缩略图路径，暂时为空
                };
                await _eYunMessageWrapper.SendVideoMessageAsync(videoRequest);
                _logger.LogInformation("CoZe视频消息发送成功 - VideoUrl: {VideoUrl}", videoUrl);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送CoZe解析消息异常 - WId: {WId}, WcId: {WcId}", wId, wcId);
        }
    }

    public async Task<ApiResponse<string>> SendMixedContentMessageAsync(Guid userId, MixedContentMessage mixedMessage)
    {
        try
        {
            _logger.LogInformation("发送混合内容消息 - UserId: {UserId}, MessageId: {MessageId}, ItemCount: {ItemCount}",
                userId, mixedMessage.MessageId, mixedMessage.TotalCount);

            var results = new List<string>();
            var failedCount = 0;

            foreach (var messageItem in mixedMessage.MessageItems.OrderBy(m => m.Order))
            {
                try
                {
                    var result = await SendMessageItemAsync(userId, messageItem);
                    if (result.IsSuccess)
                    {
                        results.Add(result.Data ?? "");
                        messageItem.Status = MessageQueueStatus.Sent;
                    }
                    else
                    {
                        failedCount++;
                        messageItem.Status = MessageQueueStatus.Failed;
                        messageItem.ErrorMessage = result.Message;
                        _logger.LogWarning("混合消息项发送失败 - MessageId: {MessageId}, Error: {Error}",
                            messageItem.MessageId, result.Message);
                    }

                    // 消息间隔
                    if (mixedMessage.IntervalMilliseconds > 0)
                    {
                        await Task.Delay(mixedMessage.IntervalMilliseconds);
                    }
                }
                catch (Exception ex)
                {
                    failedCount++;
                    messageItem.Status = MessageQueueStatus.Failed;
                    messageItem.ErrorMessage = ex.Message;
                    _logger.LogError(ex, "混合消息项发送异常 - MessageId: {MessageId}", messageItem.MessageId);
                }
            }

            mixedMessage.CompletedTime = DateTime.UtcNow;

            if (failedCount == 0)
            {
                return ApiResponse<string>.Success(string.Join(",", results), "混合内容消息发送成功");
            }
            else if (failedCount < mixedMessage.TotalCount)
            {
                return ApiResponse<string>.Success(string.Join(",", results),
                    $"混合内容消息部分发送成功，{mixedMessage.TotalCount - failedCount}/{mixedMessage.TotalCount}条成功");
            }
            else
            {
                return ApiResponse<string>.Failure("混合内容消息发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送混合内容消息异常 - UserId: {UserId}, MessageId: {MessageId}",
                userId, mixedMessage.MessageId);
            return ApiResponse<string>.Failure($"发送混合内容消息异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendMessageItemAsync(Guid userId, MessageQueueItem messageItem)
    {
        try
        {
            _logger.LogInformation("发送消息队列项 - UserId: {UserId}, MessageId: {MessageId}, Type: {Type}",
                userId, messageItem.MessageId, messageItem.MessageType);

            messageItem.Status = MessageQueueStatus.Sending;

            switch (messageItem.MessageType)
            {
                case MessageContentType.Text:
                    var textCommand = new WxSendTextMessageCommand
                    {
                        WId = messageItem.WId,
                        WcId = messageItem.ToUser,
                        Content = messageItem.Content
                    };
                    return await SendTextMessageAsync(userId, textCommand);

                case MessageContentType.Image:
                    var imageCommand = new WxSendImageMessageCommand
                    {
                        WId = messageItem.WId,
                        WcId = messageItem.ToUser,
                        ImagePath = messageItem.FilePath ?? messageItem.FileUrl ?? ""
                    };
                    return await SendImageMessageAsync(userId, imageCommand);

                case MessageContentType.File:
                    var fileCommand = new WxSendFileMessageCommand
                    {
                        WId = messageItem.WId,
                        WcId = messageItem.ToUser,
                        FilePath = messageItem.FilePath ?? messageItem.FileUrl ?? ""
                    };
                    return await SendFileMessageAsync(userId, fileCommand);

                case MessageContentType.Video:
                    var videoCommand = new WxSendVideoMessageCommand
                    {
                        WId = messageItem.WId,
                        WcId = messageItem.ToUser,
                        Path = messageItem.FilePath ?? messageItem.FileUrl ?? "",
                        ThumbPath = "", // 缩略图路径，暂时为空
                        VideoFilePath = messageItem.FilePath
                    };
                    return await SendVideoMessageAsync(userId, videoCommand);

                case MessageContentType.Voice:
                    var voiceCommand = new WxSendVoiceMessageCommand
                    {
                        WId = messageItem.WId,
                        WcId = messageItem.ToUser,
                        VoiceFilePath = messageItem.FilePath ?? messageItem.FileUrl ?? "",
                        Content = messageItem.Content ?? "",
                        Length = 0 // 默认时长，可以从messageItem中获取
                    };
                    return await SendVoiceMessageAsync(userId, voiceCommand);

                default:
                    _logger.LogWarning("不支持的消息类型 - Type: {Type}", messageItem.MessageType);
                    return ApiResponse<string>.Failure($"不支持的消息类型: {messageItem.MessageType}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送消息队列项异常 - UserId: {UserId}, MessageId: {MessageId}",
                userId, messageItem.MessageId);
            messageItem.Status = MessageQueueStatus.Failed;
            messageItem.ErrorMessage = ex.Message;
            return ApiResponse<string>.Failure($"发送消息队列项异常: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> SendMessageAsync(Guid userId, MessageQueueItem messageItem)
    {
        // 这个方法与SendMessageItemAsync功能相同，为了兼容性保留
        return await SendMessageItemAsync(userId, messageItem);
    }
}
