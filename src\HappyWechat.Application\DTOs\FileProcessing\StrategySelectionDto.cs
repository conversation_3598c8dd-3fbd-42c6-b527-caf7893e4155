using HappyWechat.Application.DTOs.MessageProcess;

namespace HappyWechat.Application.DTOs.FileProcessing;

/// <summary>
/// 策略选择上下文
/// </summary>
public class StrategySelectionContext
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>
    public FileProcessingType ProcessingType { get; set; }
    
    /// <summary>
    /// 优先级
    /// </summary>
    public ProcessingPriority Priority { get; set; }
    
    /// <summary>
    /// 文件分析结果
    /// </summary>
    public FileAnalysisMetrics FileMetrics { get; set; } = new();
    
    /// <summary>
    /// 系统状态
    /// </summary>
    public SystemResourceStatus SystemStatus { get; set; } = new();
    
    /// <summary>
    /// 队列状态
    /// </summary>
    public QueueStatusMetrics QueueMetrics { get; set; } = new();
    
    /// <summary>
    /// 历史性能数据
    /// </summary>
    public HistoricalPerformanceData HistoricalData { get; set; } = new();
    
    /// <summary>
    /// 用户偏好设置
    /// </summary>
    public UserPreferences UserPreferences { get; set; } = new();
    
    /// <summary>
    /// 时间因素
    /// </summary>
    public TimeContext TimeContext { get; set; } = new();
}

/// <summary>
/// 文件分析指标
/// </summary>
public class FileAnalysisMetrics
{
    /// <summary>
    /// 文件总数
    /// </summary>
    public int TotalFileCount { get; set; }
    
    /// <summary>
    /// 总文件大小（字节）
    /// </summary>
    public long TotalFileSize { get; set; }
    
    /// <summary>
    /// 平均文件大小
    /// </summary>
    public long AverageFileSize { get; set; }
    
    /// <summary>
    /// 最大文件大小
    /// </summary>
    public long MaxFileSize { get; set; }
    
    /// <summary>
    /// 小文件数量
    /// </summary>
    public int SmallFileCount { get; set; }
    
    /// <summary>
    /// 中等文件数量
    /// </summary>
    public int MediumFileCount { get; set; }
    
    /// <summary>
    /// 大文件数量
    /// </summary>
    public int LargeFileCount { get; set; }
    
    /// <summary>
    /// 按文件类型分组的统计
    /// </summary>
    public Dictionary<NonTextMessageType, int> FileTypeDistribution { get; set; } = new();
    
    /// <summary>
    /// 估计的总处理时间（毫秒）
    /// </summary>
    public long EstimatedProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 复杂度评分（0-1）
    /// </summary>
    public double ComplexityScore { get; set; }
}

/// <summary>
/// 系统资源状态
/// </summary>
public class SystemResourceStatus
{
    /// <summary>
    /// CPU使用率（0-1）
    /// </summary>
    public double CpuUsage { get; set; }
    
    /// <summary>
    /// 内存使用率（0-1）
    /// </summary>
    public double MemoryUsage { get; set; }
    
    /// <summary>
    /// 磁盘使用率（0-1）
    /// </summary>
    public double DiskUsage { get; set; }
    
    /// <summary>
    /// 网络带宽使用率（0-1）
    /// </summary>
    public double NetworkUsage { get; set; }
    
    /// <summary>
    /// 当前活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }
    
    /// <summary>
    /// 当前并发任务数
    /// </summary>
    public int ConcurrentTasks { get; set; }
    
    /// <summary>
    /// 可用并发槽位
    /// </summary>
    public int AvailableConcurrencySlots { get; set; }
    
    /// <summary>
    /// 系统负载评分（0-1）
    /// </summary>
    public double LoadScore { get; set; }
}

/// <summary>
/// 队列状态指标
/// </summary>
public class QueueStatusMetrics
{
    /// <summary>
    /// 待处理任务数
    /// </summary>
    public int PendingTasks { get; set; }
    
    /// <summary>
    /// 平均等待时间（秒）
    /// </summary>
    public double AverageWaitTimeSeconds { get; set; }
    
    /// <summary>
    /// 队列深度
    /// </summary>
    public int QueueDepth { get; set; }
    
    /// <summary>
    /// 处理速率（任务/分钟）
    /// </summary>
    public double ProcessingRatePerMinute { get; set; }
    
    /// <summary>
    /// 队列健康状态
    /// </summary>
    public bool IsQueueHealthy { get; set; }
    
    /// <summary>
    /// 队列拥堵程度（0-1）
    /// </summary>
    public double CongestionLevel { get; set; }
}

/// <summary>
/// 历史性能数据
/// </summary>
public class HistoricalPerformanceData
{
    /// <summary>
    /// 过去1小时的策略成功率
    /// </summary>
    public Dictionary<ProcessingStrategy, double> StrategySuccessRates { get; set; } = new();
    
    /// <summary>
    /// 过去1小时的平均处理时间
    /// </summary>
    public Dictionary<ProcessingStrategy, double> AverageProcessingTimes { get; set; } = new();
    
    /// <summary>
    /// 类似请求的历史表现
    /// </summary>
    public List<HistoricalPerformanceRecord> SimilarRequests { get; set; } = new();
    
    /// <summary>
    /// 最近的错误模式
    /// </summary>
    public List<ErrorPattern> RecentErrorPatterns { get; set; } = new();
    
    /// <summary>
    /// 趋势分析
    /// </summary>
    public PerformanceTrend Trend { get; set; } = new();
}

/// <summary>
/// 历史性能记录
/// </summary>
public class HistoricalPerformanceRecord
{
    /// <summary>
    /// 请求特征
    /// </summary>
    public FileAnalysisMetrics RequestCharacteristics { get; set; } = new();
    
    /// <summary>
    /// 使用的策略
    /// </summary>
    public ProcessingStrategy UsedStrategy { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public long ProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 发生时间
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 相似度评分（0-1）
    /// </summary>
    public double SimilarityScore { get; set; }
}

/// <summary>
/// 错误模式
/// </summary>
public class ErrorPattern
{
    /// <summary>
    /// 错误类型
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;
    
    /// <summary>
    /// 关联的策略
    /// </summary>
    public ProcessingStrategy Strategy { get; set; }
    
    /// <summary>
    /// 发生频率
    /// </summary>
    public int Frequency { get; set; }
    
    /// <summary>
    /// 最近发生时间
    /// </summary>
    public DateTime LastOccurrence { get; set; }
    
    /// <summary>
    /// 条件模式
    /// </summary>
    public Dictionary<string, object> ConditionPattern { get; set; } = new();
}

/// <summary>
/// 性能趋势
/// </summary>
public class PerformanceTrend
{
    /// <summary>
    /// 整体性能趋势方向
    /// </summary>
    public TrendDirection Direction { get; set; }
    
    /// <summary>
    /// 趋势强度（0-1）
    /// </summary>
    public double Strength { get; set; }
    
    /// <summary>
    /// 各策略的趋势
    /// </summary>
    public Dictionary<ProcessingStrategy, TrendDirection> StrategyTrends { get; set; } = new();
    
    /// <summary>
    /// 预测信心度（0-1）
    /// </summary>
    public double Confidence { get; set; }
}

/// <summary>
/// 用户偏好设置
/// </summary>
public class UserPreferences
{
    /// <summary>
    /// 偏好的处理策略
    /// </summary>
    public ProcessingStrategy? PreferredStrategy { get; set; }
    
    /// <summary>
    /// 性能vs成本权衡（0-1，0=偏向成本，1=偏向性能）
    /// </summary>
    public double PerformanceCostTradeoff { get; set; } = 0.5;
    
    /// <summary>
    /// 可靠性要求（0-1）
    /// </summary>
    public double ReliabilityRequirement { get; set; } = 0.8;
    
    /// <summary>
    /// 响应时间要求（毫秒）
    /// </summary>
    public long MaxAcceptableResponseTimeMs { get; set; } = 30000;
    
    /// <summary>
    /// 是否允许队列处理
    /// </summary>
    public bool AllowQueueProcessing { get; set; } = true;
    
    /// <summary>
    /// 自动优化偏好
    /// </summary>
    public bool EnableAutoOptimization { get; set; } = true;
}

/// <summary>
/// 时间上下文
/// </summary>
public class TimeContext
{
    /// <summary>
    /// 当前时间
    /// </summary>
    public DateTime CurrentTime { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 是否为高峰时段
    /// </summary>
    public bool IsPeakHour { get; set; }
    
    /// <summary>
    /// 是否为工作时间
    /// </summary>
    public bool IsBusinessHour { get; set; }
    
    /// <summary>
    /// 时区信息
    /// </summary>
    public string TimeZone { get; set; } = "UTC";
    
    /// <summary>
    /// 预期的系统负载级别
    /// </summary>
    public LoadLevel ExpectedLoadLevel { get; set; }
}

/// <summary>
/// 策略选择结果
/// </summary>
public class StrategySelectionResult
{
    /// <summary>
    /// 推荐的策略
    /// </summary>
    public ProcessingStrategy RecommendedStrategy { get; set; }
    
    /// <summary>
    /// 信心度（0-1）
    /// </summary>
    public double Confidence { get; set; }
    
    /// <summary>
    /// 选择理由
    /// </summary>
    public List<string> Reasons { get; set; } = new();
    
    /// <summary>
    /// 备选策略（按优先级排序）
    /// </summary>
    public List<AlternativeStrategy> Alternatives { get; set; } = new();
    
    /// <summary>
    /// 预测的性能指标
    /// </summary>
    public PredictedPerformance PredictedPerformance { get; set; } = new();
    
    /// <summary>
    /// 风险评估
    /// </summary>
    public RiskAssessment RiskAssessment { get; set; } = new();
    
    /// <summary>
    /// 选择时间戳
    /// </summary>
    public DateTime SelectionTimestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 使用的算法版本
    /// </summary>
    public string AlgorithmVersion { get; set; } = "1.0";
}

/// <summary>
/// 备选策略
/// </summary>
public class AlternativeStrategy
{
    /// <summary>
    /// 策略
    /// </summary>
    public ProcessingStrategy Strategy { get; set; }
    
    /// <summary>
    /// 评分（0-1）
    /// </summary>
    public double Score { get; set; }
    
    /// <summary>
    /// 优势
    /// </summary>
    public List<string> Advantages { get; set; } = new();
    
    /// <summary>
    /// 劣势
    /// </summary>
    public List<string> Disadvantages { get; set; } = new();
}

/// <summary>
/// 预测性能
/// </summary>
public class PredictedPerformance
{
    /// <summary>
    /// 预计处理时间（毫秒）
    /// </summary>
    public long EstimatedProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 预计成功率
    /// </summary>
    public double EstimatedSuccessRate { get; set; }
    
    /// <summary>
    /// 预计资源消耗
    /// </summary>
    public ResourceConsumption EstimatedResourceUsage { get; set; } = new();
    
    /// <summary>
    /// 预测区间
    /// </summary>
    public PerformanceRange Range { get; set; } = new();
}

/// <summary>
/// 资源消耗
/// </summary>
public class ResourceConsumption
{
    /// <summary>
    /// CPU消耗
    /// </summary>
    public double CpuConsumption { get; set; }
    
    /// <summary>
    /// 内存消耗（字节）
    /// </summary>
    public long MemoryConsumption { get; set; }
    
    /// <summary>
    /// 网络带宽消耗（字节）
    /// </summary>
    public long NetworkConsumption { get; set; }
    
    /// <summary>
    /// 并发槽位消耗
    /// </summary>
    public int ConcurrencySlots { get; set; }
}

/// <summary>
/// 性能范围
/// </summary>
public class PerformanceRange
{
    /// <summary>
    /// 最佳情况处理时间
    /// </summary>
    public long BestCaseTimeMs { get; set; }
    
    /// <summary>
    /// 最坏情况处理时间
    /// </summary>
    public long WorstCaseTimeMs { get; set; }
    
    /// <summary>
    /// 95%置信区间下限
    /// </summary>
    public long P95LowerBoundMs { get; set; }
    
    /// <summary>
    /// 95%置信区间上限
    /// </summary>
    public long P95UpperBoundMs { get; set; }
}

/// <summary>
/// 风险评估
/// </summary>
public class RiskAssessment
{
    /// <summary>
    /// 整体风险级别
    /// </summary>
    public RiskLevel OverallRisk { get; set; }
    
    /// <summary>
    /// 具体风险项
    /// </summary>
    public List<RiskFactor> RiskFactors { get; set; } = new();
    
    /// <summary>
    /// 缓解建议
    /// </summary>
    public List<string> MitigationSuggestions { get; set; } = new();
}

/// <summary>
/// 风险因子
/// </summary>
public class RiskFactor
{
    /// <summary>
    /// 风险类型
    /// </summary>
    public string RiskType { get; set; } = string.Empty;
    
    /// <summary>
    /// 风险级别
    /// </summary>
    public RiskLevel Level { get; set; }
    
    /// <summary>
    /// 影响程度（0-1）
    /// </summary>
    public double Impact { get; set; }
    
    /// <summary>
    /// 发生概率（0-1）
    /// </summary>
    public double Probability { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

#region Enums

/// <summary>
/// 趋势方向
/// </summary>
public enum TrendDirection
{
    /// <summary>
    /// 改善
    /// </summary>
    Improving = 1,
    
    /// <summary>
    /// 稳定
    /// </summary>
    Stable = 2,
    
    /// <summary>
    /// 恶化
    /// </summary>
    Deteriorating = 3,
    
    /// <summary>
    /// 未知
    /// </summary>
    Unknown = 4
}

/// <summary>
/// 负载级别
/// </summary>
public enum LoadLevel
{
    /// <summary>
    /// 低负载
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// 中等负载
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// 高负载
    /// </summary>
    High = 3,
    
    /// <summary>
    /// 极高负载
    /// </summary>
    Critical = 4
}

/// <summary>
/// 风险级别
/// </summary>
public enum RiskLevel
{
    /// <summary>
    /// 低风险
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// 中等风险
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// 高风险
    /// </summary>
    High = 3,
    
    /// <summary>
    /// 极高风险
    /// </summary>
    Critical = 4
}

#endregion