using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageQueue.Unified;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace HappyWechat.Infrastructure.MessageProcessing.Unified;

/// <summary>
/// 媒体消息AI处理器
/// 专门处理媒体消息转AI的统一模板，携带完整的wid、wcid等参数
/// </summary>
public interface IMediaToAiProcessor
{
    /// <summary>
    /// 处理媒体消息并传递给AI
    /// </summary>
    Task<MediaToAiResult> ProcessMediaToAiAsync(UnifiedMediaRequest mediaRequest, CancellationToken cancellationToken = default);
}

public class MediaToAiProcessor : IMediaToAiProcessor
{
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly IUnifiedAiMessageTemplateService _aiMessageTemplateService;
    private readonly IUnifiedMessageQueue _unifiedMessageQueue;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MediaToAiProcessor> _logger;

    public MediaToAiProcessor(
        IUnifiedMediaProcessor mediaProcessor,
        IUnifiedAiMessageTemplateService aiMessageTemplateService,
        IUnifiedMessageQueue unifiedMessageQueue,
        IServiceProvider serviceProvider,
        ILogger<MediaToAiProcessor> logger)
    {
        _mediaProcessor = mediaProcessor;
        _aiMessageTemplateService = aiMessageTemplateService;
        _unifiedMessageQueue = unifiedMessageQueue;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 处理媒体消息并传递给AI
    /// </summary>
    public async Task<MediaToAiResult> ProcessMediaToAiAsync(UnifiedMediaRequest mediaRequest, CancellationToken cancellationToken = default)
    {
        var processingId = mediaRequest.ProcessingId;
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("[{ProcessingId}] 📁 开始媒体消息AI处理 - MessageType: {MessageType}, WxManagerId: {WxManagerId}",
                processingId, mediaRequest.CallbackMessage?.MessageType, mediaRequest.WxManagerId);

            if (mediaRequest.CallbackMessage == null)
            {
                return MediaToAiResult.CreateFailure(processingId, "回调消息为空");
            }

            // 1. 处理媒体文件
            var mediaResult = await _mediaProcessor.ProcessMediaMessageAsync(mediaRequest.CallbackMessage, cancellationToken);
            
            if (!mediaResult.Success)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 媒体处理失败 - Error: {Error}", processingId, mediaResult.ErrorMessage);
                return MediaToAiResult.CreateFailure(processingId, mediaResult.ErrorMessage ?? "媒体处理失败");
            }

            _logger.LogInformation("[{ProcessingId}] ✅ 媒体处理完成 - URL: {Url}", processingId, mediaResult.PublicUrl);

            // 2. 检查是否需要AI处理
            var shouldProcessAi = await ShouldProcessAiAsync(mediaRequest.CallbackMessage, processingId);
            
            if (!shouldProcessAi.ShouldProcess)
            {
                _logger.LogDebug("[{ProcessingId}] 媒体消息不需要AI处理 - 原因: {Reason}", processingId, shouldProcessAi.Reason);
                return MediaToAiResult.CreateSuccess(processingId, mediaResult, false, shouldProcessAi.Reason);
            }

            // 3. 构建AI消息模板
            var aiTemplate = await BuildAiTemplateAsync(mediaRequest.CallbackMessage, mediaResult, processingId);
            
            // 4. 提交到AI处理队列
            await _unifiedMessageQueue.EnqueueAiRequestAsync(
                mediaRequest.WxManagerId, 
                aiTemplate, 
                mediaRequest.CallbackMessage, 
                processingId, 
                cancellationToken);

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("[{ProcessingId}] ✅ 媒体消息AI处理完成 - Duration: {Duration}ms", 
                processingId, duration.TotalMilliseconds);

            return MediaToAiResult.CreateSuccess(processingId, mediaResult, true, "已提交AI处理");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "[{ProcessingId}] ❌ 媒体消息AI处理异常 - Duration: {Duration}ms", 
                processingId, duration.TotalMilliseconds);
            return MediaToAiResult.CreateFailure(processingId, $"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查是否需要AI处理
    /// </summary>
    private async Task<(bool ShouldProcess, string Reason)> ShouldProcessAiAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            var isGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup);

            if (isGroupMessage)
            {
                // 群聊消息：检查群聊AI触发条件
                var groupMessageProcessor = _serviceProvider.GetRequiredService<IEnhancedGroupMessageProcessor>();
                var groupResult = await groupMessageProcessor.CheckGroupMessageAiReplyTriggerAsync(callbackMessage);
                
                return (groupResult.ShouldReply, groupResult.Reason ?? "群聊AI检查");
            }
            else
            {
                // 私聊消息：检查联系人AI配置
                var contactAiChecker = _serviceProvider.GetRequiredService<IContactAiConfigChecker>();
                var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
                var fromUser = callbackMessage.Data?.FromUser ?? "";
                var aiConfig = await contactAiChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);

                return (aiConfig.IsAiConfigured && aiConfig.IsEnabled, aiConfig.ContactName ?? "私聊AI检查");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 检查AI处理条件异常", processingId);
            return (false, $"检查异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 构建AI消息模板
    /// </summary>
    private async Task<string> BuildAiTemplateAsync(WxCallbackMessageDto callbackMessage, MediaProcessingResult mediaResult, string processingId)
    {
        try
        {
            var isGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup);

            if (isGroupMessage)
            {
                // 群聊媒体消息模板
                var senderNickname = await GetSenderNicknameAsync(callbackMessage, processingId);
                return _aiMessageTemplateService.BuildGroupMediaMessageTemplate(callbackMessage, mediaResult, senderNickname);
            }
            else
            {
                // 私聊媒体消息模板
                return _aiMessageTemplateService.BuildMediaMessageTemplate(callbackMessage, mediaResult);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 构建AI模板异常", processingId);
            // 返回基础模板
            return _aiMessageTemplateService.BuildMediaMessageTemplate(callbackMessage, mediaResult);
        }
    }

    /// <summary>
    /// 获取发送者昵称
    /// </summary>
    private async Task<string> GetSenderNicknameAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            var contactNicknameService = _serviceProvider.GetRequiredService<IContactNicknameService>();
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            // 在群聊中，FromUser就是群聊中的发送者
            var senderWcId = callbackMessage.Data?.FromUser ?? "";
            
            var nickname = await contactNicknameService.GetContactNicknameAsync(wxManagerId, senderWcId);
            
            _logger.LogDebug("[{ProcessingId}] 获取发送者昵称成功 - WcId: {SenderWcId}, Nickname: {Nickname}", 
                processingId, senderWcId, nickname);
            
            return nickname;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[{ProcessingId}] 获取发送者昵称失败，使用默认值", processingId);
            return "未知用户";
        }
    }
}

/// <summary>
/// 媒体转AI处理结果
/// </summary>
public class MediaToAiResult
{
    public bool Success { get; set; }
    public string ProcessingId { get; set; } = string.Empty;
    public MediaProcessingResult? MediaResult { get; set; }
    public bool AiProcessed { get; set; }
    public string? Message { get; set; }
    public string? ErrorMessage { get; set; }

    public static MediaToAiResult CreateSuccess(string processingId, MediaProcessingResult mediaResult, bool aiProcessed, string message)
    {
        return new MediaToAiResult
        {
            Success = true,
            ProcessingId = processingId,
            MediaResult = mediaResult,
            AiProcessed = aiProcessed,
            Message = message
        };
    }

    public static MediaToAiResult CreateFailure(string processingId, string errorMessage)
    {
        return new MediaToAiResult
        {
            Success = false,
            ProcessingId = processingId,
            ErrorMessage = errorMessage
        };
    }
}
