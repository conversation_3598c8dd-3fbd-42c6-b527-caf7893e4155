#!/bin/bash

# HappyWechat Silk解码器存储卷初始化脚本
# 用于创建和初始化silk_v3_decoder存储卷

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[SILK-SETUP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
VOLUME_NAME="happywechat_silk_v3_decoder"
CONTAINER_NAME="happywechat_silk_setup_temp"
DOCKER_IMAGE="fusheng:v2.1"

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    log_success "Docker服务正常"
}

# 检查存储卷是否存在
check_volume_exists() {
    log_info "检查存储卷是否存在..."
    if docker volume inspect "$VOLUME_NAME" >/dev/null 2>&1; then
        log_success "存储卷 $VOLUME_NAME 已存在"
        return 0
    else
        log_info "存储卷 $VOLUME_NAME 不存在，将创建"
        return 1
    fi
}

# 创建存储卷
create_volume() {
    log_info "创建存储卷 $VOLUME_NAME..."
    if docker volume create "$VOLUME_NAME"; then
        log_success "存储卷创建成功"
    else
        log_error "存储卷创建失败"
        exit 1
    fi
}

# 初始化存储卷内容
initialize_volume() {
    log_info "初始化存储卷内容..."
    
    # 清理可能存在的临时容器
    docker rm -f "$CONTAINER_NAME" 2>/dev/null || true
    
    # 创建临时容器来初始化存储卷
    log_info "创建临时容器进行初始化..."
    docker run -d --name "$CONTAINER_NAME" \
        -v "$VOLUME_NAME:/opt/silk_v3_decoder" \
        "$DOCKER_IMAGE" sleep 3600
    
    # 等待容器启动
    sleep 2
    
    # 检查容器中的silk_v3_decoder
    log_info "检查容器中的silk_v3_decoder..."
    if docker exec "$CONTAINER_NAME" test -f /opt/silk_v3_decoder/bin/silk_v3_decoder; then
        log_success "silk_v3_decoder已存在于存储卷中"
    else
        log_warning "silk_v3_decoder不存在，尝试从系统路径复制..."
        
        # 尝试从系统路径复制
        if docker exec "$CONTAINER_NAME" test -f /usr/local/bin/silk_v3_decoder; then
            docker exec "$CONTAINER_NAME" mkdir -p /opt/silk_v3_decoder/bin
            docker exec "$CONTAINER_NAME" cp /usr/local/bin/silk_v3_decoder /opt/silk_v3_decoder/bin/
            docker exec "$CONTAINER_NAME" chmod +x /opt/silk_v3_decoder/bin/silk_v3_decoder
            log_success "silk_v3_decoder已复制到存储卷"
        else
            log_error "系统中未找到silk_v3_decoder，需要重新构建镜像"
        fi
    fi
    
    # 设置权限
    log_info "设置存储卷权限..."
    docker exec "$CONTAINER_NAME" chown -R 1001:1001 /opt/silk_v3_decoder 2>/dev/null || true
    docker exec "$CONTAINER_NAME" chmod -R 755 /opt/silk_v3_decoder 2>/dev/null || true
    
    # 验证安装
    log_info "验证silk_v3_decoder安装..."
    if docker exec "$CONTAINER_NAME" /opt/silk_v3_decoder/bin/silk_v3_decoder --help >/dev/null 2>&1 || 
       docker exec "$CONTAINER_NAME" /opt/silk_v3_decoder/bin/silk_v3_decoder >/dev/null 2>&1; then
        log_success "silk_v3_decoder验证通过"
    else
        log_warning "silk_v3_decoder验证失败，但文件存在"
    fi
    
    # 显示存储卷内容
    log_info "存储卷内容:"
    docker exec "$CONTAINER_NAME" ls -la /opt/silk_v3_decoder/ 2>/dev/null || true
    docker exec "$CONTAINER_NAME" ls -la /opt/silk_v3_decoder/bin/ 2>/dev/null || true
    
    # 清理临时容器
    log_info "清理临时容器..."
    docker rm -f "$CONTAINER_NAME"
    
    log_success "存储卷初始化完成"
}

# 验证存储卷
verify_volume() {
    log_info "验证存储卷配置..."
    
    # 显示存储卷信息
    log_info "存储卷详细信息:"
    docker volume inspect "$VOLUME_NAME"
    
    # 获取存储卷挂载点
    local mountpoint=$(docker volume inspect "$VOLUME_NAME" --format '{{ .Mountpoint }}')
    log_info "存储卷挂载点: $mountpoint"
    
    # 检查主机上的文件（需要root权限）
    if [ -r "$mountpoint" ]; then
        log_info "主机存储卷内容:"
        ls -la "$mountpoint" 2>/dev/null || log_warning "无法读取存储卷内容（权限不足）"
    else
        log_warning "无法访问存储卷挂载点（权限不足）"
    fi
}

# 主函数
main() {
    log_info "开始初始化HappyWechat Silk解码器存储卷"
    
    # 检查Docker
    check_docker
    
    # 检查并创建存储卷
    if ! check_volume_exists; then
        create_volume
    fi
    
    # 初始化存储卷
    initialize_volume
    
    # 验证存储卷
    verify_volume
    
    log_success "Silk解码器存储卷初始化完成！"
    log_info "现在可以启动HappyWechat服务: docker-compose up -d"
}

# 处理命令行参数
case "${1:-}" in
    "clean")
        log_info "清理存储卷..."
        docker volume rm "$VOLUME_NAME" 2>/dev/null || log_warning "存储卷不存在或正在使用中"
        log_success "清理完成"
        ;;
    "verify")
        verify_volume
        ;;
    *)
        main
        ;;
esac
