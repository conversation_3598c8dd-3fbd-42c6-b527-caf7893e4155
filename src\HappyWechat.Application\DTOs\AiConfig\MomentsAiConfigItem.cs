namespace HappyWechat.Application.DTOs.AiConfig;

/// <summary>
/// 朋友圈AI配置项
/// </summary>
public class MomentsAiConfigItem
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 账号名称
    /// </summary>
    public string AccountName { get; set; } = string.Empty;
    
    /// <summary>
    /// 账号微信号
    /// </summary>
    public string AccountWcId { get; set; } = string.Empty;
    
    /// <summary>
    /// AI智能体ID
    /// </summary>
    public Guid AiAgentId { get; set; }
    
    /// <summary>
    /// AI智能体名称
    /// </summary>
    public string AiAgentName { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 是否自动点赞
    /// </summary>
    public bool AutoLike { get; set; } = false;
    
    /// <summary>
    /// 是否自动评论
    /// </summary>
    public bool AutoComment { get; set; } = false;
    
    /// <summary>
    /// 自动评论内容模板
    /// </summary>
    public string? AutoCommentTemplate { get; set; }
    
    /// <summary>
    /// 配置描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// 朋友圈可见范围设置
    /// </summary>
    public string? VisibilitySettings { get; set; }
    
    /// <summary>
    /// 发布频率限制（分钟）
    /// </summary>
    public int PublishIntervalMinutes { get; set; } = 60;
}
