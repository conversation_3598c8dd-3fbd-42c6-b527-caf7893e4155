using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Notifications;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 离线通知处理器接口
/// </summary>
public interface IOfflineNotificationProcessor
{
    /// <summary>
    /// 处理离线通知
    /// </summary>
    Task<OfflineNotificationResult> ProcessOfflineNotificationAsync(WxCallbackMessageDto callbackMessage, string processingId);
}

/// <summary>
/// 离线通知处理器实现
/// </summary>
public class OfflineNotificationProcessor : IOfflineNotificationProcessor
{
    private readonly IOfflineNotificationService _offlineNotificationService;
    private readonly ILogger<OfflineNotificationProcessor> _logger;

    public OfflineNotificationProcessor(
        IOfflineNotificationService offlineNotificationService,
        ILogger<OfflineNotificationProcessor> logger)
    {
        _offlineNotificationService = offlineNotificationService;
        _logger = logger;
    }

    public async Task<OfflineNotificationResult> ProcessOfflineNotificationAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 开始处理离线通知 - WcId: {WcId}",
                processingId, callbackMessage.WcId);

            // 使用现有的离线通知服务处理
            var result = await _offlineNotificationService.ProcessOfflineNotificationAsync(callbackMessage, processingId);

            _logger.LogInformation("[{ProcessingId}] 离线通知处理完成 - WcId: {WcId}, 成功: {Success}",
                processingId, callbackMessage.WcId, result.Success);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理离线通知异常 - WcId: {WcId}",
                processingId, callbackMessage.WcId);

            return OfflineNotificationResult.CreateFailed($"处理离线通知异常: {ex.Message}");
        }
    }


}
