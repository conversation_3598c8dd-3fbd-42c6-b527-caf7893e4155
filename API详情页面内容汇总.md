### 登录E云平台（第1步）：https://wkteam.cn/api-wen-dang2/deng-lu/deng-lu-wei-kong-ping-tai-di-yi-bu.html

标题：登录E云平台(第一步)

内容：
登录E云平台（第一步）
简要描述：
登录E云平台
请求URL：
http://域名地址/member/login
请求方式：
POST
请求头Headers：
Content-Type：application/json
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|account|是|string|开发者 账号|
|password|是|string|开发者 密码|
域名地址和开发者信息:请登录后台->我的API-> 开通信息 中查看
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|Authorization|string|授权密钥，生成后永久有效|
|callbackUrl|string|消息回调地址|
|status|string|状态（0：正常，1：冻结，2：到期）|
请求参数示例
```
{    
   "account": "***********",
   "password": "123456"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "callbackUrl": null,
        "status": 0,
        "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************.x9bT9wDPAwGhJg7rTo0k4I0FlteKqK4AW7G9FsANgce"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 获取二维码（第2步）：https://wkteam.cn/api-wen-dang2/deng-lu/huo-qu-wei-xin-er-wei-ma2.html

标题：获取二维码(第二步-方式1)

内容：
获取二维码（第二步-方式1）
请求URL：
```http://域名地址/iPadLogin```
请求方式：
POST
请求头Headers:
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wcId|是|string|微信原始id （首次登录平台的号传""，掉线重登必须传值，否则会频繁掉线！！！） 第3步会返回此字段，记得入库保存|
|proxy|是|int|测试长效代理线路 1:北京 2:天津 3:上海 4:重庆 5:河北 6:山西 7:江苏 8:浙江 9:安徽 10:福建 11:江西 12:山东 13:河南 14:湖北 15:湖南 16:广东 17:海南 18:四川 19:云南 20:陕西 21:黑龙江 22:辽宁 23:贵州 24:广西 25:宁夏 26:青海 27:甘肃 28:西藏 29:吉林 30:内蒙|
|deviceType|否|string|登录方式， 默认不传是"ipad",也可传"mac"|
|proxyIp|否|string|自定义长效代理IP+端口|
|proxyUser|否|string|自定义长效代理IP平台账号|
|proxyPassword|否|string|自定义长效代理IP平台密码|
小提示：
若测试长效代理未包含您所在的城市，用户需自行购买/搭建自定义长效代理IP使用，否则会出现无法登录/秒掉/风控等情况 （PS：须是 中国电信 网络归属且是 支持 Socks5 的），例如第三方长效代理IP购买网站： 四叶天 、 熊猫 、 青果 等，可能部分平台需要设置白名单，将自助后台-API开通信息的IP添加即可。
若您传输地区后， 扫码显示地区非本省/接口返回网络链接异常，请联系技术支持
若传自定义proxyIp相关参数则覆盖proxy登录地点，（自定义长效代理优点：安全去异地、避免风控、解决号秒掉、代理线路用户过多导致线路挂掉等意外情况）
若传自定义proxyIp相关参数，接口却返回网络链接异常，用户可通过本方式检测购买的代理IP是否正常且可访问， 参考此处测试
开发者将本接口返回的二维码让用户去扫码，手机扫码结束后，需要调用 第3步 才会登录成功，且手机 顶部显示ipad已登录 ，
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|wId|string|登录实例标识 （本值非固定的，每次重新登录会返回新的，数据库记得实时更新wid）|
|qrCodeUrl|string|扫码登录地址|
请求参数示例
{     //例：首次取码登录     "wcId": "",     "proxy": "2",     "proxyIp": "",     "proxyUser": "",     "proxyPassword": "",     //例：掉线后，重新去取码登录     "wcId": "wxid_xxxxxxxxxxxx",     "proxy": "2",     "proxyIp": "",     "proxyUser": "",     "proxyPassword": "",      //例：自定义代理IP，Wcid根据是否首次取码选择性填写，proxy随便填写，都会被自定义代理IP覆盖     "wcId": "",     "proxy": "2",     "proxyIp": "************:91623",     "proxyUser": "test1234",     "proxyPassword": "test1234" }
成功返回示例
```
{
    "message": "登录成功",
    "code": "1000",
    "data": {
        "wId": "0000016e-63ef-3a9c-0001-ed3311628ef4",
        "qrCodeUrl": "http://127.0.0.1:18081/1573634652963-500000.png"
    }
}
```
错误返回示例
```
{
    "message": "用户名或密码错误",
    "code": "1001",
    "data": null
}
```
### 执行微信登录（第3步）：https://wkteam.cn/api-wen-dang2/deng-lu/zhi-xing-wei-xin-deng-lu.html

标题：执行微信登录(第三步)

内容：
执行微信登录（第三步）
简要描述：
执行登录（确认登录）
请求URL：
```http://域名地址/getIPadLoginInfo```
请求方式：
POST
请求头Headers:（别忘了传）
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|verifyCode|否|string|验证码|
Attention
新用户必看：
若本接口返回“请在iPad上输入验证码”，请扫码用户看下手机显示的是‘验证码’还是‘在新设备完成验证’，前者则直接调用1次本接口传验证码即可完成登录，后者则需要 重新调用获取二维码并传deviceType字段以mac方式登录，再扫码且执行本接口即可完成登录
此接口为检测耗时接口，最长250S返回请求，用户VX扫码了会返回结果，且扫码成功后手机上会显示ipad登录成功，才可以收发消息及调用其它接口！
首次登录平台 ， 24小时内会掉线1次 ，且 72小时 内不能发送朋友圈，掉线后 必须传wcid 调用获取二维码接口再次扫码登录即可实现3月内不掉线哦， 详细规范点击这里(第1大类1小节) PS： 若出现登录60S内无故掉线也看这里哦!
本文档所有接口，登录模块是最繁琐且需要注意的，到这里恭喜您对接已完成一大半！
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|wcId|string|微信id (唯一值）|
|nickName|string|昵称|
|deviceType|string|扫码的设备类型|
|uin|int|识别码|
|headUrl|string|头像url|
|wAccount|string|手机上显示的微信号 （用户若手机改变微信号，本值会变）|
|sex|int|性别|
|mobilePhone|string|绑定手机|
|status|string|保留字段|
请求参数示例
```
{
    "wId": "0000016e-63eb-f319-0001-ed01076abf1f"
}
```
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "country": "CN",         "wAccount": "hi1212",         "deviceType": "android",         "city": "",         "signature": "我的签名如风一样难以琢磨",         "nickName": "贝塔同学",         "sex": 2,         "headUrl": "http://wx.qlogo.cn/mmhead/ver_1/EImpg1FWcIdhPg3zRAnkVdVdV2hic1Mib7zZ9mLTwhv5QzhNrdTCL0nKAsOgiaRrJmQwrXnBY7c1QNDo4aNc8niaicYuQpLPbJqyaJ6sKjlm5mKY/0",         "type": 2,         "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/EImpg1FWcIdhPg3zRAnkVdVdV2hic1Mib7zZ9mLTwhv5QzhNrdTCL0nKAsOgiaRrJmQwrXnBY7c1QNDo4aNc8niaicYuQpLPbJqyaJ6sKjlm5mKY/132",         "wcId": "wxid_ylxtxxg0p8bx22",         "wId": "25d50610-1a82-4531-b9db-dd80c5a3c14a",         "mobilePhone": "***********",         "uin": *********,         "status": 3,         "username": "***********"     } }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 初始化通讯录列表：https://wkteam.cn/api-wen-dang2/deng-lu/initFriendList.html

标题：初始化通讯录列表

内容：
初始化通讯录列表
简要描述：
初始化通讯录列表
请求URL：
```http://域名地址/initAddressList```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
Attention
小提示：
本接口为耗时接口(返回在10s - 3min之间)，建议仅每次登录成功后调用一次至本地数据库，本接口和 获取通讯录列表接口 为组合接口
请求参数示例
```
{
    "wId": "6a696578-16ea-4edc-ac8b-e609bca39c69"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功、1001失败|
|msg|string|反馈信息|
|data|JSONObject|无|

### 获取通讯录列表：https://wkteam.cn/api-wen-dang2/deng-lu/queryFriendList.html

标题：获取通讯录列表

内容：
获取通讯录列表
简要描述：
获取通讯录列表
请求URL：
```http://域名地址/getAddressList```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
小提示：
获取通讯录列表之前，必须调用 初始化通讯录列表接口 。
此接口不会返回好友/群的详细信息，如需获取详细信息，请调用 获取联系人详情接口
本接口的返回群聊的是保存到通讯录的群聊 详细规范点击这里(第5大类2小节)
请求参数示例
```
{
    "wId": "6a696578-16ea-4edc-ac8b-e609bca39c69"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "获取通讯录成功",
    "data": {
        "chatrooms": [
            ""
        ],
        "friends": [
            ""
        ],
        "ghs": [
            ""
        ],
        "others": [
            ""
        ]
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|chatrooms|JSONArray|群组列表 群组返回为null的处理方法(第5大类3小节)|
|friends|JSONArray|好友列表 不包含企微好友， 获取企微好友列表点击这里|
|ghs|JSONArray|公众号列表|
|others|JSONArray|微信其他相关|

### 获取通讯录列表详情：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/queryUserInfo.html

标题：获取联系人信息

内容：
获取联系人信息
获取联系人信息
简要描述：
获取联系人信息
请求URL：
```http://域名地址/getContact```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|好友微信id/群id,多个好友/群 以","分隔每次最多支持20个微信/群号,本接口每次调用请随机间隔300ms - 800ms之间|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "wcId": "LoChaX,wxid_wl9qchkanp9u22"
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": [         {             "userName": "test558666",             "nickName": "追风少年666",             "remark": "",             "signature": "66666",             "sex": 1,             "aliasName": "test558666",             "country": "CN",             "bigHead": "http://wx.qlogo.cn/mmhead/PiajxSqBRaEL8iaRQBnStn37LYat3fREC4Y2iaStECzbX3icxntWBhWQ3w/0",             "smallHead": "http://wx.qlogo.cn/mmhead/PiajxSqBRaEL8iaRQBnStn37LYat3fREC4Y2iaStECzbX3icxntWBhWQ3w/132",             "labelList": "",             "v1": "v1_584e7774024c79af0e7304bf7afba775b31bf075651c16c964b1b5bf16369924ebf1ee7bc151c1feee1979e1dd40f0dd@stranger"         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|userName|String|微信id|
|nickName|String|昵称|
|remark|String|备注|
|signature|String|签名|
|sex|int|性别|
|aliasName|String|微信号|
|country|String|国家|
|bigHead|String|大头像|
|smallHead|String|小头像|
|labelList|String|标签列表|
|v1|String|用户的wxId，都是以v1开头的一串数值，v2数据，则是作为v1数据的辅助|

### 发送文本消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-wen-ben-xiao-xi.html

标题：发送文本消息

内容：
发送文本消息
请求URL：
```http://域名地址/sendText```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|文本内容消息|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
------------------------ 好友消息 ------------------------------
{
    "wId": "0000016e-63eb-f319-0001-ed01076abf1f",
    "wcId": "azhichao",
    "content": "天行健，君子以自强不息"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "type": 1,
        "msgId": 2562652205,
        "newMsgId": 4482117376572170921,
        "createTime": 1641457769,
        "wcId": "azhichao"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送文件消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/sendFile.html

标题：发送文件

内容：
发送文件
请求URL：
```http://域名地址/sendFile```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|path|是|string|文件url链接|
|fileName|是|string|文件名|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
   "wId": "0000016f-a805-4715-0001-848f9a297a40",
   "wcId":"jack_623555049",
   "path": "https://xc-1300726975.cos.ap-shanghai.myqcloud.com/%E4%B8%8B%E8%BD%BD%E6%96%87%E4%BB%B6.txt",
   "fileName": "文件.txt"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送文件消息成功",
    "data": {
        "type": 6,
        "msgId": 697760551,
        "newMsgId": 8262558808731059065,
        "createTime": 1641458290,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送Base64文件：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/sendFileBase64.html

标题：发送文件

内容：
发送文件
请求URL：
```http://域名地址/sendFileBase64```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|fileName|是|string|文件名|
|base64|是|string|文件Base64 可点击此处验证Base64格式是否正确|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
   "wId": "0000016f-a805-4715-0001-848f9a297a40",
   "wcId":"jack_623555049",
   "fileName": "123.txt",
   "base64": "data:text/plain;base64,5oiR5piv576O5Li955qE5rWL6K+V5paH5Lu2Cg=="
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送文件消息成功",
    "data": {
        "type": 6,
        "msgId": 697760551,
        "newMsgId": 8262558808731059065,
        "createTime": 1641458290,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送图片消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-tu-pian-xiao-xi2.html

标题：发送图片消息

内容：
发送图片消息
请求URL：
```http://域名地址/sendImage2```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|图片url链接|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "0000016e-63eb-f319-0001-ed01076abf1f",
    "wcId": "LoChaX",
    "content": "http://photocdn.sohu.com/20120323/Img338614056.jpg"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送图片消息成功",
    "data": {
        "type": null,
        "msgId": 697760516,
        "newMsgId": 901023126355472137,
        "createTime": 0,
        "wcId": "LoChaX"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送语音消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-yu-yin-xiao-xi-ji-jiang-kai-fang.html

标题：发送语音

内容：
发送语音
如需大批量微信发送同样微信内容 可点击此处查看优化方式，第2大类4小节
音频格式（如 mp3）转 silk 格式可参考此类库自行转换： https://github.com/kn007/silk-v3-decoder/
请求URL：
```http://域名地址/sendVoice```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|语音url （silk/amr 格式,可以 下载消息中的语音返回silk格式 ）|
|length|是|int|语音时长（回调消息xml数据中的voicelength字段）|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
   "wId": "0000016f-a719-5b44-0003-a567f79011fc",
   "wcId":"jack_623555049",
   "content": "https://xc-1300726975.cos.ap-shanghai.myqcloud.com/msgVoice/e17dd0a9-5c59-4a54-a3cd-1a4817f5dd29-1579005558791.silk",
    "length": 1
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送语音消息成功",
    "data": {
        "type": null,
        "msgId": 697760541,
        "newMsgId": 1375821081513076275,
        "createTime": 1641458029,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送视频消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-shi-pin-xiao-xi.html

标题：发送视频消息

内容：
发送视频消息
如需大批量微信发送同样微信内容可点击此处查看优化方式，第2大类4小节
请求URL：
```http://域名地址/sendVideo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|path|是|string|视频url链接|
|thumbPath|是|string|视频封面url链接（50KB以内）|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "0000016e-a1f1-f0d9-0002-425ea1a28d22",
    "wcId": "jack_623555049",
    "path": "https://wkgjonlines.oss-cn-shenzhen.aliyuncs.com/movies/20191113/d7c616569ac342ad1fa8e3301682844e.mp4",
    "thumbPath": "http://pic23.nipic.com/20120902/8068495_150602391000_2.jpg"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送视频消息成功",
    "data": {
        "type": null,
        "msgId": 697760511,
        "newMsgId": 3289648069366716802,
        "createTime": null,
        "wcId": null
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送链接消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-lian-jie-xiao-xi.html

标题：发送链接

内容：
发送链接
简要描述：
发送链接
请求URL：
```http://域名地址/sendUrl```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|title|是|string|标题|
|url|是|string|链接|
|description|是|string|描述|
|thumbUrl|是|string|图标url（JPG/PNG格式,50K以内）|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "0000016f-63d2-ea61-000e-a659a75ea445",
    "wcId": "jack_623555049",
    "title": "这是测试链接",
    "url": "https://timgsa.***************/timg?image&quality=80&size=b9999_10000&sec=1577945612638&di=81a0281095a337037abf85f29929b55f&imgtype=0&src=http%3A%2F%2Fimage5.92bizhi.com%2Funclassified_unclassified--122_26-1600x1200.jpg",
    "description": "",
    "thumbUrl": "https://timgsa.***************/timg?image&quality=80&size=b9999_10000&sec=1577945612638&di=81a0281095a337037abf85f29929b55f&imgtype=0&src=http%3A%2F%2Fimage5.92bizhi.com%2Funclassified_unclassified--122_26-1600x1200.jpg"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送链接成功",
    "data": {
        "type": null,
        "msgId": 697760503,
        "newMsgId": 181228940242588250,
        "createTime": 1641457185,
        "wcId": "wxid_amdhbnjfj3d"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送名片消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-ming-pian-xiao-xi.html

标题：发送名片消息

内容：
发送名片消息
请求URL：
```http://域名地址/sendNameCard```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|nameCardId|是|string|要发送的名片微信id|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
     "wId": "0000016e-abef-bb44-0002-dad3f6230dad",
     "wcId": "azhichao",
     "nameCardId": "wxid_uf44z2g3jge022"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送名片成功",
    "data": {
        "type": 42,
        "msgId": 0,
        "newMsgId": 6240562811972867706,
        "createTime": 1641457349,
        "wcId": "wxid_rfdfvhobjai8d"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送动图消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-emoji.html

标题：发送emoji表情

内容：
发送emoji表情
简要描述：
发送emoji动图表情
请求URL：
```http://域名地址/sendEmoji```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|imageMd5|是|string|取回调中xml中md5字段值|
|imgSize|是|string|取回调中xml中len字段值|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "00000171-78df-0aad-000c-70e4a3ce7d70",
    "wcId": "LoChaX",
    "imageMd5": "4cc7540a85b5b6cf4ba14e9f4ae08b7c",
    "imgSize":"102357"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送Emoji消息成功",
    "data": {
        "type": null,
        "msgId": 697760499,
        "newMsgId": 5012973909876748200,
        "createTime": null,
        "wcId": null
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送APP消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/sendApp.html

标题：发送APP类消息

内容：
发送APP类消息
和转发小程序是同一个接口，此接口可发各种类型消息，例如：引用消息，短视频，直播，音乐，第三方APP等（只要消息回调的xml中包含appmsg的消息都可发送）
请求URL：
```http://域名地址/sendApplet```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|content|是|string|消息xml回调内容, (此回调的XML需要去掉部分，截取appmsg开头的，具体请看请求参数示例）|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{

    "wId": "0000016f-78bd-21c8-0001-29c4d004ae46",
     "wcId": "jack_623555049",
    "content": "<appmsg appid=\"\" sdkver=\"0\">\n\t\t<title>首页</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wx9c4062d486855e2f&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>305c0201000455305302010002042aaae40702032f55f902048e0260b402045ed89962042e6175706170706d73675f333731636636306138623165316663615f313539313235333334353838385f32303838300204010400030201000400</cdnthumburl>\n\t\t\t<cdnthumbmd5>0d249c2dd3b3296a4aea2ac0fbeb865f</cdnthumbmd5>\n\t\t\t<cdnthumblength>72340</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>c1ce6b862ceab481955de4cbde33fffc</cdnthumbaeskey>\n\t\t\t<aeskey>c1ce6b862ceab481955de4cbde33fffc</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl>null</designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl>null</iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>0d249c2dd3b3296a4aea2ac0fbeb865f</md5>\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/venue/list.html]]></pagepath>\n\t\t\t<username>gh_6c471f8ef617@app</username>\n\t\t\t<appid>wx9c4062d486855e2f</appid>\n\t\t\t<version>198</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/Q3auHgzwzM5Rz1QFH4Wpx2ibOTJGgLA9ovlIsFkPszXW4GEIPHkf3ibg/96]]></weappiconurl>\n\t\t\t<shareId><![CDATA[1_wx9c4062d486855e2f_574177060_1591252418_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<finderFeed>\n\t\t\t<objectId>null</objectId>\n\t\t\t<objectNonceId>null</objectNonceId>\n\t\t\t<feedType>0</feedType>\n\t\t\t<nickname />\n\t\t\t<avatar><![CDATA[null]]></avatar>\n\t\t\t<desc />\n\t\t\t<mediaCount>0</mediaCount>\n\t\t\t<mediaList />\n\t\t</finderFeed>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t</findernamecard>\n\t\t<finderTopic>\n\t\t\t<topic />\n\t\t\t<topicType>-1</topicType>\n\t\t\t<iconUrl><![CDATA[]]></iconUrl>\n\t\t\t<desc />\n\t\t\t<location>\n\t\t\t\t<poiClassifyId />\n\t\t\t\t<longitude>0.0</longitude>\n\t\t\t\t<latitude>0.0</latitude>\n\t\t\t</location>\n\t\t</finderTopic>\n\t\t<finderEndorsement>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderEndorsement>\n\t\t<directshare>0</directshare>\n\t\t<websearch />\n\t</appmsg>"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送成功",
    "data": {
        "type": 0,
        "msgId": 697760545,
        "newMsgId": 7645748705605226305,
        "createTime": 1641458149,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 发送小程序消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/sendApplets.html

标题：发送小程序

内容：
发送小程序
请求URL：
```http://域名地址/sendApplets```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|displayName|是|string|小程序的名称，例如：京东|
|iconUrl|是|string|小程序卡片图标的url (50KB以内的png/jpg)|
|appId|是|string|小程序的appID,例如：wx7c544xxxxxx|
|pagePath|是|string|点击小程序卡片跳转的url|
|thumbUrl|是|string|小程序卡片缩略图的url (50KB以内的png/jpg)|
|title|是|string|标题|
|userName|是|string|小程序所有人的ID,例如：gh_1c0daexxxx@app|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
Attention
小提示：
参数来源可看消息回调中小程序消息，自定义相关参数
请求参数示例
```
{

    "wId": "0000016f-78bd-21c8-0001-29c4d004ae46",
    "wcId": "filehelper",
      "displayName": "云铺海购",
    "iconUrl": "无用",
    "appId": "wx07af7e375d21a08c",
    "pagePath": "pages/home/<USER>/index.html?shopAutoEnter=1&is_share=1&share_cmpt=native_wechat&kdt_id=109702811&from_uuid=FgPTe5LTPr00dw21663912217667",
    "thumbUrl": "https://pic3.zhimg.com/v2-f73763905eed23308466e441430a43be_r.jpg",
    "title": "云铺海购",
    "userName": "gh_12566478d436@app"

}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送小程序成功",
    "data": {
        "type": 0,
        "msgId": 697760545,
        "newMsgId": 7645748705605226305,
        "createTime": 1641458149,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 群聊@：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/qun-liao-at.html

标题：群聊@

内容：
群聊@
请求URL：
```http://域名地址/sendText```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方群id|
|content|是|string|文本内容消息（@的微信昵称需要自己拼接，必须拼接艾特符号，不然不生效）|
|at|是|string|艾特的微信id（多个以逗号分开） 群主或者管理员如果是艾特全部的人，则直接填写'notify@all'|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
 "wId": "0000016f-8911-484a-0001-db2943fc2786",
 "wcId": "22270365143@chatroom",
 "at": "wxid_lr6j4nononb921,wxid_i6qsbbjenjuj22",
 "content": "@E云Team_Mr Li@你微笑时真美 测试"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "type": 1,
        "msgId": 2562652205,
        "newMsgId": 4482117376572170921,
        "createTime": 1641457769,
        "wcId": "22270365143@chatroom"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 转发文件消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-yi-jing-shou-dao-de-wen-jian.html

标题：转发文件消息

内容：
转发文件消息
简要描述：
根据消息回调收到的xml转发文件消息，适用于同内容大批量发送， 可点击此处查看使用方式，第2大类4小节
请求URL：
```http://域名地址/sendRecvFile```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|xml文件内容|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
   "wId": "0000016f-a805-4715-0001-848f9a297a40",
   "wcId":"jack_623555049",
   "content": "xxx"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "转发文件成功",
    "data": {
        "type": 6,
        "msgId": 697760535,
        "newMsgId": 6957007917217750754,
        "createTime": 1641457929,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 转发图片消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-yi-jing-shou-dao-de-tu-pian-xiao-xi.html

标题：转发图片消息

内容：
转发图片消息
简要描述：
根据消息回调收到的xml转发图片消息，适用于同内容大批量发送， 可点击此处查看使用方式，第2大类4小节
请求URL：
```http://域名地址/sendRecvImage```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|xml图片内容|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "0000016f-4621-dde5-0002-390493cab4dc",
    "wcId": "zhongweiyu789",
    "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"849481a442044ad3a3a8130c94d2b591\" encryver=\"0\" cdnthumbaeskey=\"849481a442044ad3a3a8130c94d2b591\" cdnthumburl=\"3053020100044c304a0201000204e7d9caed02032f55f90204900060b402045e05acc00425617570696d675f386634626639356134343465613063665f31353737343330323038303739020401053a010201000400\" cdnthumblength=\"3310\" cdnthumbheight=\"80\" cdnthumbwidth=\"120\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3053020100044c304a0201000204e7d9caed02032f55f90204900060b402045e05acc00425617570696d675f386634626639356134343465613063665f31353737343330323038303739020401053a010201000400\" length=\"19842\" cdnbigimgurl=\"3053020100044c304a0201000204e7d9caed02032f55f90204900060b402045e05acc00425617570696d675f386634626639356134343465613063665f31353737343330323038303739020401053a010201000400\" hdlength=\"99007\" md5=\"39fec3c8e1ebad09ef4289b9e712a716\" hevc_mid_size=\"13869\" />\n</msg>\n"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "转发图片消息成功",
    "data": {
        "type": null,
        "msgId": 697760529,
        "newMsgId": 8689175729438895373,
        "createTime": 0,
        "wcId": "zhongweiyu789"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 转发视频消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/fa-song-yi-jing-shou-dao-de-shi-pin-xiao-xi.html

标题：转发视频消息

内容：
转发视频消息
简要描述：
根据消息回调收到的xml转发视频消息，适用于同内容大批量发送， 可点击此处查看使用方式，第2大类4小节
请求URL：
```http://域名地址/sendRecvVideo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群号id|
|content|是|string|xml视频内容|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
    "wId": "0000016f-4621-dde5-0002-390493cab4dc",
    "wcId": "zhongweiyu789",
    "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<videomsg aeskey=\"4f54430bcf53acfe9ef6b5d36d58e9f5\" cdnthumbaeskey=\"4f54430bcf53acfe9ef6b5d36d58e9f5\" cdnvideourl=\"306c020100046530630201000204f032c33602032f55f90204890260b402045e05b42a043e617570766964656f5f666661336336323865323964323566345f313537373433323130345f313533353034323731323139633662336333613434323131350204010400040201000400\" cdnthumburl=\"306c020100046530630201000204f032c33602032f55f90204890260b402045e05b42a043e617570766964656f5f666661336336323865323964323566345f313537373433323130345f313533353034323731323139633662336333613434323131350204010400040201000400\" length=\"7833957\" playlength=\"61\" cdnthumblength=\"12426\" cdnthumbwidth=\"288\" cdnthumbheight=\"512\" fromusername=\"zhongweiyu789\" md5=\"1ed727c57156b5f897e9e05a98912d80\" newmd5=\"d4f771f94ae15c4400b6dccff54068e9\" isad=\"0\" />\n</msg>\n"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "转发视频消息成功",
    "data": {
        "type": null,
        "msgId": 697760525,
        "newMsgId": 5163231378817832136,
        "createTime": null,
        "wcId": null
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 转发链接消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/forwardUrl.html

标题：转发链接消息

内容：
转发链接消息
简要描述：
根据消息回调收到的xml转发链接消息，适用于同内容大批量发送， 可点击此处查看使用方式，第2大类4小节
请求URL：
```http://域名地址/forwardUrl```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收人微信id/群id|
|content|是|string|xml文件内容|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{
   "wId": "0000016f-a805-4715-0001-848f9a297a40",
   "wcId":"jack_623555049",
   "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"\" sdkver=\"0\">\n\t\t<title>理想汽车正式登陆纳斯达克！</title>\n\t\t<des>7月30日，理想汽车正式在美国纳斯达克证券市场正式挂牌上市，股票代码为“LI”，发行价格为每股11.5美元。</des>\n\t\t<action />\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>http://mp.weixin.qq.com/s?__biz=MzU0Mjk1MDk4MA==&amp;mid=2247489268&amp;idx=1&amp;sn=b9df468408299b16ea55b804f8eaac6f&amp;chksm=fb1385dfcc640cc90de251b2d641739fe91278c6d6c3a94239cadfe0f5f1146bdf283d7b73a6&amp;mpshare=1&amp;scene=2&amp;srcid=0730zRNXTUJqhf7Fztpamu6n&amp;sharer_sharetime=1596158677187&amp;sharer_shareid=b5d32fcdbf6f6bd1700daee19cead97b#rd</url>\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<cdnthumburl>30570201000450304e0201000204502c9b9f02032f55f90204a40260b402045f2379650429777875706c6f61645f777869645f796c7874666c636730703862323237395f313539363136303335370204010400030201000400</cdnthumburl>\n\t\t\t<cdnthumbmd5>51f22eeff56ff76a7cab2bf177ef6c1a</cdnthumbmd5>\n\t\t\t<cdnthumblength>25332</cdnthumblength>\n\t\t\t<cdnthumbwidth>150</cdnthumbwidth>\n\t\t\t<cdnthumbheight>150</cdnthumbheight>\n\t\t\t<cdnthumbaeskey>99e7fd1d7d33dba159edfa52607645c3</cdnthumbaeskey>\n\t\t\t<aeskey>99e7fd1d7d33dba159edfa52607645c3</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_ylxtflcg0p8b2279_1596160357</filekey>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_89701dbd6858</sourceusername>\n\t\t<sourcedisplayname>理想汽车</sourcedisplayname>\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<directshare>0</directshare>\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<nativepage>0</nativepage>\n\t\t\t<pubtime>0</pubtime>\n\t\t\t<duration>0</duration>\n\t\t\t<width>0</width>\n\t\t\t<height>0</height>\n\t\t\t<vid />\n\t\t\t<funcflag>0</funcflag>\n\t\t\t<ispaysubscribe>0</ispaysubscribe>\n\t\t</mmreadershare>\n\t</appmsg>\n\t<fromusername>wxid_i6qsbbjenjuj22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "转发文件成功",
    "data": {
        "type": 6,
        "msgId": 697760535,
        "newMsgId": 6957007917217750754,
        "createTime": 1641457929,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 转发小程序消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/sendApplet.html

标题：转发小程序

内容：
转发小程序
请求URL：
```http://域名地址/sendApplet```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|imgUrl|是|string|小程序封面图 (50KB以内的png/jpg)|
|content|是|string|小程序xml内容, (小程序xml需先收集入库，也就是说将想要发送的小程序手动发送给机器人微信，此时消息回调中获取xml内容，xml去掉部分仅截取appmsg开头与结尾的，具体请看请求参数示例,且xml中可以自定义任意参数，例如：携带参数的跳转地址，缩略图等）|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|data.type|int|类型|
|data.msgId|long|消息msgId|
|data.newMsgId|long|消息newMsgId|
|data.createTime|long|消息发送时间戳|
|data.wcId|string|消息接收方id|
请求参数示例
```
{

    "wId": "0000016f-78bd-21c8-0001-29c4d004ae46",
    "wcId": "jack_623555049",
    "imgUrl":"http://photocdn.sohu.com/20120323/Img338614056.jpg",
    "content": "<appmsg appid=\"\" sdkver=\"0\">\n\t\t<title>云铺海购</title>\n\t\t<des>云铺海购</des>\n\t\t<type>33</type>\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wx07af7e375d21a08c&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<appattach>\n\t\t\t<cdnthumburl>3057020100044b30490201000204502c9b9f02032f55f902040ed15eda0204632dc841042461316335306262662d393337322d343361332d383631312d6166613731306362643764300204011400030201000405004c51e500</cdnthumburl>\n\t\t\t<cdnthumbmd5>e1c43f713ebc389dc8f89690aeb7ecb4</cdnthumbmd5>\n\t\t\t<cdnthumblength>58598</cdnthumblength>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbaeskey>125805800e40722f240220286e3ef74d</cdnthumbaeskey>\n\t\t\t<aeskey>125805800e40722f240220286e3ef74d</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_ctqh94e1ahe722_26_1663944768</filekey>\n\t\t</appattach>\n\t\t<sourceusername>gh_12566478d436@app</sourceusername>\n\t\t<sourcedisplayname>云铺海购</sourcedisplayname>\n\t\t<md5>e1c43f713ebc389dc8f89690aeb7ecb4</md5>\n\t\t<recorditem><![CDATA[(null)]]></recorditem>\n\t\t<weappinfo>\n\t\t\t<username><![CDATA[gh_12566478d436@app]]></username>\n\t\t\t<appid><![CDATA[wx07af7e375d21a08c]]></appid>\n\t\t\t<type>2</type>\n\t\t\t<version>14</version>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/uLxzSQcibsGzibyibBMLZhib1ick4RhO4ic203iaKMMSL35riafKicdyy8OX0ibjeDrs4Vka2KwTibiaPiaeXBKDQ24pblJO6mg/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<pagepath><![CDATA[pages/home/<USER>/index.html?shopAutoEnter=1&is_share=1&share_cmpt=native_wechat&kdt_id=109702811&from_uuid=FgPTe5LTPr00dw21663912217667]]></pagepath>\n\t\t\t<shareId><![CDATA[0_wx07af7e375d21a08c_5a36c4cc14fb8effefecbd92a1f291a6_1663944761_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<brandofficialflag>0</brandofficialflag>\n\t\t\t<showRelievedBuyFlag>0</showRelievedBuyFlag>\n\t\t\t<subType>0</subType>\n\t\t\t<isprivatemessage>0</isprivatemessage>\n\t\t</weappinfo>\n\t</appmsg>"
    ...///这个xml图片的缩略图过期  可以调用CDN图片上传接口 自定义替换参数
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "发送小程序成功",
    "data": {
        "type": 0,
        "msgId": 697760545,
        "newMsgId": 7645748705605226305,
        "createTime": 1641458149,
        "wcId": "jack_623555049"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 撤回消息：https://wkteam.cn/api-wen-dang2/xiao-xi-fa-song/revokeMsg.html

标题：撤回消息

内容：
撤回消息
请求URL：
```http://域名地址/revokeMsg```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|接收方微信id/群id|
|msgId|是|long|消息msgId(发送类接口返回的msgId)|
|newMsgId|是|long|消息newMsgId(发送类接口返回的newMsgId)|
|createTime|是|long|发送时间|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "12491ae9-62aa-4f7a-83e6-9db4e9f28e3c",
    "wcId": "wxid_1dfgh4fs8vz22",
    "msgId": 697760203,
    "newMsgId": 4792296942111367533,
    "createTime": 1641456307
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 设置消息接收地址：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/shou-xiao-xi/she-zhi-http-hui-tiao-di-zhi.html

标题：设置消息接收地址

内容：
设置消息接收地址
Attention
开发者需提供接收微信消息的公网接口URL，并将此url在此接口 配置（PS：简单理解就是腾讯服务器会将消息请求到你们编写的接口服务）
公网接口需流畅， 微信消息是Http Post Json请求 ，默认最高6秒内建立连接并发送数据，通讯时长超过6秒，不发送回调消息
若开发者提供的回调接口关闭/无法连接，新消息将间隔10min后尝试发送。（PS：若开发者回调接口恢复正常，可以再次调用本接口连接，无需等待10min）
配置成功后，会接收一条包含文字 “验证回调地址是否可用” 的JSON回调。
开发者若未配置此接口，消息默认推送至 后台系统-在线测试-消息接收模块-控制台 。
注意：机器人微信自己通过接口发送的消息不会有回调，因为回调是接收消息，发送不属于接收，但是手机微信发送的消息也会有，因为这属于消息同步（同步其他客户端的消息至本客户端，IM原理）。
简要描述：
设置http回调地址
请求URL：
http://域名地址/setHttpCallbackUrl
POST
请求头Headers:（别忘了传）
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必填|类型|说明|
| ---- | ---- | ---- | ---- |
|httpUrl|是|string|开发者接口回调地址|
|type|是|int|2:优化版 【PS：建议使用优化版】|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "httpUrl": "http://182.168.40.14:18081/userInfo/webHttpTest",
    "type": 2
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
配置成功后，即可生效
### 取消消息接收：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/shou-xiao-xi/qu-xiao-xiao-xi-jie-shou.html

标题：取消消息接收

内容：
取消消息接收
简要描述：
取消消息接收
请求URL：
```http://域名地址/cancelHttpCallbackUrl```
请求方式：
POST
请求头Headers：
Authorization：login接口返回
Content-Type：application/json
无参数
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 下载文件：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/xia-zai-wen-jian-ji-jiang-kai-fang.html

标题：下载文件

内容：
下载文件
简要描述：
下载消息中的文件
请求URL：
http://域名地址/getMsgFile
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识 包含此参数 所有参数都是从消息回调中取）|
|msgId|是|long|消息id|
|content|是|string|收到的消息的xml数据|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a3f4-7ac2-0001-4686486bb6c6",
   "msgId": 1102684150,
   "content": "<?xml version=\"1.0\"?><msg><appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\"><title>下载文件.txt</title><des /><action>view</action><type>6</type><showtype>0</showtype><content /><url /><dataurl /><lowurl /><lowdataurl /><recorditem><![CDATA[]]></recorditem><thumburl /><messageaction /><extinfo /><sourceusername /><sourcedisplayname /><commenturl /><appattach><totallen>6</totallen><attachid>@cdn_304e02010004473045020100020466883f5202032f55f90204260260b402045e1db470042036626464393436656537643431613836623065383665373034396538646566630204010400050201000400_17dd9d048f84c77db909b2161d6dbb09_1</attachid><emoticonmd5></emoticonmd5><fileext>txt</fileext><cdnattachurl>304e02010004473045020100020466883f5202032f55f90204260260b402045e1db470042036626464393436656537643431613836623065383665373034396538646566630204010400050201000400</cdnattachurl><aeskey>17dd9d048f84c77db909b2161d6dbb09</aeskey><encryver>1</encryver></appattach><weappinfo><pagepath /><username /><appid /><appservicetype>0</appservicetype></weappinfo><websearch /><md5>8c8fa3529ee34d4e69a0baafb7069da3</md5></appmsg><fromusername>wxid_lr6j4nononb921</fromusername><scene>0</scene><appinfo><version>7</version><appname>微信电脑版</appname></appinfo><commenturl /></msg>"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "url": "下载文件.txt"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 下载语音：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/xia-zai-yu-yin-ji-jiang-kai-fang.html

标题：下载消息中的语音

内容：
下载消息中的语音
简要描述：
下载消息中的语音
请求URL：
```http://域名地址/getMsgVoice```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识 包含此参数 所有参数都是从消息回调中取）|
|msgId|是|long|消息id|
|length|是|int|语音长度（xml数据中的length字段）|
|bufId|是|string|xml中返回的bufId字段值|
|fromUser|是|string|发送者|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "24a929b2-016d-4932-b93f-ff1b0a800305",
    "msgId": 1114311129,
    "fromUser": "zhongweiyu789",
    "bufId": "289139440622895483",
    "length":3227
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "url": "https://weikong-1301028514.cos.ap-shanghai.myqcloud.com//msgImg/e2045467-b1ac-4b3c-89f8-62ab2b3a2284-1591351192235-300000.silk"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 下载图片：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/xia-zai-tu-pian-ji-jiang-kai-fang.html

标题：下载图片

内容：
下载图片
简要描述：
下载图片
请求URL：
http://域名地址/getMsgImg
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识 (包含此参数 所有参数都是从消息回调中取）|
|msgId|是|long|消息id|
|content|是|string|收到的消息的xml数据|
|type|否|int|0：常规图片 1：高清图 部分图片仅有高清/缩略图，下载失败收尝试两一种图片下载|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|url|string|图片地址（7日内有效）|
请求参数示例
```
{
   "wId": "0000016f-a3f4-7ac2-0001-4686486bb6c6",
   "msgId": 1102684156,
   "content" :"<?xml version=\"1.0\"?><msg><img aeskey=\"07fea09b27952d512c0d71a52c914f3\" encryver=\"0\" cdnthumbaeskey=\"07fea09b27952d512c0d71a52c914f34\" cdnthumburl=\"30580201000451304f020100020466883f5202032f55f902048f0260b402045e1dbcac042a777875706c6f61645f777869645f676961316e776367706f627a32323135305f31353739303037313437020401051a020201000400\" cdnthumblength=\"3237\" cdnthumbheight=\"120\" cdnthumbwidth=\"80\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"30580201000451304f020100020466883f5202032f55f902048f0260b402045e1dbcac042a777875706c6f61645f777869645f676961316e776367706f627a32323135305f31353739303037313437020401051a020201000400\" length=\"51361\" md5=\"449fb858f24416adcab831859011fb21\" hevc_mid_size=\"30042\" /></msg>"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "url": "https://weikong-1301028514.cos.ap-shanghai.myqcloud.com//msgImg/dd32565c-78b0-4803-a330-6293b05674d9-1591347877463-600000.png"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 下载emoji：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/getMsgEmoji.html

标题：下载消息中的动图

内容：
下载消息中的动图
简要描述：
下载消息中的动图
请求URL：
```http://域名/getMsgEmoji```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|msgId|是|int|消息回调中返回|
|content|是|string|消息回调中返回，收到的emoji消息的xml数据|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|url|string|url地址|
请求参数示例
```
{
    "wId": "fa94e1a8-a90d-4d22-b677-b7ca3dd93a7a",
    "msgId": 1711184317,
    "content": "zhongweiyu789:\n<msg><emoji fromusername=\"zhongweiyu789\" tousername=\"18365397499@chatroom\" type=\"1\" idbuffer=\"media:0_0\" md5=\"3563976960aa367af6d65c4f0b8bc9c4\" len=\"119971\" productid=\"\" androidmd5=\"3563976960aa367af6d65c4f0b8bc9c4\" androidlen=\"119971\" s60v3md5=\"3563976960aa367af6d65c4f0b8bc9c4\" s60v3len=\"119971\" s60v5md5=\"3563976960aa367af6d65c4f0b8bc9c4\" s60v5len=\"119971\" cdnurl=\"http://emoji.qpic.cn/wx_emoji/ulFbvhrzAnjIw2coZEkgcFLiaqbcIDH9ciaC32Hhy80iczDTTaaBciab7Q/\" designerid=\"\" thumburl=\"\" encrypturl=\"http://emoji.qpic.cn/wx_emoji/ulFbvhrzAnjIw2coZEkgcFLiaqbcIDH9cMrflH5p38pq4wzftFyxXjA/\" aeskey=\"f183af3d469b1806b58b8090b8b2a886\" externurl=\"http://emoji.qpic.cn/wx_emoji/GeicwdtUCicOkRC3FZEU6K0TjOBET2ic0FC8aOiaISHr<[PLHD8_never_used_51bce0c785ca2f68081bfa7d91973934]>Vlr8oO8BjVmPoZ4aOKxs7f/\" externmd5=\"ef3c54cc09fc9edf9819969718404e7e\" width=\"170\" height=\"180\" tpurl=\"\" tpauthkey=\"\" attachedtext=\"\" attachedtextcolor=\"\" lensid=\"\"></emoji></msg>"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "url": "http://emoji.qpic.cn/wx_emoji/ulFbvhrzAnjIw2coZEkgcFLiaqbcIDH9ciaC32Hhy80iczDTTaaBciab7Q/"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 下载视频：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/asynGetMsgVideo.html

标题：异步下载消息中的视频

内容：
异步下载消息中的视频
简要描述：
异步下载消息中的视频
请求URL：
```http://域名地址/asynGetMsgVideo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识 (包含此参数 所有参数都是从消息回调中取）|
|msgId|是|long|消息id|
|content|是|string|收到的消息的xml数据|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|string||
|data.id|string|异步下载视频id，可用此参数获取下载视频结果|
请求参数示例
```
{
   "wId": "0000016f-a3f4-7ac2-0001-4686486bb6c6",
   "msgId": 1102684153,
   "content" :"<?xml version=\"1.0\"?><msg><videomsg aeskey=\"cc054b6e3e98fe91a5bb16227de67023\" cdnthumbaeskey=\"cc054b6e3e98fe91a5bb16227de67023\" cdnvideourl=\"304f02010004483046020100020466883f5202032f5081020491eff98c02045e1db76004213439346431383637346131316634356332613338363436613530633439376233320204010400040201000400\" cdnthumburl=\"304f02010004483046020100020466883f5202032f5081020491eff98c02045e1db76004213439346431383637346131316634356332613338363436613530633439376233320204010400040201000400\" length=\"966424\" playlength=\"15\" cdnthumblength=\"5819\" cdnthumbwidth=\"0\" cdnthumbheight=\"0\" fromusername=\"wxid_lr6j4nononb921\" md5=\"5056a23087ce5a8fe97042f0e5f87503\" newmd5=\"8eb2172bf0c03b2bc5af09effcaaba3d\" isad=\"0\" /></msg>"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "id": "6eb5d834-1dfe-47ad-b7a5-b9f2fb60a35a"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 获取视频下载结果：https://wkteam.cn/api-wen-dang2/xiao-xi-jie-shou/xia-zai-xiao-xi-nei-rong/getMsgVideoRes.html

标题：获取异步下载视频消息结果

内容：
获取异步下载视频消息结果
简要描述：
调用 异步下载视频接口 后可调用本接口获取下载结果，建议每间隔两秒获取一次，或可通过 回调消息 获取。
请求URL：
```http://域名地址/getMsgVideoRes```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|id|是|string|异步下载视频接口 返回的id|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|string|下载结果|
请求参数示例
```
{
    "id": "6eb5d834-1dfe-47ad-b7a5-b9f2fb60a35a"
}
```
成功返回示例
data.url：下载成功后的视频地址
data.type：
0：下载中
1：下载完成
2：下载失败
data.des：描述
data.id：id
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "url": "http://xxxxxx/20220105/wxid_phyyedw9xap22/f0aa717e-4c34-4185-a420-c831fa40be94.mp4?Expires=1735973855&OSSAccessKeyId=LTAI4G5VB9BMxMDV14c6USjt&Signature=mC8wNsED7qaNGVXL6h1e0ZY4WXE%3D",
        "type": 1,
        "des": null,
        "id": "6eb5d834-1dfe-47ad-b7a5-b9f2fb60a35a"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 搜索好友：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/serchUser.html

标题：搜索联系人

内容：
搜索联系人
简要描述：
搜索联系人
请求URL：
```http://域名地址/searchUser```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|微信号/手机号 (不支持微信id搜索)|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "wcId": "k1455804517"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "nickName": "可可",
        "sex": 2,
        "v1": "v1_90c13d2bb0ff6bb85db28041af32ec2cc80194eac15c3ab6534d28c127a2270e802c06bba0a41a904423a01855870756@stranger",
        "userName": "v1_90c13d2bb0ff6bb85db28041af32ec2cc80194eac15c3ab6534d28c127a2270e802c06bba0a41a904423a01855870756@stranger",
        "v2": "v4_000b708f0b040000010000000000b1bda847bd5ff86a7d236cdee25e1000000050ded0b020927e3c97896a09d47e6e9e387eb23497cde91ca8c3d17dc5cfb3703eb5c81a9b0c457a9cafb398238b24ad0c0e060c43c6bd464ca15269a601c3dffa3da32a659c32e7e58eeee0b9ec7873c5a4828ce51992d917@stranger",
        "bigHead": "http://wx.qlogo.cn/mmhead/ver_1/R6ibiaIVLfEqxcDCCsOGN6ice3Z4pkLnYuV6M1VbYkicuCNATqBk3x2aDmx5uS0iaTvtrDWJlnSaPUwEexPTI67m3fRK4DvIHWIbe85bILNWPhC4/0",
        "smallHead": "http://wx.qlogo.cn/mmhead/ver_1/R6ibiaIVLfEqxcDCCsOGN6ice3Z4pkLnYuV6M1VbYkicuCNATqBk3x2aDmx5uS0iaTvtrDWJlnSaPUwEexPTI67m3fRK4DvIHWIbe85bILNWPhC4/132"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
Attention
已是好友的话，v1 返回好友微信号 v2为空
搜索好友经常搭配添加好友接口使用，好友同意添加成功后会有回调，用户可根据本接口返回的v1和添加成功后回调返回的v1及wcid对应起来
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|v1|String|添加好友凭证1 （如果是好友 会返回微信id） 唯一不变值 好友添加成功后回调会返会此值|
|sex|int|性别|
|userName|String|微信号|
|v2|String|添加好友凭证2|
|bigHead|String|大头像|
|smallHead|String|小头像|

### 添加好友：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/addFriend.html

标题：添加好友

内容：
添加好友
简要描述：
添加微信好友
请求URL：
```http://域名地址/addUser```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|v1|是|string|v1 从搜索好友接口 获取|
|v2|是|string|v2 从搜索好友接口 获取|
|type|是|int|添加来源 3 ：微信号搜索 4 ：QQ好友 8 ：来自群聊 15：手机号|
|verify|是|String|验证消息|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a2f0-03e3-0003-65e826091614",
   "v1": "v1_aaf94e13d0058cdc888e388b98952e0fc23212d180e4dacb38b96dfe4b078c488e72772f907517470ac0b9b7311826da@stranger",
   "v2": "v2_13ced007472228cd1545feecf78b99f9a57a88843374513747afc7ac25d8a4cccb77590b7a9b01a96c941e047d137bbb@stranger",
   "type": 3,
   "verify": ""

}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 删除好友：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/shan-chu-hao-you.html

标题：删除好友

内容：
删除好友
简要描述：
删除联系人
请求URL：
```http://域名地址/delContact```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|微信实列ID|
|wcId|是|String|需删除的微信id|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
请求参数示例
```
{
   "wId": "0000016f-a2f0-03e3-0003-65e826091614",
   "wcId": "jack_623555049" 
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 添加隐私设置：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/userPrivacySettings.html

标题：添加隐私设置

内容：
添加隐私设置
Note
本接口设置成功后,效果立即生效，手机端展示会有延迟，可等待30S杀掉后台重启查看
本接口可调用多次，单次设置生效的都为一项
简要描述：
检测好友状态
请求URL：
```http://域名地址/userPrivacySettings```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|privacyType|是|int|选择开启/关闭的某项 4: 加我为朋友时需要验证 7: 向我推荐通讯录朋友 8: 添加我的方式 手机号 25: 添加我的方式 微信号 38: 添加我的方式 群聊 39: 添加我的方式 我的二维码 40: 添加我的方式 名片|
|switchType|是|int|1：关闭 2：开启|
请求参数示例
```
{
    "wId": "01377f33-544c-4dc4-9184-bcbbcd3b05d0",
    "privacyType": 4,
    "switchType":2
}
```
成功返回示例
```
{

    "message": "成功",
    "code": "1000"

}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|

### 修改好友备注：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/xiu-gai-hao-you-bei-zhu.html

标题：修改好友备注

内容：
修改好友备注
简要描述：
修改好友备注
请求URL：
```http://域名地址/modifyRemark```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|wcId|是|string|好友微信id|
|remark|是|string|好友备注|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a2aa-9089-0001-32dbe7c94132",
   "wcId": "wxid_ao4ziqc2g9b922",
   "remark": "备注"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 修改个人头像：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/she-zhi-ge-ren-tou-tou-xiang.html

标题：设置个人头头像

内容：
设置个人头头像
简要描述：
设置个人头像
请求URL：
```http://域名地址/sendHeadImage```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：Authorization值（登录获取二维码信息接口中返回的认证信息值）
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|path|是|string|图片url链接|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a2cb-9a5c-0003-63bc8acbec08",
   "path": "https://xc-1300726975.cos.ap-shanghai.myqcloud.com/timg.jpg"

}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 同意添加好友：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/acceptUser.html

标题：同意添加好友

内容：
同意添加好友
简要描述：
同意添加好友
请求URL：
```http://域名地址/acceptUser```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|v1|是|string|v1（从消息回调中取）|
|v2|是|string|v2（从消息回调中取）|
|type|是|int|取回调中的scene来源|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "v1": "v1_54fec2c3b452e9ec75505d5b062c0de28c08e3770a1f50a7a2d9ca509a2f82e4b03aaed0a37875c73bd5c35b91e2b060@stranger",
    "v2": "v4_000b708f0b04000001000000000014cf2b671dd639a279577eece15e1000000050ded0b020927e3c97896a09d47e6e9ef867bb94625d7dde9f2ebf03bb305a7aeddb554cc6f3f06e7d5a5327255425494854a71da02c88157e83f491afa8c17a3768b04cc1456c4a981e119a9eb93cf42a34bedc769a6c9dbe19597b2efb6d8d86cbaf97baac97ab61bda9fba80aeacf426a52b13e1d7854fc@stranger",
    "type":3
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 设置好友权限：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/setFriendPermission.html

标题：设置好友权限

内容：
设置好友权限
简要描述：
设置好友权限 本接口修改成功后 手机需退出后台，重新打开手机方可看到更改
请求URL：
```http://域名地址/setFriendPemission```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|微信实列ID|
|wcId|是|String|好友微信id|
|type|是|int|1:正常 2:仅聊天|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
请求参数示例
```
{
    "wId": "f54179d3-26ea-46b5-8aa2-97f02e031a9b",
    "wcId":"LoChaX",
    "type":"1"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 设置聊天置顶：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/setTop.html

标题：设置聊天置顶

内容：
设置聊天置顶
简要描述：
设置好友/群的会话聊天置顶
请求URL：
```http://域名/setTop```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|好友id/群id|
|operType|是|int|0：取消 1：置顶|
请求参数示例
```
{
    "wId": "xxxxxx",
    "wcId": "24608539283@chatroom",
    "operType": 0
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 检测好友状态：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/checkZombie.html

标题：检测好友状态

内容：
检测好友状态
本接口调用需要间隔1-3S
简要描述：
检测好友状态
请求URL：
```http://域名地址/checkZombie```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|好友微信id，多个已","分隔,每次最多支持个20|
请求参数示例
```
{
    "wId": "01377f33-544c-4dc4-9184-bcbbcd3b05d0",
    "wcId": "wxid_6rw5a5bpf4qg12,wxid_mv3azvtjaltu12,wxid_ez4qv5fzty422,wxid_c40ur7180psu12,wxid_jtwdho0jvo5w12,xid_1rfsa01c46m222,wxid_gb5vd0e8vtfz22"
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": [         {             "userName": "wxid_6rw55bpf4qg12",             "status": 6         },         {             "userName": "wxid_mv3aztjaltu12",             "status": 6         },         {             "userName": "wxid_e4qv5fzty422",             "status": 0         },         {             "userName": "wxid_c4ur7180psu12",             "status": 0         },         {             "userName": "wxid_jtdho0jvo5w12",             "status": 0         },         {             "userName": "",             "status": 0         },         {             "userName": "wxid_b5vd0e8vtfz22",             "status": 0         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|userName|String|微信id|
|status|int|0: 正常 1: 被删除 2: 被拉黑|

### 获取企微好友列表：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/getImAddressList.html

标题：获取企微联系人列表

内容：
获取企微联系人列表
简要描述：
获取联系人信息
请求URL：
```http://域名地址/getImAddressList```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67"
}
```
成功返回示例
{     "code": "1000",     "message": "获取企微联系人列表成功",     "data": [         {             "userName": "25984982670495377@openim",             "nickName": "张",             "remark": "",             "sex": 1,             "bigHead": "http://wework.qpic.cn/bizmail/5VlSBLWBUtiaHEuWhTlTLAbkvDCI9YkNJRwTC8clhjU9BEqjUuD1zdg/0",             "smallHead": "http://wework.qpic.cn/bizmail/5VlSBLWBUtiaHEuWhTlTLAbkvDCI9YkNJRwTC8clhjU9BEqjUuD1zdg/140",             "wordingId": "<EMAIL>",             "appId": "3552365301"         },         {             "userName": "25984984569242180@openim",             "nickName": "明周",             "remark": "",             "sex": 1,             "bigHead": "https://wework.qpic.cn/wwhead/duc2TvpEgST9hicuyypLEKNaicnxdBY5Lmc7Q2wNs5yltbE4X3OzGuOdqHdHwSTPq5BvaEByzuCn8/0",             "smallHead": "https://wework.qpic.cn/wwhead/duc2TvpEgST9hicuyypLEKNaicnxdBY5Lmc7Q2wNs5yltbE4X3OzGuOdqHdHwSTPq5BvaEByzuCn8/140",             "wordingId": "<EMAIL>",             "appId": "3552365301"         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|userName|String|企微好友微信id|
|nickName|String|企微好友昵称|
|remark|String|企微好友备注|
|sex|int|性别|
|bigHead|String|大头像|
|smallHead|String|小头像|
|wordingId|String|企微所属企业Id|
|appId|String|保留字段|

### 获取企微好友详情：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/getOpenImContact.html

标题：获取企微联系人信息

内容：
获取企微联系人信息
简要描述：
获取联系人信息
请求URL：
```http://域名地址/getOpenImContact```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|企微好友微信id|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "wcId": "2598498529278002@openim"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "获取企微联系人信息成功",
    "data": {
        "userName": "2598498529278002@openim",
        "nickName": "E.Bot",
        "remark": "E.Bot/::@",
        "sex": 1,
        "bigHead": "https://wework.qpic.cn/wwpic/256156_XYiSic_UQaS_sVk_1675839963/0",
        "smallHead": "https://wework.qpic.cn/wwpic/256156_XYiSic_UQaS_sVk_1675839963/140",
        "wordingId": "<EMAIL>",
        "wording": "AI社群"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|userName|String|企微好友微信id|
|nickName|String|企微好友昵称|
|remark|String|企微好友备注|
|sex|int|性别|
|bigHead|String|大头像|
|smallHead|String|小头像|
|wordingId|String|企微所属企业Id|
|wording|String|企微所属企业名称|

### 获取自己的二维码：https://wkteam.cn/api-wen-dang2/hao-you-cao-zuo/huo-qu-zi-ji-de-er-wei-ma.html

标题：获取我的二维码

内容：
获取我的二维码
简要描述：
获取我的二维码
请求URL：
```http://域名地址/getQrCode```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
请求参数示例
```
{
    "wId": "0000016f-a2f0-03e3-0003-65e826091614"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "qrCodeUrl": "http://qr.topscan.com/api.php?text=http://weixin.qq.com/x/QapmFw5pgyAShaPk2cLM",
        "uuId": "QapmFw5pgyAShaPk2cLM"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 修改群名：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/updateGroupName.html

标题：修改群名称

内容：
修改群名称
修改群名后，如看到群名未更改，是手机缓存问题，可以连续点击进入其他群，在点击进入修改的群，再返回即可看到修改后的群名
请求URL：
```http://域名地址/modifyGroupName```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|content|是|String|群名|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId":"24187765053@chatroom",
    "content":"我爱你中国啊啊啊啊啊"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 扫码入群：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/scanJoinRoom.html

标题：扫码入群

内容：
扫码入群
好友将群二维码发送给机器人，机器人调用本接口将自动识别入群
请求URL：
```http://域名地址/scanJoinRoom```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|url|是|string|群二维码url（二维码解析后的url）|
|type|否|int|操作类型，默认0 0: 进群 1:返回群名称及人数 10:返回原始html数据|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a340-c2d7-0003-6ab83bc1e64a",
   "url": "https://weixinxxx"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 修改群备注：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/updateGroupRemark.html

标题：修改群备注

内容：
修改群备注
修改群名备注后，如看到群备注未更改，是手机缓存问题，可以连续点击进入其他群，在点击进入修改的群，再返回即可看到修改后的群备注名，群名称的备注仅自己可见
请求URL：
```http://域名地址/modifyGroupRemark```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|content|是|String|群名|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId":"24187765053@chatroom",
    "content":"我爱你中国啊啊啊啊啊"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 创建微信群：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/chuang-jian-wei-xin-qun.html

标题：创建微信群

内容：
创建微信群
Attention
本接口为敏感接口，请查阅调用规范手册
创建后，手机上不会显示该群，往该群主动发条消息手机即可显示。
请求URL：
```http://域名地址/createChatroom```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|userList|是|String|群成员微信id，多个已 "," 分割，（必须传输2个微信id以上才可创建群聊）|
|topic|否|String|群名|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "topic":"啦啦啦",
    "userList":"wxid_wl9qchkanp9u22,wxid_i6qsbbjenjuj22"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "chatRoomID": "22264491511@chatroom",
        "base64": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCACLAIsDASIAAhEBAxEB/8QAHwAAAQUBXXXXXXXXXbSac8Uif8ATeM5/Jq9Zoo/s+l3f4f5DeYVX0X4/wCZw/hrQdTstXtprixEUYZ97mRSQCrY6H1IruKKK7KVJUY8sThq1XWlzSR//9k=",
        "status": 1
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|chatRoomID|String|群号|
|base64|String|群二维码|
|status|int|状态|

### 退出微信群：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/quitGroup.html

标题：退出群聊

内容：
退出群聊
简要描述：
退出群聊
请求URL：
```http://域名地址/quitChatRoom```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|chatRoomId|是|string|群id|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
   "wId": "0000016f-a340-c2d7-0003-6ab83bc1e64a",
   "chatRoomId": "22270365143@chatroom"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 添加群成员：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/addGroupMember.html

标题：添加群成员

内容：
添加群成员
Attention
群如果开启了群聊邀请确认，本接口将失效，则直接使用 邀人入群验证接口 ，
群人数在40以下且未开启群聊邀请确认，可直接调用本接口添加群成员，40人以上请调用 邀请群成员接口
请求URL：
```http://域名地址/addChatRoomMember```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|chatRoomId|是|String|群号|
|userList|是|String|群成员微信id，多个已 "," 分割|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId":"24187765053@chatroom",
    "userList":"wxid_ew6i9qdxlinu12,wxid_nqo37ves8w5t22"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 邀请群成员：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/inviteGroupMember.html

标题：邀请群成员（40人以上）

内容：
邀请群成员（40人以上）
Attention
群如果开启了群聊邀请确认，本接口将失效，则直接使用 邀人入群验证接口
群人数在40以上且未开启群聊邀请确认，需用本接口以发送卡片形式邀请群成员，40人以下请调用 添加群成员接口
请求URL：
```http://域名地址/inviteChatRoomMember```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|chatRoomId|是|String|群号|
|userList|是|String|群成员微信id，多个已 "," 分割|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId":"24187765053@chatroom",
    "userList":"wxid_ew6i9qdxlinu12,wxid_nqo37ves8w5t22"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 删除群成员：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/delGroupMember.html

标题：删除群成员

内容：
删除群成员
简要描述：
删除群成员
请求URL：
```http://域名地址/deleteChatRoomMember```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|chatRoomId|是|String|群号|
|userList|是|String|群成员微信id，多个已 "," 分割|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId":"24187765053@chatroom",
    "userList":"wxid_ew6i9qdxlinu12,wxid_nqo37ves8w5t22"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data":null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 设置群公告：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/setGroupAnnounct.html

标题：设置群公告

内容：
设置群公告
简要描述：
设置群公告
请求URL：
```http://域名地址/setChatRoomAnnouncement```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|content|是|String|内容|
请求参数示例
```
{
    "wId":"349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId": "24187765053@chatroom",
    "content": "修改群公告执行成功"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 设置公告群待办：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/roomTodo.html

标题：设置群待办消息

内容：
设置群待办消息
Attention
把群公告消息设置成待办消息
请求URL：
```http://域名/roomTodo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群id|
|newMsgId|是|int|群公告的消息id(设置群公告成功后，回调返回newMsgId)|
|operType|是|int|0:设置群待办 1:撤回群待办|
|sign|否|int|撤回传，设置待办成功后返回本字段|
请求参数示例
```
{
    "wId": "xxxx",
    "chatRoomId": "xxxx@chatroom",
    "newMsgId": 123412341,
    "operType": 0
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|sign|int|撤销秘钥|

### 设置小程序群待办：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/roomAppTodo.html

标题：设置群待办消息

内容：
设置群待办消息
Attention
把小程序消息设置成待办消息
请求URL：
```http://域名/roomAppTodo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群id|
|newMsgId|是|int|小程序的消息id(小程序消息回调返回newMsgid)|
|title|是|小程序标题|小程序消息回调中取|
|pagePath|是|小程序跳转地址|小程序消息回调中取|
|userName|是|小程序id|小程序回调中取|
|sendWcId|是|原小程序的发送者id|小程序回调中取|
|sign|否|int|撤回传，设置待办成功后返回本字段|
请求参数示例
```
{
    "wId": "2c7a5bf6-e23d-4fb0-8f03-b9ae844b539f",
    "chatRoomId": "25553320410@chatroom",
    "newMsgId": 128659030295046943,
    "title":"寄快递，用圆通",
    "pagePath":"pages/tabBar/index/index.html?sampshare=%7B%22i%22%3A%22oXJy05PxRKRmhJLHqmAn_NE9YrFc%22%2C%22p%22%3A%22pages%2FtabBar%2Findex%2Findex%22%2C%22d%22%3A0%2C%22m%22%3A%22%E8%BD%AC%E5%8F%91%E6%B6%88%E6%81%AF%E5%8D%A1%E7%89%87%22%7D",
    "userName":"gh_f9d9fca26a50@app",
    "sendWcId":"wxid_ylxtflc0p8b22"


}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|sign|int|撤销秘钥|

### 群管理操作（群主）：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/operateChatRoom.html

标题：群管理操作

内容：
群管理操作
简要描述：
群管理操作
请求URL：
```http://域名/operateChatRoom```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|wcId|是|String|群成员微信id，多个用 "," 分割|
|type|是|int|1：添加群管理（可添加多个微信id） 2：删除群管理（可删除多个） 3：转让（只能转让一个微信号）|
请求参数示例
```
{
    "wId": "xxxxxx",
    "chatRoomId": "24608539283@chatroom",
    "wcId": "wxid_0zssg6z7ivlm22"，
    "type": 3
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 获取群成员列表：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/queryGroupList.html

标题：获取群成员

内容：
获取群成员
简要描述：
获取群成员
请求URL：
```http://域名地址/getChatRoomMember```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId": "24343869723@chatroom"
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": [         {             "chatRoomId": "23282491030@chatroom",             "userName": "wxid_wl9qchkanp9u22",             "nickName": "E云通知小助手（机器人）",             "chatRoomOwner": null,             "bigHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/DEjvrt3YDnqggwzHj2LQTwY3K1y6TWVC615azPYb3RSWgeMvE5ny1kYQSBoNLgCicRMGa9LRp9dQJy2HHurNSYqqZNf5NTxicDMTNdjL3SrAI/0",             "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/DEjvrt3YDnqggwzHj2LQTwY3K1y6TWVC615azPYb3RSWgeMvE5ny1kYQSBoNLgCicRMGa9LRp9dQJy2HHurNSYqqZNf5NTxicDMTNdjL3SrAI/132",             "v1": null,             "memberCount": 0,             "displayName": "",             "chatRoomMembers": null         },         {             "chatRoomId": "23282491030@chatroom",             "userName": "wxid_i6qsbbjenjuj22",             "nickName": "E云Team_Mr Li",             "chatRoomOwner": null,             "bigHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/licI98sKzmtB0BWmDGvVaqcvCmDMMbLsGku18zHpxoxYibXH2QhZibTIjOPhzlpAkQic8Tlhdk4lCAIlE0twxQnqng4M4CKcV3ps52wOfcMHemo/0",             "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/licI98sKzmtB0BWmDGvVaqcvCmDMMbLsGku18zHpxoxYibXH2QhZibTIjOPhzlpAkQic8Tlhdk4lCAIlE0twxQnqng4M4CKcV3ps52wOfcMHemo/132",             "v1": null,             "memberCount": 0,             "displayName": "",             "chatRoomMembers": null         },         {             "chatRoomId": "23282491030@chatroom",             "userName": "wxid_ew6i9qdxlinu12",             "nickName": "E云客服-可可(工作日09:00-18:00)",             "chatRoomOwner": null,             "bigHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/avZnWvIiaulTibWZDqvjic9zNsW9F5n5GN5AoNIian9U1w86TAwicqjMa3esFLOzFfUNI4icCeziauRhOEOxicadyarDmQqf679VsUiaxhawibia9wficSE/0",             "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/avZnWvIiaulTibWZDqvjic9zNsW9F5n5GN5AoNIian9U1w86TAwicqjMa3esFLOzFfUNI4icCeziauRhOEOxicadyarDmQqf679VsUiaxhawibia9wficSE/132",             "v1": null,             "memberCount": 0,             "displayName": "",             "chatRoomMembers": null         },         {             "chatRoomId": "23282491030@chatroom",             "userName": "wxid_ylxtflcg0p8b22",             "nickName": "售前客服-小诺 (工作日9:00-18:00)",             "chatRoomOwner": null,             "bigHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/0",             "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/132",             "v1": null,             "memberCount": 0,             "displayName": "啦啦啦",             "chatRoomMembers": null         },         {             "chatRoomId": "23282491030@chatroom",             "userName": "wxid_nqo37ves8w5t22",             "nickName": "追风少年666",             "chatRoomOwner": null,             "bigHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/G9GD3GENzHvn9hEiaw0JJzwGYD2jIiczflo0DHcVTXuqIiavsB9W51Z3GTv3RqkdOY3xyhMicAicOZDSqBDOAelfD4AjaKo4Q5EsMa7MIgGbj8IY/0",             "smallHeadImgUrl": "http://wx.qlogo.cn/mmhead/ver_1/G9GD3GENzHvn9hEiaw0JJzwGYD2jIiczflo0DHcVTXuqIiavsB9W51Z3GTv3RqkdOY3xyhMicAicOZDSqBDOAelfD4AjaKo4Q5EsMa7MIgGbj8IY/132",             "v1": null,             "memberCount": 0,             "displayName": "",             "chatRoomMembers": null         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|chatRoomId|String|群号|
|userName|String|群成员微信号 （假如需要手机上显示的微信号或更详细的信息，则需要再调用 获取群成员详情接口获取 ）|
|nickName|String|群成员默认昵称|
|displayName|String|群成员修改后的昵称|
|bigHeadImgUrl|String|大头像|
|smallHeadImgUrl|String|小头像|
|chatRoomMemberFlag|int||
|inviterUserName|String|邀请人微信号 （仅有群主和管理可以看到）|

### 获取群成员详情：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/queryGroupMemberDetail.html

标题：获取群成员详情

内容：
获取群成员详情
简要描述：
获取群成员详情
请求URL：
```http://域名/getChatRoomMemberInfo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|userList|是|String|群成员标识 PS: 暂不支持多个群成员查询，可间隔调用获取|
请求参数示例
```
{
    "wId": "4941c159-48dc-4271-b0d0-f94adea39127",
    "chatRoomId":"232323232@chatRoom",
    "userList": "wxid_daydt60mc0ny22"
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": [         {             "userName": "wxid_daydt6xx1c0ny22",             "nickName": "北京123",             "remark": "",             "signature": "",             "sex": 0,             "aliasName": "xxxxxuai0309",             "country": null,             "bigHead": "https://t8.***************/it/u=1484500186,1503043093&fm=79&app=86&size=h300&n=0&g=4n&f=jpeg?sec=1593075215&t=4d1c7f8cab5417b9ebec450bb180d00e",             "smallHead": "https://t8.***************/it/u=1484500186,1503043093&fm=79&app=86&size=h300&n=0&g=4n&f=jpeg?sec=1593075215&t=4d1c7f8cab5417b9ebec450bb180d00e",             "labelList": null,             "v1": "v3_020b3826fd030100000xxx9b4df5b5000000501ea9a3dba12f95f6b60a0536a1adb69d4c980f5186cb7f0dbb8ee9b5f0cdcf4a075737d607e1803aededdd3a719b452a84dbf83c12e07b110dae9260e6ac806c82f3xxx80ad6085660a9@stranger",             "v2": ""         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|userName|String|微信id|
|nickName|String|昵称|
|aliasName|String|微信号|
|signature|String|签名|
|sex|int|性别|
|bigHead|String|大头像|
|smallHead|String|小头像|
|v1|String|v1|
|v2|String|v2|

### 获取群详情信息：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/queryGroupDetail.html

标题：获取群信息

内容：
获取群信息
简要描述：
获取群信息
请求URL：
```http://域名地址/getChatRoomInfo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId": "24343869723@chatroom"
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": [         {             "chatRoomId": "24343869723@chatroom",             "userName": null,             "nickName": "",             "chatRoomOwner": "wxid_ylxtflcg0p8b22",             "bigHeadImgUrl": null,             "smallHeadImgUrl": "http://wx.qlogo.cn/mmcrhead/S44kIgicdUQ1LwXCuMlDEnV37pDE2RNYOq5ic7GpZR6icDPT1UvIh7iaKh7rKZMicatXKuvB9J0gIDGVDwKTpeBMyLpoCd3FEhNGic/0",             "v1": "v1_f4ef1dde421ba4039ee0e7a2dcd555fc7a18bcb3b77face81f425e5ec66e8cab814857df2124c60cc144df1ecc83a096@stranger",             "memberCount": 3,             "chatRoomMembers": [                 {                     "userName": "wxid_wl9qchkanp9u22",                     "nikeName": "E云通知小助手（机器人）",                     "inviterUserName": "wxid_ylxtflcg0p8b22"                 },                 {                     "userName": "wxid_i6qsbbjenjuj22",                     "nikeName": "E云Team_Mr Li",                     "inviterUserName": "wxid_ylxtflcg0p8b22"                 },                 {                     "userName": "wxid_ylxtflcg0p8b22",                     "nikeName": "售前客服-小诺 (工作日9:00-18:00)",                     "inviterUserName": ""                 }             ]         }     ] }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONArray||
|chatRoomId|String|群号|
|nickName|String|群名称|
|chatRoomOwner|String|群主|
|bigHeadImgUrl|String|大头像|
|smallHeadImgUrl|String|小头像|
|v1|String|群v1|
|memberCount|int|群成员数|
|userName|String|群成员微信号|
|nickName|String|群成员昵称|
|isManage|boolean|是否是管理员|
|inviterUserName|String|邀请人微信号 （仅有群主和管理可以看到）|

### 获取群二维码：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/queryGroupQrCode.html

标题：获取群二维码

内容：
获取群二维码
Attention
生成的二维码有效期为7天
微信需在线三天后使用本接口，否则会显示二维码已过期
请求URL：
```http://域名地址/getGroupQrCode```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
请求参数示例
```
{
    "wId":"349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId": "24187765053@chatroom"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "imgUrl": "xxxxxx"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 修改我在本群的昵称：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/updateIInChatRoomNickName.html

标题：修改我在某群的昵称

内容：
修改我在某群的昵称
简要描述：
修改我在某群的昵称
请求URL：
```http://域名/updateIInChatRoomNickName```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|nickName|是|String|昵称|
请求参数示例
```
{
    "wId": "4941c159-48dc-4271-b0d0-f94adea39127",
    "chatRoomId": "23282491030@chatroom",
    "nickName":"啦啦啦"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功 1001失败|
|msg|string|反馈信息|
|data|JSONObject||

### 群保存/取消到通讯录：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/saveGroup.html

标题：群保存|取消到通讯录

内容：
群保存|取消到通讯录
简要描述：
群保存|取消到通讯录
请求URL：
```http://域名地址/showInAddressBook```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群号|
|flag|是|int|3：保存到群通讯录 2： 从通讯录移除群|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "chatRoomId": "24343869723@chatroom",
    "flag":0
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 自动同意入群邀请：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/acceptMemberGroup.html

标题：自动通过群（url）

内容：
自动通过群（url）
简要描述：
自动通过群（url）
请求URL：
```http://域名地址/acceptUrl```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：Authorization值（登录获取二维码信息接口中返回的认证信息值）
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|url|是|string|原始 url，好友发送的入群邀请卡片信息链接(回调中取)|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
请求参数示例
```
{
     "wId": "0000016f-b270-b2cd-0000-4e27e92f4502",
     "url": "http://shmmsns.qpic.cn/mmsns/CJ35Z2cnZA0zggcHCKIiaqOu0wO1gaOTaxL2Wd9StGfS1GdbbfKvJic1icfjfMXia7iaAd1B4fgN61g4/150"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
### 邀请群成员（开启群验证）：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/addChatRoomMemberVerify.html

标题：邀请群成员（开启群验证）

内容：
邀请群成员（开启群验证）
Attention
若群开启邀请确认，仅能通过本接口邀请群成员
请求URL：
```http://域名/addChatRoomMemberVerify```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群id|
|userList|是|number|邀请好友的id|
|reason|是|String|邀请理由（管理员查看，不得为空）|
请求参数示例
```
{
    "wId": "2c7a5bf6-e23d-4fb0-8f03-b90e844b539f",
    "chatRoomId": "1806832422@chatroom",
    "userList": "wxid_i6sbbjenjuj22",
    "reason":"拉个发小入群"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 群管理确认入群邀请（开启群验证）：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/agreeAddChatRoomMember.html

标题：群管理确认入群邀请

内容：
群管理确认入群邀请
Attention
本接口需管理员方可调用，因群开启邀请确认，需管理员调用本接口同意新成员，方可入群
请求URL：
```http://域名/agreeAddChatRoomMember```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群id|
|newMsgId|是|number|入群邀请回调返回newmMsgid|
|xml|是|String|入群邀请的回调xml|
请求参数示例
```
{
    "wId": "2c7a5bf6-e23d-4fb0-8f03-b90e844b539f",
    "chatRoomId": "18061832422@chatroom",
    "newMsgId": 7175333311063873621,
    "xml":"<sysmsg type=\"NewXmlChatRoomAccessVerifyApplication\">\n\t<NewXmlChatRoomAccessVerifyApplication>\n\t\t<text><![CDATA[\"朝夕\"想邀请1位朋友加入群聊]]></text>\n\t\t<link>\n\t\t\t<scene>roomaccessapplycheck_18061832422@chatroom</scene>\n\t\t\t<data>\n\t\t\t\t<roomid>18061832422@chatroom</roomid>\n\t\t\t\t<inviter>wxid_ylxtflcg0p8b22</inviter>\n\t\t\t\t<userlist>wxid_i6qsbbjenjuj22</userlist>\n\t\t\t\t<applyreason>拉个发小入群</applyreason>\n\t\t\t</data>\n\t\t</link>\n\t</NewXmlChatRoomAccessVerifyApplication>\n</sysmsg>"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||

### 添加群成员为好友：https://wkteam.cn/api-wen-dang2/qun-cao-zuo/addRoomMemberFriend.html

标题：添加群成员为好友

内容：
添加群成员为好友
请求URL：
```http://域名/addRoomMemberFriend```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|chatRoomId|是|String|群id|
|memberWcId|是|String|群成员的wcId|
|content|否|String|申请消息|
请求参数示例
```
{
    "wId": "2c7a5bf6-e23d-4fb0-8f03-b90e844b539f",
    "chatRoomId": "18061832422@chatroom",
    "memberWcId": "wxid_9z89xz56uie22",
    "content": "你好"
}
```
成功返回示例
```
{
    "message": "添加群好友成功",
    "code": "1000",
    "data": "v3_020b3826fd030100000000003c5f0ccafe295400000001ea9a3dba12f95f6b60a0536a1adb690dcccc9bf58cc80765e6eb16b92b937608dcb0a9222f1d6f88492af63e5d2b8a1fd5aa9174f287a8a9dcc631bd81887305777604164a9b37af964bf@stranger"
}
```
错误返回示例
```
{
    "message": "由于对方的隐私设置，你无法通过群聊将其添加至通讯录。",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|String|添加好友凭证|

### 朋友圈点赞：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsPraise.html

标题：朋友圈点赞

内容：
朋友圈点赞
简要描述：
朋友圈点赞
请求URL：
```http://域名地址/snsPraise```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈Id|
请求参数示例
```
{
    "wId": "0000016e-abcd-0ea8-0002-d8c2dfdb0bf3",
    "id": "13205404970681503871"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 取消点赞：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsCancelPraise.html

标题：取消点赞

内容：
取消点赞
简要描述：
取消点赞
请求URL：
```http://域名地址/snsCancelPraise```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈id|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "id": "13351161735026061409"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 朋友圈评论：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsComment.html

标题：朋友圈评论

内容：
朋友圈评论
简要描述：
朋友圈评论
请求URL：
```http://域名地址/snsComment```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈标识|
|replyCommentId|是|int|评论标识（回复评论）|
|content|是|String|内容|
请求参数示例
```
{
    "wId": "0000016e-abcd-0ea8-0002-d8c2dfdb0bf3",
    "id": "13205404970681503871",
    "replyCommentId" : 0,
    "content": "充满力量"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|int|1000成功，1001失败|
|msg|String|反馈信息|

### 删除朋友圈评论：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsCommentDel.html

标题：删除某条朋友圈的某条评论

内容：
删除某条朋友圈的某条评论
简要描述：
删除某条朋友圈的某条评论
请求URL：
```http://域名地址/snsCommentDel```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈id|
|commentId|是|int|评论id|
请求参数示例
```
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "id": "13341784993555026081",
    "commentId": "227"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 获取自己朋友圈：https://wkteam.cn/api-wen-dang2/peng-you-quan/getCircle.html

标题：获取朋友圈

内容：
获取朋友圈
简要描述：
获取朋友圈
请求URL：
```http://域名地址/getCircle```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|firstPageMd5|是|String|首次传""，第2页及以后传返回的firstPageMd5 （PS：firstPageMd5为null情况下，则用上次不为null的值）|
|maxId|是|long|首次传0 （PS：第2页及以后用返回数据最后一个条目的id）|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "firstPageMd5": "",
     "maxId" : 0
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": {         "sns": [             {                 "id": "13351211557386072142",                 "userName": "wxid_6tn88z16x6ou12",                 "nickName": "远见",                 "createTime": 1591588444,                 "content": "测试朋友圈内容",                 "imgList": [                     "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/0"                 ],                 "commentList": [                     {                         "id": 227,                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见",                         "createTime": 1591588444,                         "content": "测试朋友圈内容"                     }                 ],                 "likeList": [                     {                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见"                     }                 ]             },             {                 "id": "13351211557386072141",                 "userName": "wxid_6tn88z16x6ou12",                 "nickName": "远见",                 "createTime": 1591588444,                 "content": "测试朋友圈内容",                 "imgList": [                     "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/0"                 ],                 "commentList": [                     {                         "id": 227,                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见",                         "createTime": 1591588444,                         "content": "测试朋友圈内容"                     }                 ],                 "likeList": [                     {                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见"                     }                 ]             }         ],         "firstPageMd5": "13351211557386072142"     } }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|sns|JSONArray||
|id|String|朋友圈id|
|userName|String|发圈人微信号|
|nickName|String|发圈人昵称|
|createTime|long|发布时间|
|content|String|内容|
|imgList|JSONArray|图片列表|
|commentList|JSONArray|评论列表|
|likeList|JSONArray|点赞列表|
|firstPageMd5|String|第一页md5值 （用于翻页）|

### 获取好友朋友圈：https://wkteam.cn/api-wen-dang2/peng-you-quan/getFriendCircle.html

标题：获取某个好友的朋友圈

内容：
获取某个好友的朋友圈
简要描述：
获取某个好友的朋友圈
本接口返回xml的图片与视频链接无法直接查看，需要调用 获取某条朋友圈详细内容 接口查看
请求URL：
```http://域名地址/getFriendCircle```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|微信id|
|firstPageMd5|是|String|首页传:""，第2页及以后传返回的firstPageMd5 （PS：firstPageMd5为null情况下，则用上次不为null的值）|
|maxId|是|long|首页传：0（PS：第2页及以后用返回数据最后一个条目的id）|
请求参数示例
```
{
    "wId": "0000016e-68f9-99d5-0002-3a1cd9eaaa17",
    "wcId": "xxxxxx",
    "firstPageMd5": "",
    "maxId":0
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": {         "sns": [              {                 "id": "xxxxxx",                 "userName": "wxid_6tn88z16x6ou12",                 "nickName": "远见",                 "createTime": 1591588444,                 "content": "测试朋友圈内容",                 "imgList": [                     "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/0"                 ],                 "commentList": [                     {                         "id": 227,                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见",                         "createTime": 1591588444,                         "content": "测试朋友圈内容"                     }                 ],                 "likeList": [                     {                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见"                     }                 ]             },             {                 "id": "xxxxxx",                 "userName": "wxid_6tn88z16x6ou12",                 "nickName": "远见",                 "createTime": 1591588444,                 "content": "测试朋友圈内容",                 "imgList": [                     "http://wx.qlogo.cn/mmhead/ver_1/5Aiaticzwasiac9drMyibhHrDRIsadlS4sKWp4ia3QdaKfAe6RcOhHjTtk0qzJTEQagNTM1R4WZVvAvqVMn02DGrIOEj2ZQwDD0HzHyq95Nc5zlw/0"                 ],                 "commentList": [                     {                         "id": 227,                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见",                         "createTime": 1591588444,                         "content": "测试朋友圈内容"                     }                 ],                 "likeList": [                     {                         "userName": "wxid_6tn88z16x6ou12",                         "nickName": "远见"                     }                 ]             }         ],         "firstPageMd5": "13351211557386072142"     } }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|sns|JSONArray||
|id|String|朋友圈id|
|userName|String|发圈人微信号|
|nickName|String|发圈人昵称|
|createTime|long|发布时间|
|content|String|内容|
|imgList|JSONArray|图片列表|
|commentList|JSONArray|评论列表|
|likeList|JSONArray|点赞列表|
|firstPageMd5|String|第一页md5值 （用于翻页）|

### 获取某条朋友圈详情：https://wkteam.cn/api-wen-dang2/peng-you-quan/getSnsObject.html

标题：获取某条朋友圈详细内容

内容：
获取某条朋友圈详细内容
简要描述：
获取某条朋友圈详细内容
请求URL：
```http://域名地址/getSnsObject```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|好友微信id|
|id|是|String|朋友圈标识|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "wcId": "wxid_6tn88z16x6ou12",
     "id": 13351161735026061409
}
```
成功返回示例
{     "message": "成功",     "code": "1000",     "data": {         "id": "xxxxxx",         "userName": "xxxxxx",         "nickName": "xxxxxx",         "createTime": xxxxxx,         "objectDesc": {             "xml": "xxxxxx",             "len": xxxxxx         },         "likeCount": xxxxxx,         "snsLikes": [             {                 "userName": "xxxxxx",                 "nickName": "xxxxxx"             }         ],         "snsComments": [             {                 "userName": "xxxxxx",                 "nickName": "xxxxxx",                 "content": "xxxxxx"             }         ]     } }
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|id|String|朋友圈id|
|userName|String|发圈人微信号|
|nickName|String|发圈人昵称|
|createTime|long|发布时间|
|objectDesc|JSONObject||
|xml|String|朋友圈xml内容|
|len|int|xml长度|
|likeCount|int|点赞数|
|snsLikes|JSONArray|点赞列表|
|snsComments|JSONArray|评论列表|

### 发送文字朋友圈消息：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsSend.html

标题：发送文字朋友圈消息

内容：
发送文字朋友圈消息
微信需在线三天后使用本接口，否则微信团队会提示不可使用副设备发送
简要描述：
发送文字朋友圈消息
请求URL：
```http://域名地址/snsSend```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|文本内容|
|groupUser|否|String|对谁可见（传微信id,多个用,分隔）|
|blackList|否|String|对谁不可见（传微信id,多个用,分隔）|
|groupUserLabelIds|否|String|对谁可见（传标签id,多个用,分隔）|
|blackListLabelIds|否|String|对谁不可见（传标签id,多个用,分隔）|
请求参数示例
```
{
    "wId": "0000016e-6343-089e-0001-e2ef939176f6",
    "content": "这是一条文本朋友圈消息"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "id": "13200538655176208452",
        "userName": "wxid_6mq1pu8ngj3r22",
        "createTime": 1573626834,
        "objectDesc": "这是一条文本朋友圈消息"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|
|data|||
|id|String|ID|
|userName|String|微信id|
|createTime|String|发送时间|
|objectDesc|String|内容|

### 发送链接朋友圈消息：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsSendUrl.html

标题：发送链接朋友圈消息

内容：
Attention 
微信需在线三天后使用本接口，否则微信团队会提示不可使用副设备发送
简要描述:
发送链接朋友圈消息
请求URL:
http://域名地址/snsSendUrl
请求方式:
POST
请求头Headers:
Content-Type:application/json
Authorization:login接口返回
参数:
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|文本内容|
|title|是|String|标题|
|description|是|String|描述|
|url|是|String|url|
|thumbUrl|是|String|缩略图url|
|groupUser|否|String|对谁可见(传微信id,多个用,分隔)|
|blackList|否|String|对谁不可见(传微信id,多个用,分隔)|
|groupUserLabelIds|否|String|对谁可见(传标签id,多个用,分隔)|
|blackListLabelIds|否|String|对谁不可见(传标签id,多个用,分隔)|
请求参数示例
```
{
"wId" : "25dea7a4-ddea-40c6-87ad-3e982e998921" , 
"content" : "测试" , 
"title" : "jr加盟湖人" , 
"description" : "ddd" , 
"url" : "https://mp.weixin.qq.com/s/cxJ7pLvkRwBV_NUfQxNEjA" , 
"thumbUrl" : "http://dmjvip.oss-cn-shenzhen.aliyuncs.com/download/dailyimg/20200618155919206.jpg" 
}
```
成功返回示例
```
{
"message" : "成功" , 
"code" : "1000" , 
"data" : { 
"id" : "13200538655176208452" , 
"userName" : "wxid_6mq1pu8ngj3r22" , 
"createTime" : 1573626834 , 
"objectDesc" : "这是一条文本朋友圈消息" 
} 
}
```
错误返回示例
```
{
"message" : "失败" , 
"code" : "1001" , 
"data" : null 
}
```
返回数据:
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|
|data|||
|id|String|ID|
|userName|String|微信id|
|createTime|String|发送时间|
|objectDesc|String|内容|

### 发送图片朋友圈消息：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsSendImage.html

标题：发送图片朋友圈消息

内容：
发送图片朋友圈消息
微信需在线三天后使用本接口，否则微信团队会提示不可使用副设备发送
简要描述：
发送图片朋友圈消息
请求URL：
```http://域名地址/snsSendImage```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|文本内容|
|paths|是|String|图片url(多个用;分隔 单张图片最大3M以内,必须是png格式)|
|groupUser|否|String|对谁可见（传微信id,多个用,分隔）|
|blackList|否|String|对谁不可见（传微信id,多个用,分隔）|
|groupUserLabelIds|否|String|对谁可见（传标签id,多个用,分隔）|
|blackListLabelIds|否|String|对谁不可见（传标签id,多个用,分隔）|
请求参数示例
```
{
    "wId": "0000016e-6382-4be4-0001-e6b4e3cd1bbd",
    "content": "这是一条图文朋友圈消息",
    "paths": "http://1563240615548.jpg;http://1563253424796.png"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "status": 1,
        "object": {
            "id": "xxxxxx",
            "userName": "xxxxxx",
            "createTime": xxxxxx,
            "objectDesc": "xxxxxx"
        }
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|
|data|||
|id|String|ID|
|userName|String|微信id|
|createTime|String|发送时间|
|objectDesc|String|内容|

### 发送视频朋友圈消息：https://wkteam.cn/api-wen-dang2/peng-you-quan/asynSnsSendVideo.html

标题：异步发送视频朋友圈

内容：
异步发送视频朋友圈
微信需在线三天后使用本接口，否则微信团队会提示不可使用副设备发送
简要描述：
异步发送视频朋友圈
请求URL：
```http://域名地址/asynSnsSendVideo```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|文本内容|
|videoPath|是|String|视频链接URL 最大支持20M且30秒内|
|thumbPath|是|String|视频封面URL 最大支持2M内|
|groupUser|否|String|对谁可见（传微信号,多个用,分隔）|
|blackList|否|String|对谁不可见（传微信号,多个用.分隔）|
请求参数示例
```
{
    "wId": "0000016e-68f9-99d5-0002-3a1cd9eaaa17",
    "content": "今天还是可以的",
    "videoPath": "https://wkgjonlines.oss-cn-shenzhen.aliyuncs.com/movies/20191113/d7c616569ac342ad1fa8e3301682844e.mp4",
    "thumbPath": "http://cdn.duitang.com/uploads/item/201412/21/20141221161645_2MSeA.jpeg"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "asynId": "04d0af77-3877-4621-85ce-c8bee6a460e4"
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|
|data|||
|asynId|String|异步发送视频朋友圈asynId，可用此参数获取发送视频朋友圈结果|

### 获取发送视频朋友圈结果：https://wkteam.cn/api-wen-dang2/peng-you-quan/getAsynSnsSendVideoRes.html

标题：获取发送视频朋友圈结果

内容：
获取发送视频朋友圈结果
简要描述：
获取发送视频朋友圈结果
调用 异步发送视频朋友圈 后可调用本接口获取发送结果，建议每间隔两秒获取一次，或可通过 回调消息 获取。
请求URL：
```http://域名地址/getAsynSnsSendVideoRes```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|asynId|是|String|异步发送视频朋友圈返回的asynId|
请求参数示例
```
{
    "asynId": "04d0af77-3877-4621-85ce-c8bee6a460e4"
}
```
成功返回示例
```
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "asynId": "04d0af77-3877-4621-85ce-c8bee6a460e4",
        "type": 1,
        "id": "13768766054025736468",
        "userName": "wxid_phyyedw9xap22",
        "createTime": 1641364819,
        "objectDesc": "今天还是可以的",
        "des": null
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|
|data|||
|data.asynId|String|异步发送视频朋友圈asynId|
|data.type|int|发送状态。0：发送中、1：发送完成、2：发送失败|
|data.id|string|朋友圈id|
|data.userName|string|发送微信id|
|data.createTime|long|发送时间|
|data.objectDesc|string|朋友圈文字|
|data.des|string|描述|

### 转发朋友圈：https://wkteam.cn/api-wen-dang2/peng-you-quan/forwardSns.html

标题：转发朋友圈

内容：
转发朋友圈
Attention
微信需在线三天后使用本接口，否则微信团队会提示不可使用副设备发送
本接口可以转发除了图片任意朋友圈内容，图片朋友圈建议调用发布接口
简要描述：
转发朋友圈，直接xml数据。(对谁不可见)
请求URL：
```http://域名地址/forwardSns```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|收到的xml|
|blackList|否|String|对谁不可见（传微信id,多个用,分隔）|
|withUserList|否|String|对谁可见 （传微信id,多个用,分隔）|
请求参数示例
```
{
     "wId": "xxxxxxx",
     "content": "xxxxxxx"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": {
        "id": "xxxxxxx",
        "userName": "xxxxxxx",
        "nickName": "xxxxxxx",
        "createTime": xxxxxxx,
        "objectDesc": {
            "xml": "xxxxxxx",
            "len": xxxxxxx
        },
        "likeCount": xxxxxxx,
        "snsLikes": [
            {
                "userName": "xxxxxxx",
                "nickName": "xxxxxxx"
            }
        ],
        "snsComments": [
            {
                "userName": "xxxxxxx",
                "nickName": "xxxxxxx",
                "content": "xxxxxxx"
            }
        ]
    }
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功 1001失败|
|msg|String|反馈信息|
|data|JSONObject||
|id|String|朋友圈id|
|userName|String|发圈人微信号|
|nickName|String|发圈人昵称|
|createTime|long|发布时间|
|objectDesc|JSONObject||
|xml|String|朋友圈xml内容|
|len|int|xml长度|
|likeCount|int|点赞数|
|snsLikes|JSONArray|点赞列表|
|snsComments|JSONArray|评论列表|

### 删除朋友圈：https://wkteam.cn/api-wen-dang2/peng-you-quan/deleteSns.html

标题：删除朋友圈

内容：
删除朋友圈
简要描述：
删除朋友圈
请求URL：
```http://域名地址/deleteSns```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈id|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "id": "13351161735026061409"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 设置朋友圈权限：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsPrivacySettings.html

标题：朋友圈权限设置

内容：
朋友圈权限设置
Note
本接口设置成功后,效果立即生效，手机端展示会有延迟，可等待30S杀掉后台重启查看
请求URL：
```http://域名地址/snsPrivacySettings```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|type|是|int|10: 允许好友查看全部朋友圈 11：允许好友查看近半年 12：允许好友查看近一个月 13：允许好友查看近3天 20：允许陌生人查看十条朋友圈 21：不允许陌生人查看朋友圈|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "type": 11
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 设置某条朋友圈为私密：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsSetAsPrivacy.html

标题：设置某条朋友圈为隐私

内容：
设置某条朋友圈为隐私
简要描述：
设置某条朋友圈为隐私
请求URL：
```http://域名地址/snsSetAsPrivacy```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈id|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "id": "13351161735026061409"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 设置某条朋友圈为公开：https://wkteam.cn/api-wen-dang2/peng-you-quan/snsSetPublic.html

标题：设置某条朋友圈为公开

内容：
设置某条朋友圈为公开
简要描述：
设置某条朋友圈为公开
请求URL：
```http://域名地址/snsSetPublic```
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|String|朋友圈id|
请求参数示例
```
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "id": "13351161735026061409"
}
```
成功返回示例
```
{
    "message": "成功",
    "code": "1000",
    "data": null
}
```
错误返回示例
```
{
    "message": "失败",
    "code": "1001",
    "data": null
}
```
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|String|反馈信息|

### 下载朋友圈视频：https://wkteam.cn/api-wen-dang2/peng-you-quan/downloadSnsVideo.html

#### 标题：下载朋友圈视频

#### 内容：
下载朋友圈视频
简要描述：
下载朋友圈视频
请求URL：
<代码开始>http://域名地址/downloadSnsVideo<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|通过获取某条朋友圈详细内容接口[/getSnsObject]返回的xml|
请求参数示例
<代码开始>
{
     "wId": "b7ad08a6-77c2-4ad6-894a-29993b84c0e4",
     "content": "<TimelineObject><id><![CDATA[14038975351875178589]]></id><username><![CDATA[wxid_xupxubvp9l0322]]></username><createTime><![CDATA[1673576277]]></createTime><contentDescShowType>0</contentDescShowType><contentDescScene>0</contentDescScene><private><![CDATA[0]]></private><contentDesc><![CDATA[牛初乳，\n我只推荐优乐彤。\n配料表非常干净，\n什么都不添加。\n纯的牛初乳[强][强][强]\n只有优乐彤。\n​]]></contentDesc><contentattr><![CDATA[0]]></contentattr><sourceUserName></sourceUserName><sourceNickName></sourceNickName><statisticsData></statisticsData><weappInfo><appUserName></appUserName><pagePath></pagePath><version><![CDATA[0]]></version><debugMode><![CDATA[0]]></debugMode><shareActionId></shareActionId><isGame><![CDATA[0]]></isGame><messageExtraData></messageExtraData><subType><![CDATA[0]]></subType><preloadResources></preloadResources></weappInfo><canvasInfoXml></canvasInfoXml><ContentObject><contentStyle><![CDATA[15]]></contentStyle><contentSubStyle><![CDATA[0]]></contentSubStyle><title>微信小视频</title><description></description><contentUrl>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/common_page__upgrade&amp;v=1</contentUrl><mediaList><media><id><![CDATA[14038975352558194743]]></id><type><![CDATA[6]]></type><title><![CDATA[牛初乳，\n我只推荐优乐彤。\n配料表非常干净，\n什么都不添加。\n纯的牛初乳[强][强][强]\n只有优乐彤。\n​]]></title><description><![CDATA[牛初乳，\n我只推荐优乐彤。\n配料表非常干净，\n什么都不添加。\n纯的牛初乳[强][强][强]\n只有优乐彤。\n​]]></description><private><![CDATA[0]]></private><url videomd5=\"6996ba1ff53f976ae5b7da32f1d7a322\" type=\"1\" md5=\"4b9d4d9724363355b713bfc0af924e84\"><![CDATA[http://shzjwxsns.video.qq.com/102/20202/snsvideodownload?filekey=30340201010420301e0201660402534804104b9d4d9724363355b713bfc0af924e8402030f1f54040d00000004627466730000000132&hy=SH&storeid=563c0bf5400001c5a163c605c0000006600004eea534823fe7b01e64626bce&dotrans=9&ef=30_0&bizid=1023&ilogo=2&dur=7&sid=171]]></url><thumb type=\"1\"><![CDATA[http://vweixinthumb.tc.qq.com/150/20250/snsvideodownload?filekey=30350201010421301f02020096040253480410ed875ce3adf71ceab81ff6d9b72d17b7020301851e040d00000004627466730000000132&hy=SH&storeid=563c0bf5400001370163c605c0000009600004f1a5348240348e0b647aff0d&bizid=1023]]></thumb><videoDuration><![CDATA[7.241]]></videoDuration><size totalSize=\"99614.0\" width=\"1080\" height=\"1920\"></size><VideoColdDLRule><All>CAISBAgWEAEoAjAc</All></VideoColdDLRule></media></mediaList></ContentObject><actionInfo><appMsg><mediaTagName></mediaTagName><messageExt></messageExt><messageAction></messageAction></appMsg></actionInfo><appInfo><id></id></appInfo><location poiClassifyId=\"\" poiName=\"\" poiAddress=\"\" poiClassifyType=\"0\" city=\"\"></location><publicUserName></publicUserName><streamvideo><streamvideourl></streamvideourl><streamvideothumburl></streamvideothumburl><streamvideoweburl></streamvideoweburl></streamvideo></TimelineObject>"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "下载朋友圈视频成功",
    "data": {
        "videoUrl": "http://oos-sccd.ctyunapi.cn/20230116/478bbcfd-169a-4cfc-b768-f51f24f442fb.mp4?AWSAccessKeyId=e14b8966201775518bce&Expires=1674483076&Signature=dqPDwjl4mq6RvPsPuFdWXJOQRf0%3D"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|message|String|反馈信息|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderFollow.html

#### 标题：关注

#### 内容：
关注
请求URL：
<代码开始>http://域名/finderFollow<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|userName|是|String|视频号用户的编码（搜索接口返回的userName）|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|type|是|int|操作类型<br>1:关注<br>2:取消关注|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "meUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "userName": "v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder",
    "meRoleType": 3,
    "type": 1
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "userName": "v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder",         "nickName": "中国日报",         "headUrl": "https://wx.qlogo.cn/finderhead/ver_1/BuStUlORBaLHz4E85tq01bL7icWQJ25baldDJ8Ky5114GtJXVvibDjpZuPQrfALbF1EZU5vzAnS0GnObG4whzdBmbWyKHiblHib6RC5JaGdj0FM/0",         "signature": "",         "authInfo": {             "authIconType": 2,             "authProfession": "中国日报社",             "detailLink": "pages/index/index.html?showdetail=true&username=v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder",             "appName": "gh_4ee148a6ecaa@app"         },         "extInfo": {             "country": "CN",             "province": "",             "city": "",             "sex": null         },         "userMode": null,         "bindInfo": "CAESwwIKwAIKD2doXzM3MDdkOTU0MWMzZhIM5Lit5Zu95pel5oqlGpQBaHR0cHM6Ly93eC5xbG9nby5jbi9tbWhlYWQvdmVyXzEvQnVTdFVsT1JCYUxIejRFODV0cTAxYzQ1ZVEybjhpY3MwQVZKNFM5SHd5R3lVQ1pyanlLRWliZ3ExMVhkcUxHZmtpYUh3OXRpY0tRdEFjZ1Nsc3FaT1VLRGF2TUNQZlR4cmd6bmIyR2hLWTlzTm53LzEzMjKHAQgCEoIBaHR0cHM6Ly9kbGRpcjF2Ni5xcS5jb20vd2VpeGluL2NoZWNrcmVzdXBkYXRlL2ljb25zX2ZpbGxlZF9jaGFubmVsc19hdXRoZW50aWNhdGlvbl9lbnRlcnByaXNlX2EyNjU4MDMyMzY4MjQ1NjM5ZTY2NmZiMTE1MzNhNjAwLnBuZw=="     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderBrowse.html

#### 标题：浏览

#### 内容：
浏览
请求URL：
<代码开始>http://域名/finderBrowse<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|objectId|是|bigint|视频号作品id|
|objectNonceId|是|String|视频号作品nonceId|
|sessionBuffer|是|String|通过获取用户主页返回的sessionBuffer|
|myUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|myRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
请求参数示例
<代码开始>
{
{
    "wId": "{{wId}}",
    "objectId": 14200825020179157073,
    "objectNonceId": "1614432821379421505_0_0_2_2_0",
    "sessionBuffer": "eyJzZXNzaW9uX2lkIjoic2lkXzEzNDUxMDA3MDNfMTY5NDQ5OTM1NzE4MDM0M18yMDI4Mjg4MDM0IiwiY3VyX2xpa2VfY291bnQiOjY2Mzc1NiwiY3VyX2NvbW1lbnQiOjU1MjYsInJlY2FsbF90eXBlcyI6W10sImRlbGl2ZXJ5X3NjZW5lIjoyLCJkZWxpdmVyeV90aW1lIjoxNjk0NDk5MzU3LCJzZXRfY29uZGl0aW9uX2ZsYWciOjksImZyaWVuZF9jb21tZW50X2luZm8iOnsibGFzdF9mcmllbmRfdXNlcm5hbWUiOiJ3eGlkX2k2cXNiYmplbmp1ajIyIiwibGFzdF9mcmllbmRfbGlrZV90aW1lIjoxNjk0NDk1ODI0fSwidG90YWxfZnJpZW5kX2xpa2VfY291bnQiOjEsInJlY2FsbF90eXBlcyI6W10sIm1lZGlhX3R5cGUiOjQsInZpZF9sZW4iOjYxLCJjcmVhdGVfdGltZSI6MTY5Mjg3MDI2MSwicmVjYWxsX2luZm8iOltdLCJzZWNyZXRlX2RhdGEiOiJCZ0FBTnBUcXRwUEZ6U3V0ZXZFakJlSDdzdXJjWUN1TmdsWlQrMXc0bnpDTlwvY1ZOb0lyeFFnbUhtTDNaa1ZIOThaZm9JRXJJR3ZOME81K0gyVzh2dk1YSkx0c0R0NFJrV2c9PSIsImlkYyI6MSwiZGV2aWNlX3R5cGVfaWQiOjEzLCJkZXZpY2VfcGxhdGZvcm0iOiJpUGFkMTMsMTkiLCJmZWVkX3BvcyI6MCwiY2xpZW50X3JlcG9ydF9idWZmIjoie1wiaWZfc3BsaXRfc2NyZWVuX2lwYWRcIjowLFwiZW50ZXJTb3VyY2VJbmZvXCI6XCJ7XFxcImZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdDb250cm9sbGVyXzE2OTQ0OTkyNzIxNDQjJDBfMTY5NDQ5OTI1OTUwOCNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTkzNTY3NDcsInJzc3RtIjoxNjk0NDk5MzU3MjEwLCJycWN0bSI6MTY5NDQ5OTI4MDQwMiwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDk5Mjc3MjcwIiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9",
    "myUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "myRoleType": "3"
} 
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号浏览成功",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderComment.html

#### 标题：评论

#### 内容：
评论
请求URL：
<代码开始>http://域名/finderComment<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|评论内容|
|id|是|bigint|视频号作品id|
|objectNonceId|是|String|视频号作品nonceId|
|type|是|int|操作类型<br>0:评论<br>1:删除评论|
|sessionBuffer|是|String|通过获取用户主页返回的sessionBuffer|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|refCommentId|否|bigint|回复的评论id|
请求参数示例
<代码开始>
{
{
    "wId": "{{wId}}",
    "content": "日本有点过分",
    "id": 14200825020179157073,
    "objectNonceId": "",
    "type": 0,
    "sessionBuffer": "eyJzZXNzaW9uX2lkIjoic2lkXzEzNDUxMDA3MDNfMTY5NDQ5OTM1NzE4MDM0M18yMDI4Mjg4MDM0IiwiY3VyX2xpa2VfY291bnQiOjY2Mzc1NiwiY3VyX2NvbW1lbnRfY291bnQiOjU1MjYsInJlY2FsbF90eXBlcyI6W10sImRlbGl2ZXJ5X3NjZW5lIjoyLCJkZWxpdmVyeV90aW1lIjoxNjk0NDk5MzU3LCJzZXRfY29uZGl0aW9uX2ZsYWciOjksImZyaWVuZF9jb21tZW50X2luZm8iOnsibGFzdF9mcmllbmRfdXNlcm5hbWUiOiJ3eGlkX2k2cXNiYmplbmp1ajIyIiwibGFzdF9mcmllbmRfbGlrZV90aW1lIjoxNjk0NDk1ODI0fSwidG90YWxfZnJpZW5kX2xpa2VfY291bnQiOjEsInJlY2FsbF9pbmRleCI6W10sIm1lZGlhX3R5cGUiOjQsInZpZF9sZW4iOjYxLCJjcmVhdGVfdGltZSI6MTY5Mjg3MDI2MSwicmVjYWxsX2luZm8iOltdLCJzZWNyZXRlX2RhdGEiOiJCZ0FBTnBUcXRwUEZ6U3V0ZXZFakJlSDdzdXJjWUN1TmdsWlQrMXc0bnpDTlwvY1ZOb0lyeFFnbUhtTDNaa1ZIOThaZm9JRXJJR3ZOME81K0gyVzh2dk1YSkx0c0R0NFJrV2c9PSIsImlkYyI6MSwiZGV2aWNlX3R5cGVfaWQiOjEzLCJkZXZpY2VfcGxhdGZvcm0iOiJpUGFkMTMsMTkiLCJmZWVkX3BvcyI6MCwiY2xpZW50X3JlcG9ydF9idWZmIjoie1wiaWZfc3BsaXRfc2NyZWVuX2lwYWRcIjowLFwiZW50ZXJTb3VyY2VJbmZvXCI6XCJ7XFxcImZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdFbXB0eVZpZXdDb250cm9sbGVyXzE2OTQ0OTkyNzIxNDQjJDBfMTY5NDQ5OTI1OTUwOCNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTkzNTY3NDcsInJzc3RtIjoxNjk0NDk5MzU3MjEwLCJycWN0bSI6MTY5NDQ5OTI4MDQwMiwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDk5Mjc3MjcwIiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9",
    "meUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "meRoleType": "3"
} 
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号评论成功",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderPublish.html

#### 标题：发布视频号

#### 内容：
发布视频号
请求URL：
<代码开始>http://域名/finderPublish<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|myUserName|是|String|自己的用户编码|
|videoUrl|是|String|视频链接|
|videoThumbUrl|是|String|封面链接|
|videoWidth|是|int|视频宽度|
|videoHeight|是|int|视频高度|
|videoPlayLen|是|int|视频播放时长，单位秒|
|title|是|String|标题|
|topic|是|String[]|话题|
|videoCdn|否|JSONObject|通过“上传视频号视频”接口获取|
|videoCdn.fileUrl|是|String|通过“上传视频号视频”接口获取|
|videoCdn.thumbUrl|是|String|通过“上传视频号视频”接口获取|
|videoCdn.mp4Identify|是|String|通过“上传视频号视频”接口获取|
|videoCdn.fileSize|是|int|通过“上传视频号视频”接口获取|
|videoCdn.thumbMd5|是|String|通过“上传视频号视频”接口获取|
|videoCdn.fileKey|是|String|通过“上传视频号视频”接口获取|
直发请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "myUserName": "v2_060000231003b20faec8c6e18f10c7d6c903ec3db0776955d3d97c6b329d6aa58693bcdb7ad1@finder",
    "videoUrl": "https://30&q-header-list=&q-url-param-list=&q-signature=e7a03064c2f701137570a525e6650631d8baf4be",
    "videoThumbUrl": "https://p92309022220191E400011D6391597E30B",
    "videoWidth": 1240,
    "videoHeight": 930,
    "videoPlayLen": 13,
    "title": "可爱吗？",
    "topic": [
        "#可爱",
        "#hhh"
    ]
}
<代码结束>
使用“上传视频号视频”获取数据请求参数示例
{     "wId": "{{wId}}",     "myUserName": "v2_060000231003b20faec8c6e18f10c7d6c903ec3db0776955d3d97c6b329d6aa58693bcdb7ad1@finder",     "myRoleType":3,     "videoUrl": "",     "videoThumbUrl": "",     "videoWidth": 1240,     "videoHeight": 930,     "videoPlayLen": 13,     "videoCdn": {         "fileUrl": "http://wxapp.tc.qq.com/251/20302/stodownload?a=1&bizid=1023&dotrans=0&encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Zn59wWGKtPKU7XeFey6Gmk5u2BLznEOJrqFUy37lHx0tFeRFc5SWn3wFichT2XyDjTHjOjzDDXk4DfMaBwxrjlB9gDrgiaPA3SqYMBmCsxQUp6E&hy=SZ&idx=1&m=6e95f9d79588843ac259b780f0cbf20f&token=cztXnd9GyrGDyHSr4tfFhrIZAEiaP5UUR4XdicQO9R2mT4KR2hmbPzozzC6CLYI280ibz3wvaVeCAKzic01dGVPECQ&upid=290150",         "thumbUrl": "http://wxapp.tc.qq.com/251/20350/stodownload?m=cc9a86fb446c70f5ed6c16ca7754f4c1&filekey=30350201010421301f020200fb0402535a0410cc9a86fb446c70f5ed6c16ca7754f4c102030090c2040d00000004627466730000000132&hy=SZ&storeid=565253eba000b8def8399cc84000000fb00004f7e535a16a64bc1e046ae409&dotrans=0&bizid=1023",         "mp4Identify": "f554da4964c2c64ad97dc9623f5daa5b",         "fileSize": 1315979,         "thumbMd5": "cc9a86fb446c70f5ed6c16ca7754f4c1",         "fileKey": "finder_upload_8147263162_zhangchuan2288"     },     "title": "可爱吗？",     "topic": [         "#可爱",         "#hhh"     ] }
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号发布成功",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderUserHome.html

#### 标题：获取用户主页

#### 内容：
获取用户主页
请求URL：
<代码开始>http://域名/finderUserHome<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|userName|是|String|视频号用户的编码（搜索接口返回的userName）|
|pageCode|否|String|分页参数，首次传空，获取下一页时传响应中返回的pageCode|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "userName": "v2_060000231003b20faec8c6e48f11c7d2c901e531b077a130b51b4788d2af47a9375e2e776c46@finder",
    "pageCode": ""
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "videoList": [{             "id": 13897021715463014565,             "nickName": "摩托欧耶",             "userName": "v2_060000231003b20faec8c6e48f11c7d2c901e531b077a130b51b4788d2af47a9375e2e776c46@finder",             "createTime": 1656654085,             "forwardCount": 96,             "likeCount": 114,             "commentCount": 64,             "friendLikeCount": 0,             "objectNonceId": "10833141393859919979_0_0_2_1",             "sessionBuffer": "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

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getFollowList.html

#### 标题：获取关注列表

#### 内容：
获取关注列表
请求URL：
<代码开始>http://域名/finder/getFollowList<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|lastBuff|否|String|首次传空，后续传接口返回的lastBuffer|
|myUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|myRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "lastBuff": "",
    "myUserName":"v2_060000231003b20faec8cae18cb07ea33b077ba915250774edbea38082ea6b24af229@finder",
    "myRoleType": 3
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "获取关注列表成功",     "data": {         "contactList": [             {                 "clubInfo": {},                 "spamStatus": 0,                 "liveInfo": {                     "switchFlag": 53727,                     "micSetting": {},                     "anchorStatusFlag": 133248,                     "lotterySetting": {                         "attendType": 4,                         "settingFlag": 0                     }                 },                 "signature": "。。。",                 "headUrl": "https://wx.qlogo.cn/finderhead/ver_1/vfEnTh99QtHJXzs4hA4iar7vIRQGqZ4esmpUKlbGF2enPDVeUAUkPqonibqiaNlgaO5UZGX5FZ2rQuZec6Lrq74KPcqL9JPmnsCBrJlOGFJs/0",                 "authInfo": {},                 "extInfo": {                     "country": "CN",                     "province": "",                     "city": "",                     "sex": 2                 },                 "coverImgUrl": "",                 "extFlag": 262156,                 "followTime": 1718847606,                 "liveCoverImgUrl": "http://wxapp.tc.qq.com/251/20350/stodownload?m=be88b1cb981aa72b3328ccbd22a58e0b&filekey=30340201010420301e020200fb0403480410be88b1cb981aa72b3328ccbd22a58e0b02022814040d00000004627466730000000132&hy=SH&storeid=5649443df0009b8a38399cc84000000fb00004f7e534815c008e0b08dc805c&dotrans=0&bizid=1023",                 "nickname": "朝vvvv",                 "followFlag": 1,                 "liveStatus": 2,                 "username": "v2_060000231003b20faec8c6e18f10c7d6c903ec776955d3d97c6b329d6aa58693bcdb7ad1@finder",                 "status": 0             }         ],         "lastBuffer": "CL+fAxu8+qqBg==",         "followCount": 1,         "continueFlag": 1     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getMentionList.html

#### 标题：消息列表

#### 内容：
消息列表
请求URL：
<代码开始>http://域名/finder/getMentionList<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|lastBuff|是|String|翻页的key，首次传空，翻页传接口返回的lastBuff|
|myUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|myRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|reqScene|是|int|消息类型，3是点赞 4是评论 5是关注|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "lastBuff": "",
    "myUserName":"v2_060000231003b20faec8cae18ec5d0cb07ea33b077ba915250774edbea38082ea6b24af229@finder",
    "myRoleType": 3,
    "reqScene":3
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "获取消息列表成功",     "data": {         "lastBuff": "CC0QAA==",         "list": [             {                 "flag": 0,                 "svrMentionId": 45,                 "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/3JCic2WWGNg5Xic05huQEvDksIYMiaiaqXBpTQKUS1c9HFh8hwKWiadZiczuj0woaHYoHuyCNU2Nt8MSBJLx420RG57zkbxpEeu59iaNqadmxJqQ/132",                 "description": "我-要-带-他  #奥利奥 ",                 "followReason": {                     "followReasonType": 1                 },                 "extInfo": {                     "appName": "",                     "entityId": ""                 },                 "likeInfo": {                     "followMyFirstLike": 1,                     "likeId": 14427663669542524928,                     "likeType": 1                 },                 "refObjectNonceId": "16545206785312904238",                 "contact": {                     "relationType": 1,                     "contact": {                         "nickname": "阿",                         "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/3JCic2WWGNg5Xic05huQEvDksIYMiaiaqXBpTw2QKUS9HFh8hwKWiadZiczuj0woaHYoHuyCNU2Nt8MSBJLx420RG57zkbxpEeu59iaNqadmxJqQ/132",                         "username": "v5_020b0a166104010000000000f97523fa9f9a980000b1afa7d8728e3dd43ef4317a780e33c2996857e4fdf7e8b0f6772a3d0ed77295b3d890228fa85a9b3fee4fcea2e482220cc39f99b859576a7cdc4350fe20426405885897b149a5e97b8b4747be@stranger"                     }                 },                 "nickname": "阿",                 "mentionType": 7,                 "thumbUrl": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqziawqWSSdH0BbkiawrwDXSEEAmbWcUr69CABn2N6heg1YkWbjJPS3C65iaciaD08HCdptD9VjASGtT4Lqzmn4zK9hYg&token=6xykWLEnztKIzBicPuvgFxrvplE8JAFDJ5CwrFMoQbeYhaqV28N5ITVvTdsIhnEAXTTAJHvVcbYtnmawnzsd4qZSejk2I0PPbMgT5ujNB5nvcs0X6yXTUWUC&idx=1&dotrans=0&hy=SH&m=&scene=2&uzid=2",                 "extflag": 0,                 "createtime": 1719911551,                 "refCommentId": 0,                 "orderCount": 0,                 "mediaType": 4,                 "mentionId": ***********,                 "finderIdentity": {},                 "refObjectId": 14426124740495808818,                 "refObjectType": 0,                 "authorContact": {                     "clubInfo": {},                     "spamStatus": 0,                     "liveInfo": {                         "switchFlag": 53727,                         "micSetting": {},                         "anchorStatusFlag": 2048,                         "lotterySetting": {                             "attendType": 4,                             "settingFlag": 0                         }                     },                     "signature": "国家一级运动员",                     "headUrl": "https://wx.qlogo.cn/finderhead/ver_1/Vbwzoo1QIFPsR3GtNM4nC8jMMfbwsqQ0CSflEeFnmmzdj5RHOeoA1x9pYMKVo8NneKiaZOtibs6Z3X9opESubUe9oRp4Zv1aDC0Aztibo/0",                     "authInfo": {                         "authIconType": 1,                         "authProfession": "体育博主",                         "appName": "gh_4ee146ecaa@app",                         "detailLink": "pages/index/index.html?showdetail=true&username=v2_060000231003b20fae4e28a1dc6d4ce04eb35b07734d2a64d661f5f4f8fb8178de6c4a63d@finder"                     },                     "extInfo": {                         "country": "CN",                         "province": "Jiangsu",                         "city": "Nanjing",                         "sex": 2                     },                     "coverImgUrl": "http://mmsns.qpic.cn/mmsns/vjAoL8Pl64W0P9frlndiaMGlza0RsNm2ibKbu094YJCmOY8FZRia84NLOriboF2A7sRd6RicOcsRicQ/0",                     "extFlag": 2359308,                     "originalFlag": 2,                     "liveCoverImgUrl": "",                     "nickname": "王",                     "liveStatus": 2,                     "username": "v2_060000231003b20faec8c4e28a1dc6d4ce04e5b07734d2a64d661f5f4f8fb8178de6c4a63d@finder",                     "originalEntranceFlag": 1,                     "status": 0                 },                 "mentionContent": "<_wc_custom_img_ color=\"FG_0\" src=\"finder://dynamic_icon/FinderObjectDynamicImageKey_FinderLikeIconPng\" />",                 "refContent": "",                 "username": "wxid_tdkou9quqz22"             }         ]     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderCommentDetails.html

#### 标题：获取评论列表

#### 内容：
获取评论列表
请求URL：
<代码开始>http://域名/finderCommentDetails<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|bigint|视频号作品id|
|pageCode|是|String|分页code，首次传空，后续传接口返回的|
|sessionBuffer|是|String|通过获取用户主页返回的sessionBuffer|
|refCommentId|是|String|默认为0|
|rootCommentId|是|bigint|获取评论的回复详情时传上级评论的ID|
|nonceId|是|String|视频号作品nonceId|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "id": 14200825020179157073,
    "pageCode": "",
    "sessionBuffer": "eyJzZXNzaW9uX2lkIjoic2lkXzEzNDUxMDA3MDNfMTY5NDQ5MTA1MDk3NDcxMl8xMDkyODAzNzIwIiwiY3VyX2xpa2VfY291bnQiOjY2Mzc1MywiY3VyX2NvbW1lbnRfY291bnQiOjU1MjMsInJlY2FsbF90eXBlcyI6W10sImRlbGl2ZXJ5X3NjZW5lIjoyLCJkZWxpdmVyeV90aW1lIjoxNjk0NDkxMDUxLCJzZXRfY29uZGl0aW9uX2ZsYWciOjksInJlY2FsbF9pbmRleCI6W10sIm1lZGlhX3R5cGUiOjQsInZpZF9sZW4iOjYxLCJjcmVhdGVfdGltZSI6MTY5Mjg3MDI2MSwicmVjYWxsX2luZm8iOltdLCJzZWNyZXRlX2RhdGEiOiJCZ0FBVlwvMUhmdkVDM2s0QkdhMVJ1SXlYdGZQYVdYTzBGVVg4UHdnWHpHTVZrRnBBOHRBclJ0Q0MzVEFEZXVEUW1aOThRMUhiUVk1TitGWHFkRmYxQ1dwMXZPVThsMkhNK1E9PSIsImlkYyI6MSwiZGV2aWNlX3R5cGVfaWQiOjEzLCJkZXZpY2VfcGxhdGZvcm0iOiJpUGFkMTMsMTkiLCJmZWVkX3BvcyI6MCwiY2xpZW50X3JlcG9ydF9idWZmIjoie1wiaWZfc3BsaXRfc2NyZWVuX2lwYWRcIjowLFwiZW50ZXJTb3VyY2VJbmZvXCI6XCJ7XFxcImZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdFbXB0eVZpZXdDb250cm9sbGVyXzE2OTQ0OTA5NjYxNTEjJDBfMTY5NDQ5MDk1MzUxNSNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9=",
    "refCommentId": 0,
    "rootCommentId": 0,
    "nonceId": "14967079156574588894_0_0_2_2_0"
}
<代码结束>
成功返回示例
{     {     "code": "1000",     "message": "处理成功",     "data": {         "videoDetails": null,         "commentList": [             {                 "userName": "v5_020b0a166104010000000000ae18109352a67c000000b1afa7d8728e3dd43ef4317a780e33c2718b019b67053251a030e444e04fd520e943bd3c5be4d603186002dd12e6ec5ed990dc378a101a5ee7ffe6ac04261c74d14196876054300d15f037bf39@stranger",                 "nickName": "润春15358865586",                 "content": "日本食品也是有水制造的，停止日本所以品种",                 "commentId": -4245909463265376127,                 "replyCommentId": null,                 "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/oibcia4TUwfaxepzPkUVYo7ZhaXWOHvlBibG9S9aibgicQjxl9RicHFTt95tMAvdLudP5RAJN3qzcnIuhQEVdJnCxN6ny1usnj7OQhjwIQsLaDiaDU/132",                 "createTime": 1692871404,                 "likeCount": 46142,                 "ipRegion": "江苏",                 "replyContent": null,                 "replyUserName": null,                 "finderAuthorVo": {                     "userName": "v5_020b0a166104010000000000ae18109352a67c000000b1afa7d8728e3dd43ef4317a780e33c2718b019b67053251a030e444e04fd520e943bd3c5be4d603186002dd12e6ec5ed990dc378a101a5ee7ffe6ac04261c74d14196876054300d15f037bf39@stranger",                     "nickName": "润春15358865586",                     "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/oibcia4TUwfaxepzPkUVYo7ZhaXWOHvlBibG9S9aibgicQjxl9RicHFTt95tMAvdLudP5RAJN3qzcnIuhQEVdJnCxN6ny1usnj7OQhjwIQsLaDiaDU/132"                 }             },             {                 "userName": "v2_060000231003b20faec8c5e28e1fc5d6cf05eb32b077d9c01faaca62119f5312cb6c5bfacd82@finder",                 "nickName": "AAAAA科学运动森林",                 "content": "干日本，看一天了，生气",                 "commentId": -4245910022252656335,                 "replyCommentId": null,                 "headUrl": "http://wx.qlogo.cn/finderhead/Q3auHgzwzM5fYdITrDHxs73Vzf39Wp4F2eOqn8iad2x1acBfXpcia5cA/0",                 "createTime": 1692871338,                 "likeCount": 33836,                 "ipRegion": "北京",                 "replyContent": null,                 "replyUserName": null,                 "finderAuthorVo": {                     "userName": "v2_060000231003b20faec8c5e28e1fc5d6cf05eb32b077d9c01faaca62119f5312cb6c5bfacd82@finder",                     "nickName": "AAAAA科学运动森林",                     "headUrl": "http://wx.qlogo.cn/finderhead/Q3auHgzwzM5fYdITrDHxs73Vzf39Wp4F2eOqn8iad2x1acBfXpcia5cA/0"                 }             },             {                 "userName": "v5_020b0a16610401000000000032e135bdcc5cc1000000b1afa7d8728e3dd43ef4317a780e33c2718b019b67053251a030e444e0d7dec76b5a88c65a36450566e96ec44dcbb4c2f9a3ecdb3dd724e686e1fbc809c32047545569222e4b0ffbbdf9c7d2ee@stranger",                 "nickName": "网络歌手",                 "content": "日本人在是不清醒 那就可以灭了 [菜刀][菜刀][菜刀]",                 "commentId": -4243590245011154896,                 "replyCommentId": null,                 "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/Jice8MnhfFCYeYxy5JuqD8aVfzC3wy2ianFlWSmwAfG1RkIdU8TMbv7CsU5JQibM0pNQmHgvDnbWRBJJh1HHqxVahVPMP3hFhibNy8IkjxBL63U/132",                 "createTime": 1693147877,                 "likeCount": 3,                 "ipRegion": "云南",                 "replyContent": null,                 "replyUserName": null,                 "finderAuthorVo": {                     "userName": "v5_020b0a16610401000000000032e135bdcc5cc1000000b1afa7d8728e3dd43ef4317a780e33c2718b019b67053251a030e444e0d7dec76b5a88c65a36450566e96ec44dcbb4c2f9a3ecdb3dd724e686e1fbc809c32047545569222e4b0ffbbdf9c7d2ee@stranger",                     "nickName": "网络歌手",                     "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/Jice8MnhfFCYeYxy5JuqD8aVfzC3wy2ianFlWSmwAfG1RkIdU8TMbv7CsU5JQibM0pNQmHgvDnbWRBJJh1HHqxVahVPMP3hFhibNy8IkjxBL63U/132"                 }             }         ],         "pageCode": "CrABCLCwsM79pO+NxQEYgZHkrOz634nFARixkvD6yerficUBGKqykPn5luKJxQEYkLKM+qm54InFARjAkvzI+cngicUBGM2wlLLh49+JxQEYrJKE0q/v4YnFARjWsPjhicXgicUBGNqw+KfRxeGJxQEY1LCc4KKt4InFARjTktjgwvziicUBGOmR9LrNouCJxQEYj7C4gJ+L44nFARinoOiB+oXkicUBGLOw/Imw6uKJxQE=",         "commentCount": 5534,         "likeCount": 100002,         "forwardCount": 100002,         "favCount": 100002     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/searchFinder.html

#### 标题：搜索视频号

#### 内容：
搜索视频号
请求URL：
<代码开始>http://域名/newSearchFinder<end of the code>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|content|是|String|搜索内容|
|type|是|int|搜索类型<br>0:全部<br>1:搜索用户<br>10:搜索视频<br>（ps：不同类型，返回JSON不同）|
|filter|是|int|筛选<br>0:不限<br>1:最新<br>2:朋友赞过|
|page|是|int|页码，首次传1，后续自行累加|
|offset|是|int|偏移量，首次传0，后续传接口返回的offset|
|cookie|否|String|cookie信息，首次传空，后续传接口返回的cookie|
|searchId|否|String|搜索信息，首次传空，后续传接口返回的searchId|
请求参数示例
<beginning of the code>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "content": "中国",
    "type": 0,
    "filter": 0,
    "page": 1,
    "offset": 0,
    "cookie": "",
    "searchId": ""
}
<end of the code>
成功返回示例
<beginning of the code>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "cookie": "{\"box_offset\":0,\"businessType\":14,\"cookies_buffer\":\"UhoIexABGA4iBuS4reWbvVABeAiCAQUQBaIBAA==\",\"doc_offset\":0,\"dup_bf\":\"\",\"isHomepage\":0,\"page_cnt\":1,\"query\":\"中国\",\"scene\":123}\n",
        "searchId": "416754344366039934",
        "offset": 8,
        "authInfos": [],
        "videoInfos": [
            {
                "items": [
                    {
                        "dateTime": "3天前",
                        "docId": "14212057401545988355",
                        "duration": "08:09",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvia8Me3Iw7dE8gXOjUzSmu6r8ofw8cYtReGJEB117XjBrDOj5RP2osD7Ew7vxk6T5oBsTmbTU4k6COicM9jEAhia0LIvDjoOXMNdPNAZicjEKeKw&bizid=1023&dotrans=0&hy=SH&idx=1&m=94b80bae11184583d673d6149ce6d444&token=x5Y29zUxcibCadRELU5qibElZGLZcyHZ4ACWfJseChEibnLqlbEjCt7ZkHbd9A4dC5p",
                        "imageData": {
                            "height": 1088,
                            "width": 1920,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvia8Me3Iw7dE8gXOjUzSmu6r8ofw8cYtReGJEB117XjBrDOj5RP2osD7Ew7vxk6T5oBsTmbTU4k6COicM9jEAhia0LIvDjoOXMNdPNAZicjEKeKw&bizid=1023&dotrans=0&hy=SH&idx=1&m=94b80bae11184583d673d6149ce6d444&token=x5Y29zUxcibCadRELU5qibElZGLZcyHZ4ACWfJseChEibnLqlbEjCt7ZkHbd9A4dC5p"
                        },
                        "likeNum": "10万+",
                        "pubTime": 1694209265,
                        "reportId": "14212057401545988355:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "https://wx.qlogo.cn/finderhead/ver_1/OGtb2O7uCQZiafCiazicARJTpmSYpiaUCu1fMR3sn4zcAj78PEyUYvKhnsWuHS8HocibyqepqsOPMF187u610rxXRXdianLcPpEwYqV2NBRia36cao/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png"
                            ],
                            "title": "雪山在那里"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAInsX9AtMcwAAAAstQy6ubaLX4KHWvLEZgBPE14EAKhcxDfiGzNPgMIrQJV4ESYEKopnVZhYmR6Nt\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"11155216971528673864\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14212057401545988355",
                            "jumpType": 9
                        },
                        "title": "...父母需在孩子陪同下观看。这里是<em class=\"highlight\">中国</em>。",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=6xykWLEnztKcKCJZcV0rWCM8ua7DibZkibqXGfPxf5lrrNgPZMFicGq71GHhsibpJbcUe2mhY6SADicbuwrIP3Pk4doGYFhyticbsuZXHvNHW2A1tzA91Ku8QBKtCZ21dyZYe7t1Gjj5RKw2HFibOF9Kx5mYtRDR0LHz1Ukp7uEUEYdxKY&a=1&bizid=1023&dotrans=0&hy=SH&idx=1&m=691ea000eff81fcc9c6521f1a16dd0be&upid=500250&partscene=4&X-snsvideoflag=W21&token=AxricY7RBHdUkQSDFU11VLDP6QTzY8dtT7CE8ssJelMicM4mPLobWsBiaQLnn8LPsJTrePhjL3nicEU"
                    }
                ],
                "boxId": "0x80000000000-1-14212057401545988355",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "",
                        "docId": "4791196579134016048",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "",
                        "showType": null,
                        "source": {
                            "iconUrl": "http://p.qpic.cn/hottopic/0/1590389140e56978cf65585a00b2c57642dd008ab6/0",
                            "mark": null,
                            "title": "搜狗百科小程序"
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 2
                        },
                        "title": "<em class=\"highlight\">中国</em> - 百科",
                        "videoUrl": ""
                    }
                ],
                "boxId": "0x80000000-0-4791196579134016048",
                "type": 16777728,
                "subType": 0,
                "totalCount": 1
            },
            {
                "items": [
                    {
                        "dateTime": "18小时前",
                        "docId": "14213867078858246268",
                        "duration": "00:13",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqziceZZHuetsMbO0HYPvqOlJ7VVplVnlkIbQibGia1s6sUvOHAYrblNU6YFkEZ6Z8yu1fib6HwT0XNROZfXiaUyWQCFTw&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=cztXnd9GyrFAja2VYvGJOv3oVIqqZfDUbFLA9s2E521Qm5zic3920C1bDAiaaRXsoN",
                        "imageData": {
                            "height": 1920,
                            "width": 1080,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqziceZZHuetsMbO0HYPvqOlJ7VVplVnlkIbQibGia1s6sUvOHAYrblNU6YFkEZ6Z8yu1fib6HwT0XNROZfXiaUyWQCFTw&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=cztXnd9GyrFAja2VYvGJOv3oVIqqZfDUbFLA9s2E521Qm5zic3920C1bDAiaaRXsoN"
                        },
                        "likeNum": "317",
                        "pubTime": 1694424996,
                        "reportId": "14213867078858246268:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6icZCTWRZrNnQa9vOJmZ9ftLOztwJSXiaD3CBjiaO7NcGmw/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png"
                            ],
                            "title": "新华社电视"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAACgQI9q2TyAAAAAstQy6ubaLX4KHWvLEZgBPEqKMsAVx1KcWGzNPgMIql2-qktfOXJpQypSZWUver\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"8054243567951799894\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14213867078858246268",
                            "jumpType": 9
                        },
                        "title": "平安就好！<em class=\"highlight\">中国</em>援摩医生成功救助“地震宝宝”  \n",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAlAtVPHnBRJncljibDbia3HRRoAIcVu9T5BDqsS822zGbrTfsiawAIKkZFjYzj1NDXlt97rLFlz3w6pfLicumGDZeO&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0&partscene=4&X-snsvideoflag=WT98&token=x5Y29zUxcibDuvYLQPf5C2rGMLSLxKOUlukc8xYCp6m9iaEXibGrm03Q23vrCKeaUQXkbmUSKz13pw"
                    }
                ],
                "boxId": "0x80000000000-1-14213867078858246268",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "",
                        "docId": "中国国旗",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "%E4%B8%AD%E5%9B%BD%E5%9B%BD%E6%97%97:hint:500415",
                        "showType": null,
                        "source": {
                            "iconUrl": "",
                            "mark": null,
                            "title": ""
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 0
                        },
                        "title": "",
                        "videoUrl": ""
                    },
                    {
                        "dateTime": "",
                        "docId": "五星红旗",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "%E4%BA%94%E6%98%9F%E7%BA%A2%E6%97%97:hint:601180",
                        "showType": null,
                        "source": {
                            "iconUrl": "",
                            "mark": null,
                            "title": ""
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 0
                        },
                        "title": "",
                        "videoUrl": ""
                    },
                    {
                        "dateTime": "",
                        "docId": "中国国土面积",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "%E4%B8%AD%E5%9B%BD%E5%9B%BD%E5%9C%9F%E9%9D%A2%E7%A7%AF:hint:668959",
                        "showType": null,
                        "source": {
                            "iconUrl": "",
                            "mark": null,
                            "title": ""
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 0
                        },
                        "title": "",
                        "videoUrl": ""
                    },
                    {
                        "dateTime": "",
                        "docId": "中国纪录片",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "%E4%B8%AD%E5%9B%BD%E7%BA%AA%E5%BD%95%E7%89%87:hint:340841",
                        "showType": null,
                        "source": {
                            "iconUrl": "",
                            "mark": null,
                            "title": ""
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 0
                        },
                        "title": "",
                        "videoUrl": ""
                    },
                    {
                        "dateTime": "",
                        "docId": "中国消防",
                        "duration": "",
                        "image": "",
                        "imageData": {
                            "height": 0,
                            "width": 0,
                            "url": ""
                        },
                        "likeNum": null,
                        "pubTime": 0,
                        "reportId": "%E4%B8%AD%E5%9B%BD%E6%B6%88%E9%98%B2:hint:553702",
                        "showType": null,
                        "source": {
                            "iconUrl": "",
                            "mark": null,
                            "title": ""
                        },
                        "jumpInfo": {
                            "extInfo": "",
                            "feedId": "",
                            "jumpType": 0
                        },
                        "title": "",
                        "videoUrl": ""
                    }
                ],
                "boxId": "0x800-3-0",
                "type": 24,
                "subType": 3,
                "totalCount": 0
            },
            {
                "items": [
                    {
                        "dateTime": "9天前",
                        "docId": "14207227912484886596",
                        "duration": "00:52",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=S7s6ianIic0ia4PicKJSfB8EjyjpQibPUAXolmkD2enuo8BLbH4njzLUABgeZYVCqdlYT62vDKcOEqia4KocUy6J43H2dScOFlSgZ04j8wjmW8tAwX4aR3uhicxgQ&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=x5Y29zUxcibA8OUawCN97XiafTrhMEgMPfcbZjQeUohvUt1p5a7ddzfFIR2iaBDMdKx",
                        "imageData": {
                            "height": 1704,
                            "width": 1080,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=S7s6ianIic0ia4PicKJSfB8EjyjpQibPUAXolmkD2enuo8BLbH4njzLUABgeZYVCqdlYT62vDKcOEqia4KocUy6J43H2dScOFlSgZ04j8wjmW8tAwX4aR3uhicxgQ&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=x5Y29zUxcibA8OUawCN97XiafTrhMEgMPfcbZjQeUohvUt1p5a7ddzfFIR2iaBDMdKx"
                        },
                        "likeNum": "5万",
                        "pubTime": 1693633545,
                        "reportId": "14207227912484886596:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4z64koK7VE0lpycmRhDENFv3BLW10adic0icmh4aTM6tMA/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png"
                            ],
                            "title": "中国政府网"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAnLIQjGLE3AAAAAstQy6ubaLX4KHWvLEZgBPEkINECyY8V_CGzNPgMIo-e71qpvDe8KCwparuPFUb\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"12911466036332020329\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14207227912484886596",
                            "jumpType": 9
                        },
                        "title": "习近平：<em class=\"highlight\">中国</em>愿同各国各方一道，携手推动世界经济走上持续复苏轨道。（转自：央视新闻）",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAIAsEjiciatZ0wicd8TicicB9hUWUjFGxyVFbOhVC5d5RN03Ky5j2lQu7Ofvl4m2VU9qV1CsNvF6cl1h95TA1uLpbcN&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0&partscene=4&X-snsvideoflag=WT68&token=x5Y29zUxcibDuvYLQPf5C2r8ptXtWHXT79L25QC76LlW1QiaxSQJ8E5fCyXpiaCwDsWgTxV2LeJ4Gs"
                    }
                ],
                "boxId": "0x80000000000-1-14207227912484886596",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "2个月前",
                        "docId": "14173989676465854495",
                        "duration": "04:25",
                        "image": "http://wxapp.tc.qq.com/251/20350/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7e5cX9BxwFGstJUrVGf3OEyfwXBmY2m5khAVODvBtqunkmcoTqgsJiaUz2mDjD2PlBpaibyviazwQtngNPKrOEgWULHE95FppG1dUaRI7dB04lcg&bizid=1023&dotrans=0&hy=SH&idx=1&m=07e87a9b0ba96892e7cef94fb6ca9433&token=cztXnd9GyrGqKjnmm8EjsCicYxrtY6NWAUCKJxJyN5PNWiaugtl2jT0DHunEBpAkiba",
                        "imageData": {
                            "height": 1080,
                            "width": 1920,
                            "url": "http://wxapp.tc.qq.com/251/20350/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7e5cX9BxwFGstJUrVGf3OEyfwXBmY2m5khAVODvBtqunkmcoTqgsJiaUz2mDjD2PlBpaibyviazwQtngNPKrOEgWULHE95FppG1dUaRI7dB04lcg&bizid=1023&dotrans=0&hy=SH&idx=1&m=07e87a9b0ba96892e7cef94fb6ca9433&token=cztXnd9GyrGqKjnmm8EjsCicYxrtY6NWAUCKJxJyN5PNWiaugtl2jT0DHunEBpAkiba"
                        },
                        "likeNum": "3.2万",
                        "pubTime": 1689671239,
                        "reportId": "14173989676465854495:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7fuNOe6Mepx3orIoHZG4C5h38TW4RsbWOsDohUCFb3LQ/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png"
                            ],
                            "title": "时报热点"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAA3QgEfmAp2wAAAAstQy6ubaLX4KHWvLEZgBPEy4NEICh8Ur-HzNPgMIq-WhHkJaY5rPcTdzXRgbaL\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"13863296488069395237\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14173989676465854495",
                            "jumpType": 9
                        },
                        "title": "...你们才是麻烦制造者\n<em class=\"highlight\">中国</em>常驻联合国代表张军13日在联合国安理会公开会上，就日前北约维尔纽斯峰会公报污蔑抹黑<em class=\"highlight\">中国</em>予以严厉驳斥。",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQCoFfdy9ySVnrwx8biaAk7Zpb2Un1iaibra1cLdKzr7UJtLVjrrBZVgH5JdRE56Rq8TmicRBJyCGBew8hnI4K32iclBx&bizid=1023&dotrans=0&hy=SH&idx=1&m=&partscene=4&X-snsvideoflag=WT67&token=x5Y29zUxcibDuvYLQPf5C2lqeSYTxBibGm4It2KLw2q49XiaEOCdKVDnlZibZddDk5xCa94e7mhdPTw"
                    }
                ],
                "boxId": "0x80000000000-1-14173989676465854495",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "2个月前",
                        "docId": "14181094436820359204",
                        "duration": "01:15",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv8ncLu2tpCG4H2hib1z6D4WZJia0A7CqOo29yLHouiaGuRAYOLRvykKqAzZCxzjRjLmPhdMLSwG52GL3HjSuMpUgSprZkAHQHyp1LSsJuGo6qCA&bizid=1023&dotrans=0&hy=SH&idx=1&m=ba338dbc90c69418f32dec93d24d5375&token=cztXnd9GyrG0x7aBXH688SyLXsuKraQU02BBfpj2SCUVFyns5hldpAOc9tME9kOW",
                        "imageData": {
                            "height": 1920,
                            "width": 1080,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv8ncLu2tpCG4H2hib1z6D4WZJia0A7CqOo29yLHouiaGuRAYOLRvykKqAzZCxzjRjLmPhdMLSwG52GL3HjSuMpUgSprZkAHQHyp1LSsJuGo6qCA&bizid=1023&dotrans=0&hy=SH&idx=1&m=ba338dbc90c69418f32dec93d24d5375&token=cztXnd9GyrG0x7aBXH688SyLXsuKraQU02BBfpj2SCUVFyns5hldpAOc9tME9kOW"
                        },
                        "likeNum": "6005",
                        "pubTime": 1690518192,
                        "reportId": "14181094436820359204:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "https://wx.qlogo.cn/finderhead/ver_1/FqibFPWl9EWSwJicxVbRrABjJ8eibylqzaC0q9IRYfvC5VP3c5AT6WA4FBKomjZEZkfyvJwGp4WFHlw5PW8zicicn5AKgbZXLD1l7zMDhQYxTFNo/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png"
                            ],
                            "title": "中国国家地理"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAWBw2R-03AgAAAAstQy6ubaLX4KHWvLEZgBPE8IM4OQckDYOHzNPgMIpnDF5nVVPB8t43piY_G8Lw\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"300008098297403665\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14181094436820359204",
                            "jumpType": 9
                        },
                        "title": "...#旅行 #四川 #摄影 #地理君带你游<em class=\"highlight\">中国</em>",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=6xykWLEnztKcKCJZcV0rWCM8ua7DibZkibqXGfPxf5lropiaiatSQwhdJsTibJBmUiaKCia3QdHxMjjRR6PeWOUzBu4vJVSz8nZiaPH1ja2L7x4iafmwqwF7yoZ7qwJeC1Wt7Stx7uVkghiaSkbHsCGC7vOBs4gkPSJPB4KMlKribpWQWfoiczo&a=1&bizid=1023&dotrans=0&hy=SH&idx=1&m=ed867040f3838690a4b049fe8466cf0f&partscene=4&X-snsvideoflag=W21&token=x5Y29zUxcibDuvYLQPf5C2jzia0oSRPSPVKScSOEmK1gPnYPzw5Y4a9FsewYG99S7qY0HcyD38icicE"
                    }
                ],
                "boxId": "0x80000000000-1-14181094436820359204",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "2小时前",
                        "docId": "14214343629735135480",
                        "duration": "08:03",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvYkrVgePiaUsbslfAwcgrGvCTnkibv1opiaf3FLjONZeTSvPo2gib2eMXomZ9icUleQCdvoSklNG1GZA2Acwkk0n20DTaKbeibh8v54P01dicpVOiaC4&bizid=1023&dotrans=0&hy=SH&idx=1&m=f1f18e7964afeec730a817b4f27a36b4&token=x5Y29zUxcibBfSvBFHRvY6hRdicrFlWFrowakicsVC5NKS32pXElF90DrR0vNCLG7LE",
                        "imageData": {
                            "height": 1920,
                            "width": 1080,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvYkrVgePiaUsbslfAwcgrGvCTnkibv1opiaf3FLjONZeTSvPo2gib2eMXomZ9icUleQCdvoSklNG1GZA2Acwkk0n20DTaKbeibh8v54P01dicpVOiaC4&bizid=1023&dotrans=0&hy=SH&idx=1&m=f1f18e7964afeec730a817b4f27a36b4&token=x5Y29zUxcibBfSvBFHRvY6hRdicrFlWFrowakicsVC5NKS32pXElF90DrR0vNCLG7LE"
                        },
                        "likeNum": "44",
                        "pubTime": 1694481805,
                        "reportId": "14214343629735135480:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4JATacOGjWWZnGGC5QBiabR7PHSU5NGuAmK2oBUNIpryw/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png"
                            ],
                            "title": "何静说"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAlcgEhwKD4QAAAAstQy6ubaLX4KHWvLEZgBPErKI8UxYrBcSGzNPgMIq4WhW0pTBkkp8LgD6CX9Nc\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"15033793667473165354\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CIOy8JSH3dedxQEI_JDcv8yZ86DFAQjEsLS1ttCNlcUBCJ-wtJ64kIjaxAEIpLDIh5fI1-bEAQj4kczthsffocUBCJew8Pm6n6aexQEI2LDo_K-jyKHFAQjMsKSvvomM9sQBCOeRzI_jhtSVxQEIlLKo-MSU4aHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14214343629735135480",
                            "jumpType": 9
                        },
                        "title": "<em class=\"highlight\">中国</em>各省名字的由来～\n#街头采访#何静同学@微信派@微信视频号创造营@微信创作者@微信时刻",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=6xykWLEnztKcKCJZcV0rWCM8ua7DibZkibqXGfPxf5lrqdeJe3OZ1M6O2cLicU6cAUuiarGlLZj4wtCLXCMwcF9Ewj7q5gQnKJUcO2PqxzcVQeqr8KoPJFrNY06n1LJWk61cEdTerOq9ib85v71GbUGO48Py2DRLtZEozDl26NCrLOTU&a=1&bizid=1023&dotrans=0&hy=SH&idx=1&m=4560f7f359ffaea8c3ed0065fee80b8c&upid=290280&partscene=4&X-snsvideoflag=W21&token=x5Y29zUxcibDuvYLQPf5C2pZI2r8GAz6F5Jb25WF8aEuKDHHSvRotXAmKicz7yUibru9RcsGo52qFQ"
                    }
                ],
                "boxId": "0x80000000000-1-14214343629735135480",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "2天前",
                        "docId": "14212402730818607127",
                        "duration": "03:36",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvcJ2bCHkkr5AOXp9ghN5AEONeDJ7GpMu6OZXY7Qo5l2PF61aSJRQQldSzytv9FIkq5uLxg91KbcEUDKZQSdoyuslqalykWcyKMeqB7sBZ1mo&bizid=1023&dotrans=0&hy=SH&idx=1&m=faef1f8a48d09731507254dda7245f15&token=6xykWLEnztKIzBicPuvgFxkT5DDoBlhNqjIAkibjrlNpicwrIPvWcQiaj0IRMveibsF67",
                        "imageData": {
                            "height": 1080,
                            "width": 1920,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvcJ2bCHkkr5AOXp9ghN5AEONeDJ7GpMu6OZXY7Qo5l2PF61aSJRQQldSzytv9FIkq5uLxg91KbcEUDKZQSdoyuslqalykWcyKMeqB7sBZ1mo&bizid=1023&dotrans=0&hy=SH&idx=1&m=faef1f8a48d09731507254dda7245f15&token=6xykWLEnztKIzBicPuvgFxkT5DDoBlhNqjIAkibjrlNpicwrIPvWcQiaj0IRMveibsF67"
                        },
                        "likeNum": "1266",
                        "pubTime": **********,
                        "reportId": "14212402730818607127:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "https://wx.qlogo.cn/finderhead/ver_1/FqibFPWl9EWSwJicxVbRrABjJ8eibylqzaC0q9IRYfvC5VP3c5AT6WA4FBKomjZEZkfyvJwGp4WFHlw5PW8zicicn5AKgbZXLD1l7zMDhQYxTFNo/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png"
                            ],
                            "title": "中国国家地理"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAcCAJVnOSdAAAAAstQy6ubaLX4KHWvLEZgBPEw4MARypzfPuGzNPgMIp_Z2EYQ4GvYE5SQzrLo26C\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"8942398082743730121\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CPyQ3L_MmfOgxQEIxLC0tbbQjZXFAQifsLSeuJCI2sQBCKSwyIeXyNfmxAEI-JHM7YbH36HFAQiXsPD5up-mnsUBCNiw6Pyvo8ihxQEIzLCkr76JjPbEAQjnkcyP44bUlcUBCJSyqPjElOGhxQEIorDco4n24OXEARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBWiUkNzgt5v0xsQB\"}\n",
                            "feedId": "14212402730818607127",
                            "jumpType": 9
                        },
                        "title": "在<em class=\"highlight\">中国</em>广袤的土地上，有着得天独厚的自然之美。不论是山川地貌、风土物种，还是民族文化，总有意想不到的新奇。\n\n除了陆地上的风土人情，作为海洋生物多样性最丰富的国家之一，走入深蓝同样带给人惊喜；深藏在地下的洞穴是这个星球上隐秘的地质奇观，有着非同一般的视觉魅力；沿着河西走廊寻迹，穿越千年的历史长廊，感受着文明的绵长韵味。\n\n出发吧，让我们在路上用眼睛和脚步发现#大美<em class=\"highlight\">中国</em>。#摄影 #旅行 #地理君带你游<em class=\"highlight\">中国</em>",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=6xykWLEnztKcKCJZcV0rWCM8ua7DibZkibqXGfPxf5lrovgY0DTc8kLxib7b32Wcuts74sSeCJ8w2svT9tV4VXem55ib4qtXtVaG7MaFVgqLrf597EvdvFiazRPLrPHwzOtmoM8G4ZYtt0OlPBqxnayMKeBL4AnXliarfFaicB2ae2WLUU&a=1&bizid=1023&dotrans=0&hy=SH&idx=1&m=6516ed3be51a3851b8816f49cefa13d6&upid=500030&partscene=4&X-snsvideoflag=W21&token=x5Y29zUxcibDuvYLQPf5C2njeyBTSF3Nv2DRfpK5AicpnObBxmFXRFmIHSMk6iaqWc8vlEdcr9n7yw"
                    }
                ],
                "boxId": "0x80000000000-1-14212402730818607127",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            },
            {
                "items": [
                    {
                        "dateTime": "5小时前",
                        "docId": "14214241248752572504",
                        "duration": "00:08",
                        "image": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqznvKdf9Yt2hhicJOL9ficvjLVz5DicpzHRYuKT4KTb2dk1fljVXEDTUGYwx2DCQ33kSfiaNbO7gd0EbcK8LHkiaX9RSQ&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=cztXnd9GyrGhE2iaHGOXDiaEz50vcZdappayTuIPXeToe7hGOyREC70oib2gTCFjF3A",
                        "imageData": {
                            "height": 1920,
                            "width": 1080,
                            "url": "http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqznvKdf9Yt2hhicJOL9ficvjLVz5DicpzHRYuKT4KTb2dk1fljVXEDTUGYwx2DCQ33kSfiaNbO7gd0EbcK8LHkiaX9RSQ&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=cztXnd9GyrGhE2iaHGOXDiaEz50vcZdappayTuIPXeToe7hGOyREC70oib2gTCFjF3A"
                        },
                        "likeNum": "732",
                        "pubTime": 1694469600,
                        "reportId": "14214241248752572504:feed:0",
                        "showType": null,
                        "source": {
                            "iconUrl": "https://wx.qlogo.cn/finderhead/ver_1/AibdpibRqlqLRgICQr0dxdJtBDbeHqqP1SnaSvQvdbUZ2K5wXKViayz6K7QLBh9XFSh6OiaPr2Yn4Y5SZ1Hic8Y4VoWCY28oBUeiar2PglGZnFmUs/132",
                            "mark": [
                                "https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png"
                            ],
                            "title": "前沿科记"
                        },
                        "jumpInfo": {
                            "extInfo": "{\"behavior\":[\"report_feed_read\",\"allow_pull_top\",\"allow_infinite_top_pull\"],\"encryptedObjectId\":\"export/UzFfAgtgekIEAQAAAAAAY-ou_2TqewAAAAstQy6ubaLX4KHWvLEZgBPEjIMYQj9PEsSGzNPgMIpLxBD5k3kjaVgtJ3VQ9Z4p\",\"feedFocusChangeNotify\":true,\"feedNonceId\":\"16046618857466421592\",\"getRelatedList\":true,\"reportExtraInfo\":\"{\\\"report_json\\\":\\\"\\\"}\\n\",\"reportScene\":14,\"requestScene\":13,\"sessionId\":\"CMSwtLW20I2VxQEIn7C0nriQiNrEAQiksMiHl8jX5sQBCPiRzO2Gx9-hxQEIl7Dw-bqfpp7FAQjYsOj8r6PIocUBCMywpK--iYz2xAEI55HMj-OG1JXFAQiUsqj4xJThocUBCKKw3KOJ9uDlxAEItrDMgt7n6KHFARD-xqf04_um5AUqBuS4reWbvTAAOICAgICAgAJAAVCft7KBBQ..\"}\n",
                            "feedId": "14214241248752572504",
                            "jumpType": 9
                        },
                        "title": "<em class=\"highlight\">中国</em>成功测试太赫兹探测装备，所有潜艇将无处可藏#太赫兹#探测装备",
                        "videoUrl": "https://findermp.video.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqztFm6btTjnEsibib0PvvnkibvEvslpBKib2Ej2o39req2cs2iaqjKTLw7mEJ1aHQ33pbNLVYV2qu3ytQEAcwkuzpOvLw&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0&partscene=4&X-snsvideoflag=WT97&token=AxricY7RBHdUkQSDFU11VLPIRiaU6skKzgOlqOf6phHAiafYw1oBvX2GDeuDgBIBdFOrPZOxnYKGww"
                    }
                ],
                "boxId": "0x80000000000-1-14214241248752572504",
                "type": 86,
                "subType": 1,
                "totalCount": 270
            }
        ]
    }
}
<end of the code>
错误返回示例
<beginning of the code>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<end of the code>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/createFinder.html

#### 标题：创建视频号

#### 内容：
创建视频号
请求URL：
<代码开始>http://域名/createFinder<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|nickName|是|String|视频号名称|
|headImgUrl|是|String|视频号头像|
请求参数示例
<代码开始>
{
    "wId":"2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "nickName": "阿讯测试",
    "headImgUrl": "https://gimg2.***************/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2F1114%2F0G020114924%2F200G0114924-15-1200.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1657616129&t=f06cc1815b63173cca6f53a1f5e9f197"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/syncFinder.html

#### 标题：同步私信消息

#### 内容：
同步私信消息
请求URL：
<代码开始>http://域名/syncFinder<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|keyBuff|是|String|首次传空，后续传接口返回的keyBuff|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "keyBuff": ""
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "list": [             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"functionmsg\"><functionmsg><cgi>/cgi-bin/micromsg-bin/findergetlivetips</cgi><cmdid>6407</cmdid><functionmsgid>FinderLiveNotificationMsg_LiveStartNotifyMsg_14192909587060689023_2_14192909587060689023_2</functionmsgid><version>0</version><op>0</op><retryinterval>150</retryinterval><retrycount>3</retrycount><reportid>231759</reportid><successkey>101</successkey><failkey>102</failkey><finalfailkey>103</finalfailkey><custombuff>CAA=</custombuff><businessid>70001</businessid><actiontime>0</actiontime><delaytime>0</delaytime></functionmsg></sysmsg>"                         },                         "CreateTime": 1691926802,                         "MsgSeq": 1172,                         "NewMsgId": 3876235366388279544,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1172,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1172                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"finderliveclosenotifymsg\"><finderliveclosenotifymsg><live_id>2042557163204371840</live_id><object_id>14192909587060689023</object_id><revoke_id>14192909587060689023_13_18</revoke_id><tips_id>14192909587060689023_13_18</tips_id><functionmsgid>FinderLiveNotificationMsg_14192909587060689023_11_14192909587060689023_11</functionmsgid></finderliveclosenotifymsg></sysmsg>"                         },                         "CreateTime": 1691926812,                         "MsgSeq": 1173,                         "NewMsgId": 6876224516571785856,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1173,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1173                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "gIBC"                         },                         "CreateTime": 1691934687,                         "MsgSeq": 1174,                         "NewMsgId": 4843736772799698894,                         "FromUserName": {                             "string": "wxid_23fgdsdfh8"                         },                         "ToUserName": {                             "string": "v2_060000231003b20faec8cae18a1ec5d0cb07ea33b077ba915250774edbea38082ea6b24af229@finder"                         },                         "MsgType": 1,                         "ImgStatus": 1,                         "MsgId": 1174,                         "MsgSource": ""                     },                     "extinfo": "CAIQAw==",                     "isSender": 1,                     "msgSessionId": "a37c87fbfb8c07ca21d29bea6e3feef1cd740982cfda8407bde2dc2ccbba7f0c@findermsg",                     "seq": 1174                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"functionmsg\"><functionmsg><cgi>/cgi-bin/micromsg-bin/findergetlivetips</cgi><cmdid>6407</cmdid><functionmsgid>FinderLiveNotificationMsg_LiveStartNotifyMsg_14193708641429293196_2_14193708641429293196_2</functionmsgid><version>0</version><op>0</op><retryinterval>150</retryinterval><retrycount>3</retrycount><reportid>231759</reportid><successkey>101</successkey><failkey>102</failkey><finalfailkey>103</finalfailkey><custombuff>CAA=</custombuff><businessid>70001</businessid><actiontime>0</actiontime><delaytime>0</delaytime></functionmsg></sysmsg>"                         },                         "CreateTime": 1692022059,                         "MsgSeq": 1178,                         "NewMsgId": 574964913010478295,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1178,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1178                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"finderliveclosenotifymsg\"><finderliveclosenotifymsg><live_id>2042557553368249803</live_id><object_id>14193708641429293196</object_id><revoke_id>14193708641429293196_13_18</revoke_id><tips_id>14193708641429293196_13_18</tips_id><functionmsgid>FinderLiveNotificationMsg_14193708641429293196_11_14193708641429293196_11</functionmsgid></finderliveclosenotifymsg></sysmsg>"                         },                         "CreateTime": 1692022237,                         "MsgSeq": 1179,                         "NewMsgId": 4075679219481308731,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1179,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1179                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"finderliveclosenotifymsg\"><finderliveclosenotifymsg><live_id>2042557554977629571</live_id><object_id>14193711938459601029</object_id><revoke_id>14193711938459601029_13_18</revoke_id><tips_id>14193711938459601029_13_18</tips_id><functionmsgid>FinderLiveNotificationMsg_14193711938459601029_11_14193711938459601029_11</functionmsgid></finderliveclosenotifymsg></sysmsg>"                         },                         "CreateTime": 1692022693,                         "MsgSeq": 1180,                         "NewMsgId": 8850943028489834406,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1180,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1180                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "<sysmsg type=\"finderliveclosenotifymsg\"><finderliveclosenotifymsg><live_id>2042557557241816498</live_id><object_id>14193716574826072174</object_id><revoke_id>14193716574826072174_13_18</revoke_id><tips_id>14193716574826072174_13_18</tips_id><functionmsgid>FinderLiveNotificationMsg_14193716574826072174_11_14193716574826072174_11</functionmsgid></finderliveclosenotifymsg></sysmsg>"                         },                         "CreateTime": 1692023457,                         "MsgSeq": 1181,                         "NewMsgId": 6294812797912010456,                         "FromUserName": {                             "string": ""                         },                         "ToUserName": {                             "string": ""                         },                         "MsgType": 10002,                         "ImgStatus": 1,                         "MsgId": 1181,                         "MsgSource": ""                     },                     "isSender": 0,                     "msgSessionId": "",                     "seq": 1181                 }             },             {                 "syncKeyType": 1,                 "itemType": 1,                 "subType": 0,                 "content": {                     "msg": {                         "Status": 3,                         "ImgBuf": {                             "iLen": 0                         },                         "Content": {                             "string": "1"                         },                         "CreateTime": 1692185698,                         "MsgSeq": 1188,

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getLikeAndFavList.html

#### 标题：获取赞和收藏的视频列表

#### 内容：
获取赞和收藏的视频列表
请求URL：
<代码开始>http://域名/finder/getLikeAndFavList<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|lastBuffer|否|String|首次传空，后续传接口返回的lastBuffer|
|myUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|myRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|flag|是|int|视频类型，7:全部 1:红心 2:大拇指 4:收藏|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "lastBuff": "",
    "myUserName":"v2_060000231003b20faec8cae18cb07ea33b077ba915250774edbea38082ea6b24af229@finder",
    "myRoleType": 3,
    "flag": 7 
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "获取关注列表成功",     "data": {         "contactList": [             {                 "clubInfo": {},                 "spamStatus": 0,                 "liveInfo": {                     "switchFlag": 53727,                     "micSetting": {},                     "anchorStatusFlag": 2048,                     "lotterySetting": {                         "attendType": 4,                         "settingFlag": 0                     }                 },                 "signature": "",                 "headUrl": "https://wx.qlogo.cn/finderhead/ver_1/XBMFoPXk0XSgBdBoKSNFiaTLrO87hTOrXrAgrPyLaav7djjqvWReQz2T595mBW6ic267eYfibYbcEzAytKD9RrmGPzoBIYrUPubTWwIT4U0CeY/0",                 "authInfo": {},                 "extInfo": {                     "country": "CN",                     "province": "Jiangsu",                     "city": "Nanjing",                     "sex": 1                 },                 "coverImgUrl": "",                 "extFlag": 262156,                 "followTime": 1718847509,                 "liveCoverImgUrl": "",                 "nickname": "阿星5679",                 "followFlag": 1,                 "liveStatus": 2,                 "username": "v2_060000231003b20faec8c7ea8f1ecbd1c901ef3cb0773696efb506324185fdd53ba44426a8a7@finder",                 "status": 0             },             {                 "clubInfo": {},                 "spamStatus": 0,                 "liveInfo": {                     "switchFlag": 53727,                     "micSetting": {},                     "anchorStatusFlag": 2048,                     "lotterySetting": {                         "attendType": 4,                         "settingFlag": 0                     }                 },                 "signature": "啦啦啦啦啦啦",                 "headUrl": "https://wx.qlogo.cn/finderhead/ver_1/y0XfC6SVbzYVl1SIZt2ZaMbicpXmQqmHPK6oibGpfesFYjVevZOMvkemVWx5YgtUKH3xXNZOoPSztq0Dw23lnF3rBIZNf9S8NsicoEteQNZRqk/0",                 "authInfo": {},                 "extInfo": {                     "country": "CN",                     "province": "Jiangsu",                     "city": "Nanjing",                     "sex": 1                 },                 "coverImgUrl": "",                 "extFlag": 262156,                 "followTime": 1702883528,                 "liveCoverImgUrl": "",                 "nickname": "爱德华9813",                 "followFlag": 1,                 "liveStatus": 2,                 "username": "v2_060000231003b20faec8c7e08f10c1d4c803ef36b077bc0b9fb41ae2efc82c20ba5fb68f838a@finder",                 "status": 0             }         ],         "lastBuffer": "CK23AhAA",         "followCount": 2,         "continueFlag": 0     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderIdFav.html

#### 标题：点赞

#### 内容：
点赞
请求URL：
<代码开始>http://域名/finderIdFav<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|bigint|视频号作品id|
|nonceId|是|String|视频号作品nonceId|
|type|是|int|操作类型<br>1:点赞<br>2:取消点赞|
|sessionBuffer|是|String|通过获取用户主页返回的sessionBuffer|
|toUserName|是|String|作者的username|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
请求参数示例
<代码开始>
{
  {
    "wId": "{{wId}}",
    "id": 14200825020179157073,
    "nonceId": "",
    "type": 1,
    "sessionBuffer": "eyJzZXNzaW9uX2lkIjoic2lkXzEzNDUxMDA3MDNfMTY5NDQ5MTA1MDk3NDcxMl8xMDkyODAzNzIwIiwiY3VyX2xpa2VfY291bnQiOjY2Mzc1MywiY3VyX2NvbW1lbnRfY291bnQiOjU1MjMsInJlY2FsbF90eXBlcyI6W10sImRlbGl2ZXJ5X3NjZW5lIjoyLCJkZWxpdmVyeV90aW1lIjoxNjk0NDkxMDUxLCJzZXRfY29uZGl0aW9uX2ZsYWciOjksInJlY2FsbF9pbmRleCI6W10sIm1lZGlhX3R5cGUiOjQsInZpZF9sZW4iOjYxLCJjcmVhdGVfdGltZSI6MTY5Mjg3MDI2MSwicmVjYWxsX2luZm8iOltdLCJzZWNyZXRlX2RhdGEiOiJCZ0FBVlwvMUhmdkVDM2s0QkdhMVJ1SXlYdGZQYVdYTzBGVVg4UHdnWHpHTVZrRnBBOHRBclJ0Q0MzVEFEZXVEUW1aOThRMUhiUVk1TitGWHFkRmYxQ1dwMXZPVThsMkhNK1E9PSIsImlkYyI6MSwiZGV2aWNlX3R5cGVfaWQiOjEzLCJkZXZpY2VfcGxhdGZvcm0iOiJpUGFkMTMsMTkiLCJmZWVkX3BvcyI6MCwiY2xpZW50X3JlcG9ydF9idWZmIjoie1wiaWZfc3BsaXRfc2NyZWVuX2lwYWRcIjowLFwiZW50ZXJTb3VyY2VJbmZvXCI6XCJ7XFxcImZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdFbXB0eVZpZXdDb250cm9sbGVyXzE2OTQ0OTA5NjYxNTEjJDBfMTY5NDQ5MDk1MzUxNSNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9=",
    "toUserName": "v2_060000231003b20faec8c6e78e11c3d0cf01e83cb077761114601a6de6df2f17ee579c1@finder",
    "meUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "meRoleType": "3"
}
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号小红心id失败",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderIdLike.html

#### 标题：小红心

#### 内容：
小红心
请求URL：
<代码开始>http://域名/finderIdLike<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|id|是|bigint|视频号作品id|
|commentId|是|bigint|评论的id|
|nonceId|是|String|视频号作品nonceId|
|type|是|int|视频操作 3喜欢 4不喜欢 评论操作 1喜欢 2不喜欢|
|sessionBuffer|是|String|通过获取用户主页返回的sessionBuffer|
|toUserName|是|String|作者的username|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "id": 14200825020179157073,
    "commentId": 0,
    "nonceId": "2000987984335065941_0_39_2_1_1719289344187698",
    "type": 3,
    "sessionBuffer": "eyJzZXNzaW9uX2lkIjoic2lkXzEzNDUxMDA3MDNfMTY5NDQ5MTA1MDk3NDcxMl8xMDkyODAzNzIwIiwiY3VyX2xpa2VfY291bnQiOjY2Mzc1MywiY3VyX2NvbW1lbnRfY291bnQiOjU1MjMsInJlY2FsbF90eXBlcyI6W10sImRlbGl2ZXJ5X3NjZW5lIjoyLCJkZWxpdmVyeV90aW1lIjoxNjk0NDkxMDUxLCJzZXRfY29uZGl0aW9uX2ZsYWciOjksInJlY2FsbF9pbmRleCI6W10sIm1lZGlhX3R5cGUiOjQsInZpZF9sZW4iOjYxLCJjcmVhdGVfdGltZSI6MTY5Mjg3MDI2MSwicmVjYWxsX2luZm8iOltdLCJzZWNyZXRlX2RhdGEiOiJCZ0FBVlwvMUhmdkVDM2s0QkdhMVJ1SXlYdGZQYVdYTzBGVVg4UHdnWHpHTVZrRnBBOHRBclJ0Q0MzVEFEZXVEUW1aOThRMUhiUVk1TitGWHFkRmYxQ1dwMXZPVThsMkhNK1E9PSIsImlkYyI6MSwiZGV2aWNlX3R5cGVfaWQiOjEzLCJkZXZpY2VfcGxhdGZvcm0iOiJpUGFkMTMsMTkiLCJmZWVkX3BvcyI6MCwiY2xpZW50X3JlcG9ydF9idWZmIjoie1wiaWZfc3BsaXRfc2NyZWVuX2lwYWRcIjowLFwiZW50ZXJTb3VyY2VJbmZvXCI6XCJ7XFxcImZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdFbXB0eVZpZXdDb250cm9sbGVyXzE2OTQ0OTA5NjYxNTEjJDBfMTY5NDQ5MDk1MzUxNSNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9=",
    "toUserName": "v2_060000231003b20faec8c6e78e11c3d0cf01e83cb077761114601a6de6df2f17ee579c1@finder",
    "meUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "meRoleType": "3"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号小红心id成功",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderHome.html

#### 标题：获取个人主页

#### 内容：
获取个人主页
请求URL：
<代码开始>http://域名/finderHome<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f"
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "userName": "v2_060000231003b20faec03ec3db0776955d3d97c6b329d6aa58693bcdb7ad1@finder",         "aliasInfo": [             {                 "nickName": "朝夕",                 "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5grqOsJtnHiaiapZ4cv43GNBzh1sA8NGkwbrvI7Kg3vTcQ/0",                 "roleType": 1             },             {                 "nickName": "朝夕v",                 "headImgUrl": "http://wx.qlogo.cn/finderhead/Q3auHgzwzM5grqOsJtnHiaiapZ4cv43GNBJkH0guXYeulzge7e7IQwHg/0",                 "roleType": 3             }         ],         "currentAliasRoleType": 3,         "finderList": [             "CgASqwIKVnYyXzA2MDAwMDIzMTAwM2IyMGZhZWM4YzZlMThmMTBjN2Q2YzkwM2VjM2RiMDc3Njk1NWQzZDk3YzZiMzI5ZDZhYTU4NjkzYmNkYjdhZDFAZmluZGVyEgfmnJ3lpJV2GpUBaHR0cDovL3d4LnFsb2dvLmNuL2ZpbmRlcnVzZXJuYW1lXFxcIjpcXFwiXFxcIixcXFwiZmVlZGlkXFxcIjpcXFwiXFxcIn1cIixcImV4dHJhaW5mb1wiOlwie1xcbiBcXFwicmVnY291bnRyeVxcXCIgOiBcXFwiQ05cXFwiXFxufVwiLFwic2Vzc2lvbklkXCI6XCJTcGxpdFZpZXdFbXB0eVZpZXdDb250cm9sbGVyXzE2OTQ0OTA5NjYxNTEjJDBfMTY5NDQ5MDk1MzUxNSNcIixcImp1bXBJZFwiOntcInRyYWNlaWRcIjpcIlwiLFwic291cmNlaWRcIjpcIlwifX0iLCJvYmplY3RfaWQiOjE0MjAwODI1MDIwMTc5MTU3MDczLCJmaW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9=",             "CgASrgIKVnYyXzA2MDAwMDIzMTAwM2IyMGZhZWM4YzZlNzhhMWZjMmQzY2QwNmViMzBiMDc3YzU1ODdiNzljYjU2YjAzZjEyYTMyZmNjOGI3N2Y5ZTdAZmluZGVyEgjmnJ3lpJV2dhqZAWh0dHA6Ly93eC5xbG9nby5jbi9maW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9=",             "CgASrgIKVnYyXzA2MDAwMDIzMTAwM2IyMGZhZWM4YzZlNzhhMWZjMmQzY2QwNmViMzBiMDc3YzU1ODdiNzljYjU2YjAzZjEyYTMyZmNjOGI3N2Y5ZTdAZmluZGVyEgjmnJ3lpJV2dhqZAWh0dHA6Ly93eC5xbG9nby5jbi9maW5kZXJfdWluIjoxMzEwNDgwNDQ5OTAxODE3OCwiZ2VvaGFzaCI6MzM3NzY5OTcyMDUyNzg3MiwicnFzdG0iOjE2OTQ0OTEwNTA1MzMsInJzc3RtIjoxNjk0NDkxMDUxMDEwLCJycWN0bSI6MTY5NDQ5MDk3NDQwOSwiZW50cmFuY2Vfc2NlbmUiOjIsImNhcmRfdHlwZSI6MywiZXhwdF9mbGFnIjoyMTY3OTA5MSwidXNlcl9tb2RlbF9mbGFnIjo4LCJjdHhfaWQiOiIyLTMtMzItMTI0N2E0YjVhOTQ4YzI4Yjg0NWZiM2Y0N2EyNTE4M2ExNjk0NDkwOTcxMjc3Iiwib2JqX2ZsYWciOjEzNDI1MDQ5NiwiZXJpbCI6W10sInBna2V5cyI6W10sIm9ial9leHRfZmxhZyI6OTg1MTJ9="         ],         "showInWxFinderUsername": "v2_060000231003b20ffc2d3cd06eb30b077c5587b79cb56b03f12a32fcc8b77f9e7@finder"     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/modFinderProfile.html

#### 标题：修改视频号资料

#### 内容：
修改视频号资料
请求URL：
<代码开始>http://域名/modFinderProfile<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|nickName|是|String|视频号昵称|
|signature|是|String|视频号简介|
|headImgUrl|是|String|视频号头像链接|
|country|是|String|国家|
|province|是|String|省份|
|city|是|String|城市|
|sex|是|int|性别|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "meUserName": "v2_060000231003b20faec8c6e18f10c7d6c903ec3db0776955d3d97c6b329d6aa58693bcdb7ad1@finder",
    "meRoleType": 3,
    "nickName": "",
    "signature": "",
    "headImgUrl": "",
    "country": "",
    "province": "",
    "city": "",
    "sex": 2
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "视频号小红心id失败",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/privateSend.html

#### 标题：私信文字

#### 内容：
私信文字
请求URL：
<代码开始>http://域名/privateSend<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|toUserName|是|String|视频号用户的编码|
|myUserName|是|String|当前微信的用户编码|
|sessionId|是|String|通过/getSessionId接口获取|
|content|是|String|私信内容|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "toUserName": "v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder",
    "myUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "sessionId": "69fd3a9cc1180847d8b5a1533bf285b99a83784cfd4cfdadea78974509359c74@findermsg",
    "content": "哈喽"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "newMsgId": 1248576160896589973
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/privateSendImg.html

#### 标题：私信图片

#### 内容：
私信图片
请求URL：
<代码开始>http://域名/privateSendImg<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|toUserName|是|String|视频号用户的编码|
|sessionId|是|String|通过/getSessionId接口获取|
|imgUrl|是|String|图片地址|
小提示：
第一次私信无法发送图片，需对方回复后方可发送
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "toUserName": "v2_060000231003b20faec8cae18a1ec5d0cb07ea33b077ba915250774edbea38082ea6b24af229@finder",
    "sessionId": "a37c87fbfb8c07ca21d29bea6e3feef1cd740982cfda8407bde2dc2ccbba7f0c@findermsg",
    "imgUrl": "https://pics7.***************/feed/5bafa40f4bfbfbed6b32473c12ec31fdd.jpeg@f_auto?token=11b9f52e463efdcbd43acc274d55350"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "newMsgId": 1248576160896589973
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getContactDetails.html

#### 标题：获取私信联系人信息

#### 内容：
获取私信联系人信息
请求URL：
<代码开始>http://域名/finder/getContactDetails<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
|contactUserName|是|String|私信联系人的username|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "contactUserName": "fv1_552fe39c023a38d299394e3832455d574586ae4e399e51f3f17d414691ae85b@findermsgstranger",
    "myUserName":"v2_060000231003b20faec8cae18a1ec5d0cb07eab077ba915250774edbea38082ea6b24af229@finder",
    "myRoleType": 3
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "查询视频号私信联系人详情成功",     "data": [         {             "wxUsernameV5": "v5_020b0a16610401000000000023390adabe0bb3000000b1afa7d87e3dd43ef4317a780e33c2996857e4fdf7e8b0f6772a3d0e16c79d1a10bf247770dfef7bd0280d9b6a78205e554b90a5a94de2a3349cfcf4@stranger",             "signature": "从",             "nickname": "。",             "headUrl": "https://wx.qlogo.cn/mmhead/ver_1/IJW4tmWjmDDKtcD2BcK876rfEDuYNqhayoFJOK1DfxX3z3BfecTRJiael8s9ibNjKiaeVjnOkiabTyatdxxnIbmGWfa2Shv4GONDiaIhQCFvXA/132",             "msgInfo": {                 "sessionId": "552fe39c023a38d299394e3832455d5774586ae399e51f3f17d414691ae85b@findermsg",                 "msgUsername": "fv1_552fe39c023a38d299394e3832455d57586ae4e399e51f3f17d414691ae85b@findermsgstranger"             },             "username": "fv1_552fe39c023a38d299394e3832455774586ae4e399e51f3f17d414691ae85b@findermsgstranger",             "extInfo": {                 "country": "CN",                 "province": "Jiangsu",                 "city": "Nanjing",                 "sex": 1             }         }     ] }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getSessionId.html

#### 标题：获取私信SessionId

#### 内容：
获取私信SessionId
请求URL：
<代码开始>http://域名/getSessionId<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|toUserName|是|String|视频号用户的编码|
|myUserName|是|String|自己的用户编码|
|type|是|int|类型 1是视频号身份 2是自身微信号身份<br>type=1时，myUserName必传 type=2时，myUserName为空|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "toUserName": "v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder",
    "myUserName": "xxxxxxxxxxxxxxxxxxxxxxxxxxx@finder",
    "type": 1
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "sessionId": "69fd3a9cc1180847d8b5a1533bf285b99a83784cfd4cfdadea78974509359c74@findermsg",
        "enableAction": 0,
        "toUsername": "v2_060000231003b20faec8c6e78010c3d4c605eb3cb077f16e37c172145877400390b1170a0299@finder"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/getQrCode.html

#### 标题：获取我的视频号二维码

#### 内容：
获取我的视频号二维码
请求URL：
<代码开始>http://域名/finder/getQrCode<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|meUserName|是|String|自己的用户编码（获取个人主页接口返回的userName）|
|meRoleType|是|int|自己的角色类型，根据角色关注（获取个人主页接口返回的roleType）|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "myUserName":"v2_060000231003b20faec8cae18a1ec5d0cb07eab077ba915250774edbea38082ea6b24af229@finder",
    "myRoleType": 3
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "获取视频号我的二维码成功",
    "data": {
        "qrUrl": "https://weixin.qq.com/f/EKhjEMLxIQHxoyT3vffquQ"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/finderUpload.html

#### 标题：上传视频号视频

#### 内容：
上传视频号视频
请求URL：
<代码开始>http://域名/finderUpload<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|videoUrl|是|String|视频链接|
|imgUrl|是|String|视频封面图片链接|
请求参数示例
<代码开始>
{
    "wId": "{{wId}}",
    "videoUrl":"https://pics6.***************/feed/e824b899a9014c083a6e7dfa8df4e1047af4f4b0.mp4",
    "imgUrl": "https://pics6.***************/feed/e824b899a9014c083a6e7dfa8df4e1047af4f4b0.jpeg@f_auto?token=fb7ac3bf714b8a6d05f4d1d8f44d7ca"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "fileUrl": "http://wxapp.tc.qq.com/251/20302/stodownload?bizid=1023&dotrans=0&encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Zn59wWGKtPKU7XeFey6Gmk5u2BLznEOJrqFUy37lHx0tFeRFc5SWn3wEic7G47PU6PjFia6lA35eRpS6FlNe4jhbMLfic3vY58FG7tSE1iaMA42ss&hy=SZ&idx=1&m=6e95f9d79588843ac259b780f0cbf20f&token=6xykWLEnztIy9Tia9kRZ3cECxpZ0O13rmJ2rkChlzDG9JDicybIofoLAsGVz9GTe9sqQ3ckuAbKr7M1dBMmptWZg&uzid=2",
        "thumbUrl": "http://wxapp.tc.qq.com/251/20350/stodownload?bizid=1023&dotrans=0&filekey=30350201010421301f020200fb0402535a0410cc9a86fb446c70f5ed6c16ca7754f4c102030090c2040d00000004627466730000000132&hy=SZ&m=cc9a86fb446c70f5ed6c16ca7754f4c1&storeid=56684c7b4000466e28399cc84000000fb00004f7e535a2f2f00115674d8bad&uzid=2",
        "mp4Identify": "5118be81f2de929d3c79fc8777732b43",
        "fileSize": 1315979,
        "thumbMd5": "cc9a86fb446c70f5ed6c16ca7754f4c1",
        "fileKey": "finder_upload_6764713041_zhangchuan2288"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/shipinhao/scanFinderHelper.html

#### 标题：登录视频号助手

#### 内容：
登录视频号助手
请求URL：
<代码开始>http://域名/scanFinderHelper<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|url|是|String|视频号助手官方二维码解析的地址（二维码 ）|
请求参数示例
<代码开始>
{
    "wId": "2c7a5bf6-e23d-x-8f03-b90e844b539f",
    "url": "https://channels.weixin.qq.com/mobile/confirm_login.html?token=AQAAADuBfwIdyYlrciNBWQ"
}
<代码结束>
成功返回示例
{     "code": "1000",     "message": "处理成功",     "data": {         "sessionId": "BgAAoFaoS/vANMJKEyZH+Au/nki9zvLCL0es8VRNvfPrAC96a8lTJRzJHPzeUODIw7y/yQPlPGp5C07/D+hsIwjjOSx6q0m",         "acctStatus": 1,         "finderList": [             {                 "finderUsername": "v2_060000231003b20faec8c6e18f10cc903ec3db0776955d3d97c6b329d6aa58693bcdb7ad1@finder",                 "nickname": "vvvv",                 "headImgUrl": "https://wx.qlogo.cn/finderhead/Q3auHgzwqOsJtnHiaiapZ4cv43GNBJkH0guXYeulzge7e7IQwHg/0",                 "coverImgUrl": "",                 "spamFlag": 0,                 "acctType": 1,                 "authIconType": 0,                 "ownerWxUin": 2207814660,                 "adminNickname": "hhh。",                 "categoryFlag": "0",                 "uniqId": "sphZ1RF6CMuZAn",                 "isMasterFinder": true             }         ]     } }
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/biao-qian/addContactLabel.html

#### 标题：创建标签

#### 内容：
创建标签
简要描述：
添加标签
请求URL：
<代码开始>http://域名地址/addContactLabel<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|labelName|是|String|标签名称|
请求参数示例
<代码开始>
{
    "wId":"349be9b5-8734-45ce-811d-4e10ca568c67",
    "labelName": "看一看世界"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": {
        "labelId": 3,
        "labelName": "看一看世界"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/biao-qian/modifyContactLabel.html

#### 标题：修改联系人标签

#### 内容：
修改联系人标签
Attention
移除标签下的好友： 把需移除的好友所有标签查出来（通讯录详情接口返回标签id，数据库需缓存），去掉想移出的标签id，labelIdList参数放进其他所有标签id。
增加标签新好友： 把需添加的好友所有标签查出来（通讯录详情接口返回标签id，数据库需缓存），labelIdList参数放进新标签id和原有所有标签id。
某个标签下批量添加/移除好友： 查出所有好友所在的标签id，每个和上方一样单独调用。
简要描述：
修改联系人标签
请求URL：
<代码开始>http://域名地址/modifyContactLabel<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|wcId|是|String|好友微信id|
|labelIdList|是|String|标签标识，多个标签已 "，" 号分割|
请求参数示例
<代码开始>
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "wcId":"wxid_jg6e5v0b8bp322",
    "labelIdList":"1,2"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/biao-qian/delContactLabel.html

#### 标题：删除联系人标签

#### 内容：
删除联系人标签
简要描述：
删除联系人标签
请求URL：
<代码开始>http://域名地址/delContactLabel<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
|labelIdList|是|String|标签标识，多个标签已 "，" 号分割|
请求参数示例
<代码开始>
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "labelIdList":"1,2"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|

### 链接：https://wkteam.cn/api-wen-dang2/biao-qian/getContactLabelList.html

#### 标题：获取标签列表

#### 内容：
获取标签列表
简要描述：
获取标签列表
请求URL：
<代码开始>http://域名地址/getContactLabelList<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|登录实例标识|
请求参数示例
<代码开始>
{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": [
        {
            "labelName": "看一看世界",
            "labelId": 3
        }
    ]
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|String|1000成功<br>1001失败|
|msg|String|反馈信息|
|data|JSONObject|
|labelId|int|标签标识|
|labelName|int|标签名称|

### 链接：https://wkteam.cn/api-wen-dang2/shou-cang-jia/huo-qu-shou-cang-jia-lie-biao.html

#### 标题：获取收藏夹列表

#### 内容：
获取收藏夹列表
简要描述：
获取收藏夹内容
请求URL：
<代码开始>http://域名地址/weChatFavorites/favSync<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|微信实列ID|
|keyBuf|是|byte[]|第一次传null,如果接口返回keyBuf第二次传keyBuf|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data||
|ret|int|接口状态 0:成功|
|favList|list|收藏列表|
|keyBuf|byte[]|同步密钥|
|continueFlag|int|0:表示同步结束，1:表示还需要继续同步|
|favId|int|收藏标识|
|type|int|收藏类型|
|updateTime|long|收藏时间戳|
请求参数示例
<代码开始>
{
    "wId": "0000016e-c561-9bbd-0001-3dc796084901",
    "keyBuf":null
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": {
        "ret": 0,
        "favList": [
            {
                "favId": 1,
                "type": 2,
                "updateTime": 1538560491
            },
            ......
        ],
        "keyBuf": null,
        "continueFlag": 0
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/shou-cang-jia/huo-qu-shou-cang-jia-nei-rong.html

#### 标题：获取收藏夹内容

#### 内容：
获取收藏夹内容
简要描述：
获取收藏详细信息
请求URL：
<代码开始>http://域名地址/weChatFavorites/getFavItem<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|微信实列ID|
|favId|是|int|收藏标识|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data||
|object|xml|收藏详情|
|updateTime|long|收藏时间戳|
请求参数示例
<代码开始>
{
    "wId": "0000016e-c561-9bbd-0001-3dc796084901",
    "favId":1
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": {
        "favId": 1,
        "object": "<favitem type=\"2\"><source sourcetype=\"1\"><fromusr>wxid_gia1nwcgpobz22</fromusr><tousr>wxid_gia1nwcgpobz22</tousr></source><datalist count=\"1\"><dataitem datatype=\"2\" dataid=\"fe0f5bf7a6e734f10f4c94c1afe8c0e9\"><cdn_thumburl>30500201000449304702010002049c1da4d602033d11fd0204ed3e5b6502045b345cc6042265653239376562613737326138343233383430356466326462356330613266375f740204020027110201000400</cdn_thumburl><cdn_dataurl>304e020100044730450201000204359e277602033d14b90204717124b702045b4d9436042066386135656234396533636365373466353663376130323963393637353564370204020027110201000400</cdn_dataurl><cdn_thumbkey>f3698526452141a298c5a28f8fdf94e2</cdn_thumbkey><cdn_datakey>740d9b78211f47288213c9b6e6fd25b4</cdn_datakey><fullmd5>105062c3583d322f8336f0b38ba286ec</fullmd5><head256md5>5f0455eea82e20d7ba9a377d04aaa35a</head256md5><fullsize>18622</fullsize><thumbfullmd5>e961ae8fcecc36474f0d4a169fc5ade5</thumbfullmd5><thumbhead256md5>d41d8cd98f00b204e9800998ecf8427e</thumbhead256md5><thumbfullsize>12061</thumbfullsize><datadesc></datadesc><datatitle></datatitle></dataitem></datalist><recommendtaglist></recommendtaglist></favitem>",
        "updateTime": 1538560491
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/shou-cang-jia/shan-chu-shou-cang-jia-nei-rong.html

#### 标题：删除收藏夹内容

#### 内容：
删除收藏夹内容
简要描述：
删除收藏夹内容
请求URL：
<代码开始>http://域名地址/weChatFavorites/delFavItem<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|微信实列ID|
|favId|是|int|收藏标识|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data||
请求参数示例
<代码开始>
{
    "wId": "0000016e-c561-9bbd-0001-3dc796084901",
    "favId":1
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": "收藏删除成功"
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/wei-xin-guan-li/pi-liang-xia-xian-wei-xin-hao.html

#### 标题：批量下线微信号

#### 内容：
批量下线微信号
简要描述：
下线某个或某些已登录的微信（假如出现登录数量已满，则调用本接口释放）
请求URL：
<代码开始>http://域名地址/member/offline<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|account|是|string|账号|
|wcIds|是|list|须下线的微信id|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data||
请求参数示例
<代码开始>
{

   "account": "**********",
   "wcIds": ["wxid_1r6wafuhou3e22","wxid_wl9qchkanp9u22"]

}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": null
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/wei-xin-guan-li/duan-xian-chong-lian.html

#### 标题：查询账号中在线的微信列表

#### 内容：
查询账号中在线的微信列表
简要描述：
此接口应用场景是查询在线的wid和wcid列表
请求URL：
<代码开始>http://域名地址/queryLoginWx<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
无参数：
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功（在线），1001失败（离线）|
|message|string|反馈信息|
|wcId|string|微信id|
|wId|string|登录实例标识|
请求参数示例
<代码开始>
{    

}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "成功",
    "data": [
        {
            "wcId": "wxid_i6qsbbjenju2",
            "wId": "72223018-7f2a-4f4f-bfa3-26e47dbd61"
        }
    ]
}
<代码结束>
失败返回示例
<代码开始>
{
    "code": "1001",
    "message": "失败"
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/wei-xin-guan-li/cha-xun-wei-xin-shi-fou-zai-xian.html

#### 标题：查询微信是否在线

#### 内容：
查询微信是否在线
简要描述：
查询是否在线
请求URL：
<代码开始>http://域名地址/isOnline<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功（在线），1001失败（离线）|
|msg|string|反馈信息|
请求参数示例
<代码开始>
{
    "wId": "0000016e-abcd-0ea8-0002-d8c2dfdb0bf3"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": {
        "isOnline": true
    }
}
<代码结束>
失败返回示例
<代码开始>
{
    "message": "成功",
    "code": "1000",
    "data": {
        "isOnline": false
    }
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/te-shu/refreshDevice.html

#### 标题：刷新设备

#### 内容：
刷新设备
简要描述：
当第三步接口返回"请使用/refreshDevice接口刷新设备"时，调用此接口刷新设备，用户重新扫码此接口返回的二维码，然后再调用第三步接口即可
PS：一般提示验证码时需要用到本接口，这种完整处理流程：第三步接口返回说明需要调用此接口->调用此接口取码且扫码->调用第三步接口且不传验证码即可登录成功。
请求URL：
<代码开始>http://域名地址/refreshDevice<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|String|获取二维码返回的wId|
小提示：
一般本接口出现在需要手机上输入验证ipad验证码的场景，一般情况下无需调用
请求参数示例
<代码开始>
{
    "wId": "6a696578-16ea-4edc-ac8b-e609bca39c69"
}
<代码结束>
成功返回示例
<代码开始>
{
    "message": "登录成功",
    "code": "1000",
    "data": {
        "wId": "0000016e-63ef-3a9c-0001-ed3311628ef4",
        "qrCodeUrl": "http://127.0.0.1:18081/1573634652963-500000.png"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "刷新设备失败",
    "code": "1001",
    "data": null
}
<代码结束>
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|data|||
|wId|string|登录实例标识|
|qrCodeUrl|string|扫码登录地址|

### 链接：https://wkteam.cn/api-wen-dang2/te-shu/cdnDownFile.html

#### 标题：CDN资源下载

#### 内容：
CDN资源下载
简要描述：
下载资源类接口，例如小程序封面图，链接消息封面图，收藏夹中的加密的视频、图片、文件、笔记等
请求URL：
<代码开始>http://域名地址/cdnDownFile<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回
参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|cdnUrl|是|string|XML获取|
|aeskey|是|string|XML获取|
|fileType|是|int|1：高清图片 2：常规图片 3：缩略图 4：视频 5：文件<br>图片如果下载失败，建议尝试其他几种，并不是所有图片都有高清、常规、缩略|
|fileName|是|string|资源全称（例 'test.png'）|
|totalSize|是|int|XML中的length|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
请求参数示例
<代码开始>
{
  "wId": "59c7a5b1-3c01-480c-828c-46eade762287",
  "cdnUrl": "307f02010004783076020100020464e5eb6502033d14b90204ac612fb70204611399240451777875706c6f61645f7a68616e67636875616e32323838584d755078697958744f5f313632383637343334305f346564666331302d613337622d343439382d393934372d6431663230333633376237640204011818020201000400",
  "aeskey": "4eddfc10-a37b-4498-9947-d1f203637b7d",
  "fileType":3,
  "fileName": "test.jpg",
  "totalSize": 206249
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "url": "http://xdkj-enterprise.oss-cn-beijing.aliyuncs.com/20210812/cab24cf9-867c-486b-bef4-d22bef89d444-test.jpg?Expires=1629350242&OSSAccessKeyId=LTAI4G5VB9BMxMDV14c6USjt&Signature=fWvkoKlE%2F4iIMxbN99DBGJDKRjA%3D"
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/te-shu/uploadCdnImage.html

#### 标题：CDN图片上传

#### 内容：
CDN图片上传
简要描述：
图片上传接口，主要应用于动态替换小程序封面图（更改XML中的cdnkey相关信息）+ 多微信发同内容场景，动态组装转发接口xml发送
请求URL：
<代码开始>http://域名地址/uploadCdnImage<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|content|是|string|图片url链接|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|cdnUrl|string|图片cdn信息（用于自定义小程序图片参数）|
|aesKey|string|图片key信息（用于自定义小程序图片参数）|
|hdLength|string|图片大小（用于自定义小程序图片参数）|
请求参数示例
<代码开始>
{
    "wId": "0000016e-63eb-f319-0001-ed01076abf1f",
    "content": "http://photocdn.sohu.com/20120323/Img338614056.jpg"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "发送图片消息成功",
    "data": {
        "cdnUrl": "307b0201000474307202010002041b9042eb02033d14b902045412607102046113de29044d777875706c6f61645f636861745f7365766f6e4b4f4a494d70615456765f313632383639323030395f32656631613130312d613939392d346161622d626263352d3765363034383262633135350204011818020201000400",
        "aesKey": "52efd887fcfdad1d71c29d0129daaabd",
        "hdLength": 173475
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>

### 链接：https://wkteam.cn/api-wen-dang2/te-shu/sendCdnVideo.html

#### 标题：CDN视频上传

#### 内容：
CDN视频上传
简要描述：
视频上传接口，主要应用于多微信发同内容场景，动态组装转发接口xml发送
请求URL：
<代码开始>http://域名地址/sendCdnVideo<代码结束>
请求方式：
POST
请求头Headers：
Content-Type：application/json
Authorization：login接口返回 参数：
|参数名|必选|类型|说明|
| ---- | ---- | ---- | ---- |
|wId|是|string|登录实例标识|
|path|是|string|视频url链接|
|thumbPath|是|string|图片url链接|
返回数据：
|参数名|类型|说明|
| ---- | ---- | ---- |
|code|string|1000成功，1001失败|
|msg|string|反馈信息|
|cdnUrl|string|视频cdn信息|
|aesKey|string|视频key信息|
|length|string|视频长度|
请求参数示例
<代码开始>
{
    "wId": "xxxxx",
    "path": "https://img.xingkonglian.net/img/1434818025755774976.mp4",
    "thumbPath": "https://img.xingkonglian.net/img/1434770921234632704.png"
}
<代码结束>
成功返回示例
<代码开始>
{
    "code": "1000",
    "message": "发送视频消息成功",
    "data": {
        "cdnUrl": "30670201000460305e02010002041b9042eb02033d14b90204fdb2120e020461554e300439777875706c6f61645f5f313633323938303532375f63396333393539312d326639652d346631352d386131662d6166323265343962643130640204011800040201000400",
        "aesKey": "1a8e3941e3cc233e6f151430088eec6e",
        "length": 3124240
    }
}
<代码结束>
错误返回示例
<代码开始>
{
    "message": "失败",
    "code": "1001",
    "data": null
}
<代码结束>