using HappyWechat.Application.DTOs.MessageProcess;

namespace HappyWechat.Application.DTOs.FileProcessing;

/// <summary>
/// 文件处理选项
/// </summary>
public class FileProcessingOptions
{
    /// <summary>
    /// 最大并发处理数
    /// </summary>
    public int MaxConcurrency { get; set; } = 5;
    
    /// <summary>
    /// 处理超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300;
    
    /// <summary>
    /// 是否启用文件去重
    /// </summary>
    public bool EnableDeduplication { get; set; } = true;
    
    /// <summary>
    /// 是否启用智能重试
    /// </summary>
    public bool EnableSmartRetry { get; set; } = true;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 是否保留本地文件
    /// </summary>
    public bool KeepLocalFiles { get; set; } = false;
    
    /// <summary>
    /// 本地文件保存天数
    /// </summary>
    public int LocalFileRetentionDays { get; set; } = 7;
    
    /// <summary>
    /// 强制使用的处理策略（可选）
    /// </summary>
    public ProcessingStrategy? ForceStrategy { get; set; }
    
    /// <summary>
    /// 文件大小阈值配置
    /// </summary>
    public FileSizeThresholds SizeThresholds { get; set; } = new();
    
    /// <summary>
    /// 质量控制选项
    /// </summary>
    public QualityControlOptions QualityControl { get; set; } = new();
    
    /// <summary>
    /// 性能优化选项
    /// </summary>
    public PerformanceOptions Performance { get; set; } = new();
    
    /// <summary>
    /// 监控选项
    /// </summary>
    public MonitoringOptions Monitoring { get; set; } = new();
    
    /// <summary>
    /// 安全选项
    /// </summary>
    public SecurityOptions Security { get; set; } = new();
    
    /// <summary>
    /// 扩展选项
    /// </summary>
    public Dictionary<string, object>? ExtendedOptions { get; set; }
}

/// <summary>
/// 文件大小阈值配置
/// </summary>
public class FileSizeThresholds
{
    /// <summary>
    /// 小文件阈值（字节）- 直接处理
    /// </summary>
    public long SmallFileThreshold { get; set; } = 5 * 1024 * 1024; // 5MB
    
    /// <summary>
    /// 中等文件阈值（字节）- 队列处理
    /// </summary>
    public long MediumFileThreshold { get; set; } = 50 * 1024 * 1024; // 50MB
    
    /// <summary>
    /// 大文件阈值（字节）- 特殊处理
    /// </summary>
    public long LargeFileThreshold { get; set; } = 200 * 1024 * 1024; // 200MB
    
    /// <summary>
    /// 最大允许文件大小（字节）
    /// </summary>
    public long MaxFileSize { get; set; } = 500 * 1024 * 1024; // 500MB
}

/// <summary>
/// 质量控制选项
/// </summary>
public class QualityControlOptions
{
    /// <summary>
    /// 是否验证文件完整性
    /// </summary>
    public bool ValidateFileIntegrity { get; set; } = true;
    
    /// <summary>
    /// 是否检查文件格式
    /// </summary>
    public bool ValidateFileFormat { get; set; } = true;
    
    /// <summary>
    /// 允许的图片格式
    /// </summary>
    public List<string> AllowedImageFormats { get; set; } = new() { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
    
    /// <summary>
    /// 允许的视频格式
    /// </summary>
    public List<string> AllowedVideoFormats { get; set; } = new() { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv" };
    
    /// <summary>
    /// 允许的音频格式
    /// </summary>
    public List<string> AllowedAudioFormats { get; set; } = new() { ".mp3", ".wav", ".flac", ".aac", ".m4a", ".amr" };
    
    /// <summary>
    /// 允许的文档格式
    /// </summary>
    public List<string> AllowedDocumentFormats { get; set; } = new() { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt" };
    
    /// <summary>
    /// 是否启用内容安全扫描
    /// </summary>
    public bool EnableContentScan { get; set; } = true;
    
    /// <summary>
    /// 是否生成文件摘要
    /// </summary>
    public bool GenerateDigest { get; set; } = true;
}

/// <summary>
/// 性能优化选项
/// </summary>
public class PerformanceOptions
{
    /// <summary>
    /// 是否启用并行下载
    /// </summary>
    public bool EnableParallelDownload { get; set; } = true;
    
    /// <summary>
    /// 是否启用断点续传
    /// </summary>
    public bool EnableResumeDownload { get; set; } = true;
    
    /// <summary>
    /// 是否启用压缩
    /// </summary>
    public bool EnableCompression { get; set; } = true;
    
    /// <summary>
    /// 压缩质量（1-100）
    /// </summary>
    public int CompressionQuality { get; set; } = 85;
    
    /// <summary>
    /// 缓冲区大小（字节）
    /// </summary>
    public int BufferSize { get; set; } = 8192;
    
    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;
    
    /// <summary>
    /// 读取超时时间（秒）
    /// </summary>
    public int ReadTimeoutSeconds { get; set; } = 60;
    
    /// <summary>
    /// 是否启用智能负载均衡
    /// </summary>
    public bool EnableLoadBalancing { get; set; } = true;
}

/// <summary>
/// 监控选项
/// </summary>
public class MonitoringOptions
{
    /// <summary>
    /// 是否记录详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;
    
    /// <summary>
    /// 是否收集性能指标
    /// </summary>
    public bool CollectMetrics { get; set; } = true;
    
    /// <summary>
    /// 是否启用实时监控
    /// </summary>
    public bool EnableRealTimeMonitoring { get; set; } = true;
    
    /// <summary>
    /// 指标收集间隔（秒）
    /// </summary>
    public int MetricsIntervalSeconds { get; set; } = 10;
    
    /// <summary>
    /// 是否发送处理进度通知
    /// </summary>
    public bool SendProgressNotifications { get; set; } = false;
    
    /// <summary>
    /// 进度通知间隔（秒）
    /// </summary>
    public int ProgressNotificationIntervalSeconds { get; set; } = 30;
    
    /// <summary>
    /// 是否记录错误详情
    /// </summary>
    public bool LogErrorDetails { get; set; } = true;
    
    /// <summary>
    /// 错误日志级别
    /// </summary>
    public string ErrorLogLevel { get; set; } = "Error";
}

/// <summary>
/// 安全选项
/// </summary>
public class SecurityOptions
{
    /// <summary>
    /// 是否验证文件来源
    /// </summary>
    public bool ValidateFileSource { get; set; } = true;
    
    /// <summary>
    /// 允许的域名白名单
    /// </summary>
    public List<string> AllowedDomains { get; set; } = new();
    
    /// <summary>
    /// 禁止的域名黑名单
    /// </summary>
    public List<string> BlockedDomains { get; set; } = new();
    
    /// <summary>
    /// 是否启用病毒扫描
    /// </summary>
    public bool EnableVirusScan { get; set; } = false;
    
    /// <summary>
    /// 是否检查恶意文件
    /// </summary>
    public bool CheckMaliciousFiles { get; set; } = true;
    
    /// <summary>
    /// 最大下载尝试次数
    /// </summary>
    public int MaxDownloadAttempts { get; set; } = 5;
    
    /// <summary>
    /// 是否启用内容类型验证
    /// </summary>
    public bool ValidateContentType { get; set; } = true;
    
    /// <summary>
    /// 是否记录安全事件
    /// </summary>
    public bool LogSecurityEvents { get; set; } = true;
}

/// <summary>
/// 策略选择规则
/// </summary>
public class StrategySelectionRules
{
    /// <summary>
    /// 文件数量阈值
    /// </summary>
    public int FileCountThreshold { get; set; } = 5;
    
    /// <summary>
    /// 队列深度阈值
    /// </summary>
    public int QueueDepthThreshold { get; set; } = 10;
    
    /// <summary>
    /// 系统负载阈值
    /// </summary>
    public double SystemLoadThreshold { get; set; } = 0.8;
    
    /// <summary>
    /// 内存使用阈值
    /// </summary>
    public double MemoryUsageThreshold { get; set; } = 0.8;
    
    /// <summary>
    /// 优先级权重
    /// </summary>
    public Dictionary<ProcessingPriority, double> PriorityWeights { get; set; } = new()
    {
        { ProcessingPriority.Low, 0.25 },
        { ProcessingPriority.Normal, 0.5 },
        { ProcessingPriority.High, 0.75 },
        { ProcessingPriority.Urgent, 1.0 }
    };
    
    /// <summary>
    /// 文件类型权重
    /// </summary>
    public Dictionary<NonTextMessageType, double> FileTypeWeights { get; set; } = new()
    {
        { NonTextMessageType.Image, 1.0 },
        { NonTextMessageType.Voice, 0.8 },
        { NonTextMessageType.Video, 0.6 },
        { NonTextMessageType.File, 0.4 },
        { NonTextMessageType.Emoji, 1.0 }
    };
}