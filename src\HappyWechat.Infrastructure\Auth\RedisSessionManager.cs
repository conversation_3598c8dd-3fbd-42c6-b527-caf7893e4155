using System.Text.Json;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis会话管理器实现
/// </summary>
public class RedisSessionManager : IRedisSessionManager
{
    private readonly IDatabase _redis;
    private readonly ILogger<RedisSessionManager> _logger;
    private readonly RedisAuthenticationOptions _options;
    private readonly string _sessionKeyPrefix;
    private readonly string _userSessionKeyPrefix;
    private readonly string _sessionIndexKey;
    private readonly string _permissionKeyPrefix;

    public RedisSessionManager(
        IServiceProvider serviceProvider,
        ILogger<RedisSessionManager> logger,
        IOptions<RedisAuthenticationOptions> options)
    {
        // 🔧 使用指定的Session数据库（数据库1）
        var connectionMultiplexer = serviceProvider.GetRequiredService<IConnectionMultiplexer>();
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        
        // 从配置获取Session数据库编号
        var sessionDb = configuration.GetValue<int>("Redis:Databases:Session", 1);
        _redis = connectionMultiplexer.GetDatabase(sessionDb);
        _logger = logger;
        _options = options.Value;
        
        var keyPrefix = _options.KeyPrefix;
        _sessionKeyPrefix = $"{keyPrefix}:session:";
        _userSessionKeyPrefix = $"{keyPrefix}:user:";
        _sessionIndexKey = $"{keyPrefix}:sessions";
        _permissionKeyPrefix = $"{keyPrefix}:permissions:";
    }

    public async Task<string> CreateSessionAsync(string userId, string username, IEnumerable<string> roles, IEnumerable<string>? permissions = null)
    {
        try
        {
            var sessionId = GenerateSessionId();
            var now = DateTime.UtcNow;
            var expiresAt = now.AddMinutes(_options.SessionExpirationMinutes);

            var sessionInfo = new RedisSessionInfo
            {
                SessionId = sessionId,
                UserId = userId,
                Username = username,
                Roles = roles,
                Permissions = permissions ?? new List<string>(),
                CreatedAt = now,
                LastAccessAt = now,
                ExpiresAt = expiresAt
            };

            // 检查并清理用户的旧会话（如果超过最大并发数）
            await CleanupUserExcessSessionsAsync(userId);

            // 存储会话信息
            var sessionKey = _sessionKeyPrefix + sessionId;
            var sessionJson = JsonSerializer.Serialize(sessionInfo);
            var expiration = TimeSpan.FromMinutes(_options.SessionExpirationMinutes);
            
            await _redis.StringSetAsync(sessionKey, sessionJson, expiration);

            // 更新用户会话映射
            var userSessionKey = _userSessionKeyPrefix + userId;
            await _redis.SetAddAsync(userSessionKey, sessionId);
            await _redis.KeyExpireAsync(userSessionKey, expiration);

            // 添加到会话索引
            await _redis.SetAddAsync(_sessionIndexKey, sessionId);

            // 缓存用户权限
            if (permissions != null && permissions.Any())
            {
                await UpdateUserPermissionsAsync(userId, permissions);
            }

            if (_options.EnableDetailedLogging)
            {
                _logger.LogInformation("✅ 创建会话成功 - SessionId: {SessionId}, UserId: {UserId}, ExpiresAt: {ExpiresAt}",
                    sessionId, userId, expiresAt);
            }

            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 创建会话失败 - UserId: {UserId}", userId);
            throw;
        }
    }

    public async Task<RedisSessionInfo?> ValidateSessionAsync(string sessionId)
    {
        try
        {
            if (string.IsNullOrEmpty(sessionId))
                return null;

            var sessionKey = _sessionKeyPrefix + sessionId;
            var sessionJson = await _redis.StringGetAsync(sessionKey);

            if (!sessionJson.HasValue)
            {
                if (_options.EnableDetailedLogging)
                {
                    _logger.LogDebug("🔍 会话不存在 - SessionId: {SessionId}", sessionId);
                }
                return null;
            }

            var sessionInfo = JsonSerializer.Deserialize<RedisSessionInfo>(sessionJson!);
            if (sessionInfo == null)
            {
                _logger.LogWarning("⚠️ 会话数据反序列化失败 - SessionId: {SessionId}", sessionId);
                return null;
            }

            // 检查会话是否过期
            if (sessionInfo.ExpiresAt <= DateTime.UtcNow)
            {
                await DestroySessionAsync(sessionId);
                if (_options.EnableDetailedLogging)
                {
                    _logger.LogDebug("⏰ 会话已过期 - SessionId: {SessionId}", sessionId);
                }
                return null;
            }

            // 自动续期
            if (_options.EnableAutoRenewal)
            {
                var renewalThreshold = sessionInfo.ExpiresAt.AddMinutes(-_options.SessionRenewalMinutes);
                if (DateTime.UtcNow >= renewalThreshold)
                {
                    await RenewSessionAsync(sessionId);
                    sessionInfo.ExpiresAt = DateTime.UtcNow.AddMinutes(_options.SessionExpirationMinutes);
                }
            }

            // 更新最后访问时间
            sessionInfo.LastAccessAt = DateTime.UtcNow;
            var updatedJson = JsonSerializer.Serialize(sessionInfo);
            await _redis.StringSetAsync(sessionKey, updatedJson, TimeSpan.FromMinutes(_options.SessionExpirationMinutes));

            return sessionInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 验证会话失败 - SessionId: {SessionId}", sessionId);
            return null;
        }
    }

    public async Task<bool> RenewSessionAsync(string sessionId)
    {
        try
        {
            var sessionKey = _sessionKeyPrefix + sessionId;
            var expiration = TimeSpan.FromMinutes(_options.SessionExpirationMinutes);
            
            var renewed = await _redis.KeyExpireAsync(sessionKey, expiration);
            
            if (renewed && _options.EnableDetailedLogging)
            {
                _logger.LogDebug("🔄 会话续期成功 - SessionId: {SessionId}", sessionId);
            }

            return renewed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 会话续期失败 - SessionId: {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<bool> DestroySessionAsync(string sessionId)
    {
        try
        {
            if (string.IsNullOrEmpty(sessionId))
                return false;

            // 获取会话信息以便清理用户映射
            var sessionInfo = await ValidateSessionAsync(sessionId);
            
            var sessionKey = _sessionKeyPrefix + sessionId;
            var deleted = await _redis.KeyDeleteAsync(sessionKey);

            if (deleted)
            {
                // 从会话索引中移除
                await _redis.SetRemoveAsync(_sessionIndexKey, sessionId);

                // 从用户会话映射中移除
                if (sessionInfo != null)
                {
                    var userSessionKey = _userSessionKeyPrefix + sessionInfo.UserId;
                    await _redis.SetRemoveAsync(userSessionKey, sessionId);
                }

                if (_options.EnableDetailedLogging)
                {
                    _logger.LogInformation("🗑️ 销毁会话成功 - SessionId: {SessionId}", sessionId);
                }
            }

            return deleted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 销毁会话失败 - SessionId: {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<int> DestroyUserSessionsAsync(string userId)
    {
        try
        {
            var userSessionKey = _userSessionKeyPrefix + userId;
            var sessionIds = await _redis.SetMembersAsync(userSessionKey);
            
            var destroyedCount = 0;
            foreach (var sessionId in sessionIds)
            {
                if (await DestroySessionAsync(sessionId!))
                {
                    destroyedCount++;
                }
            }

            // 清理用户会话映射
            await _redis.KeyDeleteAsync(userSessionKey);

            if (_options.EnableDetailedLogging)
            {
                _logger.LogInformation("🗑️ 销毁用户所有会话 - UserId: {UserId}, Count: {Count}", userId, destroyedCount);
            }

            return destroyedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 销毁用户会话失败 - UserId: {UserId}", userId);
            return 0;
        }
    }

    public async Task<IEnumerable<RedisSessionInfo>> GetUserSessionsAsync(string userId)
    {
        try
        {
            var userSessionKey = _userSessionKeyPrefix + userId;
            var sessionIds = await _redis.SetMembersAsync(userSessionKey);
            
            var sessions = new List<RedisSessionInfo>();
            foreach (var sessionId in sessionIds)
            {
                var sessionInfo = await ValidateSessionAsync(sessionId!);
                if (sessionInfo != null)
                {
                    sessions.Add(sessionInfo);
                }
            }

            return sessions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取用户会话失败 - UserId: {UserId}", userId);
            return new List<RedisSessionInfo>();
        }
    }

    public async Task<int> CleanupExpiredSessionsAsync()
    {
        try
        {
            var sessionIds = await _redis.SetMembersAsync(_sessionIndexKey);
            var cleanedCount = 0;

            foreach (var sessionId in sessionIds)
            {
                var sessionKey = _sessionKeyPrefix + sessionId;
                var exists = await _redis.KeyExistsAsync(sessionKey);
                
                if (!exists)
                {
                    // 会话已过期，从索引中移除
                    await _redis.SetRemoveAsync(_sessionIndexKey, sessionId);
                    cleanedCount++;
                }
            }

            if (_options.EnableDetailedLogging && cleanedCount > 0)
            {
                _logger.LogInformation("🧹 清理过期会话完成 - Count: {Count}", cleanedCount);
            }

            return cleanedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 清理过期会话失败");
            return 0;
        }
    }

    public async Task<RedisSessionStatistics> GetSessionStatisticsAsync()
    {
        try
        {
            var sessionIds = await _redis.SetMembersAsync(_sessionIndexKey);
            var stats = new RedisSessionStatistics
            {
                TotalActiveSessions = sessionIds.Length,
                LastCleanupAt = DateTime.UtcNow
            };

            // 统计用户数和角色分布
            var users = new HashSet<string>();
            var roleCount = new Dictionary<string, int>();

            foreach (var sessionId in sessionIds)
            {
                var sessionInfo = await ValidateSessionAsync(sessionId!);
                if (sessionInfo != null)
                {
                    users.Add(sessionInfo.UserId);
                    
                    foreach (var role in sessionInfo.Roles)
                    {
                        roleCount[role] = roleCount.GetValueOrDefault(role, 0) + 1;
                    }
                }
            }

            stats.TotalUsers = users.Count;
            stats.SessionsByRole = roleCount;

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取会话统计失败");
            return new RedisSessionStatistics();
        }
    }

    public async Task<bool> IsAuthenticatedAsync(string sessionId)
    {
        var sessionInfo = await ValidateSessionAsync(sessionId);
        return sessionInfo != null;
    }

    public async Task<IEnumerable<string>> GetUserPermissionsAsync(string userId)
    {
        try
        {
            var permissionKey = _permissionKeyPrefix + userId;
            var permissionsJson = await _redis.StringGetAsync(permissionKey);
            
            if (permissionsJson.HasValue)
            {
                var permissions = JsonSerializer.Deserialize<List<string>>(permissionsJson!);
                return permissions ?? new List<string>();
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取用户权限失败 - UserId: {UserId}", userId);
            return new List<string>();
        }
    }

    public async Task<bool> UpdateUserPermissionsAsync(string userId, IEnumerable<string> permissions)
    {
        try
        {
            var permissionKey = _permissionKeyPrefix + userId;
            var permissionsJson = JsonSerializer.Serialize(permissions);
            var expiration = TimeSpan.FromMinutes(_options.PermissionCacheExpirationMinutes);
            
            await _redis.StringSetAsync(permissionKey, permissionsJson, expiration);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 更新用户权限失败 - UserId: {UserId}", userId);
            return false;
        }
    }

    private string GenerateSessionId()
    {
        return Guid.NewGuid().ToString("N") + DateTime.UtcNow.Ticks.ToString("X");
    }

    private async Task CleanupUserExcessSessionsAsync(string userId)
    {
        try
        {
            var userSessions = await GetUserSessionsAsync(userId);
            var sessionCount = userSessions.Count();

            if (sessionCount >= _options.MaxConcurrentSessions)
            {
                // 按创建时间排序，删除最旧的会话
                var sessionsToDelete = userSessions
                    .OrderBy(s => s.CreatedAt)
                    .Take(sessionCount - _options.MaxConcurrentSessions + 1);

                foreach (var session in sessionsToDelete)
                {
                    await DestroySessionAsync(session.SessionId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 清理用户多余会话失败 - UserId: {UserId}", userId);
        }
    }
}
