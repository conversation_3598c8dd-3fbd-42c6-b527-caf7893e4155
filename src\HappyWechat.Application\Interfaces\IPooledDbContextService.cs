using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// 池化DbContext服务接口
/// 提供高性能的数据库访问能力
/// </summary>
public interface IPooledDbContextService
{
    /// <summary>
    /// 获取池化的DbContext实例
    /// 注意：调用者负责释放返回的DbContext
    /// </summary>
    Task<DbContext> GetDbContextAsync();
    
    /// <summary>
    /// 执行查询操作（自动管理DbContext生命周期）
    /// 适用于只读查询操作，自动禁用变更跟踪以提升性能
    /// </summary>
    /// <typeparam name="TResult">查询结果类型</typeparam>
    /// <param name="query">查询操作委托</param>
    /// <returns>查询结果</returns>
    Task<TResult> ExecuteQueryAsync<TResult>(Func<DbContext, Task<TResult>> query);
    
    /// <summary>
    /// 执行命令操作（自动管理DbContext生命周期和事务）
    /// 适用于写入操作，自动包装事务确保数据一致性
    /// </summary>
    /// <typeparam name="TResult">操作结果类型</typeparam>
    /// <param name="command">命令操作委托</param>
    /// <returns>操作结果</returns>
    Task<TResult> ExecuteCommandAsync<TResult>(Func<DbContext, Task<TResult>> command);
    
    /// <summary>
    /// 批量操作（高性能批处理）
    /// 适用于大批量数据操作，优化性能和事务处理
    /// </summary>
    /// <param name="batchOperation">批量操作委托</param>
    Task ExecuteBatchAsync(Func<DbContext, Task> batchOperation);
    
    /// <summary>
    /// 获取池化统计信息
    /// </summary>
    PooledDbContextStatistics GetStatistics();
}

/// <summary>
/// 池化DbContext统计信息
/// </summary>
public class PooledDbContextStatistics
{
    /// <summary>
    /// 连接池大小
    /// </summary>
    public int PoolSize { get; set; }
    
    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }
    
    /// <summary>
    /// 可用连接数
    /// </summary>
    public int AvailableConnections { get; set; }
    
    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; set; }
    
    /// <summary>
    /// 平均操作时间（毫秒）
    /// </summary>
    public double AverageOperationTime { get; set; }
    
    /// <summary>
    /// 统计信息更新时间
    /// </summary>
    public DateTime LastStatisticsUpdate { get; set; }
    
    /// <summary>
    /// 成功操作数
    /// </summary>
    public long SuccessfulOperations { get; set; }
    
    /// <summary>
    /// 失败操作数
    /// </summary>
    public long FailedOperations { get; set; }
    
    /// <summary>
    /// 成功率（百分比）
    /// </summary>
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;
}