using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 群聊消息处理器 - 专门处理群聊消息的@逻辑和昵称替换
/// </summary>
public class GroupMessageProcessor : IGroupMessageProcessor
{
    private readonly IContactNicknameService _contactNicknameService;
    private readonly ILogger<GroupMessageProcessor> _logger;
    
    // @替换的正则表达式模式
    private static readonly Regex AtPatternRegex = new(@"@心\s*", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex GenericAtPatternRegex = new(@"@(\w+)\s*", RegexOptions.Compiled);

    public GroupMessageProcessor(
        IContactNicknameService contactNicknameService,
        ILogger<GroupMessageProcessor> logger)
    {
        _contactNicknameService = contactNicknameService;
        _logger = logger;
    }

    /// <summary>
    /// 处理群聊AI回复内容，替换@逻辑
    /// </summary>
    public async Task<string> ProcessGroupAiReplyAsync(
        string aiResponse, 
        Guid wxManagerId, 
        string senderWcId, 
        string processingId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                return aiResponse;
            }

            _logger.LogDebug("[{ProcessingId}] 开始处理群聊AI回复 - 原始内容长度: {Length}, 发送者: {SenderWcId}", 
                processingId, aiResponse.Length, senderWcId);

            // 1. 获取发送者昵称
            var senderNickname = await _contactNicknameService.GetContactNicknameAsync(wxManagerId, senderWcId);
            
            _logger.LogDebug("[{ProcessingId}] 获取发送者昵称成功 - WcId: {SenderWcId}, Nickname: {SenderNickname}", 
                processingId, senderWcId, senderNickname);

            // 2. 替换@心为实际昵称
            var processedContent = ReplaceAtMentions(aiResponse, senderNickname, processingId);

            // 🔧 精简日志：注释掉群聊AI回复处理完成日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] 群聊AI回复处理完成 - 原始长度: {OriginalLength}, 处理后长度: {ProcessedLength}, 发送者昵称: {SenderNickname}",
            //     processingId, aiResponse.Length, processedContent.Length, senderNickname);

            return processedContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理群聊AI回复失败 - 发送者: {SenderWcId}", processingId, senderWcId);
            
            // 发生错误时返回原始内容，但尝试简单替换
            return aiResponse.Replace("@心", "@群友");
        }
    }

    /// <summary>
    /// 批量处理群聊AI回复内容
    /// </summary>
    public async Task<List<string>> ProcessGroupAiRepliesAsync(
        List<string> aiResponses, 
        Guid wxManagerId, 
        string senderWcId, 
        string processingId)
    {
        if (!aiResponses?.Any() == true)
        {
            return aiResponses ?? new List<string>();
        }

        try
        {
            // 获取发送者昵称（只查询一次）
            var senderNickname = await _contactNicknameService.GetContactNicknameAsync(wxManagerId, senderWcId);
            
            var processedResponses = new List<string>();
            
            for (int i = 0; i < aiResponses.Count; i++)
            {
                var response = aiResponses[i];
                var processedResponse = ReplaceAtMentions(response, senderNickname, $"{processingId}-{i}");
                processedResponses.Add(processedResponse);
            }

            _logger.LogDebug("[{ProcessingId}] 批量处理群聊AI回复完成 - 数量: {Count}, 发送者昵称: {SenderNickname}", 
                processingId, aiResponses.Count, senderNickname);

            return processedResponses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 批量处理群聊AI回复失败 - 发送者: {SenderWcId}", processingId, senderWcId);
            
            // 发生错误时返回简单替换的结果
            return aiResponses.Select(response => response.Replace("@心", "@群友")).ToList();
        }
    }

    /// <summary>
    /// 检查是否需要@回复
    /// </summary>
    public bool ShouldAtReply(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage)
    {
        try
        {
            // 检查是否为群聊消息
            if (string.IsNullOrWhiteSpace(callbackMessage.Data?.FromGroup))
            {
                return false;
            }

            // 检查是否被@
            var atList = callbackMessage.Data.Atlist ?? new List<string>();
            var isAtMe = atList.Any(atId => atId == callbackMessage.WcId);

            _logger.LogDebug("检查@回复逻辑 - 群组: {GroupId}, 是否被@: {IsAtMe}, @列表: [{AtList}]", 
                callbackMessage.Data.FromGroup, isAtMe, string.Join(",", atList));

            return isAtMe;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查@回复逻辑失败");
            return false;
        }
    }

    /// <summary>
    /// 提取群聊消息中的发送者信息
    /// </summary>
    public GroupSenderInfo ExtractSenderInfo(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage)
    {
        try
        {
            return new GroupSenderInfo
            {
                SenderWcId = callbackMessage.Data?.FromUser ?? string.Empty,
                GroupId = callbackMessage.Data?.FromGroup ?? string.Empty,
                MessageContent = callbackMessage.Data?.Content ?? string.Empty,
                AtList = callbackMessage.Data?.Atlist ?? new List<string>(),
                IsAtBot = callbackMessage.Data?.Atlist?.Contains(callbackMessage.WcId) == true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取群聊发送者信息失败");
            return new GroupSenderInfo
            {
                SenderWcId = string.Empty,
                GroupId = string.Empty,
                MessageContent = string.Empty,
                AtList = new List<string>(),
                IsAtBot = false
            };
        }
    }

    /// <summary>
    /// 替换@提及内容
    /// </summary>
    private string ReplaceAtMentions(string content, string senderNickname, string processingId)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            return content;
        }

        try
        {
            var originalContent = content;
            
            // 1. 替换特定的@心模式
            content = AtPatternRegex.Replace(content, $"@{senderNickname} ");
            
            // 2. 记录替换结果
            if (originalContent != content)
            {
                _logger.LogDebug("[{ProcessingId}] @提及替换完成 - 原始: '{Original}' -> 替换后: '{Replaced}'", 
                    processingId, originalContent.Substring(0, Math.Min(50, originalContent.Length)), 
                    content.Substring(0, Math.Min(50, content.Length)));
            }

            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 替换@提及失败", processingId);
            return content;
        }
    }

    /// <summary>
    /// 🔧 新增：处理群聊AI回复并设置@信息
    /// </summary>
    public async Task<GroupAiReplyResult> ProcessGroupAiReplyWithAtAsync(
        string aiResponse,
        Guid wxManagerId,
        string senderWcId,
        string groupChatRoomId,
        bool shouldAtSender,
        string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔧 开始处理群聊AI回复（含@信息） - 发送者: {SenderWcId}, 群组: {GroupId}, 是否@发送者: {ShouldAt}",
                processingId, senderWcId, groupChatRoomId, shouldAtSender);

            // 1. 🔧 修复：优先从群组成员表获取昵称
            var senderNickname = await _contactNicknameService.GetGroupMemberNicknameAsync(wxManagerId, groupChatRoomId, senderWcId);

            // 2. 处理AI回复内容
            var processedContent = aiResponse;

            // 3. 如果需要@发送者，在内容前添加@昵称
            if (shouldAtSender && !string.IsNullOrEmpty(senderNickname))
            {
                // 检查内容是否已经包含@发送者
                if (!processedContent.Contains($"@{senderNickname}"))
                {
                    processedContent = $"@{senderNickname} {processedContent}";
                }
            }

            // 4. 替换其他@提及（如@心等）
            processedContent = ReplaceAtMentions(processedContent, senderNickname, processingId);

            var result = new GroupAiReplyResult
            {
                ProcessedContent = processedContent,
                AtUsers = shouldAtSender ? senderWcId : null,
                SenderNickname = senderNickname,
                SenderWcId = senderWcId,
                GroupChatRoomId = groupChatRoomId,
                IsSuccess = true
            };

            // 🔧 精简日志：注释掉群聊AI回复处理完成日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] ✅ 群聊AI回复处理完成 - 原始长度: {OriginalLength}, 处理后长度: {ProcessedLength}, 群组昵称: {SenderNickname}, AtUsers: {AtUsers}",
            //     processingId, aiResponse.Length, processedContent.Length, senderNickname, result.AtUsers);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 处理群聊AI回复失败 - 发送者: {SenderWcId}", processingId, senderWcId);

            return new GroupAiReplyResult
            {
                ProcessedContent = aiResponse.Replace("@心", "@群友"),
                AtUsers = null,
                SenderNickname = "未知用户",
                SenderWcId = senderWcId,
                GroupChatRoomId = groupChatRoomId,
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }
}

/// <summary>
/// 群聊发送者信息
/// </summary>
public class GroupSenderInfo
{
    public string SenderWcId { get; set; } = string.Empty;
    public string GroupId { get; set; } = string.Empty;
    public string MessageContent { get; set; } = string.Empty;
    public List<string> AtList { get; set; } = new();
    public bool IsAtBot { get; set; }
}

/// <summary>
/// 群聊AI回复结果
/// </summary>
public class GroupAiReplyResult
{
    /// <summary>
    /// 处理后的内容
    /// </summary>
    public string ProcessedContent { get; set; } = string.Empty;

    /// <summary>
    /// 需要@的用户ID（多个用逗号分隔）
    /// </summary>
    public string? AtUsers { get; set; }

    /// <summary>
    /// 发送者昵称
    /// </summary>
    public string SenderNickname { get; set; } = string.Empty;

    /// <summary>
    /// 发送者微信ID
    /// </summary>
    public string SenderWcId { get; set; } = string.Empty;

    /// <summary>
    /// 群聊ID
    /// </summary>
    public string GroupChatRoomId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
