using HappyWechat.Application.DTOs.AiConfig;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Moments;

/// <summary>
/// 朋友圈AI配置DTO
/// </summary>
public class MomentsAiConfigDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 配置名称
    /// </summary>
    public string ConfigName { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否启用AI自动评论回复
    /// </summary>
    public bool IsCommentReplyEnabled { get; set; } = false;
    
    /// <summary>
    /// 是否启用AI内容生成
    /// </summary>
    public bool IsContentGenerationEnabled { get; set; } = false;
    
    /// <summary>
    /// 是否启用AI自动点赞
    /// </summary>
    public bool IsAutoLikeEnabled { get; set; } = false;
    

    
    /// <summary>
    /// 关联的AI智能体ID（用于评论回复）
    /// </summary>
    public Guid? CommentReplyAiAgentId { get; set; }
    
    /// <summary>
    /// 评论回复AI智能体名称
    /// </summary>
    public string? CommentReplyAiAgentName { get; set; }
    
    /// <summary>
    /// 关联的AI智能体ID（用于内容生成）
    /// </summary>
    public Guid? ContentGenerationAiAgentId { get; set; }
    
    /// <summary>
    /// 内容生成AI智能体名称
    /// </summary>
    public string? ContentGenerationAiAgentName { get; set; }
    
    /// <summary>
    /// 评论回复自定义提示词
    /// </summary>
    public string? CommentReplyPrompt { get; set; }
    
    /// <summary>
    /// 内容生成自定义提示词
    /// </summary>
    public string? ContentGenerationPrompt { get; set; }
    
    /// <summary>
    /// 回复延迟时间（秒）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 30;
    
    /// <summary>
    /// 是否启用敏感词过滤
    /// </summary>
    public bool EnableSensitiveWordFilter { get; set; } = true;
    
    /// <summary>
    /// 评论触发条件
    /// </summary>
    public MomentsCommentTrigger CommentTrigger { get; set; } = new();
    
    /// <summary>
    /// 内容生成配置
    /// </summary>
    public MomentsContentConfig ContentConfig { get; set; } = new();
    
    /// <summary>
    /// 自动点赞配置
    /// </summary>
    public MomentsAutoLikeConfig AutoLikeConfig { get; set; } = new();
    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 朋友圈评论触发条件
/// </summary>
public class MomentsCommentTrigger
{
    /// <summary>
    /// 是否回复所有评论
    /// </summary>
    public bool ReplyToAllComments { get; set; } = false;
    
    /// <summary>
    /// 触发关键词列表
    /// </summary>
    public List<string> TriggerKeywords { get; set; } = new();
    
    /// <summary>
    /// 排除的评论者列表
    /// </summary>
    public List<string> ExcludedCommenters { get; set; } = new();
    
    /// <summary>
    /// 只回复好友评论
    /// </summary>
    public bool OnlyReplyToFriends { get; set; } = true;
    
    /// <summary>
    /// 最小评论间隔（秒）
    /// </summary>
    public int MinCommentInterval { get; set; } = 300;
    
    /// <summary>
    /// 最大回复长度
    /// </summary>
    public int MaxReplyLength { get; set; } = 100;
    
    /// <summary>
    /// 是否启用情感分析
    /// </summary>
    public bool EnableSentimentAnalysis { get; set; } = true;
    
    /// <summary>
    /// 只回复正面评论
    /// </summary>
    public bool OnlyReplyToPositiveComments { get; set; } = false;
    
    /// <summary>
    /// 是否回复自己的朋友圈
    /// </summary>
    public bool ReplyToOwnMoments { get; set; } = true;
}

/// <summary>
/// 朋友圈内容配置
/// </summary>
public class MomentsContentConfig
{
    /// <summary>
    /// 内容类型
    /// </summary>
    public MomentsContentType ContentType { get; set; } = MomentsContentType.Text;
    
    /// <summary>
    /// 内容主题
    /// </summary>
    public List<string> ContentThemes { get; set; } = new();
    
    /// <summary>
    /// 内容风格
    /// </summary>
    public string ContentStyle { get; set; } = "生活化";
    
    /// <summary>
    /// 内容长度限制
    /// </summary>
    public int MaxContentLength { get; set; } = 200;
    
    /// <summary>
    /// 是否包含表情符号
    /// </summary>
    public bool IncludeEmojis { get; set; } = true;
    
    /// <summary>
    /// 是否包含位置信息
    /// </summary>
    public bool IncludeLocation { get; set; } = false;
    
    /// <summary>
    /// 发布频率（每天）
    /// </summary>
    public int PublishFrequencyPerDay { get; set; } = 1;
    
    /// <summary>
    /// 最佳发布时间段
    /// </summary>
    public List<TimeSpan> OptimalPublishTimes { get; set; } = new() { new(8, 0, 0), new(12, 0, 0), new(20, 0, 0) };
    
    /// <summary>
    /// 可见性设置
    /// </summary>
    public MomentsVisibility Visibility { get; set; } = MomentsVisibility.AllFriends;
    
    /// <summary>
    /// 特定可见好友列表（当Visibility为SpecificFriends时使用）
    /// </summary>
    public List<string> VisibleToFriends { get; set; } = new();
    
    /// <summary>
    /// 不可见好友列表（当Visibility为ExcludeFriends时使用）
    /// </summary>
    public List<string> InvisibleToFriends { get; set; } = new();
}

/// <summary>
/// 朋友圈自动点赞配置
/// </summary>
public class MomentsAutoLikeConfig
{
    /// <summary>
    /// 是否启用自动点赞
    /// </summary>
    public bool IsEnabled { get; set; } = false;
    
    /// <summary>
    /// 点赞概率（0-100）
    /// </summary>
    public int LikeProbability { get; set; } = 30;
    
    /// <summary>
    /// 点赞延迟时间范围（秒）
    /// </summary>
    public int MinLikeDelaySeconds { get; set; } = 60;
    public int MaxLikeDelaySeconds { get; set; } = 300;
    
    /// <summary>
    /// 只点赞好友的朋友圈
    /// </summary>
    public bool OnlyLikeFriends { get; set; } = true;
    
    /// <summary>
    /// 排除的好友列表（不自动点赞）
    /// </summary>
    public List<string> ExcludedFriends { get; set; } = new();
    
    /// <summary>
    /// 特定点赞好友列表（优先点赞）
    /// </summary>
    public List<string> PreferredFriends { get; set; } = new();
    
    /// <summary>
    /// 每日最大点赞数量
    /// </summary>
    public int MaxLikesPerDay { get; set; } = 50;
}

/// <summary>
/// 朋友圈内容类型
/// </summary>
public enum MomentsContentType
{
    /// <summary>
    /// 纯文本
    /// </summary>
    Text = 1,
    
    /// <summary>
    /// 图文
    /// </summary>
    ImageText = 2,
    
    /// <summary>
    /// 视频
    /// </summary>
    Video = 3,
    
    /// <summary>
    /// 链接分享
    /// </summary>
    LinkShare = 4
}

/// <summary>
/// 朋友圈可见性
/// </summary>
public enum MomentsVisibility
{
    /// <summary>
    /// 所有好友可见
    /// </summary>
    AllFriends = 1,
    
    /// <summary>
    /// 仅自己可见
    /// </summary>
    OnlyMe = 2,
    
    /// <summary>
    /// 指定好友可见
    /// </summary>
    SpecificFriends = 3,
    
    /// <summary>
    /// 排除指定好友
    /// </summary>
    ExcludeFriends = 4
}

/// <summary>
/// 朋友圈消息类型
/// </summary>
public enum MomentsMessageType
{
    /// <summary>
    /// 评论
    /// </summary>
    Comment = 1,
    
    /// <summary>
    /// 点赞
    /// </summary>
    Like = 2,
    
    /// <summary>
    /// 新朋友圈动态
    /// </summary>
    NewMoment = 3
}

/// <summary>
/// 朋友圈AI处理结果
/// </summary>
public class MomentsAiResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// AI生成的内容
    /// </summary>
    public string? GeneratedContent { get; set; }
    
    /// <summary>
    /// 处理的消息类型
    /// </summary>
    public MomentsMessageType MessageType { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ProcessDurationMs { get; set; }
    
    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object>? ExtendedProperties { get; set; }
    
    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static MomentsAiResult Success(string content, MomentsMessageType messageType, long durationMs = 0)
    {
        return new MomentsAiResult
        {
            IsSuccess = true,
            GeneratedContent = content,
            MessageType = messageType,
            ProcessDurationMs = durationMs
        };
    }
    
    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static MomentsAiResult Failure(string errorMessage, MomentsMessageType messageType, long durationMs = 0)
    {
        return new MomentsAiResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            MessageType = messageType,
            ProcessDurationMs = durationMs
        };
    }
}
