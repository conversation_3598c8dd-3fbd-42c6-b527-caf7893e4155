namespace HappyWechat.Infrastructure.MessageProcessing.Models;

/// <summary>
/// 消息目标决策结果
/// </summary>
public class MessageTargetDecision
{
    /// <summary>
    /// 是否应该发送消息
    /// </summary>
    public bool ShouldSend { get; set; }

    /// <summary>
    /// 目标类型
    /// </summary>
    public MessageTargetType TargetType { get; set; }

    /// <summary>
    /// 目标ID
    /// </summary>
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 目标名称
    /// </summary>
    public string TargetName { get; set; } = string.Empty;

    /// <summary>
    /// 发送模式
    /// </summary>
    public MessageSendMode SendMode { get; set; } = MessageSendMode.Normal;

    /// <summary>
    /// 目标用户
    /// </summary>
    public string TargetUser { get; set; } = string.Empty;

    /// <summary>
    /// @用户列表
    /// </summary>
    public List<string> AtUsers { get; set; } = new();

    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }

    /// <summary>
    /// 是否需要群组@处理
    /// </summary>
    public bool RequiresGroupAtProcessing { get; set; }

    /// <summary>
    /// 原始发送者ID
    /// </summary>
    public string OriginalSenderId { get; set; } = string.Empty;

    /// <summary>
    /// 群组ID
    /// </summary>
    public string GroupId { get; set; } = string.Empty;

    /// <summary>
    /// 决策原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 是否应该处理
    /// </summary>
    public bool ShouldProcess { get; set; } = true;

    /// <summary>
    /// 是否等待组合
    /// </summary>
    public bool ShouldWaitForCombination { get; set; }

    /// <summary>
    /// 组合内容
    /// </summary>
    public string CombinedContent { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 延迟发送时间（毫秒）
    /// </summary>
    public int DelayMs { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();

    /// <summary>
    /// 决策时间
    /// </summary>
    public DateTime DecisionTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建发送决策
    /// </summary>
    public static MessageTargetDecision CreateSend(
        MessageTargetType targetType,
        string targetId,
        string targetName,
        string reason,
        MessageSendMode sendMode = MessageSendMode.Normal)
    {
        return new MessageTargetDecision
        {
            ShouldSend = true,
            TargetType = targetType,
            TargetId = targetId,
            TargetName = targetName,
            Reason = reason,
            SendMode = sendMode
        };
    }

    /// <summary>
    /// 创建不发送决策
    /// </summary>
    public static MessageTargetDecision CreateNoSend(string reason)
    {
        return new MessageTargetDecision
        {
            ShouldSend = false,
            Reason = reason
        };
    }

    /// <summary>
    /// 创建延迟发送决策
    /// </summary>
    public static MessageTargetDecision CreateDelayedSend(
        MessageTargetType targetType,
        string targetId,
        string targetName,
        int delayMs,
        string reason)
    {
        return new MessageTargetDecision
        {
            ShouldSend = true,
            TargetType = targetType,
            TargetId = targetId,
            TargetName = targetName,
            DelayMs = delayMs,
            Reason = reason,
            SendMode = MessageSendMode.Delayed
        };
    }
}

/// <summary>
/// 消息目标类型
/// </summary>
public enum MessageTargetType
{
    /// <summary>
    /// 私聊联系人
    /// </summary>
    Contact = 0,

    /// <summary>
    /// 群聊
    /// </summary>
    Group = 1,

    /// <summary>
    /// 系统通知
    /// </summary>
    System = 2
}

/// <summary>
/// 消息发送模式
/// </summary>
public enum MessageSendMode
{
    /// <summary>
    /// 正常发送
    /// </summary>
    Normal = 0,

    /// <summary>
    /// 私聊发送
    /// </summary>
    Private = 1,

    /// <summary>
    /// 群聊回复被@的消息
    /// </summary>
    GroupReplyMentioned = 2,

    /// <summary>
    /// 群聊回复所有消息
    /// </summary>
    GroupReplyAll = 3,

    /// <summary>
    /// 延迟发送
    /// </summary>
    Delayed = 4,

    /// <summary>
    /// 批量发送
    /// </summary>
    Batch = 5,

    /// <summary>
    /// 高优先级发送
    /// </summary>
    Priority = 6,

    /// <summary>
    /// 静默发送
    /// </summary>
    Silent = 7
}

/// <summary>
/// 文件@组合结果
/// </summary>
public class FileAtCombinationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 是否有组合
    /// </summary>
    public bool HasCombination { get; set; }

    /// <summary>
    /// 组合后的文件路径
    /// </summary>
    public string? CombinedFilePath { get; set; }

    /// <summary>
    /// 组合的文件数量
    /// </summary>
    public int FileCount { get; set; }

    /// <summary>
    /// 总文件大小
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static FileAtCombinationResult CreateSuccess(string combinedFilePath, int fileCount, long totalSize)
    {
        return new FileAtCombinationResult
        {
            Success = true,
            CombinedFilePath = combinedFilePath,
            FileCount = fileCount,
            TotalSize = totalSize
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static FileAtCombinationResult CreateFailure(string errorMessage)
    {
        return new FileAtCombinationResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
